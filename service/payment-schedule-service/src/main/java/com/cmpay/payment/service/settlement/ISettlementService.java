package com.cmpay.payment.service.settlement;

import com.cmpay.payment.schedule.bo.settlement.TradeSettlementBO;
import com.cmpay.payment.schedule.entity.pay.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;

/**
 * <AUTHOR>
 * @date 2025/6/30 14:57
 */
public interface ISettlementService {

    /**
     * 支付成功结算数据登记
     *
     * @param tradeOrderDO
     * @return
     */
    TradeSettlementBO registerPaymentData(TradeOrderDO tradeOrderDO);

    /**
     * 退款成功结算数据登记
     *
     * @param refundOrderDO
     * @return
     */
    TradeSettlementBO registerRefundData(RefundOrderDO refundOrderDO);
}
