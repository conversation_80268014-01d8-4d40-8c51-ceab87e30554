package com.cmpay.payment.service.pay;


import com.cmpay.payment.schedule.bo.pay.RefundOrderCloneBO;

/**
 * Created on 2018/11/14
 *
 * @author: chen_lan
 */

public interface UndefinedRefundQueryService {

    /**
     * 查询未明确状态退款订单
     * @param refundOrderCloneBO
     * @return
     */
    void undefinedRefundOrderQuery(RefundOrderCloneBO refundOrderCloneBO);

    /**
     * 查询已登记异步状态进行订单退款
     * @param refundOrderCloneBO
     * @return
     */
    void asynRefundOrder(RefundOrderCloneBO refundOrderCloneBO);
}
