package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author： PengAnHai
 * @date： 2024-08-21
 * @description：招行一网通支付订单查询分布式任务
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class CmbaionOrderSyn {

    @Autowired
    private ICommonManagerDataService commonManagerDataService;
    @Autowired
    private TimerRunLocationService runLocationService;

    /**
     * 招行一网通支付订单查询分布式任务
     * @return
     */
    @XxlJob("CmbaionOrderSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> CmbaionOrderSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerData(PaymentWayEnum.CMBAION.name().toLowerCase(),shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

}
