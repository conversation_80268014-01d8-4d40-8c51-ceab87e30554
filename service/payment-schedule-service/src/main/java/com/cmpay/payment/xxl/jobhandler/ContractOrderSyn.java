package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.schedule.service.withhold.IContractWithholdServiceExt;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.schedule.service.withhold.IExtContractWithholdService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Created on 2020/7/28
 */
@Component
public class ContractOrderSyn extends XxlJobExecutor {

    private static final Logger logger = LoggerFactory.getLogger(ContractOrderSyn.class);

    @Autowired
    private TimerRunLocationService runLocationService;
    @Autowired
    private IExtContractWithholdService contractWithholdService;
    @Autowired
    private ICommonManagerDataService managerDataService;
    @Autowired
    private IContractWithholdServiceExt contractWithholdServiceExt;

    /**
     * 非和包协议查询
     * @param
     * @return
     */
    @XxlJob("wechatContractSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> wechatContractSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return managerDataService.managerDataContract(PaymentWayEnum.WECHAT.name().toLowerCase(),shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

    @XxlJob("wechatHourContractSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> wechatHourContractSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return managerDataService.managerDataHourContract(PaymentWayEnum.WECHAT.name().toLowerCase(),shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

    @XxlJob("alipayContractSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> alipayContractSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return managerDataService.managerDataContract(PaymentWayEnum.ALIPAY.name().toLowerCase(),shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("alipayHourContractSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> alipayHourContractSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return managerDataService.managerDataHourContract(PaymentWayEnum.ALIPAY.name().toLowerCase(),shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    /**
     * 和包协议查询
     * @param
     * @return
     */
    @XxlJob("cmpayContractSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> cmpayContractSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return managerDataService.managerDataHourContract(PaymentWayEnum.CMPAY.name().toLowerCase(),shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("contractCleanAndBackupSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> contractCleanAndBackupSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        // 分批次操作数据
        logger.info("contractCleanAndBackupSyn");
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        logger.info("分片参数：当前分片序号 = {}, 总分片数 = {}", shardIndex, shardTotal);
        if (!managerDataService.dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        // 查询要进行备份的数据
        boolean result = contractWithholdServiceExt.clearAndBackupContract(shardIndex, shardTotal,param, Boolean.FALSE,scheduleRunBO.getDataScope());
        return result ? ReturnT.SUCCESS : ReturnT.FAIL;
    }

    @XxlJob("terminationContractCleanAndBackupSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> terminationContractCleanAndBackupSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        logger.info("terminationContractCleanAndBackupSyn");
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        logger.info("分片参数：当前分片序号 = {}, 总分片数 = {}", shardIndex, shardTotal);
        if (!managerDataService.dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        // 查询要进行备份的数据
        boolean result = contractWithholdServiceExt.clearAndBackupContract(shardIndex, shardTotal,param, Boolean.TRUE, scheduleRunBO.getDataScope());
        return result ? ReturnT.SUCCESS : ReturnT.FAIL;
    }
}
