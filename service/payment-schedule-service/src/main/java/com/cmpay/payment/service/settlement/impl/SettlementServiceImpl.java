package com.cmpay.payment.service.settlement.impl;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.schedule.bo.pay.RefundOrderCloneBO;
import com.cmpay.payment.schedule.bo.settlement.TradeSettlementBO;
import com.cmpay.payment.schedule.entity.pay.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.schedule.service.ext.data.CompleteOrderSplitService;
import com.cmpay.payment.schedule.service.ext.data.IDataRefundSettlementService;
import com.cmpay.payment.schedule.service.ext.data.IDataSuccessSettlementService;
import com.cmpay.payment.schedule.service.ext.data.SubOrderInfosResolveService;
import com.cmpay.payment.schedule.service.pay.ExtPayOrderService;
import com.cmpay.payment.service.settlement.ISettlementService;
import com.cmpay.payment.utils.JhIdGenUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/30 14:59
 */
@Service
@Slf4j
public class SettlementServiceImpl implements ISettlementService {

    @Autowired
    private CompleteOrderSplitService splitService;
    @Autowired
    private IDataSuccessSettlementService successSettlementService;
    @Autowired
    private IDataRefundSettlementService refundSettlementService;
    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private SubOrderInfosResolveService resolveService;

    @Override
    public TradeSettlementBO registerPaymentData(TradeOrderDO tradeOrder) {
        if (JudgeUtils.equalsAny(tradeOrder.getStatus(), OrderStatusEnum.TRADE_SUCCESS.name(), OrderStatusEnum.REFUND_SUCCESS.name(),
                OrderStatusEnum.REFUND_PART.name(), OrderStatusEnum.REFUND_PEND.name(), OrderStatusEnum.REFUND_FAIL.name(),
                OrderStatusEnum.REFUND_WAIT.name(), OrderStatusEnum.REFUND_ACCEPT.name())) {
            try {
                //登记结算表
                TradeSettlementBO tradeSettlementBO = registerSettlement(tradeOrder);
                // 拆分子订单进结算
                splitService.paymentOrderSplit(tradeOrder, tradeSettlementBO);
                return tradeSettlementBO;
            } catch (Exception e) {
                log.error("登记支付成功结算数据异常，订单信息：{}，{}", tradeOrder, e);
            }
        }
        return null;

    }

    @Override
    public TradeSettlementBO registerRefundData(RefundOrderDO refundOrderDO) {
        try {
            if (JudgeUtils.equals(OrderStatusEnum.REFUND_SUCCESS.name(), refundOrderDO.getStatus())) {
                TradeSettlementBO tradeSettlementBO = registerRefundSettlementData(refundOrderDO);
                return tradeSettlementBO;
            }
        } catch (Exception e) {
            log.error("登记退款成功结算数据异常，订单信息：{},{}", refundOrderDO, e);
        }
        return null;
    }


    private TradeSettlementBO registerRefundSettlementData(RefundOrderDO refundOrderDO) {
        RefundOrderCloneBO refundOrderCloneBO = new RefundOrderCloneBO();
        BeanUtils.copyProperties(refundOrderDO, refundOrderCloneBO);
        TradeOrderDO originalPayTradeOrderDO = payOrderService.get(refundOrderDO.getOrgOrderNo());
        if (JudgeUtils.isNull(originalPayTradeOrderDO)) {
            log.info("Org Payment Order of Refund Order Not Exists,tradeOrderNo is {},orgOrderNo is {},msgcd is {}",
                    refundOrderDO.getTradeOrderNo(), refundOrderDO.getOrgOrderNo(), MsgCodeEnum.REFUND_ORG_ORDER_NOT_EXISTS.getMsgCd());
            return null;
        }
        if (OrderStatusEnum.REFUND_SUCCESS.name().equals(refundOrderDO.getStatus())) {
            //登记结算
            TradeSettlementBO settlementBO = registerRefundSettlement(refundOrderCloneBO, originalPayTradeOrderDO);
            // 分账订单拆分子结算
            splitService.refundOrderSplit(refundOrderDO, originalPayTradeOrderDO, settlementBO.getSettlementDate());
            return settlementBO;
        }
        return null;
    }

    /**
     * 登记结算
     */
    private TradeSettlementBO registerSettlement(TradeOrderDO tradeOrderDO) {
        TradeSettlementBO tradeSettlementBO = new TradeSettlementBO();
        tradeSettlementBO.setDcepFlag(tradeOrderDO.getDcepFlag());
        tradeSettlementBO.setTradeOrderNo(tradeOrderDO.getTradeOrderNo());
        tradeSettlementBO.setTradeJrnNo(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.SETTLE_JRN_NO, 18));
        tradeSettlementBO.setSettlementDate(com.cmpay.lemon.common.utils.DateTimeUtils.getCurrentDateStr());
        tradeSettlementBO.setOrderDate(tradeOrderDO.getOrderDate());
        tradeSettlementBO.setOrderTime(tradeOrderDO.getOrderTime());
        tradeSettlementBO.setMerchantNo(tradeOrderDO.getMerchantNo());
        tradeSettlementBO.setProvinceName(tradeOrderDO.getProvinceName());
        tradeSettlementBO.setProvinceCode(tradeOrderDO.getProvinceCode());
        tradeSettlementBO.setOutTradeNo(tradeOrderDO.getOutTradeNo());
        tradeSettlementBO.setRequestDate(tradeOrderDO.getRequestDate());
        tradeSettlementBO.setBankOrderNo(tradeOrderDO.getBankOrderNo());
        tradeSettlementBO.setBankMerchantNo(tradeOrderDO.getBankMerchantNo());
        tradeSettlementBO.setPaymentChannel(tradeOrderDO.getPayProductCode());
        tradeSettlementBO.setOrderScene(tradeOrderDO.getPayWayCode());
        tradeSettlementBO.setBusinessType(tradeOrderDO.getBusinessType());
        tradeSettlementBO.setOrderAmount(tradeOrderDO.getOrderAmount());
        tradeSettlementBO.setPaymentAmount(tradeOrderDO.getRealAmount());
        tradeSettlementBO.setJkFeeAmount(tradeOrderDO.getOrderFeeAmount());
        tradeSettlementBO.setRefundAmount(BigDecimal.ZERO);
        tradeSettlementBO.setRefundFeeAmount(BigDecimal.ZERO);
        tradeSettlementBO.setOrderRate(tradeOrderDO.getOrderRate());
        tradeSettlementBO.setOrderCompleteTime(com.cmpay.lemon.common.utils.DateTimeUtils.getCurrentDateTimeStr());
        tradeSettlementBO.setCardType(tradeOrderDO.getCrdAcTyp());
        tradeSettlementBO.setRefundFeeWay(tradeOrderDO.getRefundFeeWay());
        tradeSettlementBO.setRemark(tradeOrderDO.getRemark());
        tradeSettlementBO.setSettlementStatus(SettlementStatusEnum.SETTLEMENT_SUCCESS.getDesc());
        tradeSettlementBO.setAimProductCode(tradeOrderDO.getAimProductCode());
        tradeSettlementBO.setAccountDate(tradeOrderDO.getAccountDate());
        tradeSettlementBO.setServiceCharge(tradeOrderDO.getServiceCharge());
        tradeSettlementBO.setSubMerchantNo(tradeOrderDO.getSubMerchantNo());
        tradeSettlementBO.setSettlementDept(tradeOrderDO.getSettlementDept());
        tradeSettlementBO.setSettlementItem(tradeOrderDO.getSettlementItem());
        tradeSettlementBO.setMerchantChannelType(tradeOrderDO.getMerchantChannelType());
        tradeSettlementBO.setSplitFlag(tradeOrderDO.getSplitFlag());
        tradeSettlementBO.setOrderPoints(tradeOrderDO.getOrderPoints());
        tradeSettlementBO.setInstDiscountUnsettledAmount(tradeOrderDO.getInstDiscountUnsettledAmount());
        tradeSettlementBO.setInstDiscountSettlementAmount(tradeOrderDO.getInstDiscountSettlementAmount());
        tradeSettlementBO.setInstPaidAmount(tradeOrderDO.getInstPaidAmount());
        successSettlementService.orderSuccessSettlement(tradeSettlementBO);
        return tradeSettlementBO;
    }

    /**
     * 登记结算
     */
    private TradeSettlementBO registerRefundSettlement(RefundOrderCloneBO refundOrderDO, TradeOrderDO tradeOrderDO) {
        String settlementDate = com.cmpay.lemon.common.utils.DateTimeUtils.getCurrentDateStr();
        TradeSettlementBO tradeSettlementBO = new TradeSettlementBO();
        tradeSettlementBO.setTradeOrderNo(refundOrderDO.getTradeOrderNo());
        tradeSettlementBO.setTradeJrnNo(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.SETTLE_JRN_NO, 18));
        tradeSettlementBO.setSettlementDate(settlementDate);
        tradeSettlementBO.setOrderDate(refundOrderDO.getOrderDate());
        tradeSettlementBO.setOrderTime(refundOrderDO.getOrderTime());
        tradeSettlementBO.setMerchantNo(refundOrderDO.getMerchantNo());
        tradeSettlementBO.setProvinceName(refundOrderDO.getProvinceName());
        tradeSettlementBO.setProvinceCode(refundOrderDO.getProvinceCode());
        tradeSettlementBO.setOutTradeNo(refundOrderDO.getOrgOrderNo());
        tradeSettlementBO.setRequestDate(refundOrderDO.getRequestDate());
        tradeSettlementBO.setBankOrderNo(refundOrderDO.getBankOrderNo());
        tradeSettlementBO.setTradeRefundNo(refundOrderDO.getTradeOrderNo());
        if (DcepConstants.DCEP.equals(refundOrderDO.getDcepFlag())) {
            tradeSettlementBO.setTradeRefundNo(refundOrderDO.getOutTradeNo());
            tradeSettlementBO.setDcepFlag(DcepConstants.DCEP);
        }
        tradeSettlementBO.setBankMerchantNo(refundOrderDO.getBankMerchantNo());
        tradeSettlementBO.setPaymentChannel(refundOrderDO.getPayProductCode());
        tradeSettlementBO.setOrderScene(refundOrderDO.getPayWayCode());
        tradeSettlementBO.setBusinessType(refundOrderDO.getBusinessType());
        tradeSettlementBO.setOrderAmount(refundOrderDO.getOrderAmount());
        tradeSettlementBO.setPaymentAmount(BigDecimal.ZERO);
        tradeSettlementBO.setPaymentFeeAmount(BigDecimal.ZERO);
        tradeSettlementBO.setRefundAmount(refundOrderDO.getOrderAmount());
        tradeSettlementBO.setJkFeeAmount(refundOrderDO.getOrderFeeAmount());
        tradeSettlementBO.setOrderRate(BigDecimal.ZERO);
        tradeSettlementBO.setOrderCompleteTime(com.cmpay.lemon.common.utils.DateTimeUtils.getCurrentDateTimeStr());
        tradeSettlementBO.setCardType(tradeOrderDO.getCrdAcTyp());
        tradeSettlementBO.setRefundFeeWay(refundOrderDO.getRefundFeeWay());
        tradeSettlementBO.setRemark(refundOrderDO.getRemark());
        tradeSettlementBO.setSettlementStatus(SettlementStatusEnum.SETTLEMENT_REFUND.getDesc());
        tradeSettlementBO.setAimProductCode(refundOrderDO.getAimProductCode());
        tradeSettlementBO.setAccountDate(refundOrderDO.getAccountDate());
        tradeSettlementBO.setServiceCharge(tradeOrderDO.getServiceCharge());
        if (!StringUtils.equals(tradeOrderDO.getSplitFlag(), Constants.Y) && StringUtils.isNotEmpty(tradeOrderDO.getSubOrderInfos())) {
            resolveService.getCrossRefundSubOrderInfo(tradeSettlementBO, refundOrderDO.getTradeOrderNo(), tradeOrderDO.getSubOrderInfos());
        } else {
            tradeSettlementBO.setSubMerchantNo(tradeOrderDO.getSubMerchantNo());
            tradeSettlementBO.setSettlementDept(tradeOrderDO.getSettlementDept());
            tradeSettlementBO.setSettlementItem(tradeOrderDO.getSettlementItem());
            tradeSettlementBO.setMerchantChannelType(tradeOrderDO.getMerchantChannelType());
        }
        tradeSettlementBO.setSplitFlag(refundOrderDO.getSplitFlag());
        tradeSettlementBO.setOrderPoints(tradeOrderDO.getOrderPoints());
        tradeSettlementBO.setInstDiscountUnsettledAmount(refundOrderDO.getInstDiscountUnsettledAmount());
        tradeSettlementBO.setInstDiscountSettlementAmount(refundOrderDO.getInstDiscountSettlementAmount());
        tradeSettlementBO.setInstPaidAmount(refundOrderDO.getInstPaidAmount());
        return refundSettlementService.orderRefundSettlement(tradeSettlementBO);
    }

}
