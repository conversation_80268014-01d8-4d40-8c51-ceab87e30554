package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Created on 2020/7/28
 */
@Component
public class ContractDeleteOrderSyn extends XxlJobExecutor {
    @Autowired
    private TimerRunLocationService runLocationService;
    @Autowired
    private ICommonManagerDataService commonManagerDataService;

    @XxlJob("cmpayContractDeleteSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> cmpayContractDeleteSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerDataContractDelete(PaymentWayEnum.CMPAY.name().toLowerCase(),shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

    @XxlJob("alipayContractDeleteSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> alipayContractDeleteSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerDataContractDelete(PaymentWayEnum.ALIPAY.name().toLowerCase(),shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("wechatContractDeleteSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> wechatContractDeleteSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerDataContractDelete(PaymentWayEnum.WECHAT.name().toLowerCase(),shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }
}
