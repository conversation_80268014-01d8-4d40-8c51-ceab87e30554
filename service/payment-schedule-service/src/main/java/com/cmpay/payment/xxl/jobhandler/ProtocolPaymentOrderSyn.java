package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO;
import com.cmpay.payment.schedule.bo.pay.PaymentQueryBO;
import com.cmpay.payment.schedule.bo.notify.TradeOrderAndNotifyBO;
import com.cmpay.payment.constant.AppEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.schedule.entity.pay.OrderPaymentAttachDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.schedule.service.ext.async.AsynCommonService;
import com.cmpay.payment.schedule.service.pay.ExtPayOrderAttachService;
import com.cmpay.payment.schedule.service.pay.ExtPayOrderService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.cmpay.payment.service.pay.TradeSynService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * Created on 2020/8/31
 */
@Component
public class ProtocolPaymentOrderSyn extends XxlJobExecutor {

    private static final Logger logger = LoggerFactory.getLogger(ProtocolPaymentOrderSyn.class);

    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private ExtPayOrderAttachService payOrderAttachService;
    @Autowired
    private TradeSynService tradeSynService;
    @Autowired
    private ICommonManagerDataService managerDataService;
    @Autowired
    private TimerRunLocationService runLocationService;
    @Autowired
    private AsynCommonService asynCommonService;

    private static final String TOTAL = "2000";

    @XxlJob("protocolPaymentOrderSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> protocolPaymentOrderSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        logger.info("分片参数：当前分片序号 = {}, 总分片数 = {}", shardIndex, shardTotal);
        if (!managerDataService.dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        String param=XxlJobHelper.getJobParam();
        //查询订单的起始时间和结束
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        FapOrderQueryBO queryBO = new FapOrderQueryBO();
        queryBO.setRequestDate(DateTimeUtils.getCurrentDateStr());
        queryBO.setOrderDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(5)));
        queryBO.setOrderTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusHours(2)));
        queryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
        queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
        queryBO.setCurrentIndex(shardIndex);
        queryBO.setTotalSelect(shardTotal - 1);
        try {
            queryBO.setDataCenter(scheduleRunBO.getDataScope());
        } catch (BusinessException e) {
            logger.info("DecpPayment handle fail:{}", e.getMessage());
            return ReturnT.FAIL;
        }
        List<TradeOrderDO> tradeOrderDOList = payOrderService.queryIcbcWaitPaymentScheduleList(queryBO);
        if (tradeOrderDOList.size() == 0) {
            logger.info("ICBC Payment Order is Empty return !!");
            return ReturnT.SUCCESS;
        }
        for (TradeOrderDO tradeOrder : tradeOrderDOList) {
            logger.info("当前分片: {}, 处理对象: {}", shardIndex, tradeOrder.getOutTradeNo());
            PaymentQueryBO paymentQueryBO = new PaymentQueryBO();
            OrderPaymentAttachDO attachDO = new OrderPaymentAttachDO();
            attachDO.setOrderDate(tradeOrder.getRequestDate());
            attachDO.setOutTradeNo(tradeOrder.getOutTradeNo());
            OrderPaymentAttachDO orderPaymentAttachDO = payOrderAttachService.load(attachDO);
            if (JudgeUtils.isNull(orderPaymentAttachDO)) {
                continue;
            }
            if (JudgeUtils.isEmpty(orderPaymentAttachDO.getAgreementId()) &&JudgeUtils.isEmpty(orderPaymentAttachDO.getSubMerchantId()) ) {
                continue;
            }
            paymentQueryBO.setPaymentOrder(tradeOrder);
            paymentQueryBO.setOrderPaymentAttachDO(orderPaymentAttachDO);
            try {
                paymentQueryBO.setSourceApp(AppEnum.integrationshecdule.name());
                TradeOrderAndNotifyBO orderAndNotifyBO = tradeSynService.paymentOrderSyn(paymentQueryBO);
                if (JudgeUtils.isNull(orderAndNotifyBO) || JudgeUtils.isNull(orderAndNotifyBO.getTradeOrderDO())){
                    continue;
                }
                //后台通知
                asynCommonService.asyncNotify(orderAndNotifyBO.getTradeNotifyBO());
            } catch (Exception e) {
                if (e instanceof BusinessException) {
                    logger.info("ICBCOrderInfo:{},error msg:{}", tradeOrder.getTradeOrderNo(), e.getMessage());
                } else {
                    logger.error("ICBCOrderInfo:{},error msg:{}", tradeOrder.getTradeOrderNo(), e);
                }
            }
        }
        return ReturnT.SUCCESS;
    }
}
