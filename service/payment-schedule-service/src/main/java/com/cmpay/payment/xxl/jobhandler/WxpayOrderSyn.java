package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Created on 2020/7/28
 */
@Component
public class WxpayOrderSyn extends XxlJobExecutor {

    @Autowired
    private ICommonManagerDataService commonManagerDataService;
    @Autowired
    private TimerRunLocationService runLocationService;

    @XxlJob("wxpayOrderSyn")
    @InitialLemonData("lemonDataInitializer")
    @DistributedLocked(lockName = "'wxpayAllOrderSyn'", leaseTime = 40, waitTime = 10)
    public ReturnT<String> wpayOrderSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerData(PaymentWayEnum.WECHAT.name().toLowerCase(), shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

    @XxlJob("wxpayOrderScanBarcodeSyn")
    @InitialLemonData("lemonDataInitializer")
    @DistributedLocked(lockName = "'wxpayOrderScanBarcodeSyn'", leaseTime = 40, waitTime = 10)
    public ReturnT<String> wxpayOrderScanBarcodeSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataScanBarcode(PaymentWayEnum.WECHAT.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("wxpayOrderFirstSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> wxpayOrderFirstSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataFirst(PaymentWayEnum.WECHAT.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("wxpayOrderSecondSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> wxpayOrderSecondSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataSecond(PaymentWayEnum.WECHAT.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("wxpayOrderThirdSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> wxpayOrderThirdSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataThird(PaymentWayEnum.WECHAT.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("wxpayOrderFourSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> wxpayOrderFourSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataFour(PaymentWayEnum.WECHAT.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("wxpayOrderScanBarcodeFirstSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> wxpayOrderScanBarcodeFirstSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataScanBarcodeFirst(PaymentWayEnum.WECHAT.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("wxpayOrderScanBarcodeSecondSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> wxpayOrderScanBarcodeSecondSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataScanBarcodeSecond(PaymentWayEnum.WECHAT.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("wxpayOrderScanBarcodeThirdSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> wxpayOrderScanBarcodeThirdSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataScanBarcodeThird(PaymentWayEnum.WECHAT.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }
}
