package com.cmpay.payment.service.base.impl;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.constant.AlipayTradeStatusEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.schedule.ScheduleDataCenterEnum;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.schedule.service.config.IExtParamInfoService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.cmpay.payment.utils.PaymentUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;

/**
 * @date 2024-05-16 09:27
 * <AUTHOR>
 * @Version 1.0
 */
@Service
@Slf4j
public class TimerRunLocationServiceImpl implements TimerRunLocationService {

    @Autowired
    private IExtParamInfoService paramInfoService;

    @Override
    public ScheduleRunBO getScheduleRunParams() {
        String paramLocation = paramInfoService.getScheduleDataCenter();
        String location = PaymentUtils.getDataCenterFlag();
        ScheduleRunBO scheduleRunBO = new ScheduleRunBO();
        ScheduleDataCenterEnum dataCenterEnum = EnumUtils.getEnum(ScheduleDataCenterEnum.class, paramLocation);
        switch (dataCenterEnum) {
            case XPQ:
            case YL:
                if (JudgeUtils.equalsIgnoreCase(paramLocation, location)) {
                    scheduleRunBO.setEnd(false);
                }else{
                    scheduleRunBO.setEnd(true);
                    log.info("非定时器任务指定运行机房{}，任务在本机房{}终止！",paramLocation,location);
                }
                break;
            case ALL:
                scheduleRunBO.setEnd(false);
                scheduleRunBO.setDataScope(location);
                break;
            default:
                break;
        }
        return scheduleRunBO;
    }

}
