package com.cmpay.payment.complaint.service;

import com.cmpay.payment.entity.FapCustomerComplaintDO;

import java.time.LocalDate;
import java.util.List;

public interface IFapCustomerComplaintService {
    /**
     * 查询未完成列表
     */
    List<FapCustomerComplaintDO> findNotFinishedList(LocalDate beginDate, LocalDate endDate);

    /**
     * 新增投诉列表
     */
    void insertComplaintList(List<FapCustomerComplaintDO> complaintDOS);

    /**
     * 更新投诉状态
     */
    void updateComplaintState(FapCustomerComplaintDO complaintDO);
}
