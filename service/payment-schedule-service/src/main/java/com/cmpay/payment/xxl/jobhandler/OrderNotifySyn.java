package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.schedule.entity.notify.TradeNotifyRecordDO;
import com.cmpay.payment.schedule.entity.notify.TradeNotifyRecordExtDO;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.schedule.service.ext.data.TradeNotifyService;
import com.cmpay.payment.schedule.service.notify.ExtNotifyRecordService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * Created on 2020/8/3
 */
@Component
public class OrderNotifySyn extends XxlJobExecutor {

    private static Logger logger = LoggerFactory.getLogger(OrderNotifySyn.class);

    @Autowired
    private ExtNotifyRecordService notifyRecordService;
    @Autowired
    private TradeNotifyService tradeNotifyService;
    @Autowired
    private ICommonManagerDataService managerDataService;
    @Autowired
    private TimerRunLocationService runLocationService;

    private static final String TOTAL = "2000";
    private static final long MIN_START = 60;
    private static final long MIN_END = 5;

    @XxlJob("orderNotify")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT orderNotify() {
        ScheduleRunBO scheduleRunBO = runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)) {
            return ReturnT.FAIL;
        }
        if (scheduleRunBO.isEnd()) {
            return ReturnT.SUCCESS;
        }
        String logInfo = "orderNotify"+DateTimeUtils.formatLocalDateTime(DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr()));
        logger.info("start {}",logInfo);
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        logger.info("分片参数：当前分片序号 = {}, 总分片数 = {}", shardIndex, shardTotal);
        if (!managerDataService.dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        TradeNotifyRecordExtDO tradeNotifyRecordDO = new TradeNotifyRecordExtDO();
        String queryDate = DateTimeUtils.formatLocalDate(DateTimeUtils.getCurrentLocalDate().minusDays(1));
        tradeNotifyRecordDO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
        LocalDateTime beginDate=DateTimeUtils.getCurrentLocalDateTime().minusMinutes(MIN_START);
        LocalDateTime endDateTime=DateTimeUtils.getCurrentLocalDateTime().minusMinutes(MIN_END);
        // 支持指定当前时间前多少分钟查询  任务参数配置 500,5,60
        if(StringUtils.isNotBlank(param)){
            // 获取当前日期时间
            String[] params = param.split(",");
            String totalNum = params[0];
            tradeNotifyRecordDO.setTotal(StringUtils.isNotBlank(totalNum) ? totalNum : TOTAL);
            if(params.length >= 2){
                //说明有第二个参数
                long num = Long.parseLong(params[1]);
                // 设置时间
                endDateTime=DateTimeUtils.getCurrentLocalDateTime().minusMinutes(num);
            }
            if(params.length >= 3){
                //说明有第二个参数
                long beginMin = Long.parseLong(params[2]);
                // 设置时间
                beginDate=DateTimeUtils.getCurrentLocalDateTime().minusMinutes(beginMin);
            }
        }
        LocalDate nowDate = LocalDate.now();
        if(nowDate.isEqual(beginDate.toLocalDate())){
            queryDate = DateTimeUtils.formatLocalDate(DateTimeUtils.getCurrentLocalDate());
        }
        tradeNotifyRecordDO.setJrnNo(DateTimeUtils.formatLocalDateTime(beginDate));
        tradeNotifyRecordDO.setJrnNoEnd(DateTimeUtils.formatLocalDateTime(endDateTime));
        tradeNotifyRecordDO.setNotifyEndDate(DateTimeUtils.getCurrentDateStr());
        tradeNotifyRecordDO.setNotifyDate(queryDate);
        tradeNotifyRecordDO.setTotalSelect(shardTotal - 1);
        tradeNotifyRecordDO.setCurrentIndex(shardIndex);
        try {
            tradeNotifyRecordDO.setDataCenter(scheduleRunBO.getDataScope());
        } catch (BusinessException e) {
            logger.info("Notify handle fail:{}", e.getMessage());
            return new ReturnT(ReturnT.FAIL_CODE,logInfo);
        }
        List<TradeNotifyRecordDO> notifyRecordDOList = notifyRecordService.queryWaitNotifyScheduleRecord(tradeNotifyRecordDO);
        if (notifyRecordDOList.size() == 0) {
            logger.info("Notify Order is Empty!!");
            logger.info("end {}",logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE,logInfo);
        }
        for (TradeNotifyRecordDO tradeNotify : notifyRecordDOList) {
            logger.info("当前分片: {}, 处理对象: {}", shardIndex, tradeNotify.getOutOrderNo());

            TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
            tradeNotifyBO.setTradeOrderNo(tradeNotify.getTradeOrderNo());
            tradeNotifyBO.setOutOrderNo(tradeNotify.getOutOrderNo());
            tradeNotifyBO.setTradeAmount(tradeNotify.getTradeAmount());
            tradeNotifyBO.setDiscountableAmount(tradeNotify.getDiscountableAmount());
            tradeNotifyBO.setTradeDate(tradeNotify.getTradeDate());
            tradeNotifyBO.setNotifyDate(tradeNotify.getNotifyDate());
            tradeNotifyBO.setNotifyTime(tradeNotify.getNotifyTime());
            tradeNotifyBO.setNotifyType(tradeNotify.getNotifyType());
            tradeNotifyBO.setNotifyUrl(tradeNotify.getNotifyUrl());
            tradeNotifyBO.setOutRequestNo(tradeNotify.getOutRequestNo());
            tradeNotifyBO.setMerchantNo(tradeNotify.getMerchantNo());
            tradeNotifyBO.setJournalNo(tradeNotify.getJrnNo());
            tradeNotifyBO.setExtra(tradeNotify.getRemark());
            tradeNotifyBO.setNotifyCount(tradeNotify.getNotifyCount());
            tradeNotifyBO.setSecretIndex(tradeNotify.getSecretIndex());
            tradeNotifyBO.setFinishDate(tradeNotify.getFinishTime());
            tradeNotifyBO.setFirstNotify(false);
            try {
                tradeNotifyService.tradeNotify(tradeNotifyBO);
            } catch (Exception e) {
                if (e instanceof BusinessException) {
                    logger.info("tradeOrderNo:{},error msg:{}", tradeNotifyBO.getTradeOrderNo(), e.getMessage());
                } else {
                    logger.error("tradeOrderNo:{},error msg:{}", tradeNotifyBO.getTradeOrderNo(), e);
                }
            }
        }
        logger.info("end {}",logInfo);
        return new ReturnT(ReturnT.SUCCESS_CODE,logInfo);
    }
}
