package com.cmpay.payment.complaint.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.framework.datasource.TargetDataSource;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.complaint.StateEnum;
import com.cmpay.payment.dao.IFapCustomerComplaintDao;
import com.cmpay.payment.entity.FapCustomerComplaintDO;
import com.cmpay.payment.complaint.service.IFapCustomerComplaintService;
import com.cmpay.payment.entity.FapCustomerComplaintDOKey;
import com.cmpay.payment.utils.DateTimeConvertUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

@Service
public class FapCustomerComplaintServiceImpl implements IFapCustomerComplaintService {
    private static final Logger logger = LoggerFactory.getLogger(FapCustomerComplaintServiceImpl.class);

    @Resource
    private IFapCustomerComplaintDao complaintDao;

    @Override
    @TargetDataSource("oceanBaseDB")
    public List<FapCustomerComplaintDO> findNotFinishedList(LocalDate beginDate, LocalDate endDate) {
        return complaintDao.findNotFinishedList(DateTimeUtils.formatLocalDate(beginDate),
                DateTimeUtils.formatLocalDate(endDate));
    }

    @Override
    @TargetDataSource("oceanBaseDB")
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void insertComplaintList(List<FapCustomerComplaintDO> complaintDOS) {
        for (FapCustomerComplaintDO complaintDO : complaintDOS) {

            complaintDO.setComplaintDate(getComplaintDate(complaintDO.getComplaintTime()));

            // 记录存在则跳出本次循环，继续下一条
            FapCustomerComplaintDOKey complaintDOKey = new FapCustomerComplaintDOKey();
            complaintDOKey.setComplaintChannel(complaintDO.getComplaintChannel());
            complaintDOKey.setComplaintDate(complaintDO.getComplaintDate());
            complaintDOKey.setComplaintId(complaintDO.getComplaintId());
            if (complaintDao.get(complaintDOKey) != null) {
                continue;
            }

            complaintDao.insert(complaintDO);
        }
    }

    @Override
    @TargetDataSource("oceanBaseDB")
    public void updateComplaintState(FapCustomerComplaintDO complaintDO) {
        FapCustomerComplaintDO fapCustomerComplaintDO = new FapCustomerComplaintDO();
        fapCustomerComplaintDO.setComplaintId(complaintDO.getComplaintId());
        fapCustomerComplaintDO.setComplaintDate(complaintDO.getComplaintDate());
        fapCustomerComplaintDO.setComplaintChannel(complaintDO.getComplaintChannel());
        fapCustomerComplaintDO.setComplaintState(StateEnum.PROCESSED.getCode());
        if (complaintDao.update(fapCustomerComplaintDO) != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_COMPLAINT_STATE_ERROR);
        }
    }

    /**
     * 根据投诉时间获取投诉日期
     * yyyy-MM-dd HH:mm:ss->yyyyMMdd
     */
    private String getComplaintDate(String complaintTime) {
        return DateTimeConvertUtils.convertDateTimeFormat(complaintTime, DateTimeConvertUtils.STANDARD_DATE_TIME, DateTimeConvertUtils.DEFAULT_DATE);
    }
}
