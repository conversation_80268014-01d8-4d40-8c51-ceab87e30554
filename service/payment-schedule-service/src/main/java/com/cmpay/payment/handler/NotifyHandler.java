package com.cmpay.payment.handler;

import com.cmpay.framework.data.message.CmpayCmdDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.stream.MessageHandler;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.TradeTypeEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.schedule.entity.pay.RefundOrderDO;
import com.cmpay.payment.schedule.service.ext.data.TradeNotifyService;
import com.cmpay.payment.schedule.service.pay.ExtPayOrderService;
import com.cmpay.payment.schedule.service.pay.ExtRefundOrderService;
import com.cmpay.payment.service.settlement.ISettlementService;
import com.cmpay.payment.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created on 2019/05/13
 *
 * <AUTHOR>
 */
@Component("notifyHandler")
@Slf4j
public class NotifyHandler implements MessageHandler<TradeNotifyBO, CmpayCmdDTO<TradeNotifyBO>> {

    @Autowired
    private TradeNotifyService notifyService;
    @Autowired
    private ISettlementService settlementService;
    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private ExtRefundOrderService refundOrderService;

    /**
     * 支付成功 通知应用
     *
     * @param cmpayCmdDTO
     */
    @Override
    public void onMessageReceive(CmpayCmdDTO<TradeNotifyBO> cmpayCmdDTO) {
        TradeNotifyBO tradeNotifyBO = cmpayCmdDTO.getBody();
        // 3天前的通知不进行通知
        if (JudgeUtils.isBlank(tradeNotifyBO.getNotifyDate()) ||
                DateTimeUtils.isBeforeNdays(tradeNotifyBO.getNotifyDate(), 3)) {
            return;
        }
        switch (TradeTypeEnum.valueOf(tradeNotifyBO.getNotifyType().toUpperCase())) {
            case PAYMENT:
                TradeOrderDO tradeOrderDO = payOrderService.get(tradeNotifyBO.getTradeOrderNo());
                if (JudgeUtils.isNull(tradeOrderDO)) {
                    BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
                }
                settlementService.registerPaymentData(tradeOrderDO);
                tradeNotifyBO.setTradeOrderDO(tradeOrderDO);
                notifyService.tradeNotify(tradeNotifyBO);
                break;
            case REFUND:
                RefundOrderDO refundOrderDO = refundOrderService.get(tradeNotifyBO.getTradeOrderNo());
                settlementService.registerRefundData(refundOrderDO);
                tradeNotifyBO.setRefundOrderDO(refundOrderDO);
                notifyService.tradeNotify(tradeNotifyBO);
                break;
            case SIGN:
            case ABOLISH:
            case ADD:
            case DELETE:
                notifyService.tradeNotify(tradeNotifyBO);
                break;
            default:
                BusinessException.throwBusinessException(MsgCodeEnum.TREAD_TYPE_ERROR);
        }
    }

}
