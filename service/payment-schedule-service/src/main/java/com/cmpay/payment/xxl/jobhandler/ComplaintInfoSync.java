package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.framework.data.utils.GwaUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import com.cmpay.payment.complaint.service.IComplaintScheduleService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class ComplaintInfoSync {
    @Resource
    private IComplaintScheduleService complaintScheduleService;

    @XxlJob("complaintDataSyncScheduleJob")
    @InitialLemonData("lemonDataInitializer")
    @DistributedLocked(lockName = "'complaintDataSync'", leaseTime = 3600)
    public void complaintDataSync() {
        // 记录标识requestId，区分日志
        MDC.put("requestId", "complaintDataSync" + GwaUtils.getJrnNo());

        // 应用日志
        log.info("complaintDataSyncScheduleJob Start With JrnNo [{}] ......", GwaUtils.getJrnNo());

        // 调用实际业务逻辑处理
        complaintScheduleService.complaintDataSync();
    }

    @XxlJob("complaintStatusSyncScheduleJob")
    @InitialLemonData("lemonDataInitializer")
    @DistributedLocked(lockName = "'complaintStatusSync'", leaseTime = 3600)
    public void complaintStatusSync() {
        // 记录标识requestId，区分日志
        MDC.put("requestId", "complaintStatusSync" + GwaUtils.getJrnNo());

        // 应用日志
        log.info("complaintDataSyncScheduleJob Start With JrnNo [{}] ......", GwaUtils.getJrnNo());

        // 调用实际业务逻辑处理
        complaintScheduleService.complaintStatusSync();
    }
}
