package com.cmpay.payment.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @date 2022-10-28 09:35
 * <AUTHOR>
 * @Version 1.0
 */
@Configuration
@ConfigurationProperties(prefix = "shanxiblockchaincheck")
public class ShanxiBlockCheckProperties {
    /**
     * 请求地址
     */
    private String url;
    /**
     * 请求路径
     */
    private String requestPath;
    //**********************线下邮件提供**********************
    /**
     * EOS账户名字
     */
    private String actor;
    /**
     * EOS账户公钥
     */
    private String publicKey;
    /**
     * 钱包名
     */
    private String walletName;
    /**
     * 钱包密码
     */
    private String walletPassword;
    //**********************定值**********************
    /**
     * 区块链类型
     */
    private String chaincodeType;
    /**
     * 调用的EOS智能合约方法
     */
    private String contractAction;
    /**
     * EOS合约托管账户名字
     */
    private String contractName;
    /**
     * EOS账户权限
     */
    private String permission;
    /**
     * 系统名称
     */
    private String nm;
    /**
     * 渠道编码
     */
    private String channel;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getRequestPath() {
        return requestPath;
    }

    public void setRequestPath(String requestPath) {
        this.requestPath = requestPath;
    }

    public String getActor() {
        return actor;
    }

    public void setActor(String actor) {
        this.actor = actor;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getWalletName() {
        return walletName;
    }

    public void setWalletName(String walletName) {
        this.walletName = walletName;
    }

    public String getWalletPassword() {
        return walletPassword;
    }

    public void setWalletPassword(String walletPassword) {
        this.walletPassword = walletPassword;
    }

    public String getChaincodeType() {
        return chaincodeType;
    }

    public void setChaincodeType(String chaincodeType) {
        this.chaincodeType = chaincodeType;
    }

    public String getContractAction() {
        return contractAction;
    }

    public void setContractAction(String contractAction) {
        this.contractAction = contractAction;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getNm() {
        return nm;
    }

    public void setNm(String nm) {
        this.nm = nm;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
}
