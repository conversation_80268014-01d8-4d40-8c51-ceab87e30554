package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.lemon.framework.lock.DistributedLocked;

import com.cmpay.payment.constant.schedule.ScheduleDataCenterEnum;
import com.cmpay.payment.schedule.bo.settlement.TradeSettlementBO;
import com.cmpay.payment.schedule.entity.settlement.TradeSettlementDO;
import com.cmpay.payment.schedule.service.settlement.IExtTradeSettlementService;
import com.cmpay.payment.service.settlement.IBlockChainCheckService;
import com.cmpay.payment.utils.BeanConvertUtils;
import com.cmpay.payment.utils.PaymentUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @date 2022-10-13 16:13
 * <AUTHOR>
 * @Version 1.0
 */
@Component
public class ShanxiBlockChainCheckSynSchedule {
    private static final Logger logger = LoggerFactory.getLogger(ShanxiBlockChainCheckSynSchedule.class);
    @Resource(name = "redisTemplate")
    private RedisTemplate redisTemplate;

    @Autowired
    IBlockChainCheckService iBlockChainCheckService;

    @Autowired
    IExtTradeSettlementService iExtTradeSettlementService;

    /**
     * 山西省份码
     */
    public static final String SHANXI_PROVINCE_CODE = "0351";
    /**
     * 山西支付业务区块链对账，缓存key，最大流水号
     */
    public static final String SHANXI_BLOCK_CHAIN_CHECK_TRADE_JRN_KEY = "SHANXI_BLOCK_CHAIN_CHECK_TRADE_JRN_KEY";



    /**
     * 根据数据量:月初峰值3万笔，按翻倍6万笔算，需要跑60000/100=600次；
     * 一天1440分钟，则1440/600 = 2.3 分钟跑一次，故设置为2分钟跑一次 锁5分钟，执行完会自动释放，没执行完不允许下一次任务执行
     */
    @XxlJob("shanxiBlockChainCheckSyn")
    @InitialLemonData("lemonDataInitializer")
    @DistributedLocked(lockName = "'shanxiBlockChainCheckSyn'", leaseTime = 300, waitTime = 10)
    public void shanxiBlockChainCheckSyn() {
        String location = PaymentUtils.getDataCenterFlag();
        if(JudgeUtils.notEquals(location, ScheduleDataCenterEnum.XPQ.name())){
            return;
        }
        logger.info("山西支付业务区块链对账开始:{}", DateTimeUtils.getCurrentDateTimeStr());
        //2、redis获取上一次对账文件最大时间，和流水号，如果没有则查查询当天第一笔，写入redis
        String lastNumber = (String) redisTemplate.opsForValue().get(SHANXI_BLOCK_CHAIN_CHECK_TRADE_JRN_KEY);
        TradeSettlementDO tradeSettlementDO = null;
        if (JudgeUtils.isEmpty(lastNumber)) {
            //redis 失效则从今日凌晨开始对账
            tradeSettlementDO = iExtTradeSettlementService.getFirstSettlementByDay(SHANXI_PROVINCE_CODE);
            lastNumber = tradeSettlementDO.getTradeJrnNo();
            redisTemplate.opsForValue().set(SHANXI_BLOCK_CHAIN_CHECK_TRADE_JRN_KEY, lastNumber, 1, TimeUnit.HOURS);
            logger.info("上一次对账订单流水号为空，重新设值");
        }
        //3、根据时间查询结算表，并更新redis时间
        String param = XxlJobHelper.getJobParam();
        param=StringUtils.isNotBlank(param) ? param : "100";
        List<TradeSettlementDO> tradeSettlementDOS = iExtTradeSettlementService.getSettlementByProvinceCode(lastNumber, SHANXI_PROVINCE_CODE,param);
        if (JudgeUtils.isNotNull(tradeSettlementDO)) {
            //当天第一笔加入通知集合
            tradeSettlementDOS.add(tradeSettlementDO);
        }
        // 查出为空或者size<=0,说明暂时对账完成
        if (JudgeUtils.isEmpty(tradeSettlementDOS)) {
            logger.info("山西支付业务对账完成，上一次对账流水为:{}", lastNumber);
            redisTemplate.opsForValue().set(SHANXI_BLOCK_CHAIN_CHECK_TRADE_JRN_KEY, lastNumber, 1, TimeUnit.HOURS);
            return;
        } else {
            String maxNumber = tradeSettlementDOS.get(0).getTradeJrnNo();
            redisTemplate.opsForValue().set(SHANXI_BLOCK_CHAIN_CHECK_TRADE_JRN_KEY, maxNumber, 1, TimeUnit.HOURS);
            logger.info("此次对账最大流水号设值为:{}", maxNumber);
        }
        List<TradeSettlementBO> tradeSettlementBOS = BeanConvertUtils.convertList(tradeSettlementDOS, TradeSettlementBO.class);
        //4、ogw-out通知对账
        iBlockChainCheckService.blockChainCheck(tradeSettlementBOS);

    }
}
