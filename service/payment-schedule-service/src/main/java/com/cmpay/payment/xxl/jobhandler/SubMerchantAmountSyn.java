package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import com.cmpay.payment.constant.schedule.ScheduleDataCenterEnum;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.schedule.service.pay.IExtCountSubMerTradeAmountService;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author： pengAnHai
 * @date： 2024-05-13
 * @description：
 */
@Slf4j
@Component
public class SubMerchantAmountSyn {

    @Autowired
    private ICommonManagerDataService commonManagerDataService;
    @Autowired
    private IExtCountSubMerTradeAmountService subMerTradeAmountService;
    @Autowired
    private TimerRunLocationService runLocationService;

    @XxlJob("addSubMerchantPaymentAmountSyn")
    @InitialLemonData("lemonDataInitializer")
    @DistributedLocked(lockName = "'addSubMerchantPaymentAmountSyn'", leaseTime = 50, waitTime = 10)
    public ReturnT<String> addSubMerchantPaymentAmountSyn() {
        ScheduleRunBO scheduleRunBO = runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)) {
            return ReturnT.FAIL;
        }
        if (scheduleRunBO.isEnd()) {
            return ReturnT.SUCCESS;
        }
        if(JudgeUtils.isNotNull(scheduleRunBO.getDataScope())){
            if(JudgeUtils.notEquals(scheduleRunBO.getDataScope(), ScheduleDataCenterEnum.XPQ.name())){
                return ReturnT.FAIL;
            }
        }
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        String logInfo = "SubMerchantAmountSyn" + DateTimeUtils.getCurrentDateTimeStr();
        log.info("start {}", logInfo);
        if (!commonManagerDataService.dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        boolean result = subMerTradeAmountService.addSubMerchantPaymentTotalAmountByMin();
        return result ? ReturnT.SUCCESS : ReturnT.FAIL;
    }

}
