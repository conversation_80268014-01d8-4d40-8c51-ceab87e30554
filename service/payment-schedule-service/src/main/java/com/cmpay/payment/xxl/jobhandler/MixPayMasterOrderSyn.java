package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.payment.constant.schedule.ScheduleDataCenterEnum;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.cmpay.payment.utils.PaymentUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @date 2024-10-08 15:40
 * <AUTHOR>
 * @Version 1.0
 */
@Component
public class MixPayMasterOrderSyn extends XxlJobExecutor {

    @Autowired
    private ICommonManagerDataService commonManagerDataService;
    @Autowired
    private TimerRunLocationService runLocationService;
    /**
     * 子商户混合支付查询主订单信息
     * @return
     */
    @XxlJob("masterOrderQuerySyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> masterOrderQuerySyn() {
        ScheduleRunBO scheduleRunBO = runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)) {
            return ReturnT.FAIL;
        }
        if (scheduleRunBO.isEnd()) {
            return ReturnT.SUCCESS;
        }
        if(JudgeUtils.isNotNull(scheduleRunBO.getDataScope())){
            if(JudgeUtils.notEquals(scheduleRunBO.getDataScope(), ScheduleDataCenterEnum.XPQ.name())){
                return ReturnT.FAIL;
            }
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerDataMasterOrder(shardIndex, shardTotal, param);
    }
}
