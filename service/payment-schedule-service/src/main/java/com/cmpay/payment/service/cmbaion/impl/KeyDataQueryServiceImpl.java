package com.cmpay.payment.service.cmbaion.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.schedule.bo.config.ContractMerKeyQueryBO;
import com.cmpay.payment.constant.AppEnum;
import com.cmpay.payment.constant.Constants;
import com.cmpay.payment.service.cmbaion.CmbaionMerKeyQueryChannelService;
import com.cmpay.payment.service.cmbaion.KeyDataQueryService;
import com.cmpay.payment.schedule.service.config.IExtContractService;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author： PengAnHai
 * @date： 2024-08-22
 * @description： 查询公钥处理类
 * @modifiedBy：
 * @version: 1.0
 */
@Slf4j
@Service
public class KeyDataQueryServiceImpl implements KeyDataQueryService {

    @Autowired
    private IExtContractService contractService;
    @Autowired
    private CmbaionMerKeyQueryChannelService cmbaionMerKeyQueryChannelService;

    @Override
    public ReturnT<String> keyDataQuery(String channel, String param) {
        // 解析参数
        String[] params = param.split(Constants.COMMA);
        // 如果分布式参数大于2个就判定为手动查询
        boolean runFlag = params.length < 2;
        // 构建日志信息
        String logInfo = channel + "keyDataQuery" + DateTimeUtils.getCurrentDateTimeStr();
        // 初始化合同商户查询对象
        ContractMerKeyQueryBO contractQueryBO = new ContractMerKeyQueryBO();
        contractQueryBO.setPaymentMechanism(channel);
        contractQueryBO.setTotal(JudgeUtils.isNotBlank(params[0]) ? params[0] : Constants.MERCHANT_TOTAL);
        // 查询支付机构商户信息
        List<ContractMerKeyQueryBO> contractQueryBOS = contractService.queryContractMerchant(contractQueryBO);
        // 如果查询结果为空，记录日志并返回成功状态
        if (JudgeUtils.isEmpty(contractQueryBOS)) {
            log.info("{},ContractMerchant is Empty!!, end {}", channel, logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        }
        try {
            // 记录开始查询日志
            log.info("{} keyDataQuery is start!", channel);
            // 发送商户数据查询公钥
            sendMerchantData(contractQueryBOS, runFlag);
            // 记录结束查询日志
            log.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (BusinessException e) {
            // 捕捉业务异常
            log.info("keyDataQuery fail: {}", e.getMessage());
            return ReturnT.FAIL;
        } catch (Exception e) {
            // 捕捉其他所有异常
            log.error("keyDataQuery unexpected failure: {}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 处理一组ContractMerKeyQueryBO对象，检查每个对象的密钥是否过期，并根据需要发送商户数据。
     *
     * @param contractQueryBOS 要处理的ContractMerKeyQueryBO对象列表
     * @param runFlag 一个布尔标志，指示是否根据密钥过期时间运行该过程或跳过
     */
    private void sendMerchantData(List<ContractMerKeyQueryBO> contractQueryBOS, boolean runFlag) {
        // 遍历提供的ContractMerKeyQueryBO列表中的每个对象
        for (ContractMerKeyQueryBO contractQueryBO : contractQueryBOS) {
            // 记录当前正在处理的商户编号
            log.info("当处理对象: {}", contractQueryBO.getMerchantNumber());
            // 检查当前时间是否小于或等于密钥过期时间且runFlag为true，如果是，则跳过当前循环
            if (JudgeUtils.isNotBlank(contractQueryBO.getKeyExpireTime()) &&
                    DateTimeUtils.getCurrentDateTimeStr().compareTo(contractQueryBO.getKeyExpireTime()) <= 0 && runFlag) {
                continue;
            }
            try {
                // 设置来源应用程序名称
                contractQueryBO.setSourceApp(AppEnum.integrationshecdule.name());
                // 调用服务处理商户密钥查询
                cmbaionMerKeyQueryChannelService.cmbaionMerKeyQuery(contractQueryBO);
            } catch (BusinessException e) {
                // 记录业务异常的信息，包括商户编号、错误代码和错误消息，日志级别为警告
                log.warn("ContractMerchantInfo: {}, 错误代码: {}, 错误信息: {}",
                        contractQueryBO.getMerchantNumber(), e.getMsgCd(), e.getMsgInfo());
            } catch (Exception e) {
                // 记录所有未预见的异常，包括商户编号和异常详细信息，日志级别为错误
                log.error("ContractMerchantInfo: {}, 意外错误: {}",
                        contractQueryBO.getMerchantNumber(), e);
            }
        }
    }
}
