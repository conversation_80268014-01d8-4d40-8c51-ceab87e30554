package com.cmpay.payment.service.pay.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.schedule.bo.config.PayServiceBeanBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.schedule.bo.notify.TradeOrderAndNotifyBO;
import com.cmpay.payment.schedule.bo.pay.PaymentQueryBO;
import com.cmpay.payment.schedule.bo.settlement.OrderFeeBO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.schedule.service.config.IExtRateService;
import com.cmpay.payment.schedule.service.ext.async.AsynCommonService;
import com.cmpay.payment.schedule.service.ext.data.CompleteOrderSplitService;
import com.cmpay.payment.schedule.service.ext.data.IDataFeeService;
import com.cmpay.payment.schedule.service.ext.risk.RiskControlService;
import com.cmpay.payment.schedule.service.pay.ExtPayOrderService;
import com.cmpay.payment.service.pay.TradeSynService;
import com.cmpay.payment.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * Created on 2018/12/04
 *
 * @author: li_zhen
 */
@Service
public class TradeSynServiceImpl implements TradeSynService {
    private static final Logger logger = LoggerFactory.getLogger(TradeSynServiceImpl.class);
    @Autowired
    private ExtPayOrderService paymentOrderService;

    @Autowired
    private IDataFeeService dataFeeService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IExtRateService rateService;
    @Autowired
    private RiskControlService riskControlService;
    @Autowired
    private AsynCommonService asynCommonService;
    @Autowired
    private CompleteOrderSplitService splitService;

    @Override
    public TradeOrderAndNotifyBO paymentOrderSyn(PaymentQueryBO paymentQueryBO) {
        logger.info("paymentOrderSyn request before:{}", JSONObject.toJSONString(paymentQueryBO));
        applicationContext.publishEvent(paymentQueryBO);
        logger.info("paymentOrderSyn request after:{}", JSONObject.toJSONString(paymentQueryBO));
        TradeOrderDO tradeOrder = paymentQueryBO.getPaymentOrder();
        if (JudgeUtils.isNull(paymentQueryBO.getStatus()) || StringUtils.equals(paymentQueryBO.getStatus(), OrderStatusEnum.WAIT_PAY.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_STATUS_UNDEFINED);
        }
        if (JudgeUtils.equals(tradeOrder.getAimProductCode(), PaymentWayEnum.CMBAION.name().toLowerCase())) {
            return null;
        }
        tradeOrder.setStatus(paymentQueryBO.getStatus());
        if (JudgeUtils.isBlank(paymentQueryBO.getFinishDateTime())) {
            paymentQueryBO.setFinishDateTime(DateTimeUtils.getCurrentDateTimeStr());
        }
        //保存报错信息
        tradeOrder.setErrMsgCd(paymentQueryBO.getErrMsgCd());
        tradeOrder.setErrMsgInfo(paymentQueryBO.getErrMsgInfo());
        tradeOrder.setOrderCompleteTime(paymentQueryBO.getFinishDateTime());
        //判断是否从CmPayPos进入
        if (JudgeUtils.equals(Constants.CMPAY_POS_FLG, paymentQueryBO.getCmpayPosFlag())) {
            tradeOrder.setReceiveNotifyTime(paymentQueryBO.getFinishDateTime());
        }
        tradeOrder.setBnkTraceNo(paymentQueryBO.getBnkTraceNo());
        // 仅在支付成功时，设置账期
        if (JudgeUtils.equals(OrderStatusEnum.TRADE_SUCCESS.name(), tradeOrder.getStatus())) {
            tradeOrder.setAccountDate(DateUtils.verifiAndSetAccountDate((paymentQueryBO.getPaymentOrder().getAccountDate())));
        }
        if (JudgeUtils.isEmpty(paymentQueryBO.getCrdType())) {
            tradeOrder.setCrdAcTyp("2");
        } else {
            tradeOrder.setCrdAcTyp(paymentQueryBO.getCrdType());
        }
        if (StringUtils.isEmpty(tradeOrder.getDcepFlag())) {
            if (!JudgeUtils.equalsAny(tradeOrder.getAimProductCode(), PaymentWayEnum.ICBCPAY.name().toLowerCase(), PaymentWayEnum.DCEPPAY.name().toLowerCase())) {
                //查询商户配置费率
                PayServiceBeanBO rateBO = new PayServiceBeanBO();
                rateBO.setMerchantNo(tradeOrder.getMerchantNo());
                rateBO.setPaymentChannl(tradeOrder.getPayProductCode());
                rateBO.setOrderScene(tradeOrder.getPayWayCode());
                rateBO.setOrderAmount(tradeOrder.getRealAmount());
                rateBO.setOrderDate(DateTimeUtils.getCurrentDateStr());
                rateBO.setProvinceCode(tradeOrder.getProvinceCode());
                if (JudgeUtils.equals(tradeOrder.getPayProductCode(), PaymentWayEnum.UNIONPAY.name().toLowerCase())) {
                    rateBO.setCardType(paymentQueryBO.getCrdType());
                    if (JudgeUtils.equals(paymentQueryBO.getCrdType(), CardTypeEnum.DEFUND_CARD_TYPE.name())) {
                        rateBO.setCardType(CardTypeEnum.CEBIT_CARD_TYPE.name());
                    }
                    rateBO = rateService.findUnionPayPaymentRout(rateBO);
                } else {
                    rateBO = rateService.findPaymentRout(rateBO);
                }
                tradeOrder.setOrderRate(rateBO.getRate());
                //计算服务费
                OrderFeeBO orderFeeBO = new OrderFeeBO();
                orderFeeBO.setOrderAmount(tradeOrder.getRealAmount());
                orderFeeBO.setOrderRate(tradeOrder.getOrderRate());
                orderFeeBO.setMaxFeeLimit(rateBO.getMaxFeeLimit());
                orderFeeBO.setMinFeeLimit(rateBO.getMinFeeLimit());
                orderFeeBO.setInstDiscountUnsettledAmount(paymentQueryBO.getInstDiscountUnsettledAmount());
                orderFeeBO = dataFeeService.feeCalculate(orderFeeBO);
                tradeOrder.setOrderFeeAmount(orderFeeBO.getOrderFeeAmount());
                tradeOrder.setRefundFeeWay(rateBO.getRefundFeeWay());
                //处理银联优惠信息
                unionpayDiscountInfoDeal(paymentQueryBO.getUnionpayDiscountInfo(), tradeOrder);
                //分账订单计算服务费（子订单向上取整后求和）
                splitService.splitOrderFeeCalculate(tradeOrder);
            }
        }
        if (JudgeUtils.isNotEmpty(paymentQueryBO.getPayAmountList())) {
            // 支付金额明细集合转换成json字符串存入订单表
            tradeOrder.setPayAmountList(JSONObject.toJSONString(paymentQueryBO.getPayAmountList()));
        }
        if (JudgeUtils.isNotNull(paymentQueryBO.getInstPaidAmount())) {
            tradeOrder.setInstPaidAmount(paymentQueryBO.getInstPaidAmount());
        }
        if (JudgeUtils.isNotNull(paymentQueryBO.getInstDiscountSettlementAmount())) {
            tradeOrder.setInstDiscountSettlementAmount(paymentQueryBO.getInstDiscountSettlementAmount());
        }
        if (JudgeUtils.isNotNull(paymentQueryBO.getInstDiscountUnsettledAmount())) {
            tradeOrder.setInstDiscountUnsettledAmount(paymentQueryBO.getInstDiscountUnsettledAmount());
        }
        if (0 == paymentOrderService.updatePaymentStatus(tradeOrder)) {
            return null;
        }
        asynCommonService.asynConnectThirdNo(tradeOrder);
        riskControlService.riskAfterPay(tradeOrder);
        // 新增签约扣款失败通知 订单失败，订单关闭，订单成功
        TradeOrderAndNotifyBO orderAndNotifyBO = new TradeOrderAndNotifyBO();
        orderAndNotifyBO.setTradeOrderDO(tradeOrder);
        boolean ifNotify = JudgeUtils.equals(paymentQueryBO.getStatus(), OrderStatusEnum.TRADE_SUCCESS.name())
                || (
                JudgeUtils.equalsIgnoreCase(tradeOrder.getPayWayCode(), PaymentSceneEnum.CONTRACTPAY.name())
                        && JudgeUtils.notEquals(paymentQueryBO.getStatus(), OrderStatusEnum.WAIT_PAY.name())
        );
        if (ifNotify) {
            TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
            tradeNotifyBO.setMerchantNo(tradeOrder.getMerchantNo());
            tradeNotifyBO.setTradeOrderNo(tradeOrder.getTradeOrderNo());
            tradeNotifyBO.setOutOrderNo(tradeOrder.getOutTradeNo());
            tradeNotifyBO.setTradeDate(tradeOrder.getRequestDate());
            tradeNotifyBO.setTradeAmount(tradeOrder.getOrderAmount());
            tradeNotifyBO.setDiscountableAmount(tradeOrder.getDiscountableAmount());
            tradeNotifyBO.setNotifyType(TradeTypeEnum.PAYMENT.name().toLowerCase());
            tradeNotifyBO.setNotifyUrl(tradeOrder.getNotifyUrl());
            tradeNotifyBO.setExtra(tradeOrder.getRemark());
            tradeNotifyBO.setSecretIndex(paymentQueryBO.getPaymentOrder().getSecretIndex());
            tradeNotifyBO.setFinishDate(paymentQueryBO.getFinishDateTime());
            orderAndNotifyBO.setTradeNotifyBO(tradeNotifyBO);
        }

        return orderAndNotifyBO;
    }

    /**
     * 处理银联优惠信息
     */
    private void unionpayDiscountInfoDeal(String unionpayDiscountInfo, TradeOrderDO tradeOrder) {
        if (JudgeUtils.isBlank(unionpayDiscountInfo)) {
            return;
        }
        try {
            String[] strs = unionpayDiscountInfo.split(Constants.HASH, -1);
            tradeOrder.setDiscountAmount(new BigDecimal(strs[0]));
            tradeOrder.setActivityId(strs[1]);
            tradeOrder.setActivityName(strs[2]);
        } catch (Exception e) {
            logger.error("deal union pay discount info fail, discount info:{},{} ", unionpayDiscountInfo, e);
        }
    }

}
