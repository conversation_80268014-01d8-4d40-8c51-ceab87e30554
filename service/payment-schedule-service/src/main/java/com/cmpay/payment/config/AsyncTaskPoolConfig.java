package com.cmpay.payment.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@Slf4j
public class AsyncTaskPoolConfig {
    @Value("${asyncPool.corePoolSize:}")
    private int corePoolSize;
    @Value("${asyncPool.maxPoolSize:}")
    private int maxPoolSize;

    @Bean(name = "asyncTaskExecutor")
    public Executor taskExecutor() {
        log.info("AsyncTaskPoolConfig corePoolSize:{},maxPoolSize:{}", corePoolSize, maxPoolSize);
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数为
        executor.setCorePoolSize(corePoolSize);
        // 设置最大线程数为
        executor.setMaxPoolSize(maxPoolSize);
        // 空闲线程存活时间
        executor.setKeepAliveSeconds(60);
        // 设置队列容量
        executor.setQueueCapacity(100);
        // 设置拒绝策略为 CallerRunsPolicy 任务被回退到调用线程中执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}