package com.cmpay.payment.bo;

import lombok.Data;

@Data
public class ComplaintSyncBO {
    /**
     * 起始日期
     */
    private String beginDate;
    /**
     * 结束日期
     */
    private String endDate;
    /**
     * 起始日期时间
     */
    private String beginDateTime;
    /**
     * 结束日期时间
     */
    private String endDateTime;
    /**
     * 投诉渠道
     */
    private String complaintChannel;
    /**
     * 商户编号
     */
    private String merchantId;
    /**
     * 计数条数开始位置
     */
    private Integer offset;
    /**
     * 每页条数
     */
    private Integer limit;
    /**
     * 投诉总条数
     */
    private Integer totalCount;
    /**
     * 页码
     */
    private Long pageNum;
    /**
     * 每页条数
     */
    private Long pageSize;
    /**
     * 请求号
     */
    private String requestId;
}
