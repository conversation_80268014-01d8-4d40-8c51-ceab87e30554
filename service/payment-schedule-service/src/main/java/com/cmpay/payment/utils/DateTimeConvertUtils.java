package com.cmpay.payment.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class DateTimeConvertUtils {
    public static final DateTimeFormatter DEFAULT_DATE_TIME = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter STANDARD_DATE_TIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DEFAULT_DATE = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter STANDARD_DATE= DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private DateTimeConvertUtils() {

    }

    /**
     * 日期字符串格式转换
     */
    public static String convertDateFormat(String str, DateTimeFormatter oriFormat, DateTimeFormatter desFormat) {
        LocalDate date = LocalDate.parse(str, oriFormat);

        return date.format(desFormat);
    }

    /**
     * 日期时间字符串格式转换
     */
    public static String convertDateTimeFormat(String str, DateTimeFormatter oriFormat, DateTimeFormatter desFormat) {
        LocalDateTime dateTime = LocalDateTime.parse(str, oriFormat);

        return dateTime.format(desFormat);
    }
}