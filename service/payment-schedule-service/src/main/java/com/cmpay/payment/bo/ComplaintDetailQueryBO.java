package com.cmpay.payment.bo;

import lombok.Data;

import java.util.List;

@Data
public class ComplaintDetailQueryBO {
    /**
     * 投诉编号
     */
    private String complaintId;
    /**
     * 投诉渠道
     */
    private String complaintChannel;
    /**
     * 交易单号
     */
    private String tradeOrderNo;
    /**
     * 机构单号
     */
    private String organizationOrderNo;
    /**
     * 投诉状态
     */
    private String complaintState;
    /**
     * 机构商户号
     */
    private String organizationMerchantId;
    /**
     * 投诉内容
     */
    private String complaintContent;
    /**
     * 交易金额
     */
    private String tradeAmount;
    /**
     * 投诉时间
     */
    private String complaintTime;
    /**
     * 交易状态
     */
    private String tradeStatus;
    /**
     * 问题类型
     */
    private String problemType;
    /**
     * 问题描述
     */
    private String problemDescription;
    /**
     * 是否需要及时服务用户
     */
    private String needImmediateService;
    /**
     * 投诉人联系方式
     */
    private String mobileNo;
    /**
     * 投诉人标签
     */
    private String complaintUserTag;
    /**
     * 投诉动态
     */
    private List<ComplaintProcessBO> complaintProcessList;
}
