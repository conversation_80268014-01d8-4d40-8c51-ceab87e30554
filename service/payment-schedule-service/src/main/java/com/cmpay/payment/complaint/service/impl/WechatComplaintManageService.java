package com.cmpay.payment.complaint.service.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.CommonUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.ComplaintDetailQueryBO;
import com.cmpay.payment.bo.ComplaintProcessBO;
import com.cmpay.payment.bo.ComplaintSyncBO;
import com.cmpay.payment.bo.MediaInfoBO;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.dto.wechat.complaint.*;
import com.cmpay.payment.utils.DateTimeConvertUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.cmpay.payment.channel.WXPayChannel.*;
import static com.cmpay.payment.channel.WXPayChannel.COMPLAINT_DEAL_HISTORY_QUERY;

@Service
public class WechatComplaintManageService {
    @Resource
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;

    /**
     * 获取微信投诉列表
     */
    public WxPayComplaintListQueryResponse getComplaintList(ComplaintSyncBO syncBO) {
        WxPayComplaintListQueryRequest queryReq = new WxPayComplaintListQueryRequest();
        queryReq.setBeginDate(DateTimeConvertUtils.convertDateFormat(syncBO.getBeginDate(), DateTimeConvertUtils.DEFAULT_DATE, DateTimeConvertUtils.STANDARD_DATE));
        queryReq.setEndDate(DateTimeConvertUtils.convertDateFormat(syncBO.getEndDate(), DateTimeConvertUtils.DEFAULT_DATE, DateTimeConvertUtils.STANDARD_DATE));
        queryReq.setOrganizationMerchantId(syncBO.getMerchantId());
        queryReq.setMchId(syncBO.getMerchantId());
        queryReq.setOffset(syncBO.getOffset());

        Request request = new Request();
        request.setRequestId(Optional.ofNullable(syncBO.getRequestId()).orElse(LemonUtils.getRequestId()));
        request.setRoute(COMPLAINT_LIST_QUERY.getRoute());
        request.setBusiType(COMPLAINT_LIST_QUERY.getBusType());
        request.setSource(COMPLAINT_LIST_QUERY.getSource());
        request.setTarget(queryReq);
        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        if (CommonUtils.isNull(genericRspDTO.getBody().getResult())) {
            BusinessException.throwBusinessException(MsgCodeEnum.QUERY_WECHAT_COMPLAINT_LIST_ERROR);
        }
        return (WxPayComplaintListQueryResponse) genericRspDTO.getBody().getResult();
    }

    /**
     * 获取微信投诉详情
     */
    public WxPayComplaintDetailQueryResponse getComplaintDetail(String complaintId, String merchantId) {
        WxPayComplaintDetailQueryRequest queryReq = new WxPayComplaintDetailQueryRequest();
        queryReq.setComplaintId(complaintId);
        queryReq.setMchId(merchantId);

        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(COMPLAINT_DETAIL_QUERY.getRoute());
        request.setBusiType(COMPLAINT_DETAIL_QUERY.getBusType());
        request.setSource(COMPLAINT_DETAIL_QUERY.getSource());
        request.setTarget(queryReq);
        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        if (CommonUtils.isNull(genericRspDTO.getBody().getResult())) {
            BusinessException.throwBusinessException(MsgCodeEnum.QUERY_WECHAT_COMPLAINT_DETAIL_ERROR);
        }

        return (WxPayComplaintDetailQueryResponse) genericRspDTO.getBody().getResult();
    }

    /**
     * 获取微信投诉操作历史
     */
    public void getWeChatComplaintDealHistory(ComplaintDetailQueryBO queryBO) {
        WxPayComplaintDealHistoryQueryRequest queryReq = new WxPayComplaintDealHistoryQueryRequest();
        queryReq.setComplaintId(queryBO.getComplaintId());
        queryReq.setMchId(queryBO.getOrganizationMerchantId());

        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(COMPLAINT_DEAL_HISTORY_QUERY.getRoute());
        request.setBusiType(COMPLAINT_DEAL_HISTORY_QUERY.getBusType());
        request.setSource(COMPLAINT_DEAL_HISTORY_QUERY.getSource());
        request.setTarget(queryReq);
        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        if (CommonUtils.isNull(genericRspDTO.getBody().getResult())) {
            BusinessException.throwBusinessException(MsgCodeEnum.QUERY_WECHAT_COMPLAINT_DEAL_HISTORY_ERROR);
        }
        WxPayComplaintDealHistoryQueryResponse historyQueryResponse = (WxPayComplaintDealHistoryQueryResponse) genericRspDTO.getBody().getResult();

        if (CommonUtils.isEmpty(historyQueryResponse.getComplaintDealHistoryList())) {
            queryBO.setComplaintProcessList(new ArrayList<>());
            return;
        }

        List<ComplaintProcessBO> complaintProcessBOS = new ArrayList<>();
        for (ComplaintDealHistory complaintDealHistory : historyQueryResponse.getComplaintDealHistoryList()) {
            ComplaintProcessBO complaintProcessBO = new ComplaintProcessBO();
            complaintProcessBO.setOperateType(complaintDealHistory.getOperateType());
            complaintProcessBO.setOperateDetails(complaintDealHistory.getOperateDetails());
            complaintProcessBO.setOperateTime(DateTimeConvertUtils.convertDateTimeFormat(complaintDealHistory.getOperateTime(), DateTimeFormatter.ISO_DATE_TIME, DateTimeConvertUtils.STANDARD_DATE_TIME));

            if (CommonUtils.isEmpty(complaintDealHistory.getComplaintMediaInfoList())) {
                complaintProcessBO.setMediaInfoBOList(new ArrayList<>());
            } else {
                List<MediaInfoBO> mediaInfoBOList = new ArrayList<>();
                for (ComplaintMediaInfo mediaInfo : complaintDealHistory.getComplaintMediaInfoList()) {
                    MediaInfoBO mediaInfoBO = new MediaInfoBO();
                    mediaInfoBO.setMediaType(mediaInfo.getMediaType());
                    mediaInfoBO.setMediaUrl(mediaInfo.getMediaUrl());
                    List<byte[]> mediaDataList = new ArrayList<>();
                    for (String mediaUrl : mediaInfo.getMediaUrl()) {
                        mediaDataList.add(getWeChatComplaintMediaInfo(mediaUrl, queryBO.getOrganizationMerchantId()));
                    }
                    mediaInfoBO.setMediaData(mediaDataList);
                    mediaInfoBOList.add(mediaInfoBO);
                }
                complaintProcessBO.setMediaInfoBOList(mediaInfoBOList);
            }

            complaintProcessBOS.add(complaintProcessBO);
        }
        queryBO.setComplaintProcessList(complaintProcessBOS);
    }

    /**
     * 获取微信媒体文件信息
     */
    private byte[] getWeChatComplaintMediaInfo(String mediaUrl, String merchantId) {
        WxPayComplaintImagesQueryRequest queryReq = new WxPayComplaintImagesQueryRequest();
        queryReq.setMediaId(mediaUrl.substring(mediaUrl.lastIndexOf("/") + 1));
        queryReq.setMchId(merchantId);

        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(COMPLAINT_IMAGE_QUERY.getRoute());
        request.setBusiType(COMPLAINT_IMAGE_QUERY.getBusType());
        request.setSource(COMPLAINT_IMAGE_QUERY.getSource());
        request.setTarget(queryReq);
        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        if (CommonUtils.isNull(genericRspDTO.getBody().getResult())) {
            BusinessException.throwBusinessException(MsgCodeEnum.QUERY_WECHAT_COMPLAINT_IMAGE_ERROR);
        }

        WxPayComplaintImagesQueryResponse imagesQueryResponse = (WxPayComplaintImagesQueryResponse) genericRspDTO.getBody().getResult();
        return imagesQueryResponse.getMediaData();
    }
}