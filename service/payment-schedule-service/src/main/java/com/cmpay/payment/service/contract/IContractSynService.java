package com.cmpay.payment.service.contract;

import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.schedule.bo.withhold.ContractQueryBO;
import com.cmpay.payment.schedule.bo.withhold.ContractWithholdQueryBO;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface IContractSynService {

    /**
     * 代扣签约订单查询
     *
     * @param contractQueryBO
     * @return
     */
    void contractQuery(ContractQueryBO contractQueryBO);


    /**
     * 代扣解约订单查询
     *
     * @param contractQueryBO
     * @return
     */
    void contractDeleteQuery(ContractQueryBO contractQueryBO);


    /**
     * 代扣订单查询
     *
     * @param contractWithholdQueryBO
     * @return
     */
    TradeNotifyBO contractWithholdQuery(ContractWithholdQueryBO contractWithholdQueryBO);
}
