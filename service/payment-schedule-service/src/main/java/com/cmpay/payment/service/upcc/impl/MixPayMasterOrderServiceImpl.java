package com.cmpay.payment.service.upcc.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.schedule.bo.settlement.TradeSettlementBO;
import com.cmpay.payment.schedule.bo.upcc.UpccMasterMapperBO;
import com.cmpay.payment.schedule.bo.upcc.UpccMasterOrderBO;
import com.cmpay.payment.schedule.bo.settlement.QueryMasterOrderRspBO;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.schedule.entity.settlement.TradeSettlementDOKey;
import com.cmpay.payment.schedule.service.upcc.IExtUpccMasterMapperService;
import com.cmpay.payment.schedule.service.upcc.IExtUpccMasterOrderService;
import com.cmpay.payment.service.upcc.IMixPayMasterQueryService;
import com.cmpay.payment.service.upcc.MixPayMasterOrderService;
import com.cmpay.payment.schedule.service.settlement.IExtTradeSettlementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @date 2024-10-09 10:13
 * <AUTHOR>
 * @Version 1.0
 */
@Service
public class MixPayMasterOrderServiceImpl implements MixPayMasterOrderService {
    @Autowired
    IMixPayMasterQueryService masterQueryService;
    @Autowired
    IExtTradeSettlementService settlementService;
    @Autowired
    IExtUpccMasterOrderService masterOrderService;
    @Autowired
    IExtUpccMasterMapperService masterMapperService;

    @Override
    public void orderQueryHandler(TradeSettlementBO settlementBO) {
        // 1、查询到结果
        QueryMasterOrderRspBO rspBO = masterQueryService.queryMasterOrder(settlementBO);
        // 2、判断不为空，直接将“主订单号”登记结算表
        if (JudgeUtils.isNull(rspBO) || JudgeUtils.isBlankAll(rspBO.getPayTransaction(), rspBO.getRefundTransaction())) {
            BusinessException.throwBusinessException(MsgCodeEnum.SUB_MASTER_ORDER_QUERY_FAIL);
        } else {
            settlementBO.setMasterOrderNo(StringUtils.equalsIgnoreCase(settlementBO.getSettlementStatus(),
                    SettlementStatusEnum.SETTLEMENT_SUCCESS.getDesc()) ? rspBO.getPayTransaction() : rspBO.getRefundTransaction());
            settlementService.updateMasterOrderNo(settlementBO);
        }
        // 登记到主-子订单映射，和上面的更新结算表在同一个事务中
        UpccMasterMapperBO masterMapperBO = registrationMapper(rspBO, settlementBO);
//         先是判断，然后查结算表，去计算金额
        UpccMasterOrderBO masterOrderBO = verifyAmount(masterMapperBO, settlementBO);
        // 登记到主订单表
        if(JudgeUtils.isNotNull(masterOrderBO)){
            masterOrderService.insert(masterOrderBO);
        }
    }

    private UpccMasterOrderBO verifyAmount(UpccMasterMapperBO masterMapperBO, TradeSettlementBO orgSett) {
        // 查询到所有映射关系
        List<UpccMasterMapperBO> masterMapperBOS = masterMapperService.findByMasterOrder(masterMapperBO);
        if(JudgeUtils.isEmpty(masterMapperBOS)){
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        UpccMasterOrderBO masterOrderBO = new UpccMasterOrderBO();
        masterOrderBO.setSubMerchantNo(masterMapperBO.getSubMerchantNo());
        // 主订单号
        masterOrderBO.setMasterOrderNo(masterMapperBO.getMasterOrderNo());
        // 原支付主订单号
        masterOrderBO.setOrgPayMasterOrder(masterMapperBO.getOrgPayMasterOrder());
        // 总金额
        masterOrderBO.setOrderAmount(masterMapperBO.getMasterOrderAmount());
        // 总服务费
        BigDecimal masterAmountFee = BigDecimal.ZERO;
        // 交易类型
        masterOrderBO.setOrderType(StringUtils.equals(Constants.S, orgSett.getSettlementStatus()) ? Constants.PAYMENT : Constants.REFUND);
        // 账单日期
        masterOrderBO.setAccountDate(orgSett.getAccountDate());
        // 交易完成时间
        masterOrderBO.setOrderCompleteTime(orgSett.getOrderCompleteTime());
        // 支付机构
        masterOrderBO.setPaymentChannel(orgSett.getPaymentChannel());
        // 支付场景
        masterOrderBO.setOrderScene(orgSett.getOrderScene());

        //现金金额：取支付方式不为积分的
        BigDecimal cashAmount = BigDecimal.ZERO;
        //积分金额：取支付方式为积分的
        BigDecimal integralAmount = BigDecimal.ZERO;
        //积分值:从积分那笔里面取
        BigDecimal orderPoints = BigDecimal.ZERO;
        // 判断是否是组合支付
        if(StringUtils.equals(Constants.Y, masterMapperBO.getCombination())) {
            BigDecimal masterOrderAmount = masterMapperBOS.get(0).getMasterOrderAmount();
            BigDecimal calculatAmount = masterMapperBOS.stream().map(UpccMasterMapperBO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (calculatAmount.compareTo(masterOrderAmount) != 0) {
                //金额不相等
                return null;
            } else {
                //金额相等，查询结算表，去统计服务费，现金金额，积分金额，积分值，账单日期，支付场景等
                List<TradeSettlementBO> settlementBOS = querySettlemts(masterMapperBOS);
                if (JudgeUtils.isEmpty(settlementBOS)) {
                    BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
                }
                //循环累计服务费
                for (TradeSettlementBO refSett : settlementBOS) {
                    if (StringUtils.equals(Constants.S, orgSett.getSettlementStatus())) {
                        // 支付
                        masterAmountFee = masterAmountFee.add(refSett.getPaymentFeeAmount());
                    } else {
                        //退款
                        masterAmountFee = masterAmountFee.add(refSett.getRefundFeeAmount());
                    }
                    if (StringUtils.equalsIgnoreCase(PaymentWayEnum.INTEGRALPAY.name(), refSett.getPaymentChannel())) {
                        integralAmount = integralAmount.add(refSett.getOrderAmount());
                        orderPoints = orderPoints.add(JudgeUtils.isNotNull(refSett.getOrderPoints()) ? refSett.getOrderPoints() : BigDecimal.ZERO);
                    } else {
                        cashAmount = cashAmount.add(refSett.getOrderAmount());
                        masterOrderBO.setPaymentChannel(refSett.getPaymentChannel());
                    }
                }
            }
            masterOrderBO.setMasterOrderFee(masterAmountFee);
            masterOrderBO.setCashAmount(cashAmount);
            masterOrderBO.setIntegralAmount(integralAmount);
            masterOrderBO.setOrderPoints(orderPoints);
        } else {
            // 不为组合支付模式，纯现金或纯积分
            // 金额不相等，直接报错
            if(masterMapperBO.getOrderAmount().compareTo(masterMapperBO.getMasterOrderAmount()) != 0){
                BusinessException.throwBusinessException(MsgCodeEnum.SUB_MASTER_ORDER_QUERY_FAIL);
            }
            masterOrderBO.setMasterOrderFee(StringUtils.equals(Constants.S, orgSett.getSettlementStatus())
                    ? orgSett.getPaymentFeeAmount() : orgSett.getRefundFeeAmount());
            if (StringUtils.equalsIgnoreCase(PaymentWayEnum.INTEGRALPAY.name(), orgSett.getPaymentChannel())) {
                integralAmount = orgSett.getOrderAmount();
                orderPoints = orgSett.getOrderPoints();
            } else {
                cashAmount = orgSett.getOrderAmount();
            }
            masterOrderBO.setCashAmount(cashAmount);
            masterOrderBO.setIntegralAmount(integralAmount);
            masterOrderBO.setOrderPoints(orderPoints);
        }
        return masterOrderBO;
    }

    /**
     * 根据主键，查询结算表数据
     * @param masterMapperBOS
     * @return
     */
    List<TradeSettlementBO> querySettlemts(List<UpccMasterMapperBO> masterMapperBOS){
        List<TradeSettlementBO> settlementBOS = new ArrayList<>();
        for (UpccMasterMapperBO mapperBO : masterMapperBOS) {
            TradeSettlementDOKey settlementDOKey = new TradeSettlementDOKey();
            settlementDOKey.setTradeJrnNo(mapperBO.getTradeJrnNo());
            settlementDOKey.setSettlementDate(mapperBO.getSettlementDate());
            TradeSettlementBO settlementBO = settlementService.getByPrimary(settlementDOKey);
            settlementBOS.add(settlementBO);
        }
        return settlementBOS;
    }

    /**
     * 登记主-子订单映射表
     * @param rspBO
     * @param settlementBO
     * @return
     */
    private UpccMasterMapperBO registrationMapper(QueryMasterOrderRspBO rspBO, TradeSettlementBO settlementBO) {
        UpccMasterMapperBO masterMapperBO = new UpccMasterMapperBO();
        masterMapperBO.setMasterOrderNo(StringUtils.equalsIgnoreCase(settlementBO.getSettlementStatus(),
                SettlementStatusEnum.SETTLEMENT_SUCCESS.getDesc()) ? rspBO.getPayTransaction() : rspBO.getRefundTransaction());
        masterMapperBO.setOrgPayMasterOrder(rspBO.getPayTransaction());
        masterMapperBO.setTradeJrnNo(settlementBO.getTradeJrnNo());
        masterMapperBO.setSubMerchantNo(settlementBO.getSubMerchantNo());
        masterMapperBO.setMasterOrderAmount(new BigDecimal(rspBO.getPayAmount()));
        masterMapperBO.setOrderAmount(settlementBO.getOrderAmount());
        masterMapperBO.setOrderType(settlementBO.getSettlementStatus());
        masterMapperBO.setCombination(rspBO.getCombinationPay() ? Constants.Y : Constants.N);
        masterMapperBO.setSettlementDate(settlementBO.getSettlementDate());
        masterMapperService.insert(masterMapperBO);
        return masterMapperBO;
    }
}
