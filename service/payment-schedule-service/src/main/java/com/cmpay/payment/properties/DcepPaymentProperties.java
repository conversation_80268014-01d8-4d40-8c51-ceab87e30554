package com.cmpay.payment.properties;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/5/7
 */
@Component
@ConfigurationProperties(prefix = "dcep")
@Getter
@Setter
@ToString
public class DcepPaymentProperties {

    /**
     * 数字货币支付渠道号  子钱包支付
     */
    private String channelNo;
    /**
     * 数币支付渠道号  免密，签约，付款码等
     */
    private String newChannelNo;
    /**
     * aes密钥  子钱包支付
     */
    private String aesKey;
    /**
     * aes密钥  免密，签约，付款码等
     */
    private String newAesKey;
    /**
     * 支付机构运营编码
     */
    private String orgNo;
    /**
     * 支付机构运营编码
     */
    private String newOrgNo;
    /**
     * 通知地址
     */
    private String notifyUrl;
    /**
     * 退款通知地址
     */
    private String refundNotifyUrl;
    /**
     * 页面通知地址
     */
    private String pageNotifyUrl;
    /**
     * 跳板链接无参前缀
     */
    private String wakeCmpayUrl;
    /**
     * 硬钱包机构号
     */
    private String hdWalletOrgNo;
}
