package com.cmpay.payment.service.base;

import com.xxl.job.core.biz.model.ReturnT;

/**
 * <AUTHOR>
 * @date 2025/9/10 14:56
 */
public interface IContractPayDataService {
    /**
     * 代扣支付订单同步 - 昨日订单轮询（优化版）
     * 每15分钟轮询一次，查询昨日特定时间段的等待支付订单
     *
     * @param channel 支付渠道 (alipay/wechat)
     * @param shardIndex 分片索引
     * @param shardTotal 分片总数
     * @param param 任务参数
     * @param dataScope 数据范围
     * @return 执行结果
     */
    ReturnT<String> managerDataContractPaymentYesterday(String channel, int shardIndex, int shardTotal, String param, String dataScope);

    /**
     * 代扣支付订单同步 - 当日订单轮询（优化版）
     * 每30分钟轮询一次，查询当日特定时间段的等待支付订单
     *
     * @param channel 支付渠道 (alipay/wechat)
     * @param shardIndex 分片索引
     * @param shardTotal 分片总数
     * @param param 任务参数
     * @param dataScope 数据范围
     * @return 执行结果
     */
    ReturnT<String> managerDataContractPaymentToday(String channel, int shardIndex, int shardTotal, String param, String dataScope);

}
