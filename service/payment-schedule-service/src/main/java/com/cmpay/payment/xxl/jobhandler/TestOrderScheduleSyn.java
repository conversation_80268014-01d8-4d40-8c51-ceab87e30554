package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.schedule.bo.pay.RefundOrderCloneBO;
import com.cmpay.payment.schedule.entity.pay.RefundOrderDO;
import com.cmpay.payment.schedule.service.pay.ExtRefundOrderService;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.cmpay.payment.service.pay.UndefinedRefundQueryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/13
 */
@Component
public class TestOrderScheduleSyn extends XxlJobExecutor {

    private static Logger logger = LoggerFactory.getLogger(RefundOrderSyn.class);

    @Autowired
    private ICommonManagerDataService managerDataService;
    @Autowired
    private ExtRefundOrderService refundOrderService;
    @Autowired
    private UndefinedRefundQueryService undefinedRefundQueryService;
    @Autowired
    private TimerRunLocationService runLocationService;

    @Value("${test.merchant:}")
    private String testMerchant;

    /**
     * 退款状态查询条件
     */
    private static final String STATUS = "status";
    /**
     * 退款订单日期
     */
    private static final String ORDER_DATE = "orderDate";
    /**
     * 退款订单日期
     */
    private static final String TOTAL_SELECT = "totalSelect";
    /**
     * 当前分片
     */
    private static final String CURRENT_INDEX = "currentIndex";
    /**
     * 机房标识
     */
    private static final String DATA_CENTER = "dataCenter";
    /**
     * 商户号
     */
    private static final String MERCHANT_NO = "merchantNo";
    /**
     * 退款查询笔数
     */
    private static final int PAGE_NUM = 1;
    private static final int PAGE_SIZE = 500;

    /**
     * 异步退款查询
     */
    @XxlJob("refundAllTestOrderStatusSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT refundAllOrderStatusSyn() {
        ScheduleRunBO scheduleRunBO = runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)) {
            return ReturnT.FAIL;
        }
        if (scheduleRunBO.isEnd()) {
            return ReturnT.SUCCESS;
        }
        String logInfo = "refundAllOrderStatusSyn" + DateTimeUtils.formatLocalDateTime(DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr()));
        logger.info("start {}", logInfo);
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        if (!managerDataService.dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        // 查询日期跨度3天
        Map<String, String> refundOrderMap = new HashMap<>(3);
        String queryDate = DateTimeUtils.formatLocalDate(DateTimeUtils.getCurrentLocalDate().minusDays(2));
        refundOrderMap.put(STATUS, OrderStatusEnum.REFUND_WAIT.name());
        refundOrderMap.put(ORDER_DATE, queryDate);
        refundOrderMap.put(TOTAL_SELECT, String.valueOf(shardTotal - 1));
        refundOrderMap.put(CURRENT_INDEX, String.valueOf(shardIndex));
        refundOrderMap.put(MERCHANT_NO, testMerchant);
        try {
            refundOrderMap.put(DATA_CENTER, scheduleRunBO.getDataScope());
        } catch (BusinessException e) {
            logger.info("Refund Order handle fail:{}", e.getMessage());
            return ReturnT.FAIL;
        }
        logger.info("Refund Order Query condition,status is {},queryDate is {}",
                OrderStatusEnum.REFUND_WAIT.name(), queryDate);
        //分页查询需要登记银企的商户
        int total = StringUtils.isNotBlank(param) ? Integer.parseInt(param) : PAGE_SIZE;
        PageInfo<RefundOrderDO> totalBranchNeedStlMerchantLists = PageUtils.pageQueryWithCount(PAGE_NUM, total,
                () -> refundOrderService.getRefundWaitQueryScheduleTestOrder(refundOrderMap));
        logger.info("Refund Order Total Pages is {}", totalBranchNeedStlMerchantLists.getPages());
        List<RefundOrderDO> refundOrderQueryList = totalBranchNeedStlMerchantLists.getList();
        if (refundOrderQueryList.size() == 0) {
            logger.info("Refund Order is Empty!!");
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        }
        for (RefundOrderDO refundOrderDO : refundOrderQueryList) {
            logger.info("当前分片: {}, 处理对象: {}", shardIndex, refundOrderDO.getOutTradeNo());
            try {
                RefundOrderCloneBO refundOrderBO = new RefundOrderCloneBO();
                BeanUtils.copyProperties(refundOrderDO, refundOrderBO);
                undefinedRefundQueryService.undefinedRefundOrderQuery(refundOrderBO);
            } catch (Exception e) {
                if (e instanceof BusinessException) {
                    logger.info("Refund org order:{},tradeNo:{}  query fail, error info: {}",
                            refundOrderDO.getOrgOrderNo(), refundOrderDO.getTradeOrderNo(), e.getMessage());
                } else {
                    logger.error("Refund org order:{},tradeNo:{}  query fail, error info: {}",
                            refundOrderDO.getOrgOrderNo(), refundOrderDO.getTradeOrderNo(), e);
                }
                continue;
            }
        }
        logger.info("end {}", logInfo);
        return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
    }

    @XxlJob("refundTestAsynOrderDeal")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT refundAsynOrderDeal() {
        ScheduleRunBO scheduleRunBO = runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)) {
            return ReturnT.FAIL;
        }
        if (scheduleRunBO.isEnd()) {
            return ReturnT.SUCCESS;
        }
        String logInfo = "refundAsynOrderDeal" + DateTimeUtils.formatLocalDateTime(DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr()));
        logger.info("start {}", logInfo);
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        if (!managerDataService.dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        // 查询日期跨度2天
        String queryDate = DateTimeUtils.formatLocalDate(DateTimeUtils.getCurrentLocalDate().minusDays(2));
        // 查询退款中的退款订单
        Map<String, String> refundOrderMap = new HashMap<>(3);
        refundOrderMap.put(STATUS, OrderStatusEnum.REFUND_PEND.name());
        refundOrderMap.put(TOTAL_SELECT, String.valueOf(shardTotal - 1));
        refundOrderMap.put(ORDER_DATE, queryDate);
        refundOrderMap.put(CURRENT_INDEX, String.valueOf(shardIndex));
        refundOrderMap.put(MERCHANT_NO, testMerchant);
        try {
            refundOrderMap.put(DATA_CENTER, scheduleRunBO.getDataScope());
        } catch (BusinessException e) {
            logger.info("Refund Order handle fail:{}", e.getMessage());
            return ReturnT.FAIL;
        }
        int total = StringUtils.isNotBlank(param) ? Integer.parseInt(param) : PAGE_SIZE;
        PageInfo<RefundOrderDO> totalBranchNeedStlMerchantLists = PageUtils.pageQueryWithCount(PAGE_NUM, total,
                () -> refundOrderService.getRefundWaitQueryScheduleTestOrder(refundOrderMap));
        List<RefundOrderDO> refundOrderQueryList = totalBranchNeedStlMerchantLists.getList();
        if (refundOrderQueryList.size() == 0) {
            logger.info("Refund Order is Empty!!");
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        }
        // 轮询进行退款订单查询
        for (RefundOrderDO refundOrderDO : refundOrderQueryList) {
            logger.info("当前分片: {}, 处理对象: {}", shardIndex, refundOrderDO.getOutTradeNo());
            // 单笔退款订单结果查询
            try {
                RefundOrderCloneBO refundOrderBO = new RefundOrderCloneBO();
                BeanUtils.copyProperties(refundOrderDO, refundOrderBO);
                undefinedRefundQueryService.asynRefundOrder(refundOrderBO);
            } catch (Exception e) {
                if (e instanceof BusinessException) {
                    logger.info("Refund org order:{},tradeNo:{}  sendOrderData fail, error info: {}",
                            refundOrderDO.getOrgOrderNo(), refundOrderDO.getTradeOrderNo(), e.getMessage());
                } else {
                    logger.error("Refund org order:{},tradeNo:{}  sendOrderData fail, error info: {}",
                            refundOrderDO.getOrgOrderNo(), refundOrderDO.getTradeOrderNo(), e);
                }
                continue;
            }
        }
        logger.info("end {}", logInfo);
        return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
    }

    @XxlJob("allTestOrderSyn")
    @InitialLemonData("lemonDataInitializer")
    @DistributedLocked(lockName = "'alipayAllOrderSyn'", leaseTime = 40, waitTime = 10)
    public ReturnT<String> allTestOrderSyn() {
        ScheduleRunBO scheduleRunBO = runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)) {
            return ReturnT.FAIL;
        }
        if (scheduleRunBO.isEnd()) {
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return managerDataService.allTestOrderSyn(shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }
}
