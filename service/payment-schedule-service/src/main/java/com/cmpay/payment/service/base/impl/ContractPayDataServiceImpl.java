package com.cmpay.payment.service.base.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.TimeRangeResultBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO;
import com.cmpay.payment.schedule.bo.withhold.ContractWithholdQueryBO;
import com.cmpay.payment.schedule.service.ext.async.AsynCommonService;
import com.cmpay.payment.schedule.service.pay.ExtPayOrderService;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.service.base.IContractPayDataService;
import com.cmpay.payment.service.contract.IContractSynService;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/10 14:58
 */
@Service
@Slf4j
public class ContractPayDataServiceImpl implements IContractPayDataService {
    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private ICommonManagerDataService commonManagerDataService;
    @Autowired
    private IContractSynService contractSynService;
    @Autowired
    private AsynCommonService asynCommonService;
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HHmmss");

    private static final String TOTAL = "2000";

    @Override
    public ReturnT<String> managerDataContractPaymentYesterday(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        return processContractPaymentOrders("Yesterday", channel, shardIndex, shardTotal, param, dataScope, this::queryYesterdayOrdersWithCrossDay);
    }

    @Override
    public ReturnT<String> managerDataContractPaymentToday(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        return processContractPaymentOrders("Today", channel, shardIndex, shardTotal, param, dataScope, this::queryTodayOrdersWithCrossDay);
    }

    /**
     * 处理代扣支付订单的通用方法
     */
    private ReturnT<String> processContractPaymentOrders(String orderType, String channel, int shardIndex, int shardTotal,
            String param, String dataScope, OrderQueryFunction queryFunction) {
        LocalDateTime currentDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = channel + "queryContractPayOrder" + orderType + "_" + DateTimeUtils.formatLocalDateTime(currentDateTime);
        log.info("start {}", logInfo);

        if (!commonManagerDataService.dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }

        try {
            List<TradeOrderDO> tradeOrderDOList = queryFunction.query(currentDateTime, channel, shardIndex, shardTotal, param, dataScope);
            if (tradeOrderDOList.isEmpty()) {
                log.info("ContractPayment {} Order is Empty!!", orderType);
                return ReturnT.SUCCESS;
            }

            log.info("contractPaymentOrderSyn{} - 查询到 {} 条记录", orderType, tradeOrderDOList.size());
            sendOrderDataContractPay(tradeOrderDOList, shardIndex);
            log.info("end {}", logInfo);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, logInfo);

        } catch (Exception e) {
            String errorMsg = orderType.toLowerCase() + " order handle fail:{}";
            if (e instanceof BusinessException) {
                log.info(errorMsg, e.getMessage());
            } else {
                log.error(errorMsg, e);
            }
            return ReturnT.FAIL;
        }
    }

    /**
     * 订单查询函数式接口
     */
    @FunctionalInterface
    private interface OrderQueryFunction {
        List<TradeOrderDO> query(LocalDateTime currentDateTime, String channel, int shardIndex, int shardTotal, String param, String dataScope);
    }

    /**
     * 查询昨日订单（支持跨日查询，查询两次）
     */
    private List<TradeOrderDO> queryYesterdayOrdersWithCrossDay(LocalDateTime currentDateTime, String channel,
                                                                int shardIndex, int shardTotal, String param, String dataScope) {

        LocalDate yesterdayDate = currentDateTime.toLocalDate().minusDays(1);
        LocalDateTime startDateTime = currentDateTime.minusMinutes(65);
        LocalDateTime endDateTime = currentDateTime.minusMinutes(5);
        List<TradeOrderDO> allOrders = new ArrayList<>();
        if (!endDateTime.toLocalDate().equals(yesterdayDate)) {
            // 跨日情况：查询两次
            // 第一次查询：昨天的数据（开始时间到23:59:59）
            String yesterdayDateStr = DateTimeUtils.formatLocalDate(yesterdayDate);
            String yesterdayStartTime = yesterdayDateStr + startDateTime.toLocalTime().format(TIME_FORMATTER);
            String yesterdayEndTime = yesterdayDateStr + DateTimeUtils.formatLocalTime(LocalDateTime.MAX.toLocalTime());
            ;

            TimeRangeResultBO yesterdayRange = new TimeRangeResultBO(yesterdayDateStr, yesterdayStartTime, yesterdayEndTime);
            FapOrderQueryBO yesterdayQueryBO = buildContractPaymentQueryBO(channel, shardIndex, shardTotal, param, dataScope, yesterdayRange);
            log.info("昨日订单跨日查询 - 昨天部分: 日期={}, 时间范围={}-{}", yesterdayDateStr, yesterdayStartTime, yesterdayEndTime);
            List<TradeOrderDO> yesterdayOrders = payOrderService.queryWaitContractPaymentScheduleList(yesterdayQueryBO);
            if (yesterdayOrders != null) {
                allOrders.addAll(yesterdayOrders);
            }
            // 第二次查询：今天的数据（00:00:00到当前时间）
            LocalDate todayDate = currentDateTime.toLocalDate();
            String todayDateStr = DateTimeUtils.formatLocalDate(todayDate);
            String todayStartTime = todayDateStr + DateTimeUtils.formatLocalTime(LocalDateTime.MIN.toLocalTime());
            ;
            String todayEndTime = todayDateStr + endDateTime.toLocalTime().format(TIME_FORMATTER);
            TimeRangeResultBO todayRange = new TimeRangeResultBO(todayDateStr, todayStartTime, todayEndTime);
            FapOrderQueryBO todayQueryBO = buildContractPaymentQueryBO(channel, shardIndex, shardTotal, param, dataScope, todayRange);
            log.info("昨日订单跨日查询 - 今天部分: 日期={}, 时间范围={}-{}", todayDateStr, todayStartTime, todayEndTime);
            List<TradeOrderDO> todayOrders = payOrderService.queryWaitContractPaymentScheduleList(todayQueryBO);
            if (todayOrders != null) {
                allOrders.addAll(todayOrders);
            }
            log.info("昨日订单跨日查询完成 - 昨天订单数: {}, 今天订单数: {}, 总计: {}",
                    yesterdayOrders != null ? yesterdayOrders.size() : 0,
                    todayOrders != null ? todayOrders.size() : 0,
                    allOrders.size());
        } else {
            // 正常情况：单次查询昨天的数据
            String yesterdayDateStr = DateTimeUtils.formatLocalDate(yesterdayDate);
            String startTimeStr = yesterdayDateStr + startDateTime.toLocalTime().format(TIME_FORMATTER);
            String endTimeStr = yesterdayDateStr + endDateTime.toLocalTime().format(TIME_FORMATTER);
            TimeRangeResultBO timeRange = new TimeRangeResultBO(yesterdayDateStr, startTimeStr, endTimeStr);
            FapOrderQueryBO queryBO = buildContractPaymentQueryBO(channel, shardIndex, shardTotal, param, dataScope, timeRange);
            log.info("昨日订单正常查询 - 日期: {}, 时间范围: {}-{}", yesterdayDateStr, startTimeStr, endTimeStr);
            List<TradeOrderDO> orders = payOrderService.queryWaitContractPaymentScheduleList(queryBO);
            if (orders != null) {
                allOrders.addAll(orders);
            }
        }
        return allOrders;
    }

    /**
     * 查询当日订单（支持跨日查询，查询两次）
     */
    private List<TradeOrderDO> queryTodayOrdersWithCrossDay(LocalDateTime currentDateTime, String channel,
                                                            int shardIndex, int shardTotal, String param, String dataScope) {

        LocalDate todayDate = currentDateTime.toLocalDate();
        LocalDateTime twoHourBefore = currentDateTime.minusMinutes(120);
        LocalDateTime oneHourBefore = currentDateTime.minusMinutes(60);
        List<TradeOrderDO> allOrders = new ArrayList<>();
        // 检查是否跨日
        if (!twoHourBefore.toLocalDate().equals(todayDate)) {
            // 跨日情况：查询两次
            LocalDate yesterdayDate = todayDate.minusDays(1);

            // 第一次查询：昨天的数据
            String yesterdayDateStr = DateTimeUtils.formatLocalDate(yesterdayDate);
            String yesterdayStartTime = yesterdayDateStr + twoHourBefore.toLocalTime().format(TIME_FORMATTER);
            String yesterdayEndTime = yesterdayDateStr + DateTimeUtils.formatLocalTime(LocalDateTime.MAX.toLocalTime());
            TimeRangeResultBO yesterdayRange = new TimeRangeResultBO(yesterdayDateStr, yesterdayStartTime, yesterdayEndTime);
            FapOrderQueryBO yesterdayQueryBO = buildContractPaymentQueryBO(channel, shardIndex, shardTotal, param, dataScope, yesterdayRange);
            log.info("当日订单跨日查询 - 昨天部分: 日期={}, 时间范围={}-{}", yesterdayDateStr, yesterdayStartTime, yesterdayEndTime);
            List<TradeOrderDO> yesterdayOrders = payOrderService.queryWaitContractPaymentScheduleList(yesterdayQueryBO);
            if (yesterdayOrders != null) {
                allOrders.addAll(yesterdayOrders);
            }
            // 第二次查询：今天的数据
            String todayDateStr = DateTimeUtils.formatLocalDate(todayDate);
            String todayStartTime = todayDateStr + DateTimeUtils.formatLocalTime(LocalDateTime.MIN.toLocalTime());
            String todayEndTime = todayDateStr + oneHourBefore.toLocalTime().format(TIME_FORMATTER);
            TimeRangeResultBO todayRange = new TimeRangeResultBO(todayDateStr, todayStartTime, todayEndTime);
            FapOrderQueryBO todayQueryBO = buildContractPaymentQueryBO(channel, shardIndex, shardTotal, param, dataScope, todayRange);
            log.info("当日订单跨日查询 - 今天部分: 日期={}, 时间范围={}-{}", todayDateStr, todayStartTime, todayEndTime);
            List<TradeOrderDO> todayOrders = payOrderService.queryWaitContractPaymentScheduleList(todayQueryBO);
            if (todayOrders != null) {
                allOrders.addAll(todayOrders);
            }
            log.info("当日订单跨日查询完成 - 昨天订单数: {}, 今天订单数: {}, 总计: {}",
                    yesterdayOrders != null ? yesterdayOrders.size() : 0,
                    todayOrders != null ? todayOrders.size() : 0,
                    allOrders.size());
        } else {
            // 正常情况：单次查询
            String todayDateStr = DateTimeUtils.formatLocalDate(todayDate);
            String startTimeStr = todayDateStr + twoHourBefore.toLocalTime().format(TIME_FORMATTER);
            String endTimeStr = todayDateStr + oneHourBefore.toLocalTime().format(TIME_FORMATTER);
            TimeRangeResultBO timeRange = new TimeRangeResultBO(todayDateStr, startTimeStr, endTimeStr);
            FapOrderQueryBO queryBO = buildContractPaymentQueryBO(channel, shardIndex, shardTotal, param, dataScope, timeRange);
            log.info("当日订单正常查询 - 日期: {}, 时间范围: {}-{}", todayDateStr, startTimeStr, endTimeStr);
            List<TradeOrderDO> orders = payOrderService.queryWaitContractPaymentScheduleList(queryBO);
            if (orders != null) {
                allOrders.addAll(orders);
            }
        }

        return allOrders;
    }


    /**
     * 构建代扣支付查询参数
     */
    private FapOrderQueryBO buildContractPaymentQueryBO(String channel, int shardIndex, int shardTotal, String param, String dataScope, TimeRangeResultBO timeRange) {
        FapOrderQueryBO queryBO = new FapOrderQueryBO();
        queryBO.setRequestDate(timeRange.getOrderDate());
        queryBO.setOrderDate(timeRange.getOrderDate());
        queryBO.setStartTime(timeRange.getStartTime());
        queryBO.setEndTime(timeRange.getEndTime());
        queryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
        queryBO.setAimProductCode(channel);
        queryBO.setCurrentIndex(shardIndex);
        queryBO.setTotalSelect(shardTotal - 1);
        queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
        queryBO.setDataCenter(dataScope);
        return queryBO;
    }

    /**
     * 数据发送上游(签约扣款订单)
     *
     * @param tradeOrderDOList
     * @param shardIndex
     */
    private void sendOrderDataContractPay(List<TradeOrderDO> tradeOrderDOList, int shardIndex) {
        for (TradeOrderDO tradeOrder : tradeOrderDOList) {
            log.info("当前分片: {}, 处理对象: {}", shardIndex, tradeOrder.getOutTradeNo());
            ContractWithholdQueryBO contractWithholdQueryBO = new ContractWithholdQueryBO();
            contractWithholdQueryBO.setPaymentOrder(tradeOrder);
            try {
                TradeNotifyBO notifyBO = contractSynService.contractWithholdQuery(contractWithholdQueryBO);
                //后台通知
                if (!StringUtils.equals(contractWithholdQueryBO.getPaymentStatus(), OrderStatusEnum.WAIT_PAY.name())) {
                    asynCommonService.asyncNotify(notifyBO);
                }
            } catch (Exception e) {
                if (e instanceof BusinessException) {
                    log.info("contractOrderInfo:{},error msg:{}", contractWithholdQueryBO.getPaymentOrder().getTradeOrderNo(), e.getMessage());
                } else {
                    log.error("contractOrderInfo:{},error msg:{}", contractWithholdQueryBO.getPaymentOrder().getTradeOrderNo(), e);
                }
            }
        }
    }
}
