package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Created on 2020/7/28
 */
@Component
public class AlipayOrderSyn extends XxlJobExecutor {

    @Autowired
    private ICommonManagerDataService commonManagerDataService;
    @Autowired
    private TimerRunLocationService runLocationService;

    @XxlJob("alipayOrderSyn")
    @InitialLemonData("lemonDataInitializer")
    @DistributedLocked(lockName = "'alipayAllOrderSyn'", leaseTime = 40, waitTime = 10)
    public ReturnT<String> alipayAllOrderSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerData(PaymentWayEnum.ALIPAY.name().toLowerCase(),shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

    @XxlJob("alipayOrderScanBarcodeSyn")
    @InitialLemonData("lemonDataInitializer")
    @DistributedLocked(lockName = "'alipayOrderScanBarcodeSyn'", leaseTime = 40, waitTime = 10)
    public ReturnT<String> alipayOrderScanBarcodeSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerDataScanBarcode(PaymentWayEnum.ALIPAY.name().toLowerCase(), shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

    @XxlJob("alipayOrderFirstSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> alipayOrderFirstSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerDataFirst(PaymentWayEnum.ALIPAY.name().toLowerCase(), shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

    @XxlJob("alipayOrderSecondSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> alipayOrderSecondSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerDataSecond(PaymentWayEnum.ALIPAY.name().toLowerCase(), shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

    @XxlJob("alipayOrderThirdSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> alipayOrderThirdSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerDataThird(PaymentWayEnum.ALIPAY.name().toLowerCase(), shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

    @XxlJob("alipayOrderFourSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> alipayOrderFourSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerDataFour(PaymentWayEnum.ALIPAY.name().toLowerCase(), shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

    @XxlJob("alipayOrderScanBarcodeFirstSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> alipayOrderScanBarcodeFirstSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerDataScanBarcodeFirst(PaymentWayEnum.ALIPAY.name().toLowerCase(), shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

    @XxlJob("alipayOrderScanBarcodeSecondSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> alipayOrderScanBarcodeSecondSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerDataScanBarcodeSecond(PaymentWayEnum.ALIPAY.name().toLowerCase(), shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

    @XxlJob("alipayOrderScanBarcodeThirdSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> alipayOrderScanBarcodeThirdSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerDataScanBarcodeThird(PaymentWayEnum.ALIPAY.name().toLowerCase(), shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }
}
