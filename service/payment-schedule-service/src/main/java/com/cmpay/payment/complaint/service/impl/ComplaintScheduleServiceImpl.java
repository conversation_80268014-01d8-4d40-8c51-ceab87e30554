package com.cmpay.payment.complaint.service.impl;

import com.cmpay.framework.data.utils.GwaUtils;
import com.cmpay.lemon.common.utils.CommonUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.payment.bo.ComplaintSyncBO;
import com.cmpay.payment.constant.complaint.ChannelEnum;
import com.cmpay.payment.constant.complaint.StateEnum;
import com.cmpay.payment.dto.alipay.complaint.ComplaintDetailQueryRsp;
import com.cmpay.payment.dto.alipay.complaint.ComplaintQueryDTO;
import com.cmpay.payment.dto.alipay.complaint.ComplaintQueryListRsp;
import com.cmpay.payment.dto.wechat.complaint.ComplaintInfo;
import com.cmpay.payment.dto.wechat.complaint.ComplaintOrderInfo;
import com.cmpay.payment.dto.wechat.complaint.WxPayComplaintDetailQueryResponse;
import com.cmpay.payment.dto.wechat.complaint.WxPayComplaintListQueryResponse;
import com.cmpay.payment.entity.FapCustomerComplaintDO;
import com.cmpay.payment.complaint.service.IComplaintScheduleService;
import com.cmpay.payment.complaint.service.IFapCustomerComplaintService;
import com.cmpay.payment.schedule.bo.config.ContractMerKeyQueryBO;
import com.cmpay.payment.schedule.service.config.IExtContractService;
import com.cmpay.payment.utils.AmountUtils;
import com.cmpay.payment.utils.DateTimeConvertUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
public class ComplaintScheduleServiceImpl implements IComplaintScheduleService {
    @Resource
    private IExtContractService extContractService;
    @Resource
    private IFapCustomerComplaintService fapCustomerComplaintService;
    @Resource
    private AlipayComplaintManageService alipayComplaintManageService;
    @Resource
    private WechatComplaintManageService wechatComplaintManageService;

    @Override
    public void complaintDataSync() {
        ComplaintSyncBO syncBO = new ComplaintSyncBO();

        LocalDateTime beginDateTime = DateTimeUtils.getCurrentLocalDateTime().minusHours(2);
        LocalDateTime endDateTime = DateTimeUtils.getCurrentLocalDateTime().minusHours(1);

        syncBO.setBeginDate(beginDateTime.toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        syncBO.setEndDate(endDateTime.toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        syncBO.setBeginDateTime(beginDateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        syncBO.setEndDateTime(endDateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));

        List<ContractMerKeyQueryBO> contractMerKeyQueryBOS = getWeChatContractMerchantList();

        for (ContractMerKeyQueryBO contractMerKeyQueryBO : contractMerKeyQueryBOS) {
            syncBO.setMerchantId(contractMerKeyQueryBO.getMerchantNumber());

            syncWeChatComplaintListOneHourBefore(syncBO);
        }

        syncAliPayComplaintListOneHourBefore(syncBO);
    }

    /**
     * 同步微信一小时前投诉列表
     */
    private void syncWeChatComplaintListOneHourBefore(ComplaintSyncBO syncBO) {
        WxPayComplaintListQueryResponse listQueryResponse = wechatComplaintManageService.getComplaintList(syncBO);
        if (CommonUtils.isEmpty(listQueryResponse.getComplaintList())) {
            return;
        }

        syncBO.setTotalCount(listQueryResponse.getTotalCount());
        syncBO.setOffset(Integer.valueOf(listQueryResponse.getPageNum()));
        syncBO.setLimit(Integer.valueOf(listQueryResponse.getPageCount()));

        for (int offset = 0; offset < syncBO.getTotalCount(); offset += syncBO.getLimit()) {
            syncBO.setOffset(offset);

            List<FapCustomerComplaintDO> wechatComplaintList = getWeChatComplaintListOneHourBefore(syncBO);

            fapCustomerComplaintService.insertComplaintList(wechatComplaintList);
        }
    }

    /**
     * 同步支付宝一小时前投诉列表
     */
    private void syncAliPayComplaintListOneHourBefore(ComplaintSyncBO syncBO) {
        ComplaintQueryListRsp listQueryResponse = alipayComplaintManageService.getComplaintList(syncBO);
        if (CommonUtils.isEmpty(listQueryResponse.getComplaintQueryDTOList())) {
            return;
        }

        syncBO.setTotalCount(listQueryResponse.getTotalSize().intValue());
        syncBO.setPageNum(listQueryResponse.getCurrentPage());
        syncBO.setPageSize(listQueryResponse.getPageSize());

        long totalPages = (syncBO.getTotalCount() + syncBO.getPageSize() - 1) / syncBO.getPageSize();

        for (long pageNum = 1; pageNum <= totalPages; pageNum += 1) {
            syncBO.setPageNum(pageNum);

            List<FapCustomerComplaintDO> aliPayComplaintList = getAliPayComplaintListOneHourBefore(syncBO);

            fapCustomerComplaintService.insertComplaintList(aliPayComplaintList);
        }
    }

    /**
     * 获取支付宝一小时前投诉列表
     */
    private List<FapCustomerComplaintDO> getAliPayComplaintListOneHourBefore(ComplaintSyncBO syncBO) {
        List<FapCustomerComplaintDO> complaintDOList = new ArrayList<>();
        ComplaintQueryListRsp listQueryResponse = alipayComplaintManageService.getComplaintList(syncBO);
        if (CommonUtils.isEmpty(listQueryResponse.getComplaintQueryDTOList())) {
            return complaintDOList;
        }
        for (ComplaintQueryDTO complaintQueryDTO : listQueryResponse.getComplaintQueryDTOList()) {
            FapCustomerComplaintDO complaintDO = new FapCustomerComplaintDO();
            complaintDO.setComplaintId(String.valueOf(complaintQueryDTO.getComplaintId()));
            complaintDO.setComplaintChannel(ChannelEnum.ALI_PAY.getCode());
            complaintDO.setComplaintContent(complaintQueryDTO.getComplaintContent());
            complaintDO.setComplaintTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(complaintQueryDTO.getComplaintTime()));
            complaintDO.setMerchantOrderNo(complaintQueryDTO.getTradeOrderNo());
            complaintDO.setOrgOrderNo(complaintQueryDTO.getOrganizationOrderNo());
            complaintDO.setOrgMerchantId(complaintQueryDTO.getOrganizationMerchantId());
            complaintDO.setAmount(complaintQueryDTO.getTradeAmount());
            complaintDO.setComplaintState(complaintQueryDTO.getComplaintState());

            complaintDOList.add(complaintDO);
        }
        return complaintDOList;
    }

    @Override
    public void complaintStatusSync() {
        LocalDate endDate = DateTimeUtils.parseLocalDate(GwaUtils.getTxDt());
        LocalDate beginDate = endDate.plusDays(-3);
        List<FapCustomerComplaintDO> complaintDOList = fapCustomerComplaintService.findNotFinishedList(beginDate, endDate);
        if (CommonUtils.isEmpty(complaintDOList)) {
            return;
        }
        for (FapCustomerComplaintDO complaintDO : complaintDOList) {
            String status = "";
            if (CommonUtils.equals(complaintDO.getComplaintChannel(), ChannelEnum.ALI_PAY.getCode())) {
                status = getAliPayComplaintState(complaintDO.getComplaintId());
            }
            if (CommonUtils.equals(complaintDO.getComplaintChannel(), ChannelEnum.WE_CHAT.getCode())) {
                status = getWeChatComplaintState(complaintDO.getComplaintId(), complaintDO.getOrgMerchantId());
            }
            if (CommonUtils.notEquals(StateEnum.PROCESSED.getCode(), status)) {
                continue;
            }
            fapCustomerComplaintService.updateComplaintState(complaintDO);
        }
    }

    /**
     * 获取微信一小时前投诉列表
     */
    private List<FapCustomerComplaintDO> getWeChatComplaintListOneHourBefore(ComplaintSyncBO syncBO) {
        List<FapCustomerComplaintDO> complaintDOList = new ArrayList<>();

        WxPayComplaintListQueryResponse listQueryResponse = wechatComplaintManageService.getComplaintList(syncBO);
        if (CommonUtils.isEmpty(listQueryResponse.getComplaintList())) {
            return complaintDOList;
        }

        for (ComplaintInfo complaintInfo : listQueryResponse.getComplaintList()) {
            FapCustomerComplaintDO complaintDO = new FapCustomerComplaintDO();
            complaintDO.setComplaintId(complaintInfo.getComplaintId());
            complaintDO.setComplaintChannel(ChannelEnum.WE_CHAT.getCode());
            complaintDO.setComplaintContent(complaintInfo.getComplaintDetail());

            if (complaintTimeNotInRange(syncBO, complaintInfo.getComplaintTime())) {
                continue;
            }

            complaintDO.setComplaintTime(convertWeChatComplaintTime(complaintInfo.getComplaintTime()));
            complaintDO.setOrgMerchantId(syncBO.getMerchantId());
            complaintDO.setComplaintState(complaintInfo.getComplaintState());

            ComplaintOrderInfo complaintOrderInfo = complaintInfo.getComplaintOrderList().get(0);
            complaintDO.setMerchantOrderNo(complaintInfo.getComplaintOrderList().get(0).getTradeOrderNo());
            complaintDO.setOrgOrderNo(complaintInfo.getComplaintOrderList().get(0).getOrganizationOrderNo());
            complaintDO.setAmount(convertWeChatComplaintAmount(complaintOrderInfo.getTradeAmount()));

            complaintDOList.add(complaintDO);
        }
        return complaintDOList;
    }

    /**
     * 投诉时间不在当前时间前两小时->一小时范围内
     */
    private boolean complaintTimeNotInRange(ComplaintSyncBO syncBO, String complaintTime) {
        complaintTime = DateTimeConvertUtils.convertDateTimeFormat(complaintTime, DateTimeFormatter.ISO_DATE_TIME, DateTimeConvertUtils.DEFAULT_DATE_TIME);
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(complaintTime);
        LocalDateTime beginDateTime = DateTimeUtils.parseLocalDateTime(syncBO.getBeginDateTime());
        LocalDateTime endDateTime = DateTimeUtils.parseLocalDateTime(syncBO.getEndDateTime());
        return localDateTime.isAfter(endDateTime) || localDateTime.isBefore(beginDateTime);
    }

    /**
     * 获取微信签约商户列表
     */
    private List<ContractMerKeyQueryBO> getWeChatContractMerchantList() {
        ContractMerKeyQueryBO contractMerKeyQueryBO = new ContractMerKeyQueryBO();
        contractMerKeyQueryBO.setPaymentMechanism("wechat");
        contractMerKeyQueryBO.setTotal("5000");
        return extContractService.queryContractMerchant(contractMerKeyQueryBO);
    }

    /**
     * 转换微信投诉时间
     * yyyy-MM-dd'T'HH:mm:ss.SSSXXX->yyyy-MM-dd HH:mm:ss
     */
    private String convertWeChatComplaintTime(String complaintTime) {
        return DateTimeConvertUtils.convertDateTimeFormat(complaintTime, DateTimeFormatter.ISO_DATE_TIME, DateTimeConvertUtils.STANDARD_DATE_TIME);
    }

    /**
     * 转换微信投诉金额
     */
    private String convertWeChatComplaintAmount(int centAmount) {
        BigDecimal amount = new BigDecimal(centAmount);
        amount = AmountUtils.changeYuanReturnBigDecimal(amount);
        return String.valueOf(amount);
    }

    /**
     * 获取微信投诉状态
     */
    private String getWeChatComplaintState(String complaintId, String merchantId) {
        WxPayComplaintDetailQueryResponse detailQueryRsp = wechatComplaintManageService.getComplaintDetail(complaintId, merchantId);
        return detailQueryRsp.getComplaintState();
    }

    /**
     * 获取支付宝投诉状态
     */
    private String getAliPayComplaintState(String complaintId) {
        ComplaintDetailQueryRsp detailQueryRsp = alipayComplaintManageService.getComplaintDetail(complaintId);
        return detailQueryRsp.getComplaintState();
    }
}
