package com.cmpay.payment.service.cmbaion.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.schedule.bo.base.BaseHandlerBO;
import com.cmpay.payment.schedule.bo.config.ContractMerKeyQueryBO;
import com.cmpay.payment.channel.CmbaionPayNrtChannelEnum;
import com.cmpay.payment.constant.cmbaion.CmbaionConstants;
import com.cmpay.payment.dto.cmbaion.AbstractCmbaionDTO;
import com.cmpay.payment.dto.cmbaion.CmbaionPubKeyQueryReqDTO;
import com.cmpay.payment.dto.cmbaion.CmbaionPubKeyQueryRspDTO;
import com.cmpay.payment.schedule.service.config.IExtContractService;
import com.cmpay.payment.service.channel.cmbaion.impl.CmbaionBaseRequestServiceImpl;
import com.cmpay.payment.service.cmbaion.CmbaionMerKeyQueryChannelService;
import com.cmpay.payment.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author： PengAnHai
 * @date： 2024-08-22
 * @description：招行一网通公钥查询处理类
 * @modifiedBy：
 * @version: 1.0
 */
@Service
public class CmbaionMerKeyQueryChannelServiceImpl extends CmbaionBaseRequestServiceImpl implements CmbaionMerKeyQueryChannelService {

    @Autowired
    private IExtContractService contractService;

    @Override
    public void cmbaionMerKeyQuery(ContractMerKeyQueryBO contractQueryBO) {
        // 创建查询请求DTO
        CmbaionPubKeyQueryReqDTO pubKeyQueryReqDTO = createnPubKeyQueryReqDTO(contractQueryBO);
        // 构建并发送查询请求
        buildCmbaionRequest(pubKeyQueryReqDTO, contractQueryBO, CmbaionPayNrtChannelEnum.CMBAION_PUBKEY_QUERY.getName());
    }

    @Override
    protected BaseHandlerBO handlerCmbaionResponse(AbstractCmbaionDTO result, BaseHandlerBO baseHandlerBO) {
        // 将结果转型为CmbaionPubKeyQueryRspDTO类型
        CmbaionPubKeyQueryRspDTO queryRspDTO = (CmbaionPubKeyQueryRspDTO) result;
        // 将基础处理对象转型为ContractMerKeyQueryBO类型
        ContractMerKeyQueryBO contractQueryBO = (ContractMerKeyQueryBO) baseHandlerBO;
        // 获取响应数据
        CmbaionPubKeyQueryRspDTO.RsqData rsqData = queryRspDTO.getRsqData();
        // 判断响应代码是否为成功状态
        if (JudgeUtils.equals(rsqData.getRspCode(), CmbaionConstants.SUCCESS)) {
            // 如果响应成功，设置商户的公钥和密钥过期时间
            contractQueryBO.setPublicKey(rsqData.getFbPubKey());
            contractQueryBO.setKeyExpireTime(DateUtils.getCurrentDateEndStr());
            // 更新商户密钥信息
            contractService.updatePubKeyByMerchantNumber(contractQueryBO);
            // 判断响应代码是否为服务繁忙状态
        } else if (JudgeUtils.equals(rsqData.getRspCode(), CmbaionConstants.SERVICE_BUSY)) {
            // 如果服务繁忙，返回当前的处理对象，不做其他处理
            return contractQueryBO;
            // 其他响应状态
        } else {
            // 如果响应不成功，且不是服务繁忙，抛出业务异常
            BusinessException.throwBusinessException(rsqData.getRspCode(), rsqData.getRspMsg());
        }
        // 返回更新后的处理对象
        return contractQueryBO;
    }

    /**
     * 创建Cmbaion公钥查询请求DTO。
     *
     * @param contractQueryBO 合同查询业务对象
     * @return 构建好的公钥查询请求DTO
     */
    private CmbaionPubKeyQueryReqDTO createnPubKeyQueryReqDTO(ContractMerKeyQueryBO contractQueryBO) {
        CmbaionPubKeyQueryReqDTO pubKeyQueryReqDTO = new CmbaionPubKeyQueryReqDTO();
        CmbaionPubKeyQueryReqDTO.ReqData reqData = new CmbaionPubKeyQueryReqDTO.ReqData();
        // 设置当前日期时间字符串
        reqData.setDateTime(DateTimeUtils.getCurrentDateTimeStr());
        //公钥查询交易码,固定为“FBPK”
        reqData.setTxCode(CmbaionConstants.PUB_KEY_QUERY_CODE);
        // 设置分行号
        reqData.setBranchNo(getBranchNo(contractQueryBO.getMerchantNumber()));
        // 设置商户号
        reqData.setMerchantNo(getMerchantNo(contractQueryBO.getMerchantNumber()));
        pubKeyQueryReqDTO.setReqData(reqData);
        // 设置请求版本数据信息
        setRequestVersionDataDTO(pubKeyQueryReqDTO);
        // 返回构建好的公钥查询请求DTO
        return pubKeyQueryReqDTO;
    }

}
