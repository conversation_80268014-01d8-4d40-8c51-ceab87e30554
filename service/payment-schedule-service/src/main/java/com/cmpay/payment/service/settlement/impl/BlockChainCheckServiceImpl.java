package com.cmpay.payment.service.settlement.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.channel.PaymentOgwOutEnum;
import com.cmpay.payment.channel.dto.TradeNotifyReq;
import com.cmpay.payment.client.IntegrationPaymentOgwOutClient;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.constant.SettlementStatusEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.properties.ShanxiBlockCheckProperties;
import com.cmpay.payment.schedule.bo.config.SecretBO;
import com.cmpay.payment.schedule.bo.pay.PayOrderBO;
import com.cmpay.payment.schedule.bo.settlement.BlockChainCheckReq;
import com.cmpay.payment.schedule.bo.settlement.TradeSettlementBO;
import com.cmpay.payment.schedule.entity.config.SecretDO;
import com.cmpay.payment.schedule.service.config.IExtSecretService;
import com.cmpay.payment.schedule.service.pay.ExtPayOrderService;
import com.cmpay.payment.service.settlement.IBlockChainCheckService;
import com.cmpay.payment.utils.PaymentUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @date 2022-10-14 11:10
 * <AUTHOR>
 * @Version 1.0
 */
@Service
public class BlockChainCheckServiceImpl implements IBlockChainCheckService {
    private static final Logger logger = LoggerFactory.getLogger(BlockChainCheckServiceImpl.class);
    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private IExtSecretService secretService;
    @Autowired
    private IntegrationPaymentOgwOutClient paymentOgwOutClient;
    @Autowired
    private ShanxiBlockCheckProperties properties;

    @Override
    public void blockChainCheck(List<TradeSettlementBO> tradeSettlementBOS) {
        //通知地址
        String url = properties.getUrl() + properties.getRequestPath();
        for(TradeSettlementBO tradeSettlementBO : tradeSettlementBOS) {
        //5、字符串拼接
        String content = getCheckContent(tradeSettlementBO);
        //6、获取定值,放循环外面
        BlockChainCheckReq blockChainCheckReq = new BlockChainCheckReq();
        blockChainCheckReq.setActor(properties.getActor());
        //配置类中获取数据
        blockChainCheckReq.setChaincodeType(properties.getChaincodeType());
        blockChainCheckReq.setContractAction(properties.getContractAction());
        blockChainCheckReq.setContractName(properties.getContractName());
        blockChainCheckReq.setPermission(properties.getPermission());
        blockChainCheckReq.setPublicKey(properties.getPublicKey());
        blockChainCheckReq.setWalletName(properties.getWalletName());
        blockChainCheckReq.setWalletPassword(properties.getWalletPassword());

        Map<String, String> args = new HashMap<String, String>();
        args.put("nm", properties.getNm());
        args.put("channel", properties.getChannel());
        args.put("content", content);
        args.put("reserve", "");
        blockChainCheckReq.setArgs(args);

        TradeNotifyReq notifyBaseReq = new TradeNotifyReq();
        // 密钥索引：订单表里有，从订单表里拿？
        PayOrderBO payOrderBO = new PayOrderBO();
        payOrderBO.setTradeOrderNo(tradeSettlementBO.getOutTradeNo());
        //原订单
        TradeOrderDO tradeOrderDO = payOrderService.get(payOrderBO);
        //查询密钥
        SecretBO secretBO = new SecretBO();
        secretBO.setSecretIndex(tradeOrderDO.getSecretIndex());
        SecretDO secretDO = secretService.get(secretBO);

        notifyBaseReq.setSecureIndex(tradeOrderDO.getSecretIndex());
        notifyBaseReq.setSecureValue(secretDO.getSecretNumber());

        notifyBaseReq.setNotifyUrl(url);
        notifyBaseReq.setMerchantId(tradeSettlementBO.getMerchantNo());
        notifyBaseReq.setMethod(CommonConstant.SHANXI_BLOCK_CHAIN_CHECK_NOTIFY);

        GenericRspDTO<Response> response;
        try {
            response = this.paymentOgwOutClient.request(PaymentUtils.buildRequest(PaymentOgwOutEnum.TRADE_NOTIFY.getName(), notifyBaseReq, blockChainCheckReq));
        } catch (Exception e) {
            logger.info("山西区块链对账失败:{}", tradeSettlementBO.getOutTradeNo());
                continue;
        }
        //成功日志
        if (JudgeUtils.isNotNull(response.getBody().getResult())) {
            logger.info("山西区块链对账成功订单号:{}", tradeSettlementBO.getOutTradeNo());
        } else {
            logger.info("山西区块链对账失败:{}", tradeSettlementBO.getOutTradeNo());
        }
        }
    }

    /**
     * 拼接对账数据
     *
     * @param tradeSettlementBO
     * @return
     */
    public String getCheckContent(TradeSettlementBO tradeSettlementBO) {
        StringBuffer contentBuffer = new StringBuffer();
        //1.商户编号
        contentBuffer.append(tradeSettlementBO.getMerchantNo() + "|");
        if (SettlementStatusEnum.SETTLEMENT_SUCCESS.getDesc().equalsIgnoreCase(tradeSettlementBO.getSettlementStatus())) {
            //2.交易订单号
            contentBuffer.append(tradeSettlementBO.getOutTradeNo() + "|");
            //3.商户订单号
            contentBuffer.append(tradeSettlementBO.getOutTradeNo() + "|");
            //4.订单实付金额
            contentBuffer.append(tradeSettlementBO.getPaymentAmount() == null ? "0.00|" : tradeSettlementBO.getPaymentAmount() + "|");
            //5.手续费
            contentBuffer.append(tradeSettlementBO.getPaymentFeeAmount() == null ? "0.00|" : tradeSettlementBO.getPaymentFeeAmount() + "|");
            //6.交易类型 payment:支付
            contentBuffer.append("payment|");
        } else if (SettlementStatusEnum.SETTLEMENT_REFUND.getDesc().equalsIgnoreCase(tradeSettlementBO.getSettlementStatus())) {
            //2.交易订单号
            contentBuffer.append(tradeSettlementBO.getTradeRefundNo() + "|");
            //3.商户订单号
            contentBuffer.append(tradeSettlementBO.getOutTradeNo() + "|");
            //4.订单实付金额
            contentBuffer.append(tradeSettlementBO.getRefundAmount() == null ? "0.00|" : tradeSettlementBO.getRefundAmount().toString() + "|");
            //5.手续费
            contentBuffer.append(tradeSettlementBO.getRefundFeeAmount() == null ? "0.00|" : tradeSettlementBO.getRefundFeeAmount().toString() + "|");
            //6.交易类型 payment:支付
            contentBuffer.append("refund|");
        }
        //7.账单日期   未对账，对账日期为null，设置""
        if (StringUtils.isEmpty(tradeSettlementBO.getCheckCompleteDate())) {
            contentBuffer.append("|");
        } else {
            contentBuffer.append(tradeSettlementBO.getCheckCompleteDate() + "|");
        }
        //8.交易完成时间
        contentBuffer.append(tradeSettlementBO.getOrderCompleteTime() + "|");
        //9.备注，为空
        contentBuffer.append("" + "|");
        //10.卡种
        contentBuffer.append(tradeSettlementBO.getCardType() + "|");
        //11.支付机构
        if (JudgeUtils.equals(tradeSettlementBO.getPaymentChannel(), PaymentWayEnum.NETBANK.name().toLowerCase())) {
            contentBuffer.append(PaymentWayEnum.CMPAY.name().toLowerCase() + "|");
        } else {
            contentBuffer.append(tradeSettlementBO.getPaymentChannel() + "|");
        }
        //12.支付场景
        contentBuffer.append(tradeSettlementBO.getOrderScene());
        String content = contentBuffer.toString();
        return content;
    }
}
