package com.cmpay.payment.service.base;

import com.xxl.job.core.biz.model.ReturnT;

/**
 * <AUTHOR>
 * Created on 2020/8/6
 */
public interface ICommonManagerDataService {

    /**
     * 数据提取
     * @param channel
     * @param shardTotal
     * @param param
     * @return
     */
    ReturnT<String> managerData(String channel,int shardIndex, int shardTotal, String param,String dataScope);

    /**
     * 支付机构查询条码扫码
     *
     * @param channel
     * @param shardIndex
     * @param shardTotal
     * @param param
     * @param dataScope
     * @return
     */
    ReturnT<String> managerDataScanBarcode(String channel, int shardIndex, int shardTotal, String param, String dataScope);

    /**
     * 分片数检查
     * @param shardIndex
     * @param shardTotal
     * @return
     */
    boolean dataInspect(int shardIndex, int shardTotal);

    /**
     * 支付机构查询非条码扫码
     * 5秒-1分钟的单  5秒查一次 12次
     *
     * @param channel
     * @param shardIndex
     * @param shardTotal
     * @param param
     * @param dataScope
     * @return
     */
    ReturnT<String> managerDataFirst(String channel, int shardIndex, int shardTotal, String param, String dataScope);

    /**
     * 支付机构查询非条码扫码
     * 1分-5分钟的单  30秒查一次 10次
     *
     * @param channel
     * @param shardIndex
     * @param shardTotal
     * @param param
     * @param dataScope
     * @return
     */
    ReturnT<String> managerDataSecond(String channel, int shardIndex, int shardTotal, String param, String dataScope);

    /**
     * 支付机构查询非条码扫码
     * 5分-30分钟的单 5分钟查一次 6次
     *
     * @param channel
     * @param shardIndex
     * @param shardTotal
     * @param param
     * @param dataScope
     * @return
     */
    ReturnT<String> managerDataThird(String channel, int shardIndex, int shardTotal, String param, String dataScope);

    /**
     * 支付机构查询非条码扫码
     * 30分-2小时的单 30分钟查一次 3次
     *
     * @param channel
     * @param shardIndex
     * @param shardTotal
     * @param param
     * @param dataScope
     * @return
     */
    ReturnT<String> managerDataFour(String channel, int shardIndex, int shardTotal, String param, String dataScope);

    /**
     * 支付机构查询条码扫码
     * 5秒-1分钟的单  5秒查一次 12次
     *
     * @param channel
     * @param shardIndex
     * @param shardTotal
     * @param param
     * @param dataScope
     * @return
     */
    ReturnT<String> managerDataScanBarcodeFirst(String channel, int shardIndex, int shardTotal, String param, String dataScope);

    /**
     * 支付机构查询条码扫码
     * 1分-5分钟的单  30秒查一次 10次
     *
     * @param channel
     * @param shardIndex
     * @param shardTotal
     * @param param
     * @param dataScope
     * @return
     */
    ReturnT<String> managerDataScanBarcodeSecond(String channel, int shardIndex, int shardTotal, String param, String dataScope);

    /**
     * 支付机构查询条码扫码
     * 5分-30分钟的单 5分钟查一次 6次
     *
     * @param channel
     * @param shardIndex
     * @param shardTotal
     * @param param
     * @param dataScope
     * @return
     */
    ReturnT<String> managerDataScanBarcodeThird(String channel, int shardIndex, int shardTotal, String param, String dataScope);

    /**
     * 数据提取
     *
     * @param shardIndex
     * @param shardTotal
     * @param param
     * @param dataScope
     * @return
     */
    ReturnT<String> allTestOrderSyn(int shardIndex, int shardTotal, String param, String dataScope);

    ReturnT<String> standbyOrderSyn(String param, int shardIndex, int shardTotal, String dataScope);

    /**
     * 查询支付机构解约协议
     *
     * @param channel
     * @param shardIndex
     * @param shardTotal
     * @param param
     * @param dataScope
     * @return
     */
    ReturnT<String> managerDataContractDelete(String channel, int shardIndex, int shardTotal, String param, String dataScope);

    /**
     * 签约状态查询（20小时前的4小时）
     *
     * @param channel
     * @param shardIndex
     * @param shardTotal
     * @param param
     * @param dataScope
     * @return
     */
    ReturnT<String> managerDataContract(String channel, int shardIndex, int shardTotal, String param, String dataScope);

    /**
     * 签约状态查询（2小时内）
     *
     * @param channel
     * @param shardIndex
     * @param shardTotal
     * @param param
     * @param dataScope
     * @return
     */
    ReturnT<String> managerDataHourContract(String channel, int shardIndex, int shardTotal, String param, String dataScope);



    /**
     * 查询统一收银台主订单号
     * @param shardIndex
     * @param shardTotal
     * @param param
     * @return
     */
    ReturnT<String> managerDataMasterOrder(int shardIndex, int shardTotal, String param);
}
