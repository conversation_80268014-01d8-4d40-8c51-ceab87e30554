package com.cmpay.payment.service.cmbaion;

import com.xxl.job.core.biz.model.ReturnT;

/**
 * @author： PengAnHai
 * @date： 2024-08-22
 * @description：公钥信息查询Service层
 * @modifiedBy：
 * @version: 1.0
 */
public interface KeyDataQueryService {

    /**
     * 商户密钥数据查询
     *
     * @param channel 渠道标识
     * @param param   查询参数
     * @return 查询结果的返回类型
     */
    ReturnT<String> keyDataQuery(String channel, String param);

}
