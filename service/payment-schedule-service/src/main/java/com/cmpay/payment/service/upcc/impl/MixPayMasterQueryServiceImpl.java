package com.cmpay.payment.service.upcc.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.schedule.bo.settlement.TradeSettlementBO;
import com.cmpay.payment.schedule.bo.settlement.QueryMasterOrderReqBO;
import com.cmpay.payment.schedule.bo.settlement.QueryMasterOrderRspBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderTypeEnum;
import com.cmpay.payment.service.upcc.IMixPayMasterQueryService;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/**
 * @date 2024-09-30 10:05
 * <AUTHOR>
 * @Version 1.0
 */
@Service
public class MixPayMasterQueryServiceImpl implements IMixPayMasterQueryService {
    private static final Logger logger = LoggerFactory.getLogger(MixPayMasterQueryServiceImpl.class);

    @Value("${upcc.queryUrl:}")
    private String queryUrl;
    private static final String PAY = "1";
    private static final String REFUND = "2";

    @Override
    public QueryMasterOrderRspBO queryMasterOrder(TradeSettlementBO settlementBO) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建HttpPost
            HttpPost httpPost = new HttpPost(queryUrl);
            // 参数以及请求头配置
            QueryMasterOrderReqBO masterOrderReq = new QueryMasterOrderReqBO();
            masterOrderReq.setTradeOrderId(settlementBO.getTradeOrderNo());
            masterOrderReq.setPayDate(settlementBO.getRequestDate());
            masterOrderReq.setTransType(StringUtils.equals(OrderTypeEnum.TRADE.getValue(), settlementBO.getSettlementStatus()) ? PAY : REFUND);
            masterOrderReq.setMerchantId(settlementBO.getMerchantNo());
            masterOrderReq.setSubMerchant(settlementBO.getSubMerchantNo());
            logger.info("查询支付网关参数:{}",JSONObject.toJSONString(masterOrderReq));
            StringEntity requestEntity = new StringEntity(JSONObject.toJSONString(masterOrderReq),"utf-8");
            requestEntity.setContentEncoding("UTF-8");
            httpPost.setHeader("Content-type", "application/json");
//            httpPost.setHeader("x-lemon-application", CommonConstants.APP_NAME);
            httpPost.setEntity(requestEntity);
            // 请求数据
            HttpResponse response = httpClient.execute(httpPost);
            if (response.getStatusLine().getStatusCode() != 200) {
                BusinessException.throwBusinessException(MsgCodeEnum.SUB_MASTER_ORDER_QUERY_FAIL);
            }
            String data = EntityUtils.toString(response.getEntity());
            QueryMasterOrderRspBO orderRspBO = JSONObject.parseObject(data,QueryMasterOrderRspBO.class);
            logger.info("查询支付网关返回:{}",orderRspBO);
            if (JudgeUtils.isNull(orderRspBO) || !StringUtils.equals(orderRspBO.getStatus(), "200")) {
                BusinessException.throwBusinessException(MsgCodeEnum.SUB_MASTER_ORDER_QUERY_FAIL);
            }
            return orderRspBO;
        } catch (Exception e) {
            logger.info("查询支付网关失败:{}",e);
            BusinessException.throwBusinessException(MsgCodeEnum.SUB_MASTER_ORDER_QUERY_FAIL);
        }
        return null;
    }
}
