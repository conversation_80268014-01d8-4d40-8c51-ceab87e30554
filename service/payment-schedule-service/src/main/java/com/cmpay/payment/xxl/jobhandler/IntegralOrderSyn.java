package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.payment.constant.AppEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.schedule.bo.notify.TradeOrderAndNotifyBO;
import com.cmpay.payment.schedule.bo.pay.FapFinishOrderQueryBO;
import com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO;
import com.cmpay.payment.schedule.bo.pay.IntegralOrderQueryBO;
import com.cmpay.payment.schedule.bo.pay.PaymentQueryBO;
import com.cmpay.payment.schedule.entity.pay.TradeFinishExtDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.schedule.service.ext.async.AsynCommonService;
import com.cmpay.payment.schedule.service.pay.ExtIntegralFinishOrderService;
import com.cmpay.payment.schedule.service.pay.ExtPayOrderService;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.cmpay.payment.service.pay.TradeSynService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/3 9:42
 * @description ：积分支付
 */
@Component
public class IntegralOrderSyn extends XxlJobExecutor {
    private static Logger logger = LoggerFactory.getLogger(IntegralOrderSyn.class);

    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private TradeSynService tradeSynService;
    @Autowired
    private ICommonManagerDataService managerDataService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ExtPayOrderService paymentOrderService;
    @Autowired
    private ExtIntegralFinishOrderService finishOrderService;
    @Autowired
    private AsynCommonService asynCommonService;

    private static final String TOTAL = "2000";
    private static final long SECONDS = 30;
    private static final long DAYS = 1;
    private static final long MIN = 2;
    private static final int SIZE = 0;
    private static final int TOTALS = 1;
    @Autowired
    private TimerRunLocationService runLocationService;

    /**
     * 积分支付订单状态查询
     */
    @XxlJob("integralOrderTimeOutSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> integralOrderTimeOutSyn() {
        ScheduleRunBO scheduleRunBO = runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)) {
            return ReturnT.FAIL;
        }
        if (scheduleRunBO.isEnd()) {
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        if (!managerDataService.dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            FapOrderQueryBO queryBO = new FapOrderQueryBO();
            LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
            LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setOrderDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusSeconds(SECONDS)));
            queryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(DAYS)));
            queryBO.setOrderTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusHours(MIN)));
            queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
            queryBO.setCurrentIndex(shardIndex);
            queryBO.setTotalSelect(shardTotal - TOTALS);
            queryBO.setDataCenter(scheduleRunBO.getDataScope());
            List<TradeOrderDO> tradeOrderList = payOrderService.queryTimeOutPaymentChannelList(queryBO);
            if (tradeOrderList == null || tradeOrderList.size() == SIZE) {
                logger.info("{},Payment Order is Empty!!", queryBO.getPayProductCode());
                return ReturnT.SUCCESS;
            }
            logger.info("queryAllPayOrder");
            for (TradeOrderDO tradeOrder : tradeOrderList) {
                logger.info("当前分片: {}, 处理对象: {}", shardIndex, tradeOrder.getOutTradeNo());
                PaymentQueryBO paymentQueryBO = new PaymentQueryBO();
                paymentQueryBO.setPaymentOrder(tradeOrder);
                try {
                    //判断当前订单是否已经是23小时之前的订单
                    if (checkOrderData(tradeOrder)) {
                        logger.info("订单已过23小时:{}", tradeOrder.getOutTradeNo());
                        continue;
                    }
                    paymentQueryBO.setSourceApp(AppEnum.integrationshecdule.name());
                    TradeOrderAndNotifyBO orderAndNotifyBO = tradeSynService.paymentOrderSyn(paymentQueryBO);
                    TradeNotifyBO notifyBO = new TradeNotifyBO();
                    notifyBO.setTradeOrderNo(paymentQueryBO.getPaymentOrder().getTradeOrderNo());
                    logger.info("tradeOrderDO:{}", paymentQueryBO.getPaymentOrder().toString());
                    if (StringUtils.equals(paymentQueryBO.getPaymentOrder().getStatus(), OrderStatusEnum.TRADE_SUCCESS.name())) {
                        asynCommonService.asyncNotify(orderAndNotifyBO.getTradeNotifyBO());
                    }
                } catch (Exception e) {
                    logger.error("orderInfo:{},error message: {}", paymentQueryBO.getPaymentOrder().getTradeOrderNo(), e);
                }
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            logger.info("order handle fail:{}", e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob("integralOrderFinishSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> integralOrderFinishSyn() {
        ScheduleRunBO scheduleRunBO = runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)) {
            return ReturnT.FAIL;
        }
        if (scheduleRunBO.isEnd()) {
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        if (!managerDataService.dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            FapFinishOrderQueryBO queryBO = new FapFinishOrderQueryBO();
            LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
            LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setOrderDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusSeconds(10)));
            queryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
            queryBO.setOrderTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusHours(1)));
            queryBO.setPayProductCode(PaymentWayEnum.INTEGRALPAY.name().toLowerCase());
            queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
            queryBO.setTotalSelect(shardTotal - 1);
            queryBO.setCurrentIndex(shardIndex);
            queryBO.setDataCenter(scheduleRunBO.getDataScope());
            List<TradeFinishExtDO> fapFinishOrderList = finishOrderService.queryIntegralWaitFinishOrderList(queryBO);
            if (fapFinishOrderList == null || fapFinishOrderList.size() == 0) {
                logger.info("{},Payment Order is Empty!!", queryBO.getPayProductCode());
                return ReturnT.SUCCESS;
            }
            logger.info("queryAllFinishOrder");
            for (TradeFinishExtDO fapFinishOrder : fapFinishOrderList) {
                logger.info("当前分片: {}, 处理对象: {}", shardIndex, fapFinishOrder.getOutTradeNo());
                IntegralOrderQueryBO IntegralQueryBO = new IntegralOrderQueryBO();
                TradeOrderDO tradeOrderFindDO = new TradeOrderDO();
                tradeOrderFindDO.setOutTradeNo(fapFinishOrder.getOrgOrderNo());
                List<TradeOrderDO> tradeOrderDOList = paymentOrderService.find(tradeOrderFindDO);
                fapFinishOrder.setMobileNumber(tradeOrderDOList.get(0).getMobileNumber());
                IntegralQueryBO.setBnkTraceNo(tradeOrderDOList.get(0).getBankOrderNo());
                IntegralQueryBO.setFinishOrder(fapFinishOrder);
                try {
                    applicationContext.publishEvent(IntegralQueryBO);
                    TradeFinishExtDO fapFinishOrderDO = IntegralQueryBO.getFinishOrder();
                    if (!JudgeUtils.equalsAny(IntegralQueryBO.getFinishOrder().getStatus(),
                            OrderStatusEnum.SEND_WAIT.name(), OrderStatusEnum.SHIP_WAIT.name())) {
                        finishOrderService.updateIntegralFinishOrder(fapFinishOrderDO);
                    }
                } catch (BusinessException e) {
                    logger.warn("orderInfo:{},error message: {},{}", IntegralQueryBO.getFinishOrder().getTradeOrderNo(), e.getMessage());
                } catch (Exception e) {
                    logger.error("orderInfo:{},error message: {}", IntegralQueryBO.getFinishOrder().getTradeOrderNo(), e);
                }
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            logger.info("order handle fail:{}", e);
            return ReturnT.FAIL;
        }
    }

    public boolean checkOrderData(TradeOrderDO tradeOrder) {
        if (DateTimeUtils.getCurrentLocalDateTime().isAfter(DateTimeUtils.parseLocalDateTime(tradeOrder.getOrderDate() + tradeOrder.getOrderTime()).plusHours(1).plusMinutes(30))) {
            tradeOrder.setStatus(OrderStatusEnum.TRADE_CLOSED.name());
            paymentOrderService.updatePaymentStatus(tradeOrder);
            return true;
        } else {
            return false;
        }
    }

}
