package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.cmbaion.KeyDataQueryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author： PengAnHai
 * @date： 2024-08-22
 * @description：公钥信息查询定时任务
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class KeyQuerySyn {

    @Autowired
    private KeyDataQueryService keyDataQueryService;

    /**
     * 招行一网通商户公钥信息查询任务
     *
     * @return
     */
    @XxlJob("CmbaionKeyQuerySyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> CmbaionKeyQuerySyn() {
        String param = XxlJobHelper.getJobParam();
        return keyDataQueryService.keyDataQuery(PaymentWayEnum.CMBAION.name().toLowerCase(), param);
    }
}
