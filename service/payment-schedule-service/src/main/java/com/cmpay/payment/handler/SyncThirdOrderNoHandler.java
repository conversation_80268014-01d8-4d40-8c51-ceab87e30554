package com.cmpay.payment.handler;


import com.cmpay.framework.data.message.CmpayCmdDTO;
import com.cmpay.lemon.framework.stream.MessageHandler;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.schedule.service.pay.ExtThirdOrderMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author： pengAnHai
 * @date： 2023-11-22
 * @description：
 */
@Component("connectThirdOrderNoHandler")
public class SyncThirdOrderNoHandler implements MessageHandler<TradeOrderDO, CmpayCmdDTO<TradeOrderDO>> {

    @Autowired
    private ExtThirdOrderMappingService thirdOrderMappingService;

    @Override
    public void onMessageReceive(CmpayCmdDTO<TradeOrderDO> cmpayCmdDTO) {
        TradeOrderDO tradeOrderDO = cmpayCmdDTO.getBody();
        thirdOrderMappingService.insertThirdOrderMapping(tradeOrderDO);
    }
}
