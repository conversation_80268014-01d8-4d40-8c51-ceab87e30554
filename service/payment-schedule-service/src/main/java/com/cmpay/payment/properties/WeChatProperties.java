package com.cmpay.payment.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "wechat")
public class WeChatProperties {
    /**
     * 通知地址
     * 接收微信支付异步通知回调地址，通知url必须为直接可访问的url，不能携带参数。
     */
    private String notifyUrl;

    /**
     * 退款结果通知url
     * 异步接收微信支付退款结果通知的回调地址，通知URL必须为外网可访问的url，不允许带参数
     * 如果参数中传了notify_url，则商户平台上配置的回调地址将不会生效。
     */
    private String refundNofityUrl;

    /**
     * 微信appid
     */
    private String appid;
    /**
     * 微信商户号
     */
    private String mchId;

    /**
     * 微信mchId
     */
    private String contractNofityUrl;
    /**
     * 微信解约通知地址
     */
    private String contractDeleteNofityUrl;

    /**
     * 微信代扣支付结果通知
     */
    private String contractPaymentUrl;

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getRefundNofityUrl() {
        return refundNofityUrl;
    }

    public void setRefundNofityUrl(String refundNofityUrl) {
        this.refundNofityUrl = refundNofityUrl;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getContractNofityUrl() {
        return contractNofityUrl;
    }

    public void setContractNofityUrl(String contractNofityUrl) {
        this.contractNofityUrl = contractNofityUrl;
    }

    public String getContractDeleteNofityUrl() {
        return contractDeleteNofityUrl;
    }

    public void setContractDeleteNofityUrl(String contractDeleteNofityUrl) {
        this.contractDeleteNofityUrl = contractDeleteNofityUrl;
    }

    public String getContractPaymentUrl() {
        return contractPaymentUrl;
    }

    public void setContractPaymentUrl(String contractPaymentUrl) {
        this.contractPaymentUrl = contractPaymentUrl;
    }
}
