package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Created on 2020/7/28
 */
@Component
public class CmpayOrderSyn extends XxlJobExecutor {

    @Autowired
    private ICommonManagerDataService commonManagerDataService;
    @Autowired
    private TimerRunLocationService runLocationService;

    @XxlJob("cmpayOrderSyn")
    @InitialLemonData("lemonDataInitializer")
    @DistributedLocked(lockName = "'cmpayAllOrderSyn'", leaseTime = 40, waitTime = 10)
    public ReturnT<String> cmpayAllOrderSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.managerData(PaymentWayEnum.CMPAY.name().toLowerCase(), shardIndex, shardTotal, param,scheduleRunBO.getDataScope());
    }

    @XxlJob("cmpayOrderScanBarcodeSyn")
    @InitialLemonData("lemonDataInitializer")
    @DistributedLocked(lockName = "'cmpayOrderScanBarcodeSyn'", leaseTime = 40, waitTime = 10)
    public ReturnT<String> cmpayOrderScanBarcodeSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataScanBarcode(PaymentWayEnum.CMPAY.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("cmpayOrderFirstSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> cmpayOrderFirstSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataFirst(PaymentWayEnum.CMPAY.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("cmpayOrderSecondSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> cmpayOrderSecondSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataSecond(PaymentWayEnum.CMPAY.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("cmpayOrderThirdSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> cmpayOrderThirdSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataThird(PaymentWayEnum.CMPAY.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("cmpayOrderFourSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> cmpayOrderFourSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataFour(PaymentWayEnum.CMPAY.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("cmpayOrderScanBarcodeFirstSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> cmpayOrderScanBarcodeFirstSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataScanBarcodeFirst(PaymentWayEnum.CMPAY.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("cmpayOrderScanBarcodeSecondSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> cmpayOrderScanBarcodeSecondSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataScanBarcodeSecond(PaymentWayEnum.CMPAY.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }

    @XxlJob("cmpayOrderScanBarcodeThirdSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> cmpayOrderScanBarcodeThirdSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return commonManagerDataService.managerDataScanBarcodeThird(PaymentWayEnum.CMPAY.name().toLowerCase(), shardIndex, shardTotal, param, scheduleRunBO.getDataScope());
    }
}
