package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.service.base.IContractPayDataService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 代扣支付订单同步 - 优化版
 * <p>
 * 原问题：查询两天订单表中等待支付的数据，SQL平均执行耗时超20s
 * 优化方案：拆分为两个独立的轮询任务
 * 1. 轮询昨日订单：每15分钟轮询一次，查询昨日特定时间段的等待支付订单
 * 2. 轮询当日订单：每30分钟轮询一次，查询当日特定时间段的等待支付订单
 *
 * <AUTHOR>
 * Created on 2020/7/28
 * @modified 2024/12/19 - 性能优化，拆分轮询任务
 */
@Component
public class ContractPaymentOrderSyn extends XxlJobExecutor {

    @Autowired
    private TimerRunLocationService runLocationService;
    @Autowired
    private IContractPayDataService contractPayDataService;

    /**
     * 支付宝代扣支付订单同步 - 昨日订单轮询（优化版）
     * 每15分钟轮询一次，查询昨日特定时间段的等待支付订单
     */
    @XxlJob("alipayContractPaymentSynYesterday")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> alipayContractPaymentSynYesterday() {
        ScheduleRunBO scheduleRunBO = runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)) {
            return ReturnT.FAIL;
        }
        if (scheduleRunBO.isEnd()) {
            return ReturnT.SUCCESS;
        }

        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return contractPayDataService.managerDataContractPaymentYesterday(
                PaymentWayEnum.ALIPAY.name().toLowerCase(),
                shardIndex,
                shardTotal,
                param,
                scheduleRunBO.getDataScope()
        );
    }

    /**
     * 支付宝代扣支付订单同步 - 当日订单轮询（优化版）
     * 每30分钟轮询一次，查询当日特定时间段的等待支付订单
     */
    @XxlJob("alipayContractPaymentSynToday")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> alipayContractPaymentSynToday() {
        ScheduleRunBO scheduleRunBO = runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)) {
            return ReturnT.FAIL;
        }
        if (scheduleRunBO.isEnd()) {
            return ReturnT.SUCCESS;
        }

        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return contractPayDataService.managerDataContractPaymentToday(
                PaymentWayEnum.ALIPAY.name().toLowerCase(),
                shardIndex,
                shardTotal,
                param,
                scheduleRunBO.getDataScope()
        );
    }

    /**
     * 微信代扣支付订单同步 - 昨日订单轮询（优化版）
     * 每15分钟轮询一次，查询昨日特定时间段的等待支付订单
     */
    @XxlJob("wechatContractPaymentSynYesterday")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> wechatContractPaymentSynYesterday() {
        ScheduleRunBO scheduleRunBO = runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)) {
            return ReturnT.FAIL;
        }
        if (scheduleRunBO.isEnd()) {
            return ReturnT.SUCCESS;
        }

        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return contractPayDataService.managerDataContractPaymentYesterday(
                PaymentWayEnum.WECHAT.name().toLowerCase(),
                shardIndex,
                shardTotal,
                param,
                scheduleRunBO.getDataScope()
        );
    }

    /**
     * 微信代扣支付订单同步 - 当日订单轮询（优化版）
     * 每30分钟轮询一次，查询当日特定时间段的等待支付订单
     */
    @XxlJob("wechatContractPaymentSynToday")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> wechatContractPaymentSynToday() {
        ScheduleRunBO scheduleRunBO = runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)) {
            return ReturnT.FAIL;
        }
        if (scheduleRunBO.isEnd()) {
            return ReturnT.SUCCESS;
        }

        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();

        return contractPayDataService.managerDataContractPaymentToday(
                PaymentWayEnum.WECHAT.name().toLowerCase(),
                shardIndex,
                shardTotal,
                param,
                scheduleRunBO.getDataScope()
        );
    }
}
