package com.cmpay.payment.utils;

import java.math.BigDecimal;
import java.util.Optional;

public class AmountUtils {
    private AmountUtils() {

    }

    private static final BigDecimal HUNDRED = new BigDecimal(100);

    /**
     * 分转元
     */
    public static BigDecimal changeYuanReturnBigDecimal(BigDecimal amount) {
        if(!Optional.ofNullable(amount).isPresent() || amount.compareTo(BigDecimal.ZERO) == 0){
            return BigDecimal.ZERO;
        }
        return amount.divide(HUNDRED).setScale(2,BigDecimal.ROUND_DOWN );
    }
}