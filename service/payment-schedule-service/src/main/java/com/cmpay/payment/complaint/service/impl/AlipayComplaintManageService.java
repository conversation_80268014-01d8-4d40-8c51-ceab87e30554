package com.cmpay.payment.complaint.service.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.CommonUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.ComplaintSyncBO;
import com.cmpay.payment.channel.OutNrtGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.dto.alipay.complaint.*;
import com.cmpay.payment.utils.DateTimeConvertUtils;
import com.cmpay.payment.utils.PaymentUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;


@Service
public class AlipayComplaintManageService {
    @Resource
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;

    /**
     * 获取支付宝投诉列表
     */
    public ComplaintQueryListRsp getComplaintList(ComplaintSyncBO syncBO) {
        ComplaintQueryReq queryReq = new ComplaintQueryReq();
        queryReq.setPageNum(syncBO.getPageNum());

        String beginDateTime = DateTimeConvertUtils.convertDateTimeFormat(syncBO.getBeginDateTime(), DateTimeConvertUtils.DEFAULT_DATE_TIME, DateTimeConvertUtils.STANDARD_DATE_TIME);
        String endDateTime = DateTimeConvertUtils.convertDateTimeFormat(syncBO.getEndDateTime(), DateTimeConvertUtils.DEFAULT_DATE_TIME, DateTimeConvertUtils.STANDARD_DATE_TIME);

        try {
            queryReq.setBeginDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(beginDateTime));
            queryReq.setEndDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(endDateTime));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(PaymentUtils.buildRequest(OutNrtGatePayEnum.COMPLAINT_LIST_QUERY.getName(), queryReq));
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        if (CommonUtils.isNull(genericRspDTO.getBody().getResult())) {
            BusinessException.throwBusinessException(MsgCodeEnum.QUERY_ALIPAY_COMPLAINT_LIST_ERROR);
        }
        return (ComplaintQueryListRsp) genericRspDTO.getBody().getResult();
    }

    /**
     * 获取支付宝投诉详情
     */
    public ComplaintDetailQueryRsp getComplaintDetail(String complaintId) {
        ComplaintDetailQueryReq queryReq = new ComplaintDetailQueryReq();
        queryReq.setComplaintId(complaintId);
        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(PaymentUtils.buildRequest(OutNrtGatePayEnum.COMPLAINT_DETAIL_QUERY.getName(), queryReq));
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        if (CommonUtils.isNull(genericRspDTO.getBody().getResult())) {
            BusinessException.throwBusinessException(MsgCodeEnum.QUERY_ALIPAY_COMPLAINT_DETAIL_ERROR);
        }
        return (ComplaintDetailQueryRsp) genericRspDTO.getBody().getResult();
    }
}