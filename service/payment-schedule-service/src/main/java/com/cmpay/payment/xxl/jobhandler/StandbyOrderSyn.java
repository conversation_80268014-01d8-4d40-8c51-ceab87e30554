package com.cmpay.payment.xxl.jobhandler;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.payment.schedule.bo.base.ScheduleRunBO;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.service.base.TimerRunLocationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 备用订单同步任务
 * <AUTHOR>
 */
@Component
public class StandbyOrderSyn {

    @Autowired
    private ICommonManagerDataService commonManagerDataService;
    @Autowired
    private TimerRunLocationService runLocationService;

    @XxlJob("standbyOrderSyn")
    @InitialLemonData("lemonDataInitializer")
    public ReturnT<String> standbyOrderSyn() {
        ScheduleRunBO scheduleRunBO=runLocationService.getScheduleRunParams();
        if (JudgeUtils.isNull(scheduleRunBO)){
            return ReturnT.FAIL;
        }
        if(scheduleRunBO.isEnd()){
            return ReturnT.SUCCESS;
        }
        String param = XxlJobHelper.getJobParam();
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        return commonManagerDataService.standbyOrderSyn(param,shardIndex,shardTotal,scheduleRunBO.getDataScope());
    }
}
