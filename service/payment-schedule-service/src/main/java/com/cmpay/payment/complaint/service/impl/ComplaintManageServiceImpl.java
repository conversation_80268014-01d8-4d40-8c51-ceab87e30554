package com.cmpay.payment.complaint.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.CommonUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.ComplaintDetailQueryBO;
import com.cmpay.payment.bo.ComplaintSyncBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.complaint.ChannelEnum;
import com.cmpay.payment.dto.alipay.complaint.*;
import com.cmpay.payment.dto.wechat.complaint.*;
import com.cmpay.payment.entity.FapCustomerComplaintDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.complaint.service.IComplaintManageService;
import com.cmpay.payment.complaint.service.IFapCustomerComplaintService;
import com.cmpay.payment.schedule.bo.config.ContractMerKeyQueryBO;
import com.cmpay.payment.schedule.service.config.IExtContractService;
import com.cmpay.payment.schedule.service.pay.ExtPayOrderService;
import com.cmpay.payment.utils.AmountUtils;
import com.cmpay.payment.utils.DateTimeConvertUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class ComplaintManageServiceImpl implements IComplaintManageService {

    @Resource
    private ExtPayOrderService payOrderService;
    @Resource
    private IExtContractService extContractService;
    @Resource
    private IFapCustomerComplaintService fapCustomerComplaintService;
    @Resource
    private AlipayComplaintManageService alipayComplaintManageService;
    @Resource
    private WechatComplaintManageService wechatComplaintManageService;

    @Override
    public void syncComplaintRecord(ComplaintSyncBO syncBO) {
        if (CommonUtils.isBlank(syncBO.getBeginDate()) || CommonUtils.isBlank(syncBO.getEndDate())) {
            BusinessException.throwBusinessException(MsgCodeEnum.COMPLAINT_DATE_CAN_NOT_BE_EMPTY);
        }
        if (!CommonUtils.equalsAny(syncBO.getComplaintChannel(), ChannelEnum.ALI_PAY.getCode(), ChannelEnum.WE_CHAT.getCode())) {
            BusinessException.throwBusinessException(MsgCodeEnum.COMPLAINT_CHANNEL_ERROR);
        }

        if (CommonUtils.equals(syncBO.getComplaintChannel(), ChannelEnum.ALI_PAY.getCode())) {
            syncAliPayComplaintList(syncBO);
        }
        if (CommonUtils.equals(syncBO.getComplaintChannel(), ChannelEnum.WE_CHAT.getCode())) {
            List<ContractMerKeyQueryBO> contractMerKeyQueryBOS = getWeChatContractMerchantList();

            for (ContractMerKeyQueryBO contractMerKeyQueryBO : contractMerKeyQueryBOS) {
                syncBO.setMerchantId(contractMerKeyQueryBO.getMerchantNumber());

                syncWechatComplaintListAsync(syncBO);
            }
        }
    }

    /**
     * 同步微信投诉列表（批量异步处理）
     */
    private void syncWechatComplaintListAsync(ComplaintSyncBO syncBO) {
        WxPayComplaintListQueryResponse listQueryResponse = wechatComplaintManageService.getComplaintList(syncBO);
        if (CommonUtils.isEmpty(listQueryResponse.getComplaintList())) {
            return;
        }

        syncBO.setTotalCount(listQueryResponse.getTotalCount());
        syncBO.setOffset(Integer.valueOf(listQueryResponse.getPageNum()));
        syncBO.setLimit(Integer.valueOf(listQueryResponse.getPageCount()));
        syncBO.setRequestId(LemonUtils.getRequestId());

        // 设置并发批次大小
        int batchSize = 10;
        int totalBatches = (syncBO.getTotalCount() + syncBO.getLimit() - 1) / syncBO.getLimit();

        for (int batchStart = 0; batchStart < totalBatches; batchStart += batchSize) {
            int batchEnd = Math.min(batchStart + batchSize, totalBatches);
            List<CompletableFuture<List<FapCustomerComplaintDO>>> futures = new ArrayList<>();

            // 创建一批异步任务
            for (int batchIndex = batchStart; batchIndex < batchEnd; batchIndex++) {
                final int offset = batchIndex * syncBO.getLimit();
                ComplaintSyncBO batchSyncBO = BeanUtils.copyPropertiesReturnDest(new ComplaintSyncBO(), syncBO);
                batchSyncBO.setOffset(offset);

                CompletableFuture<List<FapCustomerComplaintDO>> future = CompletableFuture
                        .supplyAsync(() -> getWeChatComplaintList(batchSyncBO));
                futures.add(future);
            }

            // 等待一批任务完成并处理结果
            CompletableFuture<Void> batchFuture = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0]));

            try {
                batchFuture.join();
                for (CompletableFuture<List<FapCustomerComplaintDO>> future : futures) {
                    List<FapCustomerComplaintDO> complaintList = future.get();
                    if (!CommonUtils.isEmpty(complaintList)) {
                        fapCustomerComplaintService.insertComplaintList(complaintList);
                    }
                }
            }  catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("批量获取微信投诉数据失败", e);
            } catch (Exception e) {
                log.error("批量获取微信投诉数据失败", e);
            }
        }
    }

    /**
     * 同步支付宝投诉列表
     */
    private void syncAliPayComplaintList(ComplaintSyncBO syncBO) {
        syncBO.setBeginDateTime(syncBO.getBeginDate() + "000000");
        syncBO.setEndDateTime(syncBO.getBeginDate() + "235959");

        ComplaintQueryListRsp listQueryResponse = alipayComplaintManageService.getComplaintList(syncBO);
        if (CommonUtils.isEmpty(listQueryResponse.getComplaintQueryDTOList())) {
            return;
        }

        syncBO.setTotalCount(listQueryResponse.getTotalSize().intValue());
        syncBO.setPageNum(listQueryResponse.getCurrentPage());
        syncBO.setPageSize(listQueryResponse.getPageSize());

        long totalPages = (syncBO.getTotalCount() + syncBO.getPageSize() - 1) / syncBO.getPageSize();

        for (long pageNum = 1; pageNum <= totalPages; pageNum += 1) {
            syncBO.setPageNum(pageNum);

            List<FapCustomerComplaintDO> aliPayComplaintList = getAliPayComplaintList(syncBO);

            fapCustomerComplaintService.insertComplaintList(aliPayComplaintList);
        }
    }

    @Override
    public void getComplaintDetail(ComplaintDetailQueryBO queryBO) {
        if (!CommonUtils.equalsAny(queryBO.getComplaintChannel(), ChannelEnum.ALI_PAY.getCode(), ChannelEnum.WE_CHAT.getCode())) {
            BusinessException.throwBusinessException(MsgCodeEnum.COMPLAINT_CHANNEL_ERROR);
        }

        getOrderState(queryBO);

        if (CommonUtils.equals(queryBO.getComplaintChannel(), ChannelEnum.ALI_PAY.getCode())) {
            getAliPayComplaintDetail(queryBO);
            return;
        }
        if (CommonUtils.equals(queryBO.getComplaintChannel(), ChannelEnum.WE_CHAT.getCode())) {
            getWeChatComplaintDetail(queryBO);

            wechatComplaintManageService.getWeChatComplaintDealHistory(queryBO);
        }
    }

    /**
     * 获取支付宝投诉列表
     */
    private List<FapCustomerComplaintDO> getAliPayComplaintList(ComplaintSyncBO syncBO) {
        List<FapCustomerComplaintDO> complaintDOList = new ArrayList<>();
        ComplaintQueryListRsp listQueryResponse = alipayComplaintManageService.getComplaintList(syncBO);
        if (CommonUtils.isEmpty(listQueryResponse.getComplaintQueryDTOList())) {
            return complaintDOList;
        }
        for (ComplaintQueryDTO complaintQueryDTO : listQueryResponse.getComplaintQueryDTOList()) {
            FapCustomerComplaintDO complaintDO = new FapCustomerComplaintDO();
            complaintDO.setComplaintId(String.valueOf(complaintQueryDTO.getComplaintId()));
            complaintDO.setComplaintChannel(ChannelEnum.ALI_PAY.getCode());
            complaintDO.setComplaintContent(complaintQueryDTO.getComplaintContent());
            complaintDO.setComplaintTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(complaintQueryDTO.getComplaintTime()));
            complaintDO.setMerchantOrderNo(complaintQueryDTO.getTradeOrderNo());
            complaintDO.setOrgOrderNo(complaintQueryDTO.getOrganizationOrderNo());
            complaintDO.setOrgMerchantId(complaintQueryDTO.getOrganizationMerchantId());
            complaintDO.setAmount(complaintQueryDTO.getTradeAmount());
            complaintDO.setComplaintState(complaintQueryDTO.getComplaintState());

            complaintDOList.add(complaintDO);
        }
        return complaintDOList;
    }

    /**
     * 获取微信投诉列表
     */
    private List<FapCustomerComplaintDO> getWeChatComplaintList(ComplaintSyncBO syncBO) {
        List<FapCustomerComplaintDO> complaintDOList = new ArrayList<>();
        WxPayComplaintListQueryResponse listQueryResponse = wechatComplaintManageService.getComplaintList(syncBO);
        if (CommonUtils.isEmpty(listQueryResponse.getComplaintList())) {
            return complaintDOList;
        }

        for (ComplaintInfo complaintInfo : listQueryResponse.getComplaintList()) {
            FapCustomerComplaintDO complaintDO = new FapCustomerComplaintDO();
            complaintDO.setComplaintId(complaintInfo.getComplaintId());
            complaintDO.setComplaintChannel(ChannelEnum.WE_CHAT.getCode());
            complaintDO.setComplaintContent(complaintInfo.getComplaintDetail());
            complaintDO.setComplaintTime(convertWeChatComplaintTime(complaintInfo.getComplaintTime()));
            complaintDO.setOrgMerchantId(syncBO.getMerchantId());
            complaintDO.setComplaintState(complaintInfo.getComplaintState());

            ComplaintOrderInfo complaintOrderInfo = complaintInfo.getComplaintOrderList().get(0);
            complaintDO.setMerchantOrderNo(complaintInfo.getComplaintOrderList().get(0).getTradeOrderNo());
            complaintDO.setOrgOrderNo(complaintInfo.getComplaintOrderList().get(0).getOrganizationOrderNo());
            complaintDO.setAmount(convertWeChatComplaintAmount(complaintOrderInfo.getTradeAmount()));

            complaintDOList.add(complaintDO);
        }
        return complaintDOList;
    }

    /**
     * 获取微信投诉详情
     */
    private void getWeChatComplaintDetail(ComplaintDetailQueryBO queryBO) {
        WxPayComplaintDetailQueryResponse detailQueryResponse = wechatComplaintManageService.getComplaintDetail(queryBO
                .getComplaintId(), queryBO.getOrganizationMerchantId());
        queryBO.setComplaintId(detailQueryResponse.getComplaintId());
        queryBO.setComplaintTime(convertWeChatComplaintTime(detailQueryResponse.getComplaintTime()));
        queryBO.setComplaintContent(detailQueryResponse.getComplaintDetail());
        queryBO.setComplaintState(detailQueryResponse.getComplaintState());
        queryBO.setMobileNo(detailQueryResponse.getMobileNo());
        queryBO.setProblemType(detailQueryResponse.getProblemType());
        queryBO.setProblemDescription(detailQueryResponse.getProblemDescription());
        queryBO.setComplaintUserTag(detailQueryResponse.getComplaintUserTag());
        queryBO.setNeedImmediateService(BooleanUtils.toString(detailQueryResponse.getNeedImmediateService(),"是","否"));
        queryBO.setTradeOrderNo(detailQueryResponse.getComplaintOrderList().get(0).getTradeOrderNo());
        queryBO.setOrganizationOrderNo(detailQueryResponse.getComplaintOrderList().get(0).getOrganizationOrderNo());
        queryBO.setTradeAmount(convertWeChatComplaintAmount(detailQueryResponse.getComplaintOrderList().get(0).getTradeAmount()));
    }

    /**
     * 获取支付宝投诉详情
     */
    private void getAliPayComplaintDetail(ComplaintDetailQueryBO queryBO) {
        ComplaintDetailQueryRsp detailQueryRsp = alipayComplaintManageService.getComplaintDetail(queryBO.getComplaintId());
        queryBO.setTradeOrderNo(detailQueryRsp.getMerchantOrderNo());
        queryBO.setOrganizationOrderNo(detailQueryRsp.getOrganizationOrderNo());
        queryBO.setComplaintState(detailQueryRsp.getComplaintState());
        queryBO.setOrganizationMerchantId(detailQueryRsp.getOrganizationMerchantId());
        queryBO.setComplaintId(String.valueOf(detailQueryRsp.getComplaintId()));
        queryBO.setComplaintContent(detailQueryRsp.getComplaintContent());
        queryBO.setTradeAmount(detailQueryRsp.getTradeAmount());
        queryBO.setComplaintTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(detailQueryRsp.getComplaintTime()));
        queryBO.setMobileNo(detailQueryRsp.getMobileNo());
    }

    /**
     * 转换微信投诉时间
     * yyyy-MM-dd'T'HH:mm:ss.SSSXXX->yyyy-MM-dd HH:mm:ss
     */
    private String convertWeChatComplaintTime(String complaintTime) {
        return DateTimeConvertUtils.convertDateTimeFormat(complaintTime, DateTimeFormatter.ISO_DATE_TIME, DateTimeConvertUtils.STANDARD_DATE_TIME);
    }

    /**
     * 转换微信投诉金额
     */
    private String convertWeChatComplaintAmount(int centAmount) {
        BigDecimal amount = new BigDecimal(centAmount);
        amount = AmountUtils.changeYuanReturnBigDecimal(amount);
        return String.valueOf(amount);
    }

    /**
     * 获取微信签约商户列表
     */
    private List<ContractMerKeyQueryBO> getWeChatContractMerchantList() {
        ContractMerKeyQueryBO contractMerKeyQueryBO = new ContractMerKeyQueryBO();
        contractMerKeyQueryBO.setPaymentMechanism("wechat");
        contractMerKeyQueryBO.setTotal("5000");
        return extContractService.queryContractMerchant(contractMerKeyQueryBO);
    }

    /**
     * 获取订单状态
     */
    private void getOrderState(ComplaintDetailQueryBO queryBO) {
        TradeOrderDO tradeOrderDO = payOrderService.get(queryBO.getTradeOrderNo());
        if (CommonUtils.isNull(tradeOrderDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        queryBO.setTradeStatus(tradeOrderDO.getStatus());
    }
}