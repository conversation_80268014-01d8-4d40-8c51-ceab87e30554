package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.entity.config.ContractDO;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.cmpay.*;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.ext.config.IExtContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Locale;

/**
 * Created on 2018/12/3
 *
 * @author: chen_lan
 */
@Service
@Transactional(propagation = Propagation.REQUIRES_NEW)
public class CmpayRefundQueryListenerServiceImpl extends PaymentListenerService<RefundOrderQueryBO> {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    private IExtContractService contractService;
    /**
     * cmpay退款交易
     *
     * @param eventBO
     */
    @EventListener
    @Override
    public void execute(RefundOrderQueryBO eventBO) {
        super.execute(eventBO);
    }

    @Override
    protected boolean checkChannelExecutable(RefundOrderQueryBO refundOrderQueryBO) {

        return refundOrderQueryBO != null && refundOrderQueryBO.getRefundOrder() != null &&  StringUtils.equalsIgnoreCase(PaymentWayEnum.CMPAY.name(),
                refundOrderQueryBO.getRefundOrder().getAimProductCode());
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(RefundOrderQueryBO refundOrderQueryBO) {

        if (refundOrderQueryBO == null || refundOrderQueryBO.getRefundOrder() == null) {
            return null;
        }
        ContractDO contract = contractService.getContract(refundOrderQueryBO.getOriginalPayTradeOrder().getBankMerchantNo());
        refundOrderQueryBO.setContractSecureValue(contract.getSecretKey());
        refundOrderQueryBO.setSignMethod(contract.getSignMethod());
        String payWayCode = refundOrderQueryBO.getRefundOrder().getPayWayCode().toUpperCase(Locale.ENGLISH);
        if (PaymentSceneEnum.BARCODE.name().equals(payWayCode)
                || PaymentSceneEnum.SCAN.name().equals(payWayCode)) {
            // 返回cmpay Cloud退款查询接口
            return getBean(CmpayCloudRefundQueryChannelService.class);
        } else if(PaymentSceneEnum.CONTRACTPAY.name().equals(payWayCode)){
            return getBean(CmpayContractRefundQueryChannelService.class);
        } else if(PaymentSceneEnum.CMPAYACCOUNTPAY.name().equals(payWayCode)){
            return getBean(CmpayAccountRefundQueryChannelService.class);
        }
        else {
            // 返回cmpay 远程退款查询接口
            return getBean(CmpayOnlineRefundQueryChannelService.class);
        }
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
