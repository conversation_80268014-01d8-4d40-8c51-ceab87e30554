package com.cmpay.payment.service.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.AmountInfoBO;
import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dto.cmpay.CmpayAccountPaymentQueryReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayAccountPaymentQueryRspDTO;
import com.cmpay.payment.service.channel.cmpay.CmpayAccountPaymentQueryChannelService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.PaymentUtils;
import com.cmpay.payment.utils.BeanConvertUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Created on 2024/04/27
 *
 * @author: lb
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayAccountPaymentQueryChannelServiceImpl implements CmpayAccountPaymentQueryChannelService {

    private static final Logger logger = LoggerFactory.getLogger(CmpayAccountPaymentQueryChannelServiceImpl.class);
    @Value("${cmpay.channelId:}")
    private String channelId;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;

    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Autowired
    private ExtParamInfoService paramInfoService;

    @Override
    public void send(PaymentQueryBO paymentQueryBO) {
        cmpayAccountPaymentQuery(paymentQueryBO);
    }

    @Override
    public void cmpayAccountPaymentQuery(PaymentQueryBO paymentQueryBO) {
        CmpayAccountPaymentQueryReqDTO accountPaymentQueryReqDTO = new CmpayAccountPaymentQueryReqDTO();
        accountPaymentQueryReqDTO.setVersion(CmpayConstants.VERSION);
        accountPaymentQueryReqDTO.setType(CmpayConstants.TYPE);
        accountPaymentQueryReqDTO.setRequestNo(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.CMPAY_JRN_NO, 18));
        accountPaymentQueryReqDTO.setChannelNo(channelId);
        accountPaymentQueryReqDTO.setMerchantId(paymentQueryBO.getPaymentOrder().getBankMerchantNo());
        accountPaymentQueryReqDTO.setMerchantOrderDate(paymentQueryBO.getPaymentOrder().getOrderDate());
        accountPaymentQueryReqDTO.setMerchantOrderNo(paymentQueryBO.getPaymentOrder().getTradeOrderNo());

        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(CmpayPayChannelEnum.CMPAY_ACCOUNT);
        request.setBusiType(CmpayPayChannelEnum.CMPAY_ACCOUNT_PAYMENT_QUERY.getName());
        request.setSource(CmpayPayChannelEnum.CMPAY_ACCOUNT);
        request.setTarget(accountPaymentQueryReqDTO);
        GenericRspDTO<Response> genericRspDTO;
        if (StringUtils.equals(paymentQueryBO.getSourceApp(), AppEnum.integrationpaymnet.name())) {
            genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        } else {
            genericRspDTO =nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        }

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .map(result -> handleAccountOrderQuery(paymentQueryBO, (CmpayAccountPaymentQueryRspDTO) result))
                .orElseThrow(() ->new BusinessException(MsgCodeEnum.PAYMENT_STATUS_UNDEFINED));
    }

    private PaymentQueryBO handleAccountOrderQuery(PaymentQueryBO paymentQueryBO, CmpayAccountPaymentQueryRspDTO paymentQueryRspDTO) {
        if (JudgeUtils.isNotSuccess(paymentQueryRspDTO.getMsgCd())) {
            BusinessException.throwBusinessException(paymentQueryRspDTO.getMsgCd());
        }
        String status = getOrderStatus(CmpayAccountOrderStatusEnum.valueOf(paymentQueryRspDTO.getOrderStatus()));
        if (JudgeUtils.isNull(status)) {
            BusinessException.throwBusinessException(MsgCodeEnum.FAP_SUCCESSFUL);
        }
        if (OrderStatusEnum.TRADE_FAIL.name().equals(status)) {
            paymentQueryBO.setErrMsgCd(paymentQueryRspDTO.getMsgCd());
            paymentQueryBO.setErrMsgInfo(paymentQueryRspDTO.getMsgInfo());
        }
        paymentQueryBO.setStatus(status);
        paymentQueryBO.getPaymentOrder().setThirdOrdNo(paymentQueryRspDTO.getOrderNo());
        paymentQueryBO.getPaymentOrder().setThirdOrdDt(paymentQueryRspDTO.getPayDate());
        if (JudgeUtils.isNotEmpty(paymentQueryRspDTO.getPayAmountList())) {
            // 记录支付金额明细集合
            List<AmountInfoBO> amountInfoBOS = BeanConvertUtils.convertList(paymentQueryRspDTO.getPayAmountList(), AmountInfoBO.class);
            paymentQueryBO.setPayAmountList(PaymentUtils.setAmountInfoBOList(amountInfoBOS, paramInfoService.getAmountListParam()));
        }
        if (JudgeUtils.isNotNull(paymentQueryRspDTO.getInstPaidAmount())) {
            paymentQueryBO.setInstPaidAmount(paymentQueryRspDTO.getInstPaidAmount());
        }
        if (JudgeUtils.isNotNull(paymentQueryRspDTO.getInstDiscountSettlementAmount())) {
            paymentQueryBO.setInstDiscountSettlementAmount(paymentQueryRspDTO.getInstDiscountSettlementAmount());
        }
        if (JudgeUtils.isNotNull(paymentQueryRspDTO.getInstDiscountUnsettledAmount())) {
            paymentQueryBO.setInstDiscountUnsettledAmount(paymentQueryRspDTO.getInstDiscountUnsettledAmount());
        }
        return paymentQueryBO;
    }

    private String getOrderStatus(CmpayAccountOrderStatusEnum accountOrderStatusEnum) {
        switch (accountOrderStatusEnum) {
            case S:
                return OrderStatusEnum.TRADE_SUCCESS.name();
            case F:
                return OrderStatusEnum.TRADE_FAIL.name();
            default:
                return null;
        }
    }
}
