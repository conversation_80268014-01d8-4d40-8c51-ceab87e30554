package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.protocol.ProtocolQueryBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.DcepUserProtocolQueryReq;
import com.cmpay.payment.dto.dcep.DcepUserProtocolQueryRsp;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.DcepUserProtocolQueryService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.util.AES256Util;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/8/17
 */
@Service
public class DcepUserProtocolQueryServiceImpl extends AbstractDcepRequestServiceImpl implements DcepUserProtocolQueryService {

    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;
    @Autowired
    private ExtPayOrderService payOrderService;

    @Override
    public void protocolQuery(ProtocolQueryBO protocolQueryBO) {
        DcepUserProtocolQueryReq userProtocolQueryReq = new DcepUserProtocolQueryReq();
        TradeOrderDO tradeOrderDO = new TradeOrderDO();
        tradeOrderDO.setOutTradeNo(protocolQueryBO.getOutTradeNo());
        tradeOrderDO.setRequestDate(protocolQueryBO.getTradeDate());
        tradeOrderDO = payOrderService.getOrderByOutTradeNoTradeDate(tradeOrderDO);
        buildRequestDto(userProtocolQueryReq, protocolQueryBO, tradeOrderDO.getBankMerchantNo());
        userProtocolQueryReq.setSubMerchantNo(protocolQueryBO.getSubMerchantId());
        userProtocolQueryReq.setVersion(DcepConstants.VERSION);
        userProtocolQueryReq.setOrgNo(dcepPaymentProperties.getNewOrgNo());
        userProtocolQueryReq.setSign(userProtocolQueryReq.getSmxKey());
        userProtocolQueryReq.setMobile(AES256Util.encode(userProtocolQueryReq.getSmxKey(), protocolQueryBO.getMobileNo()));
        buildDcepRequest(userProtocolQueryReq, protocolQueryBO, DcepPaymentChannelEnum.USER_PROTOCOL_SIGN_QUERY.getName());
    }

    @Override
    public void send(ProtocolQueryBO protocolQueryBO) {
        protocolQuery(protocolQueryBO);
    }

    @Override
    protected BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        DcepUserProtocolQueryRsp userProtocolQueryRsp = (DcepUserProtocolQueryRsp) abstractDcepRsp;
        ProtocolQueryBO protocolQueryBO = (ProtocolQueryBO) baseDcepHandlerBO;
        protocolQueryBO.setErrMsgInfo(userProtocolQueryRsp.getMsg());
        if (JudgeUtils.equals(DcepConstants.SUCCESS_CODE, userProtocolQueryRsp.getCode())) {
            DcepUserProtocolQueryRsp.Data data = userProtocolQueryRsp.getData();
            if (StringUtils.isBlank(data.getStatus())) {
                BusinessException.throwBusinessException(MsgCodeEnum.DCEP_PAY_QUERY_FAIL);
            }
            protocolQueryBO.setAgreementId(data.getAgreementSeq());
            protocolQueryBO.setSignFlag(data.getStatus());
        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.DCEP_PAY_QUERY_FAIL);
        }
        return protocolQueryBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }
}
