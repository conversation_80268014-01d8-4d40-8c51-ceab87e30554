package com.cmpay.payment.util;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * Created on 2020/9/3
 *
 * @author: huang_yh1
 */
public class LemonAmount implements Serializable {

    private BigDecimal bal;

    public LemonAmount(BigDecimal amt) {
        this.bal = amt.setScale(2, BigDecimal.ROUND_HALF_UP);
        this.bal.add(new BigDecimal(0));
    }

    public LemonAmount(String amt) {
        bal = new BigDecimal(amt).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.bal.add(new BigDecimal(0));
    }

    public LemonAmount(String amt,int scale) {
        bal = new BigDecimal(amt).setScale(scale, BigDecimal.ROUND_HALF_UP);
        this.bal.add(new BigDecimal(0));
    }

    public LemonAmount(float amt) {
        bal = new BigDecimal(amt).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.bal.add(new BigDecimal(0));
    }

    public LemonAmount(double amt) {
        bal = new BigDecimal(amt).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.bal.add(new BigDecimal(0));
    }

    public LemonAmount(int amt) {
        bal = new BigDecimal(amt).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.bal.add(new BigDecimal(0));
    }

    public LemonAmount(long amt) {
        bal = new BigDecimal(amt).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.bal.add(new BigDecimal(0));
    }

    /**
     * 金额加
     * @param amt
     * @return
     */
    public LemonAmount add(LemonAmount amt) {
        return new LemonAmount(bal.add(amt.toBigDecimal()));
    }

    /**
     * 金额减
     * @param amt
     * @return
     */
    public LemonAmount sub(LemonAmount amt) {
        return new LemonAmount(bal.subtract(amt.toBigDecimal()));
    }

    /**
     * 比较金额大小
     * @param amt
     * @return -1 小于  0 相等  1大于
     */
    public int compare(LemonAmount amt) {
        return bal.compareTo(amt.toBigDecimal());
    }

    public boolean equal(LemonAmount amt) {
        return bal.compareTo(amt.toBigDecimal()) == 0;
    }

    /**
     * 金额乘
     * @param amt
     * @return
     */
    public LemonAmount mul(LemonAmount amt) {
        BigDecimal value = bal.multiply(amt.toBigDecimal());
        value = value.divide(new BigDecimal("1"), 2, BigDecimal.ROUND_HALF_UP);
        return new LemonAmount(value);
    }

    /**
     * 金额乘
     * @param decimal
     * @return
     */
    public LemonAmount mul(BigDecimal decimal) {
        BigDecimal value = bal.multiply(decimal);
        value = value.divide(new BigDecimal("1"), 2, BigDecimal.ROUND_HALF_UP);
        return new LemonAmount(value);
    }

    /**
     * 金额乘
     * @param amt
     * @param scale
     * @return
     */
    public LemonAmount mul(LemonAmount amt,int scale) {
        BigDecimal value = bal.multiply(amt.toBigDecimal());
        value = value.divide(new BigDecimal("1"), scale, BigDecimal.ROUND_HALF_UP);
        return new LemonAmount(value);
    }
    /**
     * 金额相除
     * @param amt
     * @return
     */
    public LemonAmount divide(LemonAmount amt) {
        BigDecimal value = bal.divide(amt.toBigDecimal());
        value = value.divide(new BigDecimal("1"), 2, BigDecimal.ROUND_HALF_UP);
        return new LemonAmount(value);
    }

    public LemonAmount divide(BigDecimal decimal) {
        BigDecimal value = bal.divide(decimal);
        value = value.divide(new BigDecimal("1"), 2, BigDecimal.ROUND_HALF_UP);
        return new LemonAmount(value);
    }

    public String yuan2fen() {
        BigDecimal fen = this.toBigDecimal().multiply(new BigDecimal(100));
        DecimalFormat decimalFormat = new DecimalFormat("0");
        return decimalFormat.format(fen.doubleValue());
    }

    public String fen2yuan() {
        BigDecimal yuan = this.toBigDecimal().divide(new BigDecimal(100));
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        return decimalFormat.format(yuan.doubleValue());
    }

    public BigDecimal toBigDecimal() {
        return this.bal;
    }

    @Override
    public String toString() {
        return bal.toString();
    }

    public String getBal(){
        return bal.toString();
    }
}
