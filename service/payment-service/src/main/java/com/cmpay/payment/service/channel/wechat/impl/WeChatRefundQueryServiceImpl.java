package com.cmpay.payment.service.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.constant.wechat.WeChatRefundStatusEnum;
import com.cmpay.payment.dto.wechat.WXPayRefundqueryRequest;
import com.cmpay.payment.dto.wechat.WXPayRefundqueryResponse;
import com.cmpay.payment.service.TradeCommonService;
import com.cmpay.payment.service.channel.wechat.WeChatRefundQueryService;
import com.cmpay.payment.util.PaymentUtils;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatRefundQueryServiceImpl implements WeChatRefundQueryService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatRefundQueryServiceImpl.class);

    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Autowired
    private TradeCommonService tradeCommonService;
    /**
     * 微信H5支付查询退款
     *
     * @param refundOrderQueryBO
     */
    @Override
    public void weChatRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {

        WXPayRefundqueryRequest wxPayRefundqueryRequest = new WXPayRefundqueryRequest();
        // 微信订单号查询的优先级是： refund_id > out_refund_no > transaction_id > out_trade_no
        wxPayRefundqueryRequest.setOutRefundNo(refundOrderQueryBO.getRefundOrder().getOutTradeNo());
        // 沙箱环境只支持根据out_trade_no查询
        wxPayRefundqueryRequest.setOutTradeNo(refundOrderQueryBO.getOriginalPayTradeOrder().getOutTradeNo());
        wxPayRefundqueryRequest.setAppid(refundOrderQueryBO.getOriginalPayTradeOrder().getWechatAppid());
        wxPayRefundqueryRequest.setMchId(refundOrderQueryBO.getOriginalPayTradeOrder().getBankMerchantNo());
        wxPayRefundqueryRequest.setKey(refundOrderQueryBO.getContractSecureValue());
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.REFUNDQUERY.getSource());
        request.setRoute(WXPayChannel.REFUNDQUERY.getRoute());
        request.setBusiType(WXPayChannel.REFUNDQUERY.getBusType());
        request.setTarget(wxPayRefundqueryRequest);
        logger.info("WeChatRefundQueryServiceImpl Source : {} Route : {} BusiType : {}",request.getSource(),request.getRoute(),request.getBusiType());

        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(refundOrderQueryBO, (WXPayRefundqueryResponse) result));

    }

    @Override
    public void send(RefundOrderQueryBO refundOrderQueryBO) {
        weChatRefundQuery(refundOrderQueryBO);
    }

    private void handleResult(RefundOrderQueryBO refundOrderQueryBO, WXPayRefundqueryResponse response) {
        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                // 沙箱环境只支持根据out_trade_no查询，故需对退款list做过滤
                List<WXPayRefundqueryResponse.RefundInfo> refundList = response.getRefundList().stream()
                        .filter(refundInfo -> StringUtils.equals(refundInfo.getOutRefundNo(), refundOrderQueryBO.getRefundOrder().getOutTradeNo()))
                        .collect(Collectors.toList());

                // 暂不考虑根据offset查询，故过滤后当前10笔订单中没有与查询订单匹配的订单，报错返回，注意，此处仅限沙箱环境
                if (refundList.size() == 0) {
                    logger.error("微信沙箱环境退款笔数超过10笔，暂不支持根据支付订单号查询退款！");
                    BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_REFUND_FAILURE);
                }

                WXPayRefundqueryResponse.RefundInfo refundInfo = refundList.get(0);

                WeChatRefundStatusEnum status = EnumUtils.getEnum(WeChatRefundStatusEnum.class, refundInfo.getRefundStatus());
                switch (status) {
                    case SUCCESS:
                        refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
                        refundOrderQueryBO.getRefundOrder().setAccountDate(PaymentUtils.getDate(refundInfo.getRefundSuccessTime()));
                        refundOrderQueryBO.getRefundOrder().setRefundId(refundInfo.getRefundId());
                        break;
                    case REFUNDCLOSE:
                    case CHANGE:
                        refundOrderQueryBO.setErrMsgCd(response.getErrCode());
                        refundOrderQueryBO.setErrMsgInfo(response.getErrCodeDes());
                        refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
                        break;
                    case PROCESSING:
                    default:
                        refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
                        break;
                }
            } else {
                logger.error("refund query failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());

                WeChatCommonResponseEnum.ErrorCode errorCode = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getErrCode());
                switch (errorCode) {
                    case SYSTEMERROR:
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_SYSTEM_ERROR);
                    case REFUNDNOTEXIST:
                        tradeCommonService.handleRefundOrderNotExist(refundOrderQueryBO,MsgCodeEnum.REFUND_ORDER_NOT_EXISTS);
                        break;
                    default:
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_REFUND_FAILURE);
                }
            }
        } else {
            logger.error("refund query failure, weChat return message is : {}", response.getReturnMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_REFUND_ORDER_FAILURE);
        }
    }
}
