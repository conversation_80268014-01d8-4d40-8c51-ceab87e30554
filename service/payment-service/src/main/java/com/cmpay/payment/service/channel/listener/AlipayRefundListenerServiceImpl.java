package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.aplipay.AlipayRefundChannelService;
import com.cmpay.payment.service.channel.PaymentChannelService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created on 2018/12/13
 *
 * @author: wulinfeng
 */
@Service
@Transactional(propagation = Propagation.REQUIRES_NEW)
public class AlipayRefundListenerServiceImpl extends PaymentListenerService<RefundBO> {

    @Autowired
    ApplicationContext applicationContext;

    @EventListener
    @Override
    public void execute(RefundBO eventBO) {
        super.execute(eventBO);
    }

    @Override
    protected boolean checkChannelExecutable(RefundBO refundBO) {

        return Optional.ofNullable(refundBO)
                .map(RefundBO::getOriginalPayTradeOrder)
                .map(order->{
                    return StringUtils.equalsIgnoreCase(PaymentWayEnum.ALIPAY.name(),
                            refundBO.getOriginalPayTradeOrder().getAimProductCode());})
                .orElse(false);

    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(RefundBO refundBO) {
        return getBean(AlipayRefundChannelService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }

}
