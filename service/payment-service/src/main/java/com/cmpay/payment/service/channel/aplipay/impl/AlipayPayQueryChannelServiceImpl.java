package com.cmpay.payment.service.channel.aplipay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.AlipayTradeStatusEnum;
import com.cmpay.payment.constant.AppEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.dto.alipay.TradeQueryReq;
import com.cmpay.payment.dto.alipay.TradeQueryRsp;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.channel.aplipay.AlipayPayQueryChannelService;
import com.cmpay.payment.util.PaymentUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Optional;

/**
 * Created on 2018/12/10
 *
 * @author: wulinfeng
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class AlipayPayQueryChannelServiceImpl implements AlipayPayQueryChannelService {

    private static final String CODE_SUCCESS = "10000";
    private static final String CODE_BUSINESS_ERROR = "40004";
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;

    @Override
    public void alipayBasePaymentQuery(PaymentQueryBO paymentQueryBO) {
        if (checkQueryParams(paymentQueryBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.OUT_TRADE_NO_CANNOT_BE_EMPTY);
        }

        TradeQueryReq tradeQueryReq = new TradeQueryReq();
        tradeQueryReq.setOutTradeNo(paymentQueryBO.getPaymentOrder().getTradeOrderNo());
        GenericRspDTO<Response> response;
        if (StringUtils.equalsAny(paymentQueryBO.getSourceApp(), AppEnum.integrationbatch.name(), AppEnum.integrationshecdule.name())) {
            response = this.nrtCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_QUERY.getName(), tradeQueryReq));
        } else {
            response = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_QUERY.getName(), tradeQueryReq));

        }

        if (JudgeUtils.isSuccess(response.getMsgCd())) {
            TradeQueryRsp tradeQueryRsp = (TradeQueryRsp) response.getBody().getResult();
            //登记支付宝订单号
            paymentQueryBO.getPaymentOrder().setThirdOrdNo(tradeQueryRsp.getTradeNo());
            if (StringUtils.equals(CODE_SUCCESS, tradeQueryRsp.getCode())) {
                AlipayTradeStatusEnum statusEnum = EnumUtils.getEnum(AlipayTradeStatusEnum.class, tradeQueryRsp.getTradeStatus());
                switch (statusEnum) {
                    case WAIT_BUYER_PAY:
                        paymentQueryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
                        break;
                    case TRADE_CLOSED:
                        paymentQueryBO.setStatus(OrderStatusEnum.TRADE_CLOSED.name());
                        break;
                    case TRADE_SUCCESS:
                    case TRADE_FINISHED:
                        paymentQueryBO.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                        String paymentDate = new SimpleDateFormat("yyyyMMdd").format(tradeQueryRsp.getSendPayDate());
                        paymentQueryBO.getPaymentOrder().setAccountDate(paymentDate);
                        paymentQueryBO.getPaymentOrder().setThirdOrdDt(paymentDate);
                        paymentQueryBO.getPaymentOrder().setMobileNumber(tradeQueryRsp.getBuyerUserId());
                        break;
                    default:
                        break;
                }
            } else if (StringUtils.equals(CODE_BUSINESS_ERROR, tradeQueryRsp.getCode())) {
                //FIXME 应更新为TRADE_FAIL，但需要评估更新TRADE_FAIL会不会影响
                paymentQueryBO.setStatus(OrderStatusEnum.TRADE_CLOSED.name());
            }
        }
    }

    @Override
    public void send(PaymentQueryBO paymentQueryBO) {
        alipayBasePaymentQuery(paymentQueryBO);
    }

    private boolean checkQueryParams(PaymentQueryBO paymentQueryBO) {
        return Optional.ofNullable(paymentQueryBO)
                .map(PaymentQueryBO::getPaymentOrder)
                .map(TradeOrderDO::getOutTradeNo)
                .map(StringUtils::isEmpty)
                .orElse(false);
    }
}
