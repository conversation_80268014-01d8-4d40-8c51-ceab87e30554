package com.cmpay.payment.util;

import com.cmpay.payment.constant.contract.ContractConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;

/**
 * @author： pengAnHai
 * @date： 2023-12-16
 * @description： URL工具类
 */
public class UrlUtils {

    private static final Logger logger = LoggerFactory.getLogger(UrlUtils.class);

    /**
     *  支付宝URL转码
     * @param url
     * @return
     */
    public static String alipayUrlEncode(String url) {
        try {
            url = URLEncoder.encode(url, ContractConstants.CODE);
            url = URLEncoder.encode(ContractConstants.WAP_CONTRACT_URL_PRE.concat(url), ContractConstants.CODE);
            url = ContractConstants.ALIPAY_PRE.concat(url);
            return url;
        } catch (UnsupportedEncodingException e) {
            logger.error("URL转码失败", e);
        }
        return ContractConstants.NULL_STR;
    }


    public static String replaceDomainOrIp(String originalUrl, String newHost) throws URISyntaxException {
        // 解析原始 URL
        URI uri = new URI(originalUrl);
        String host;
        int port;
        if (newHost.contains(":")) {
            String[] parts = newHost.split(":");
            host = parts[0];
            port = Integer.parseInt(parts[1]);
        } else {
            host = newHost;
            port = -1;
        }

        // 构建新的 URI，仅替换 host 部分
        URI newUri = new URI(
                uri.getScheme(),     // 协议: http/https
                uri.getUserInfo(),   // 用户信息（如果有）
                host,             // 新的主机名或IP地址
                port,       // 端口
                uri.getPath(),       // 路径
                uri.getQuery(),      // 查询参数
                uri.getFragment()    // 锚点片段
        );

        return newUri.toString();
    }

}
