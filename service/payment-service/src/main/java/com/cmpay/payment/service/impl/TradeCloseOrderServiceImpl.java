package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PaymentCloseOrderBO;
import com.cmpay.payment.constant.PaymentStatusEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.TradeCloseOrderService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class TradeCloseOrderServiceImpl implements TradeCloseOrderService {

    private static final Logger logger = LoggerFactory.getLogger(TradeCloseOrderServiceImpl.class);

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ExtPayOrderService payOrderService;


    @Override
    public void closeOrder(PaymentCloseOrderBO paymentCloseOrderBO) {
        logger.info("close order: {}", paymentCloseOrderBO);

        applicationContext.publishEvent(paymentCloseOrderBO);

        if (StringUtils.equals(paymentCloseOrderBO.getTradeStatus(), PaymentStatusEnum.TRADE_CLOSED.name())) {
            TradeOrderDO tradeOrderDO = new TradeOrderDO();
            tradeOrderDO.setMerchantNo(paymentCloseOrderBO.getMerchantId());
            tradeOrderDO.setOutTradeNo(paymentCloseOrderBO.getOutTradeNo());
            tradeOrderDO.setStatus(paymentCloseOrderBO.getTradeStatus());
            tradeOrderDO.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
            payOrderService.updatePaymentOrderWaitClose(tradeOrderDO);
        }
    }
}
