package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.IntegralPayCommitRecordBO;
import com.cmpay.payment.bo.IntegralPayFinishBO;
import com.cmpay.payment.constant.IntegralPaymentStatusEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.entity.TradeFinishDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.IntegralPayFinishService;
import com.cmpay.payment.service.ext.ExtIntegralFinishService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> wenzhan
 * @Date 2021-8-31 0031 10:52
 */
@Service
public class IntegralPayFinishServiceImpl implements IntegralPayFinishService {
    private static final Logger logger = LoggerFactory.getLogger(IntegralPayFinishServiceImpl.class);

    @Autowired
    ExtPayOrderService payOrderService;
    @Autowired
    ExtIntegralFinishService integralFinishService;
    @Autowired
    ApplicationContext applicationContext;
    @Override
    public IntegralPayFinishBO payFinish(IntegralPayFinishBO integralPayFinishBO) {
        inputCheck(integralPayFinishBO);
        //查询原支付订单
        TradeOrderDO payOrderQuery = new TradeOrderDO();
        payOrderQuery.setMerchantNo(integralPayFinishBO.getMerchantId());
        payOrderQuery.setOutTradeNo(integralPayFinishBO.getOutTradeNo());
        TradeOrderDO originalPayTradeOrderDO = payOrderService.load(payOrderQuery);
        if (originalPayTradeOrderDO == null) {
            //如果原支付订单不存在，则不能发起结算
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        if(JudgeUtils.notEquals(originalPayTradeOrderDO.getStatus(), IntegralPaymentStatusEnum.TRADE_SUCCESS.name())){
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_CANNOT_FINISH);
        }
        //登记完结表
        TradeFinishDO tradeFinishDO = integralFinishService.insertByNewTranscation(integralPayFinishBO,originalPayTradeOrderDO);
        integralPayFinishBO.setPayWay(originalPayTradeOrderDO.getPayProductCode());
        integralPayFinishBO.setBankOrderNo(originalPayTradeOrderDO.getThirdOrdNo());
        try {
            applicationContext.publishEvent(integralPayFinishBO);
            //更新完结表
            tradeFinishDO.setStatus(integralPayFinishBO.getStatus());
            integralFinishService.updateByNewTranscation(tradeFinishDO);
            integralPayFinishBO.setMsgCd(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
        } catch (Exception e){
            if(e instanceof BusinessException){
                if(JudgeUtils.equals(integralPayFinishBO.getStatus(), IntegralPaymentStatusEnum.SHIP_FAIL.name())){
                    tradeFinishDO.setErrMsgCd(integralPayFinishBO.getErrMsgCd());
                    tradeFinishDO.setErrMsgInfo(integralPayFinishBO.getErrMsgInfo());
                    integralFinishService.updateByNewTranscation(tradeFinishDO);
                    integralPayFinishBO.setMsgCd(MsgCodeEnum.POINTS_FINISH_ORDER_FAIL.getMsgCd());
                }else if(JudgeUtils.equals(MsgCodeEnum.POINTS_FINISH_TIMEOUT.getMsgCd(),((BusinessException) e).getMsgCd())){
                    tradeFinishDO.setErrMsgCd(MsgCodeEnum.POINTS_FINISH_TIMEOUT.getMsgCd());
                    tradeFinishDO.setErrMsgInfo(MsgCodeEnum.POINTS_FINISH_TIMEOUT.getMsgInfo());
                    integralFinishService.updateByNewTranscation(tradeFinishDO);
                    integralPayFinishBO.setMsgCd(MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT.getMsgCd());
                }else{
                    BusinessException.throwBusinessException(MsgCodeEnum.POINTS_FINISH_ORDER_FAIL);
                }
            }else{
                BusinessException.throwBusinessException(MsgCodeEnum.POINTS_FINISH_ORDER_FAIL);
            }
        }
        return integralPayFinishBO;
    }

    public void inputCheck(IntegralPayFinishBO integralPayFinishBO){
        if(JudgeUtils.isEmpty(integralPayFinishBO.getMerchantId())){
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NO_CANNOT_BE_EMPTY);
        }
    }

    @Override
    public IntegralPayCommitRecordBO payCommitRecord(IntegralPayCommitRecordBO integralPayCommitRecordBO){
        recordCheck(integralPayCommitRecordBO);
        TradeFinishDO tradeFinishQueryDO = new TradeFinishDO();
        tradeFinishQueryDO.setMerchantNo(integralPayCommitRecordBO.getMerchantId());
        tradeFinishQueryDO.setOrgOrderNo(integralPayCommitRecordBO.getOutTradeNo());
        tradeFinishQueryDO.setStatus(IntegralPaymentStatusEnum.SEND_FINISH.name());
        List<TradeFinishDO> tradeFinishList = integralFinishService.find(tradeFinishQueryDO);
        if(JudgeUtils.isNull(tradeFinishList)||tradeFinishList.size()!=1){
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        TradeFinishDO tradeFinishDO = tradeFinishList.get(0);
        tradeFinishDO.setTradeOrderNo(integralPayCommitRecordBO.getOutRequestNo());
        tradeFinishDO.setOutTradeNo(integralPayCommitRecordBO.getOutRequestNo());
        tradeFinishDO.setOrgOrderNo(integralPayCommitRecordBO.getOutTradeNo());
        tradeFinishDO.setRequestDate(integralPayCommitRecordBO.getRequestDate().substring(0,8));
        tradeFinishDO.setVcode(integralPayCommitRecordBO.getVcode());
        tradeFinishDO.setVcodepass(integralPayCommitRecordBO.getVcodePass());
        tradeFinishDO.setUseId(integralPayCommitRecordBO.getUseId());
        tradeFinishDO.setUseAmount(integralPayCommitRecordBO.getUseAmount());
        tradeFinishDO.setUseOrderTime(integralPayCommitRecordBO.getUseOrderTime());
        tradeFinishDO.setUseOrderContent(integralPayCommitRecordBO.getUseOrderTime());
        if(JudgeUtils.isNotNull(integralPayCommitRecordBO.getMobile())){
            tradeFinishDO.setMobile(integralPayCommitRecordBO.getMobile());
        }
        tradeFinishDO.setStatus(IntegralPaymentStatusEnum.SHIP_WAIT.name());
        //登记完结表
        integralFinishService.insertTradeFinish(tradeFinishDO);
        integralPayCommitRecordBO.setBankOrderNo(tradeFinishDO.getBankOrderNo());
        try {
            applicationContext.publishEvent(integralPayCommitRecordBO);
            //更新完结表
            tradeFinishDO.setStatus(integralPayCommitRecordBO.getStatus());
            integralFinishService.updateByNewTranscation(tradeFinishDO);
            integralPayCommitRecordBO.setMsgCd(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
        } catch (Exception e){
            if(e instanceof BusinessException){
                if(JudgeUtils.equals(integralPayCommitRecordBO.getStatus(), IntegralPaymentStatusEnum.SHIP_FAIL.name())){
                    tradeFinishDO.setStatus(IntegralPaymentStatusEnum.SHIP_FAIL.name());
                    tradeFinishDO.setErrMsgCd(integralPayCommitRecordBO.getErrMsgCd());
                    tradeFinishDO.setErrMsgInfo(integralPayCommitRecordBO.getErrMsgInfo());
                    integralFinishService.updateByNewTranscation(tradeFinishDO);
                    integralPayCommitRecordBO.setMsgCd(MsgCodeEnum.POINTS_FINISH_ORDER_FAIL.getMsgCd());
                }else if(JudgeUtils.equals(MsgCodeEnum.POINTS_FINISH_TIMEOUT.getMsgCd(),((BusinessException) e).getMsgCd())){
                    tradeFinishDO.setErrMsgCd(MsgCodeEnum.POINTS_FINISH_TIMEOUT.getMsgCd());
                    tradeFinishDO.setErrMsgInfo(MsgCodeEnum.POINTS_FINISH_TIMEOUT.getMsgInfo());
                    integralFinishService.updateByNewTranscation(tradeFinishDO);
                    integralPayCommitRecordBO.setMsgCd(MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT.getMsgCd());
                }else{
                    BusinessException.throwBusinessException(MsgCodeEnum.POINTS_FINISH_ORDER_FAIL);
                }
            }else{
                BusinessException.throwBusinessException(MsgCodeEnum.POINTS_FINISH_ORDER_FAIL);
            }
        }
        return integralPayCommitRecordBO;
    }

    public void recordCheck(IntegralPayCommitRecordBO integralPayCommitRecordBO){
        try {
            Date format = new SimpleDateFormat("yyyyMMddHHmmss").parse(integralPayCommitRecordBO.getUseOrderTime());
            String useOrderTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(format);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgCodeEnum.USE_ORDER_TIME_CANNOT_BE_EMPTY);
        }
    }
}
