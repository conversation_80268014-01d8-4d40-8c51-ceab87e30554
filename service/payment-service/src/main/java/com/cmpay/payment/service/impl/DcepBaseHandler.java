package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.ProtocolPaymentBO;
import com.cmpay.payment.bo.config.MerchantAttachInFoBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.entity.OrderPaymentAttachDO;
import com.cmpay.payment.entity.config.MerchantAttachInFoDO;
import com.cmpay.payment.service.ext.ExtPayOrderAttachService;
import com.cmpay.payment.service.ext.config.IExtMerchantAttachInfoService;
import io.netty.channel.ConnectTimeoutException;
import io.netty.handler.timeout.ReadTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/7/7
 */
public abstract class DcepBaseHandler {
    @Autowired
    private IExtMerchantAttachInfoService merchantAttachInfoService;
    @Autowired
    private ExtPayOrderAttachService payOrderAttachService;

    protected void exceptionHandler(Exception e, String message) {
        if (e instanceof BusinessException) {
            if (StringUtils.equals(((BusinessException) e).getMsgCd(), MsgCodeEnum.DCEP_REUQEST_FAIL.getMsgCd())) {
                BusinessException.throwBusinessException(MsgCodeEnum.DCEP_REUQEST_FAIL, new String[]{message + ":" + e.getMessage()});
            }
            BusinessException.throwBusinessException((BusinessException) e);
        } else if (e instanceof ConnectTimeoutException || e instanceof ReadTimeoutException) {
            BusinessException.throwBusinessException(MsgCodeEnum.REQUEST_TIME_OUT);
        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.DCEP_PAY_QUERY_FAIL);
        }
    }


    protected MerchantAttachInFoDO checkMerchantAttach(ProtocolPaymentBO protocolPaymentBO) {
        MerchantAttachInFoBO merchantAttach = new MerchantAttachInFoBO();
        // 去除数币子商户与聚合渠道号的限制，只要数币子商户存在，即校验通过，可发往数币
//        merchantAttach.setChannelId(protocolPaymentBO.getMerchantId());
        merchantAttach.setMerchantId(protocolPaymentBO.getSubMerchantId());
        MerchantAttachInFoDO merchantAttachInFoDO = merchantAttachInfoService.queryMerchant(merchantAttach);
        if (merchantAttachInFoDO == null) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NOT_EXISTS);
        }
        return merchantAttachInFoDO;
    }

    protected OrderPaymentAttachDO checkOrderAttach(ProtocolPaymentBO protocolPaymentBO) {
        OrderPaymentAttachDO orderPaymentAttachDO = new OrderPaymentAttachDO();
        orderPaymentAttachDO.setOutTradeNo(protocolPaymentBO.getOutTradeNo());
        orderPaymentAttachDO.setOrderDate(protocolPaymentBO.getTradeDate());
        OrderPaymentAttachDO orderPaymentAttach = payOrderAttachService.load(orderPaymentAttachDO);
        if (JudgeUtils.isNull(orderPaymentAttach)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        return orderPaymentAttach;
    }
}
