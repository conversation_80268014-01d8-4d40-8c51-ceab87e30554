package com.cmpay.payment.service.channel.cmbaion.impl;

import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.channel.CmbaionPayNrtChannelEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.cmbaion.CmbaionConstants;
import com.cmpay.payment.dto.cmbaion.AbstractCmbaionDTO;
import com.cmpay.payment.dto.cmbaion.CmbaionRefundQueryReqDTO;
import com.cmpay.payment.dto.cmbaion.CmbaionRefundQueryRspDTO;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.TradeCommonService;
import com.cmpay.payment.service.channel.cmbaion.CmbaionRefundQueryChannelService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.ExtRefundOrderService;
import com.cmpay.payment.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author： PengAnHai
 * @date： 2024-08-21
 * @description：招行一网通退款订单查询处理类
 * @modifiedBy：
 * @version: 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmbaionRefundQueryChannelServiceImpl extends CmbaionBaseRequestServiceImpl implements CmbaionRefundQueryChannelService {

    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private ExtRefundOrderService refundOrderService;
    @Autowired
    private TradeCommonService tradeCommonService;

    @Override
    public void send(RefundOrderQueryBO refundQueryBO) {
        cmbaionRefundQuery(refundQueryBO);
    }

    @Override
    public void cmbaionRefundQuery(RefundOrderQueryBO refundQueryBO) {
        // 创建查询请求DTO
        CmbaionRefundQueryReqDTO refundQueryReqDTO = createRefundQueryReqDTO(refundQueryBO);
        // 构建并发送查询请求
        buildCmbaionRequest(refundQueryReqDTO,refundQueryBO, CmbaionPayNrtChannelEnum.CMBAION_REFUND_QUERY.getName());
    }

    /**
     * 处理来自Cmbaion的退款订单查询响应。
     *
     * @param result 响应DTO，包含响应数据。
     * @param baseHandlerBO 基础处理业务对象，用于进一步处理。
     * @return 处理后的业务对象。
     */
    @Override
    public BaseHandlerBO handlerCmbaionResponse(AbstractCmbaionDTO result, BaseHandlerBO baseHandlerBO) {
        // 强制类型转换为退款响应DTO
        CmbaionRefundQueryRspDTO queryRspDTO = (CmbaionRefundQueryRspDTO) result;
        // 强制类型转换为退款业务对象
        RefundOrderQueryBO refundQueryBO = (RefundOrderQueryBO) baseHandlerBO;
        // 获取响应数据
        CmbaionRefundQueryRspDTO.RsqData rsqData = queryRspDTO.getRsqData();
        if (JudgeUtils.equals(rsqData.getRspCode(), CmbaionConstants.SUCCESS)) {
            // 响应码为成功时，进一步判断订单状态
            if (JudgeUtils.equals(rsqData.getOrderStatus(), CmbaionConstants.REFUND_SUCCESS)) {
                // 如果退款成功，调用退款成功的处理方法
                refunSuccessHandle(refundQueryBO, rsqData);
            } else if (JudgeUtils.equals(rsqData.getOrderStatus(), CmbaionConstants.REFUND_FAIL)) {
                // 如果退款失败，调用退款失败的处理方法
                refundFailHandle(refundQueryBO);
            } else {
                // 如果订单状态不是成功也不是失败，设置退款状态为“等待退款”
                refundQueryBO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
            }
        } else if (JudgeUtils.equals(rsqData.getRspCode(), CmbaionConstants.NO_ORDER)) {
            // 响应码不为成功且订单状态为订单不存在时，调用订单不存在的处理方法
            refundNoOrderHandle(refundQueryBO);
        } else {
            // 其他情况，设置退款状态为“等待退款”
            refundQueryBO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
        }
        return refundQueryBO;
    }

    /**
     * 处理查询结果中订单不存的情况
     *
     * @param refundQueryBO 退款查询业务对象
     * @return 如果处理成功返回 true，否则返回 false
     */
    private boolean refundNoOrderHandle(RefundOrderQueryBO refundQueryBO) {
        // 支付订单号成功，退款订单号错误时，响应成功，但无订单信息
        tradeCommonService.handleRefundOrderNotExist(refundQueryBO, MsgCodeEnum.REFUND_ORDER_NOT_EXISTS);
        // 判断退款查询业务对象的状态是否为退款失败
        if (JudgeUtils.equals(refundQueryBO.getStatus(), OrderStatusEnum.REFUND_FAIL.name())) {
            // 如果状态为退款失败，调用 refundFailHandle 方法进行进一步处理
            return refundFailHandle(refundQueryBO);
        }
        // 如果没有出现退款失败的情况，返回 true 表示处理成功
        return true;
    }

    /**
     * 处理退款失败的情况
     *
     * @param refundQueryBO 退款查询业务对象
     * @return 如果订单状态更新成功返回 true，否则返回 false
     */
    private boolean refundFailHandle(RefundOrderQueryBO refundQueryBO) {
        // 设置退款查询业务对象的状态为退款失败
        refundQueryBO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
        // 设置支付订单状态为退款失败
        setPayOrderStatusRefFail(refundQueryBO);
        // 获取退款订单对象
        RefundOrderDO refundOrderDO = refundQueryBO.getRefundOrder();
        // 获取原支付订单对象
        TradeOrderDO tradeOrderDO = refundQueryBO.getOriginalPayTradeOrder();
        // 修改退款订单状态，将其设置为退款失败
        refundOrderDO.setStatus(refundQueryBO.getStatus());
        // 更新订单信息并返回更新结果
        return updateOrder(refundOrderDO, tradeOrderDO);
    }

    /**
     * 处理退款成功的情况
     *
     * @param refundQueryBO 退款查询业务对象
     * @param rsqData       退款查询响应数据对象
     * @return 总是返回 true 表示处理成功
     */
    private boolean refunSuccessHandle(RefundOrderQueryBO refundQueryBO, CmbaionRefundQueryRspDTO.RsqData rsqData) {
        // 设置退款查询业务对象的状态为退款成功
        refundQueryBO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
        // 设置支付订单状态为退款成功
        setPayOrderStatus(refundQueryBO);
        // 设置订单费用金额
        setOrderFeeAmount(refundQueryBO);
        // 获取退款订单对象
        RefundOrderDO refundOrderDO = refundQueryBO.getRefundOrder();
        // 获取原支付订单对象
        TradeOrderDO tradeOrderDO = refundQueryBO.getOriginalPayTradeOrder();
        // 修改退款订单状态，将其设置为退款成功
        refundOrderDO.setStatus(refundQueryBO.getStatus());
        // 设置退款订单完成时间为当前时间
        refundOrderDO.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
        // 设置银行流水号为响应数据中的银行流水号
        refundOrderDO.setRefundId(rsqData.getBankSerialNo());
        // 设置会计日期为响应数据中的银行日期
        refundOrderDO.setAccountDate(DateUtils.verifiAndSetAccountDate(rsqData.getBankDate()));
        // 更新订单信息
        updateOrder(refundOrderDO, tradeOrderDO);
        // 通知商户退款成功
        notifyMerchant(refundOrderDO);
        // 总是返回 true 表示处理成功
        return true;
    }

    /**
     * 从退款订单查询业务对象创建查询请求DTO。
     * @param refundQueryBO
     * @return
     */
    private CmbaionRefundQueryReqDTO createRefundQueryReqDTO(RefundOrderQueryBO refundQueryBO) {
        // 获取原始支付交易订单
        TradeOrderDO tradeOrderDO = refundQueryBO.getOriginalPayTradeOrder();
        CmbaionRefundQueryReqDTO refundQueryReqDTO = new CmbaionRefundQueryReqDTO();
        CmbaionRefundQueryReqDTO.ReqData reqData = new CmbaionRefundQueryReqDTO.ReqData();
        // 设置当前日期时间字符串
        reqData.setDateTime(DateTimeUtils.getCurrentDateTimeStr());
        // 设置分行号
        reqData.setBranchNo(getBranchNo(tradeOrderDO.getBankMerchantNo()));
        // 设置商户号
        reqData.setMerchantNo(getMerchantNo(tradeOrderDO.getBankMerchantNo()));
        // 设置类型
        reqData.setType(CmbaionConstants.PAY_QUERY_TYPE);
        // 设置订单日期
        reqData.setDate(tradeOrderDO.getOrderDate());
        // 设置退款订单号
        reqData.setOrderNo(tradeOrderDO.getTradeOrderNo());
        // 支付订单号
        reqData.setMerchantSerialNo(refundQueryBO.getRefundOrder().getTradeOrderNo());
        refundQueryReqDTO.setReqData(reqData);
        setRequestDataDTO(refundQueryReqDTO);
        return refundQueryReqDTO;
    }

    /**
     * 更新退款订单和支付订单的状态
     *
     * @param refundOrderDO 退款订单对象
     * @param tradeOrderDO  原支付订单对象
     * @return 如果两个订单都成功更新则返回 true，否则返回 false
     */
    private boolean updateOrder(RefundOrderDO refundOrderDO, TradeOrderDO tradeOrderDO) {
        // 更新退款订单的状态，如果更新失败则返回 false
        if (refundOrderService.updateRefundStatus(refundOrderDO) != 1) {
            return false;
        }
        // 记录退款订单更新成功的日志信息
        log.info("Refund Order Update Success, tradeOrderNo is {}, orgTradeNO is {}",
                refundOrderDO.getTradeOrderNo(), refundOrderDO.getOrgOrderNo());
        // 更新原支付订单的状态，如果更新失败则返回 false
        if (payOrderService.update(tradeOrderDO) != 1) {
            return false;
        }
        // 记录支付订单更新成功的日志信息
        log.info("Payment Order Update Success, tradeOrderNo is {}, outTradeNo is {}",
                tradeOrderDO.getTradeOrderNo(), tradeOrderDO.getOutTradeNo());
        // 如果两个订单都成功更新，则返回 true
        return true;
    }

}
