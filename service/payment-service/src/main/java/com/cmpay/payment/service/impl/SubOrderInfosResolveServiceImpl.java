package com.cmpay.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.config.SubOrderInfoBO;
import com.cmpay.payment.bo.config.SubOrderInfoExtBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.service.SubOrderInfosResolveService;
import com.cmpay.payment.utils.PaymentUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @date 2023-11-24 10:40
 * <AUTHOR>
 * @Version 1.0
 */
@Service
public class SubOrderInfosResolveServiceImpl implements SubOrderInfosResolveService {

    public static final int maxSize = 15;

    @Override
    public List<SubOrderInfoBO> resolve(String subOrderInfos) {
        if (StringUtils.isEmpty(subOrderInfos)) {
            BusinessException.throwBusinessException(MsgCodeEnum.PARAM_ISNULL);
        }
        List<SubOrderInfoBO> subOrderInfoBOList = null;
        try {
            subOrderInfoBOList = JSON.parseArray(subOrderInfos, SubOrderInfoBO.class);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgCodeEnum.SUB_ORDER_INFO_RESOLVE_FAIL);
        }
        for (SubOrderInfoBO subOrder : subOrderInfoBOList) {
            if (StringUtils.isAnyBlank(subOrder.getSubOrderNo(), subOrder.getSettlementDept(), subOrder.getSettlementItem(), subOrder.getSubOrderAmount())) {
                BusinessException.throwBusinessException(MsgCodeEnum.PARAM_ISNULL);
            }
        }
        // 判断是否超过最大笔数
        if (subOrderInfoBOList.size() > maxSize) {
            BusinessException.throwBusinessException(MsgCodeEnum.SUB_ORDER_INFO_RESOLVE_FAIL);
        }
        // 1、判断是否重复；
        // 提取对象list的订单号，收集到Set里，Set会自动去重，去重后判断和原list大小
        Set<String> subOrderNoSet = subOrderInfoBOList.stream().map(SubOrderInfoBO::getSubOrderNo)
                .collect(Collectors.toSet());

        if (subOrderNoSet.size() != subOrderInfoBOList.size()) {
            BusinessException.throwBusinessException(MsgCodeEnum.SUB_ORDER_NO_REPEAT);
        }

        // 2、判断金额是否可以转BigDecimal，且只有两位小数
        boolean allTwoDecimal = subOrderInfoBOList.stream()
                .map(SubOrderInfoBO::getSubOrderAmount) // 提取金额
                .allMatch(PaymentUtils::isTwoDecimalPlaces);
        if (!allTwoDecimal) {
            BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
        }
        return subOrderInfoBOList;
    }

    @Override
    public List<SubOrderInfoExtBO> resolveExt(String subOrderInfos) {
        if (StringUtils.isEmpty(subOrderInfos)) {
            BusinessException.throwBusinessException(MsgCodeEnum.PARAM_ISNULL);
        }
        try {
            return JSON.parseArray(subOrderInfos, SubOrderInfoExtBO.class);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgCodeEnum.SUB_ORDER_INFO_RESOLVE_FAIL);
        }
        return null;
    }

    @Override
    public BigDecimal sumSubOrderAmount(String subOrderInfos) {

        List<SubOrderInfoBO> subOrderInfoBOList = resolve(subOrderInfos);

        BigDecimal totalAmount = subOrderInfoBOList.stream()
                .map(SubOrderInfoBO::getSubOrderAmount)
                .map(BigDecimal::new)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalAmount;
    }
}
