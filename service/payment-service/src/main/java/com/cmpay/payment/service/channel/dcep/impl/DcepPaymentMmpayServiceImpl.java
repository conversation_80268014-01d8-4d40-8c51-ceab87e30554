package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.ProtocolPaymentBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.constant.protocol.DcepOrderStatusEnum;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.DcepTokenPayReq;
import com.cmpay.payment.dto.dcep.DcepTokenPayRsp;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.DcepPaymentMmpayService;
import com.cmpay.payment.util.LemonAmount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/7/8
 */
@Service
public class DcepPaymentMmpayServiceImpl extends AbstractDcepRequestServiceImpl implements DcepPaymentMmpayService {

    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;
    private static final String TRADE_TYPE = "payment";

    @Override
    public void protocolApply(ProtocolPaymentBO protocolPaymentBO) {
        DcepTokenPayReq dcepTokenPayReq = new DcepTokenPayReq();
        buildRequestDto(dcepTokenPayReq, protocolPaymentBO, protocolPaymentBO.getChannelNo());
        dcepTokenPayReq.setOrgNo(dcepPaymentProperties.getNewOrgNo());
        dcepTokenPayReq.setSubMerchantNo(protocolPaymentBO.getSubMerchantId());
        dcepTokenPayReq.setAgreementSeq(protocolPaymentBO.getAgreementId());
        dcepTokenPayReq.setOutTranNo(protocolPaymentBO.getOutTradeNo());
        dcepTokenPayReq.setGoodsInfo(protocolPaymentBO.getProductDesc());
        dcepTokenPayReq.setBusiType(protocolPaymentBO.getBusinessType());
        dcepTokenPayReq.setBusiCode(protocolPaymentBO.getBusinessCode());
        dcepTokenPayReq.setExpireMinutes(Integer.parseInt(protocolPaymentBO.getExpireMinutes()));
        dcepTokenPayReq.setOutTranDate(protocolPaymentBO.getOrderDate());
        dcepTokenPayReq.setOutTranTime(protocolPaymentBO.getOrderTime());
        dcepTokenPayReq.setAmount(Long.parseLong(new LemonAmount(protocolPaymentBO.getRealAmount()).yuan2fen()));
        dcepTokenPayReq.setCurrtype(DcepConstants.CURRENT_TYPE);
        dcepTokenPayReq.setNotifyUrl(dcepPaymentProperties.getNotifyUrl());
        buildDcepRequest(dcepTokenPayReq, protocolPaymentBO, DcepPaymentChannelEnum.USER_PROTOCOL_PAYMENT.getName());
    }

    @Override
    public void send(ProtocolPaymentBO protocolPaymentBO) {
        protocolApply(protocolPaymentBO);
    }

    @Override
    protected BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        DcepTokenPayRsp tokenPayRsp = (DcepTokenPayRsp) abstractDcepRsp;
        ProtocolPaymentBO protocolPaymentBO = (ProtocolPaymentBO) baseDcepHandlerBO;
        if (StringUtils.equals(tokenPayRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            DcepTokenPayRsp.Data data = tokenPayRsp.getData();
            DcepOrderStatusEnum orderStatusEnum = DcepOrderStatusEnum.getEnumName(TRADE_TYPE, data.getPayStatus());
            protocolPaymentBO.setTradeStatus(orderStatusEnum != null ? orderStatusEnum.name() : OrderStatusEnum.WAIT_PAY.name());
            String finishDateTime = data.getPaySuccessDate() + data.getPaySuccessTime();
            protocolPaymentBO.setFinishDateTime((StringUtils.isBlank(finishDateTime) || finishDateTime.length() != 14 ? DateTimeUtils.getCurrentDateTimeStr() : finishDateTime));
            protocolPaymentBO.setAccountDate(data.getPaySuccessDate());
            protocolPaymentBO.setPaymentOrderNo(data.getPaySeq());
            Optional.ofNullable(data.getErrCode()).ifPresent(errCode -> protocolPaymentBO.setErrCode(data.getErrCode()));
            Optional.ofNullable(data.getErrMsg()).ifPresent(errMsg -> protocolPaymentBO.setErrCodeDes(data.getErrMsg()));
        } else {
            protocolPaymentBO.setErrCode(tokenPayRsp.getCode());
            protocolPaymentBO.setErrCodeDes(tokenPayRsp.getMsg());
            protocolPaymentBO.setTradeStatus(OrderStatusEnum.TRADE_FAIL.name());
        }
        return protocolPaymentBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }
}
