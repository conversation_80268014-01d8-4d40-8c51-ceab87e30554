package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.dcep.DcepRefundQueryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/5/7
 */
@Service
public class DcepRefundQueryListenerServiceImpl extends PaymentListenerService<RefundOrderQueryBO> {


    @Autowired
    private ApplicationContext applicationContext;

    @EventListener
    public void handlerDcepRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {
        super.execute(refundOrderQueryBO);
    }

    @Override
    protected boolean checkChannelExecutable(RefundOrderQueryBO refundOrderQueryBO) {
        return Optional.ofNullable(refundOrderQueryBO)
                .map(RefundOrderQueryBO::getRefundOrder)
                .filter(refundOrderDO -> StringUtils.equals(PaymentWayEnum.DCEPPAY.name().toLowerCase(), refundOrderDO.getAimProductCode()))
                .isPresent();
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(RefundOrderQueryBO refundOrderQueryBO) {
        return Optional.ofNullable(refundOrderQueryBO)
                .map(RefundOrderQueryBO::getRefundOrder)
                .map(RefundOrderDO::getPayWayCode)
                .map(String::toUpperCase)
                .map(PaymentSceneEnum::valueOf)
                .map(paymentScene -> {
                    switch (paymentScene) {
                        case TOKEN:
                        case MMPAY:
                        case MICROPAY:
                        case JSAPIPAY:
                        case SCANPAY:
                        case APP:
                            return getBean(DcepRefundQueryService.class);
                        default:
                            return null;
                    }
                }).orElse(null);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
