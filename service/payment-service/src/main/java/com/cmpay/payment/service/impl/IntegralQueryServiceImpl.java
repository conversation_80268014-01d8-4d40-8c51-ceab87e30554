package com.cmpay.payment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.IntegralBalanceQueryBO;
import com.cmpay.payment.bo.IntegralGoodsQueryBO;
import com.cmpay.payment.channel.IntegralPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.IntegralpayConstants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.dto.integralpay.IntegralBalanceQueryReq;
import com.cmpay.payment.dto.integralpay.IntegralBalanceQueryRsp;
import com.cmpay.payment.dto.integralpay.IntegralListQueryReq;
import com.cmpay.payment.dto.integralpay.IntegralListQueryRsp;
import com.cmpay.payment.service.IntegralQueryService;
import com.cmpay.payment.utils.JhIdGenUtils;
import com.cmpay.payment.util.PaymentUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import feign.RetryableException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;


/**
 * Created on 2024/01/24
 * @author: wu_cm
 */
@Service
public class IntegralQueryServiceImpl implements IntegralQueryService {
    private static final Logger logger = LoggerFactory.getLogger(IntegralQueryServiceImpl.class);
    static Gson GSON = new GsonBuilder().create();
    @Autowired
    private IntegrationPaymentCgwOutClient cgwOutClient;
    @Value("${integralpay.channelCode:}")
    private String channelCode;
    /**
     * 用户积分余额查询
     * @param balanceQueryBO
     * @return
     */
    @Override
    public IntegralBalanceQueryBO integralBalanceQuery(IntegralBalanceQueryBO balanceQueryBO) {
        balanceQueryBO.setUserScore(BigDecimal.ZERO);
        //查询积分余额 B0002和积分余额查询接口
        IntegralBalanceQueryReq integralBalanceQueryReq = new IntegralBalanceQueryReq();
        integralBalanceQueryReq.setMobile(balanceQueryBO.getMobile());
        integralBalanceQueryReq.setTraceId(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.INTEGRAL_QUERY, 10));
        integralBalanceQueryReq.setSpanId(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.INTEGRAL_QUERY, 10));
        integralBalanceQueryReq.setServiceCode(IntegralpayConstants.B0002);
        integralBalanceQueryReq.setChannelCode(channelCode);
        integralBalanceQueryReq.setReqTime(String.valueOf(System.currentTimeMillis()));
        integralBalanceQueryReq.setAccessChannel(IntegralpayConstants.API_APP);
        try {
            GenericRspDTO<Response> response = this.cgwOutClient.request(PaymentUtils.buildRequest(IntegralPayChannelEnum.INTEGRAL_PAY, IntegralPayChannelEnum.INTEGRAL_BALANCE_QUERY.getName(), IntegralPayChannelEnum.INTEGRAL_PAY, integralBalanceQueryReq));
            if (JudgeUtils.isNotNull(response) && JudgeUtils.isSuccess(response.getMsgCd())) {
                IntegralBalanceQueryRsp balanceQueryRsp = (IntegralBalanceQueryRsp) response.getBody().getResult();
                if (JudgeUtils.isNotNull(balanceQueryRsp)) {
                    balanceQueryBO.setUserScore(JudgeUtils.isNotNull(balanceQueryRsp.getUserScore()) ? balanceQueryRsp.getUserScore() : BigDecimal.ZERO);
                    balanceQueryBO.setRespCode(balanceQueryRsp.getRespCode());
                    balanceQueryBO.setRespDesc(balanceQueryRsp.getRespDesc());
                }
            }
        }catch (RetryableException e){
            BusinessException.throwBusinessException(MsgCodeEnum.REQUEST_RETURN_TIME_OUT);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgCodeEnum.POINTS_EXCHANGE_FAIL);
        }
        return balanceQueryBO;
    }

    /**
     * 商品列表查询
     * @param goodsQueryBO
     * @return
     */
    @Override
    public IntegralGoodsQueryBO integralGoogsQuery(IntegralGoodsQueryBO goodsQueryBO) {
        //查询积分商品列表 JF1002 商品列表查询接口
        IntegralListQueryReq integralListQuery = new IntegralListQueryReq();
        integralListQuery.setAccessPartnerId(channelCode);
        if (StringUtils.isBlank(goodsQueryBO.getUserIntegral())) {
            integralListQuery.setUserIntegral(IntegralpayConstants.DEFAULT_INTEGRAL);
        } else {
            integralListQuery.setUserIntegral(goodsQueryBO.getUserIntegral());
        }
        if (StringUtils.isBlank(goodsQueryBO.getPageSize())) {
            integralListQuery.setPageSize(IntegralpayConstants.DEFAULT_SIZE);
        } else {
            integralListQuery.setPageSize(goodsQueryBO.getPageSize());
        }
        integralListQuery.setPageNum(goodsQueryBO.getPageNum());
        integralListQuery.setTraceId(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.INTEGRAL_QUERY, 10));
        integralListQuery.setSpanId(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.INTEGRAL_QUERY, 10));
        integralListQuery.setServiceCode(IntegralpayConstants.JF1002);
        integralListQuery.setChannelCode(channelCode);
        integralListQuery.setReqTime(String.valueOf(System.currentTimeMillis()));
        integralListQuery.setAccessChannel(IntegralpayConstants.API_APP);
        try {
            GenericRspDTO<Response> responses = this.cgwOutClient.request(PaymentUtils.buildRequest(IntegralPayChannelEnum.INTEGRAL_PAY, IntegralPayChannelEnum.INTEGRAL_LIST_QUERY.getName(), IntegralPayChannelEnum.INTEGRAL_PAY, integralListQuery));
            if (JudgeUtils.isNotNull(responses) && JudgeUtils.isSuccess(responses.getMsgCd())) {
                IntegralListQueryRsp integralListRsp = (IntegralListQueryRsp) responses.getBody().getResult();
                if (JudgeUtils.isNotNull(integralListRsp)) {
                    goodsQueryBO = GSON.fromJson(JSONObject.toJSONString(integralListRsp), IntegralGoodsQueryBO.class);
                    return goodsQueryBO;
                }
            } else {
                // 请求积分侧失败，未查询到积分商品
                BusinessException.throwBusinessException(MsgCodeEnum.GOODS_QUERY_FAIL);
            }
        } catch (RetryableException e){
            BusinessException.throwBusinessException(MsgCodeEnum.REQUEST_RETURN_TIME_OUT);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgCodeEnum.POINTS_EXCHANGE_FAIL);
        }
        BusinessException.throwBusinessException(MsgCodeEnum.GOODS_QUERY_FAIL);
        return goodsQueryBO;
    }
}
