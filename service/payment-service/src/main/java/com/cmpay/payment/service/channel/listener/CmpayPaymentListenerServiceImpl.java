package com.cmpay.payment.service.channel.listener;

import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.cmpay.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
/**
 * Created on 2018/12/03
 * cmpay支付渠道 监听类
 *
 * @author: li_zhen
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayPaymentListenerServiceImpl extends PaymentListenerService<PayOrderBO> {
    @Autowired
    ApplicationContext applicationContext;


	@EventListener
	@Override
	public void execute(PayOrderBO eventBO) {
		super.execute(eventBO);
	}
	@Override
	protected boolean checkChannelExecutable(PayOrderBO payOrderBO) {
		return payOrderBO != null && payOrderBO.getPaymentRout() != null &&  StringUtils.equalsIgnoreCase(PaymentWayEnum.CMPAY.name(),
				payOrderBO.getPaymentRout());
	}

	@Override
	protected PaymentChannelService determinateChannelExecuteBean(PayOrderBO payOrderBO) {

		if (payOrderBO == null || payOrderBO.getPaymentRout() == null) {
			return null;
		}
		switch (PaymentSceneEnum.valueOf(payOrderBO.getScene().toUpperCase())){
			case SCAN:
				return getBean(CmpayScanPaymentChannelService.class);
			case BARCODE:
				return getBean(CmpayBarcodePaymentChannelService.class);
			case CMPAYACCOUNTPAY:
				return getBean(CmpayAccountPaymentChannelService.class);
			default:
				return getBean(CmpayOnlinePaymentChannelService.class);
		}
	}

	@Override
	protected ApplicationContext getApplicationContext() {
		return applicationContext;
	}
}
