package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.IntegralPayCommitRecordBO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.integralpay.IntegralPayCommitRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/12 15:27
 * @description ：积分提交消费记录监听类
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class IntegralPayCommitRecordListener extends PaymentListenerService<IntegralPayCommitRecordBO> {
    @Autowired
    ApplicationContext applicationContext;

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    @EventListener
    @Override
    public void execute(IntegralPayCommitRecordBO integralPayCommitRecordBO) {
        super.execute(integralPayCommitRecordBO);
    }
    @Override
    protected boolean checkChannelExecutable(IntegralPayCommitRecordBO integralPayCommitRecordBO) {
        return true;
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(IntegralPayCommitRecordBO integralPayCommitRecordBO) {
        return getBean(IntegralPayCommitRecordService.class);
    }
}
