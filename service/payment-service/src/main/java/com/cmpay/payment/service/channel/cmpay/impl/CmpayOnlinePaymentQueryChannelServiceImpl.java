package com.cmpay.payment.service.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dto.cmpay.CmpayOnlinePaymentQueryReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayOnlinePaymentQueryRspDTO;
import com.cmpay.payment.service.channel.cmpay.CmpayOnlinePaymentQueryChannelService;
import com.cmpay.payment.util.DateUtils;
import com.cmpay.payment.util.PaymentUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created on 2018/12/3
 *
 * @author: li_zhen
 */
@Service
@Slf4j
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayOnlinePaymentQueryChannelServiceImpl implements CmpayOnlinePaymentQueryChannelService {

    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;

    @Override
    public void send(PaymentQueryBO paymentQueryBO) {
        cmpayOnlinePaymentQuery(paymentQueryBO);
    }

    @Override
    public void cmpayOnlinePaymentQuery(PaymentQueryBO paymentQueryBO) {
        CmpayOnlinePaymentQueryReqDTO onlinePaymentQueryReqDTO = new CmpayOnlinePaymentQueryReqDTO();
        onlinePaymentQueryReqDTO.setMerchantOrderNo(paymentQueryBO.getPaymentOrder().getTradeOrderNo());
        onlinePaymentQueryReqDTO.setMerchantOrderDate(paymentQueryBO.getPaymentOrder().getOrderDate());
        onlinePaymentQueryReqDTO.setMerchantId(paymentQueryBO.getPaymentOrder().getBankMerchantNo());
        onlinePaymentQueryReqDTO.setInterfaceVersion(CommonConstant.CMPAY_ONLINE_VERSION);
        onlinePaymentQueryReqDTO.setSignType(paymentQueryBO.getSignMethod());
        onlinePaymentQueryReqDTO.setHmac(paymentQueryBO.getContractSecureValue());
        onlinePaymentQueryReqDTO.setVersion(CommonConstant.CMPAY_ONLINE_QEURY_VERSION);
        onlinePaymentQueryReqDTO.setType(CommonConstant.CMPAY_ONLINE_QUERY_TYPE);
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(CmpayPayChannelEnum.CMPAY_ONLINE);
        request.setBusiType(CmpayPayChannelEnum.CMPAY_ONLINE_PAYMENT_QUERY.getName());
        request.setSource(CmpayPayChannelEnum.CMPAY_ONLINE);
        request.setTarget(onlinePaymentQueryReqDTO);
        GenericRspDTO<Response> genericRspDTO;
        if (StringUtils.equalsAny(paymentQueryBO.getSourceApp(), AppEnum.integrationbatch.name(), AppEnum.integrationshecdule.name())) {
            genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        } else {
            genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        }

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .map(result -> handleOnlinePaymentQuery(paymentQueryBO, (CmpayOnlinePaymentQueryRspDTO) result))
                .orElseThrow(() -> new BusinessException(MsgCodeEnum.PAYMENT_STATUS_UNDEFINED));
    }

    private PaymentQueryBO handleOnlinePaymentQuery(PaymentQueryBO paymentQueryBO, CmpayOnlinePaymentQueryRspDTO paymentQueryRspDTO) {
        paymentQueryBO.setBnkTraceNo(paymentQueryRspDTO.getUnionTraceNo());
        String status = null;
        if (JudgeUtils.isNotSuccess(paymentQueryRspDTO.getMsgCd())) {
            String payWayCode = paymentQueryBO.getPaymentOrder().getPayWayCode();
            String requestDateTime = paymentQueryBO.getPaymentOrder().getOrderDate() + paymentQueryBO.getPaymentOrder().getOrderTime();
            if (JudgeUtils.equalsIgnoreCase(payWayCode, PaymentSceneEnum.CONTRACTPAY.name())
                    && JudgeUtils.equals(paymentQueryRspDTO.getMsgCd(), MsgCodeEnum.CMPAY_ONLINE_CONTRACT_ORDER_NOT_EXISTS.getMsgCd())
                    && !DateUtils.compareTimeLessThanFive(requestDateTime)) {
                //五分钟之前不存在得订单直接失败
                status = OrderStatusEnum.TRADE_FAIL.name();
            } else {
                BusinessException.throwBusinessException(paymentQueryRspDTO.getMsgCd());
            }
        } else {
            status = getOrderStatus(CloudOrderStatusEnum.valueOf(paymentQueryRspDTO.getOrderStatus()));
            // 和包网银，返回“OVERDUE”，不处理状态
            if (JudgeUtils.isNull(status) ||
                    (StringUtils.equalsIgnoreCase(paymentQueryBO.getPaymentOrder().getPayWayCode(), PaymentSceneEnum.NETBANK.name())
                            && StringUtils.equalsIgnoreCase(paymentQueryRspDTO.getOrderStatus(), CloudOrderStatusEnum.OVERDUE.name()))) {
                BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_STATUS_UNDEFINED);
            }
        }
        if (OrderStatusEnum.TRADE_FAIL.name().equals(status)) {
            paymentQueryBO.setErrMsgCd(paymentQueryRspDTO.getMsgCd());
            paymentQueryBO.setErrMsgInfo(paymentQueryRspDTO.getMsgInfo());
        }
        paymentQueryBO.setStatus(status);
        paymentQueryBO.setCrdType(paymentQueryRspDTO.getUnionCardType());
        paymentQueryBO.getPaymentOrder().setAccountDate(paymentQueryRspDTO.getOrderAccountDate());
        paymentQueryBO.getPaymentOrder().setBnkTraceNo(paymentQueryRspDTO.getUnionTraceNo());
        paymentQueryBO.setUnionpayDiscountInfo(paymentQueryRspDTO.getUnionSaleInfo());
        paymentQueryBO.setInstPaidAmount(paymentQueryRspDTO.getInstPaidAmount());
        paymentQueryBO.setInstDiscountUnsettledAmount(paymentQueryRspDTO.getInstDiscountUnsettledAmount());
        paymentQueryBO.setInstDiscountSettlementAmount(paymentQueryRspDTO.getInstDiscountSettlementAmount());
        paymentQueryBO.setPayAmountList(PaymentUtils.convertToAmountInfoList(paymentQueryRspDTO));
        if (JudgeUtils.isNotEmpty(paymentQueryRspDTO.getBankOrderNo())) {
            paymentQueryBO.getPaymentOrder().setThirdOrdNo(paymentQueryRspDTO.getBankOrderNo());
            paymentQueryBO.getPaymentOrder().setThirdOrdDt(paymentQueryRspDTO.getOrderAccountDate());
        } else {
            paymentQueryBO.getPaymentOrder().setThirdOrdNo(paymentQueryRspDTO.getBankOrderNo());
            paymentQueryBO.getPaymentOrder().setThirdOrdDt(paymentQueryRspDTO.getOrderAccountDate());
        }
        return paymentQueryBO;
    }

    private String getOrderStatus(CloudOrderStatusEnum cloudOrderStatusEnum) {
        switch (cloudOrderStatusEnum) {
            case SUCCESS:
                return OrderStatusEnum.TRADE_SUCCESS.name();
            case OVERDUE:
                return OrderStatusEnum.TRADE_CLOSED.name();
            case CANCLE:
            case CLOSED:
            case FAILED:
                return OrderStatusEnum.TRADE_FAIL.name();
            default:
                return null;
        }
    }
}
