package com.cmpay.payment.service.notify.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.PayNotifyBO;
import com.cmpay.payment.bo.PayOrderQueryBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.config.SecretBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.PayOrderQueryService;
import com.cmpay.payment.service.RiskControlService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.config.IExtSecretService;
import com.cmpay.payment.service.impl.PaymentNotifyBaseServiceImpl;
import com.cmpay.payment.service.notify.CmbaionNotifyService;
import com.cmpay.payment.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @author： PengAnHai
 * @date： 2024-08-15
 * @description：招行通知处理实现类
 * @modifiedBy：
 * @version: 1.0
 */
@Slf4j
@Service
public class CmbaionNotifyServiceImpl extends PaymentNotifyBaseServiceImpl implements CmbaionNotifyService {

    @Autowired
    private PayOrderQueryService payOrderQueryService;
    @Autowired
    private IExtSecretService secretService;
    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private RiskControlService riskControlService;
    @Autowired
    private AsynCommonService asynCommonService;

    @Override
    public PayNotifyBO pageNotifyHandle(String orderNo) {
        // 初始化查询业务对象并设置订单号
        PayOrderQueryBO paymentOrderQueryBO = new PayOrderQueryBO();
        paymentOrderQueryBO.setTradeOrderNo(orderNo);
        // 初始化支付通知业务对象并设置订单号和默认页面标志
        PayNotifyBO payNotifyBO = new PayNotifyBO();
        payNotifyBO.setOutTradeNo(orderNo);
        payNotifyBO.setPageFlag(false);
        try {
            // 查询支付结果
            payNotifyBO = payOrderQueryService.pageNotifyQuery(paymentOrderQueryBO);
            // 获取商户的秘钥信息
            SecretBO secretBO = secretService.getSecretByMerchantId(payNotifyBO.getMerchantId());
            // 检查返回URL和安全类型是否为空
            if (JudgeUtils.isNotBlankAll(StringUtils.trim(payNotifyBO.getReturnUrl()), secretBO.getSecretType())) {
                // 设置秘钥和商户签名类型
                payNotifyBO.setSecurityValue(StringUtils.isNotBlank(secretBO.getSecretNumber()) ? secretBO.getSecretNumber() : StringUtils.EMPTY);
                payNotifyBO.setMerchantSignType(secretBO.getSecretType());
                payNotifyBO.setPageFlag(true);
            }
            return payNotifyBO;
        } catch (BusinessException e) {
            // 处理业务异常，设置失败原因
            payNotifyBO.setFailureReason(setMessage(e.getMsgInfo()));
        } catch (Exception e) {
            // 处理其他异常，设置失败原因
            payNotifyBO.setFailureReason(setMessage(e.getMessage()));
        }
        return payNotifyBO;
    }

    @Override
    public void NotifyHandle(TradeNotifyBO tradeNotifyBO) {
        // 记录通知信息
        log.info("tradeNotifyBO:{}", tradeNotifyBO.toString());
        // 创建一个新的TradeOrderDO对象并设置交易订单号
        TradeOrderDO tradeOrderDO = new TradeOrderDO();
        tradeOrderDO.setTradeOrderNo(tradeNotifyBO.getTradeOrderNo());
        // 通过交易订单号查询交易订单
        TradeOrderDO tradeOrder = payOrderService.get(tradeOrderDO.getTradeOrderNo());
        // 检查交易订单是否存在，如果不存在则抛出业务异常
        if (JudgeUtils.isNull(tradeOrder)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        // 设置银行交易号为第三方订单号
        tradeOrder.setThirdOrdNo(tradeNotifyBO.getBnkTradeNo());
        // 检查交易订单的状态是否为待支付
        if (JudgeUtils.equals(tradeOrder.getStatus(), OrderStatusEnum.WAIT_PAY.name())) {
            // 设置支付订单费率信息
            setPayOrderRate(tradeOrder);
            // 校验并设置账期
            tradeOrder.setAccountDate(DateUtils.verifiAndSetAccountDate(tradeNotifyBO.getAccountDate()));
            // 检查通知的交易金额是否与订单的实际金额一致
            if (tradeNotifyBO.getTradeAmount().compareTo(tradeOrder.getRealAmount()) == 0) {
                // 如果金额一致，则处理订单成功的情况
                orderSuccessHandle(tradeOrder);
            } else {
                // 如果金额不一致，抛出业务异常
                BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
            }
            // 异步连接第三方订单
            asynCommonService.asynConnectThirdNo(tradeOrder);
            // 通知商户
            notifyMerchant(tradeOrder);
        } else {
            // 如果订单状态不是待支付，抛出业务异常
            BusinessException.throwBusinessException(MsgCodeEnum.STATUS_IS_NOT_WAIT);
        }
    }

    /**
     * 处理订单成功的情况
     *
     * @param tradeOrder 交易订单对象
     * @return 处理结果，成功返回true，失败返回false
     */
    @Override
    protected boolean orderSuccessHandle(TradeOrderDO tradeOrder) {
        // 更新订单状态为交易成功
        tradeOrder.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
        // 设置订单完成时间为当前时间
        tradeOrder.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
        // 设置接收通知的时间为当前时间
        tradeOrder.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
        // 设置第三方订单日期为账期
        tradeOrder.setThirdOrdDt(tradeOrder.getAccountDate());
        // 更新支付订单状态，如果更新失败则返回false
        if (0 == payOrderService.updatePaymentStatus(tradeOrder)) {
            return false;
        }
        // 进行支付后的风控处理
        riskControlService.riskAfterPay(tradeOrder);
        // 返回处理成功
        return true;
    }
}
