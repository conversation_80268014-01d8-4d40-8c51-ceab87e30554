package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.aplipay.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created on 2018/12/13
 *
 * @author: wulinfeng
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class AlipayPaymentListenerServiceImpl extends PaymentListenerService<PayOrderBO> {

    @Autowired
    ApplicationContext applicationContext;

    @EventListener
    @Override
    public void execute(PayOrderBO eventBO) {
        super.execute(eventBO);
    }

    @Override
    protected boolean checkChannelExecutable(PayOrderBO payOrderBO) {
        return Optional.ofNullable(payOrderBO)
                .map(PayOrderBO::getPaymentRout)
                .map(route->{return StringUtils.equalsIgnoreCase(PaymentWayEnum.ALIPAY.name(), route);})
                .orElse(false);
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(PayOrderBO payOrderBO) {
        return Optional.ofNullable(payOrderBO)
                .map(PayOrderBO::getPaymentRout)
                .map(route->{return payOrderBO.getScene();})
                .map(String::toUpperCase)
                .map(PaymentSceneEnum::valueOf)
                .map(sence->{

                    PaymentChannelService channelService = null;

                    switch(sence) {
                        case WAPPCREDIT:
                        case WAP: {channelService = getBean(AlipayWapPaymentChannelService.class); break;}

                        case WEB: {channelService = getBean(AlipayWebPaymentChannelService.class); break;}

                        case BARCODE: {channelService = getBean(AlipayBarcodePaymentChannelService.class); break;}

                        case SCAN: {channelService = getBean(AlipayScanPaymentChannelService.class); break;}

                        case APPPCREDIT:
                        case APP: {channelService = getBean(AlipayAppPaymentChannelService.class); break;}

                        case APPLET: {channelService = getBean(AlipayAppletPaymentChannelService.class);break;}
                        default: break;
                    }

                    return channelService;

                })
                .orElse(null);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }

}
