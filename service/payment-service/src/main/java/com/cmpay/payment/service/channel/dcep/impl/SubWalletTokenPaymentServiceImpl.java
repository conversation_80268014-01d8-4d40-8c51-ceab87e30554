package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.SubWalletPaymentBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.SubWalletTokenPayReq;
import com.cmpay.payment.dto.dcep.SubWalletTokenPayRsp;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.SubWalletTokenPaymentService;
import com.cmpay.payment.util.AES256Util;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/5/6
 */
@Service
public class SubWalletTokenPaymentServiceImpl extends AbstractDcepRequestServiceImpl implements SubWalletTokenPaymentService {

    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;

    @Override
    public void handlerSubWalletTokenPayment(SubWalletPaymentBO walletPaymentBO) {
        walletPaymentBO.setPayWayCode(PaymentSceneEnum.TOKEN.name().toLowerCase());
        SubWalletTokenPayReq walletTokenPayReq = new SubWalletTokenPayReq();
        buildRequestDto(walletTokenPayReq, walletPaymentBO, walletPaymentBO.getChannelNo());
        // 这个支付机构运营编码，是子钱包token支付的，newOrgNo是工行其他支付
        walletTokenPayReq.setOrgNo(dcepPaymentProperties.getOrgNo());
        //预下单存在订单额外attach表里，从这里拿子商户号
//        walletTokenPayReq.setSubMerchantNo("********");
        walletTokenPayReq.setSubMerchantNo(walletPaymentBO.getSubMerchantId());
        walletTokenPayReq.setOutTranNo(walletPaymentBO.getOutTradeNo());
        walletTokenPayReq.setBankCode(walletPaymentBO.getChannelBankCode());
        walletTokenPayReq.setMobile(AES256Util.encode(walletTokenPayReq.getSmxKey(), walletPaymentBO.getMobileNo()));
        walletTokenPayReq.setGoodsInfo(walletPaymentBO.getProductDesc());
        walletTokenPayReq.setBusiType(walletPaymentBO.getBusinessType());
        walletTokenPayReq.setBusiCode(walletPaymentBO.getBusinessCode());
        walletTokenPayReq.setExpireMinutes(walletPaymentBO.getExpireMinutes());
        walletTokenPayReq.setOutTranDate(walletPaymentBO.getOrderDate());
        walletTokenPayReq.setOutTranTime(walletPaymentBO.getOrderTime());
        walletTokenPayReq.setAmount(walletPaymentBO.getAmount());
        walletTokenPayReq.setCurrType(DcepConstants.CURRENT_TYPE);
        walletTokenPayReq.setNotifyUrl(dcepPaymentProperties.getNotifyUrl());
        buildDcepRequest(walletTokenPayReq, walletPaymentBO, DcepPaymentChannelEnum.TOKEN_PAY.getName());
    }

    @Override
    public void send(SubWalletPaymentBO walletPaymentBO) {
        handlerSubWalletTokenPayment(walletPaymentBO);
    }

    @Override
    public SubWalletPaymentBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        SubWalletPaymentBO subWalletPaymentBO = (SubWalletPaymentBO) baseDcepHandlerBO;
        if (StringUtils.equals(abstractDcepRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            SubWalletTokenPayRsp.Data data = ((SubWalletTokenPayRsp) abstractDcepRsp).getData();
            if (StringUtils.isNotBlank(data.getErrCode())) {
                subWalletPaymentBO.setErrMsgCd(data.getErrCode());
                subWalletPaymentBO.setErrMsgInfo(data.getErrMsg());
                BusinessException.throwBusinessException(MsgCodeEnum.DCEP_REUQEST_FAIL, new String[]{"SubWalletTokenPayment fail"});
            }
        } else {
            subWalletPaymentBO.setErrMsgCd(abstractDcepRsp.getCode());
            subWalletPaymentBO.setErrMsgInfo(abstractDcepRsp.getMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.DCEP_REUQEST_FAIL, new String[]{"SubWalletTokenPayment fail"});
        }
        return (SubWalletPaymentBO) baseDcepHandlerBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }
}

