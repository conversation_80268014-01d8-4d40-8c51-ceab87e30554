package com.cmpay.payment.service.channel.cmbaion.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.constant.Constants;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.service.channel.cmbaion.CmbaionWapPayChannelService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.cmbaion.CmbaionSignUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author： PengAnHai
 * @date： 2024-08-09
 * @description：招行一网通H5支付处理实现类
 * @modifiedBy：
 * @version: 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmbaionWapPayChannelServiceImpl extends CmbaionBasePayChannelServiceImpl implements CmbaionWapPayChannelService {
    Logger logger = LoggerFactory.getLogger(CmbaionWapPayChannelServiceImpl.class);
    @Value("${cmbaion.payNoticeUrl:}")
    private String payNoticeUrl;
    @Value("${cmbaion.returnUrl:}")
    private String returnUrl;
    @Value("${cmbaion.merchantKey:}")
    private String merchantKey;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Override
    public void send(PayOrderBO payOrderBO) {
        cmbaionWapPayment(payOrderBO);
    }

    @Override
    public void cmbaionWapPayment(PayOrderBO payOrderBO) {
        // 验证支付订单的参数
        checkParams(payOrderBO);
        // 设置签名所需的参数
        JSONObject signParams = setSignParams(payOrderBO);
        // 使用SHA256算法生成签名
        String sign = CmbaionSignUtils.CmbaionSHA256Sign(signParams, merchantKey);
        // 根据签名设置请求数据
        JSONObject requestData = setRequestData(sign);
        // 将请求数据转换为字符串，并设置到支付订单对象的支付请求数据字段
        payOrderBO.setPayRequestData(convertToString(requestData, Constants.SPLICES_VERTICAL) + Constants.SPLICES
                + convertToString(signParams, Constants.SPLICES));
    }

    /**
     * 设置用于生成签名的参数。
     * 此方法构建一个包含必要信息的JSONObject，用于后续的签名过程。
     *
     * @param payOrderBO 支付订单业务对象。
     * @return JSONObject 包含签名所需的所有参数。
     */
    private JSONObject setSignParams(PayOrderBO payOrderBO) {
        JSONObject signParams = new JSONObject();
        // 当前日期时间字符串
        signParams.put("dateTime", DateTimeUtils.getCurrentDateTimeStr());
        // 分行号
        signParams.put("branchNo", getBranchNo(payOrderBO.getPaymentId()));
        // 商户号
        signParams.put("merchantNo", getMerchantNo(payOrderBO.getPaymentId()));
        // 当前日期字符串
        signParams.put("date", payOrderBO.getOrderDate());
        // 支付订单号
        signParams.put("orderNo", payOrderBO.getOutTradeNo());
        // 实际支付金额
        signParams.put("amount", payOrderBO.getRealAmount().toString());
        // 超时时间跨度
        signParams.put("expireTimeSpan", getExpireTimeSpan(payOrderBO.getTimeoutExpress()));


        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                signParams.put("payNoticeUrl", UrlUtils.replaceDomainOrIp(payNoticeUrl,host));
                signParams.put("returnUrl", UrlUtils.replaceDomainOrIp(returnUrl,host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                signParams.put("payNoticeUrl", payNoticeUrl);
                signParams.put("returnUrl", returnUrl);
            }
        } else {
            signParams.put("payNoticeUrl", payNoticeUrl);
            signParams.put("returnUrl", returnUrl);
        }
        return signParams;
    }


}
