package com.cmpay.payment.service.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.bo.config.MerchantBO;
import com.cmpay.payment.bo.config.PayServiceBeanBO;
import com.cmpay.payment.channel.RiskyChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dao.IParamInfoExtDao;
import com.cmpay.payment.dao.data.IAlipayRiskgoNotifyDao;
import com.cmpay.payment.dao.data.IRiskgoConfigDao;
import com.cmpay.payment.dto.risky.RiskAfterPayReq;
import com.cmpay.payment.dto.risky.RiskConfigReq;
import com.cmpay.payment.dto.risky.RiskConfigRsp;
import com.cmpay.payment.entity.ParamInfoDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.entity.config.MerchantDO;
import com.cmpay.payment.entity.data.AlipayRiskgoNotifyDO;
import com.cmpay.payment.entity.data.RiskgoConfigDO;
import com.cmpay.payment.service.RiskControlService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.config.IExtMerchantService;
import com.cmpay.payment.util.PaymentUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/22 10:50
 * @description ：风控拦截实现方法
 */
@Service
public class RiskControlServiceImpl implements RiskControlService {
    private static final Logger logger = LoggerFactory.getLogger(RiskControlServiceImpl.class);
    @Autowired
    private IRiskgoConfigDao riskgoConfigDao;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private IExtMerchantService merchantService;
    @Autowired
    private IAlipayRiskgoNotifyDao alipayRiskgoNotifyDao;
    @Autowired
    private IParamInfoExtDao paramInfoDao;
    @Autowired
    private ExtPayOrderService payOrderService;
    /**
     * 调用事中接口进行风控拦截
     *
     * @param payOrderBO payOrderBO
     * @param rateBO rateBO
     */
    @Override
    public void riskIntercept(PayOrderBO payOrderBO, PayServiceBeanBO rateBO){
        //设置拦截时间
        String interceptTime = DateTimeUtils.getCurrentDateTimeStr();
        //查询风控配置
        RiskgoConfigDO riskgoConfigDO = new RiskgoConfigDO();
        riskgoConfigDO.setMerchantNo(payOrderBO.getMerchantId());
        riskgoConfigDO.setPayProductCode(payOrderBO.getPayWay());
        riskgoConfigDO.setAimProductCode(rateBO.getPaymentRout());
        riskgoConfigDO.setPayWayCode(payOrderBO.getScene());
        String provinceCode = payOrderBO.getOutTradeNo().substring(0,4);
        riskgoConfigDO.setProvinceCode(provinceCode);
        riskgoConfigDO.setBusType(payOrderBO.getProductDesc());
        //判断风控配置是否开启
        int RiskgoConfigCount = riskgoConfigDao.getRiskgoConfigCount(riskgoConfigDO);
        logger.info("RiskgoConfigCount : {}",RiskgoConfigCount);
        if(RiskgoConfigCount == 1){
            RiskConfigReq riskConfigReq = new RiskConfigReq();
            AlipayRiskgoNotifyDO alipayRiskgoNotifyDO = new AlipayRiskgoNotifyDO();
            String JrnNO = JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.RISK_JRN_NO, 18);
            riskConfigReq.setSerialId(JrnNO);
            riskConfigReq.setBizChannel(RiskConfigConstants.BIZ_CHANNEL);
            riskConfigReq.setBizCode(RiskConfigConstants.BIZ_CODE);
            riskConfigReq.setOperTime(System.currentTimeMillis());
            riskConfigReq.setOperIp1(payOrderBO.getRechargeIp());
            riskConfigReq.setOperIp2(payOrderBO.getClientIps());
            riskConfigReq.setTradeNo(payOrderBO.getTradeOrderNo());
            riskConfigReq.setPayWay(payOrderBO.getPayWay());
            riskConfigReq.setScene(payOrderBO.getScene());
            riskConfigReq.setTotalAmount(payOrderBO.getTotalAmount());
            riskConfigReq.setRealAmount(payOrderBO.getRealAmount());
            riskConfigReq.setDiscountableAmount(payOrderBO.getDiscountableAmount());
            riskConfigReq.setSubject(payOrderBO.getSubject());
            riskConfigReq.setProductDesc(payOrderBO.getProductDesc());
            riskConfigReq.setTradeDate(payOrderBO.getTradeDate());
            riskConfigReq.setHallAreaCode(payOrderBO.getHallAreaCode());
            riskConfigReq.setHallCode(payOrderBO.getHallCode());
            riskConfigReq.setHallWindowCode(payOrderBO.getHallWindowCode());
            riskConfigReq.setTimeoutExpress(payOrderBO.getTimeoutExpress());
            riskConfigReq.setBuyerId(payOrderBO.getBuyerId());
            riskConfigReq.setAuthCode(payOrderBO.getAuthCode());
            riskConfigReq.setWechatOpenId(payOrderBO.getWechatOpenId());
            riskConfigReq.setAppId(payOrderBO.getAppId());
            riskConfigReq.setMobilePhone(payOrderBO.getMobileNumber());
            //设置请求风控时间
            logger.info("riskConfigReq.toString() : {}",riskConfigReq.toString());
            alipayRiskgoNotifyDO.setRequestRiskgoTime(DateTimeFormatter.ofPattern("HHmmssSSS").format(LocalDateTime.now()));
            //发起请求
            GenericRspDTO<Response> response = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(RiskyChannelEnum.RISKY, RiskyChannelEnum.RISK_CONFIG.getName(), RiskyChannelEnum.RISKY, riskConfigReq));
            //设置风控结果通知时间
            alipayRiskgoNotifyDO.setRiskgoResultNotifyTime(DateTimeFormatter.ofPattern("HHmmssSSS").format(LocalDateTime.now()));
            if(JudgeUtils.isNull(response)||JudgeUtils.isNull(response.getBody())){
                logger.info("--------response or response.getBody() is null---------");
                return;
            }
            logger.info("response : {} response.getBody() : {}",response.toString(),response.getBody().toString());

            //设置相应风险订单信息
            MerchantBO merchantBO = new MerchantBO();
            merchantBO.setMerchantNumber(payOrderBO.getMerchantId());
            MerchantDO merchantDO = merchantService.get(merchantBO);
            alipayRiskgoNotifyDO.setJrnNo(JrnNO);
            alipayRiskgoNotifyDO.setContractId(merchantDO.getMerchantNumber());
            alipayRiskgoNotifyDO.setContractName(merchantDO.getMerchantName());
            alipayRiskgoNotifyDO.setProvinceCode(provinceCode);
            alipayRiskgoNotifyDO.setOutTradeNo(payOrderBO.getOutTradeNo());
            alipayRiskgoNotifyDO.setPayProductCode(payOrderBO.getPayWay());
            alipayRiskgoNotifyDO.setPayWayCode(payOrderBO.getScene());
            alipayRiskgoNotifyDO.setAimProductCode(rateBO.getPaymentRout());
            if(JudgeUtils.equals(rateBO.getPaymentRout(), PaymentWayEnum.CMPAY.name().toLowerCase())){
                if(JudgeUtils.equalsAny(payOrderBO.getScene(), PaymentSceneEnum.SCAN.name().toLowerCase(),
                        PaymentSceneEnum.BARCODE.name().toLowerCase())){
                    alipayRiskgoNotifyDO.setBusinessType(BusinessTypeEnum.OFFLINE.name().toLowerCase());
                }else {
                    alipayRiskgoNotifyDO.setBusinessType(BusinessTypeEnum.ONLINE.name().toLowerCase());
                }
            }else {
                alipayRiskgoNotifyDO.setBusinessType(BusinessTypeEnum.STANDARD.name().toLowerCase());
            }
            alipayRiskgoNotifyDO.setOrderDate(DateTimeUtils.getCurrentDateStr());
            alipayRiskgoNotifyDO.setOrderTime(DateTimeUtils.getCurrentTimeStr());
            alipayRiskgoNotifyDO.setClientIp(payOrderBO.getClientIps());
            alipayRiskgoNotifyDO.setRechargeIp(payOrderBO.getRechargeIp());
            alipayRiskgoNotifyDO.setpId(merchantDO.getMerchantNumber());
            alipayRiskgoNotifyDO.setInterceptType(RiskConfigConstants.JINKE_RISKY);
            alipayRiskgoNotifyDO.setInterceptTime(interceptTime);
            alipayRiskgoNotifyDO.setBusType(payOrderBO.getProductDesc());
            logger.info("begin alipayRiskgoNotifyDO.toString() : {}",alipayRiskgoNotifyDO.toString());

            //该条订单拦截失败,请求超时
            if(JudgeUtils.notEquals(response.getBody().getMsgCode(),"CHN00000")){
                logger.info("--------socket time out---------");
                alipayRiskgoNotifyDO.setInterceptResult(RiskConfigConstants.INTERCEPT_FAIL);
                alipayRiskgoNotifyDO.setRiskType(RiskConfigConstants.IP_OTHER);
                alipayRiskgoNotifyDO.setRiskLevel(RiskConfigConstants.IP_OTHER);
                //插入风险订单表
                alipayRiskgoNotifyDO.setTmSmp(DateTimeUtils.getCurrentDateTimeStr());
                logger.info("INTERCEPT_FAIL alipayRiskgoNotifyDO.toString() : {}",alipayRiskgoNotifyDO.toString());
//                alipayRiskgoNotifyDao.insert(alipayRiskgoNotifyDO);
                return;
            }
            RiskConfigRsp riskConfigRsp = null;
            //获取返回参数
            if(JudgeUtils.isNotNull(response.getBody().getResult())){
                riskConfigRsp = (RiskConfigRsp) response.getBody().getResult();
                logger.info("riskConfigRsp.toString() : {}",riskConfigRsp.toString());
            }
            if(JudgeUtils.isNull(riskConfigRsp)){
                logger.info("--------riskConfigRsp is null---------");
                return;
            }
            //返回码异常
            if(JudgeUtils.notEquals(RiskConfigConstants.RET_CODE,riskConfigRsp.getRetCode())){
                logger.info("--------retCode is not DE000---------");
                return;
            }
            //判断该条订单触发规则block
            if(JudgeUtils.isNotNull(riskConfigRsp.getVerifyPolicy())&&JudgeUtils.equals(riskConfigRsp.getVerifyPolicy().getCode(),RiskConfigConstants.BLOCK)){
                logger.info("-------- Trigger rule Block ---------");
                alipayRiskgoNotifyDO.setInterceptResult(RiskConfigConstants.INTERCEPT_SUCCESS);
                String clientIpProvinceName = null;
                String rechargeIpProvinceName = null;
                if(JudgeUtils.isNotNull(riskConfigRsp.getItems())&&(riskConfigRsp.getItems().size()>0)&&JudgeUtils.isNotNull(riskConfigRsp.getItems().get(0))){
                    clientIpProvinceName = riskConfigRsp.getItems().get(0).getOperIpProv2();
                    rechargeIpProvinceName = riskConfigRsp.getItems().get(0).getOperIpProv1();
                }
                ParamInfoDO paramInfoDO = new ParamInfoDO();
                paramInfoDO.setParamName(RiskConfigConstants.PROVINCE_CODE);
                if(!JudgeUtils.isEmpty(clientIpProvinceName)){
                    paramInfoDO.setParamExpress(clientIpProvinceName);
                    String clientIpProvinceCode = paramInfoDao.getParamValue(paramInfoDO);
                    logger.info("clientIpProvinceName : {} clientIpProvinceCode : {} ",clientIpProvinceName,clientIpProvinceCode);
                    alipayRiskgoNotifyDO.setClientIpProvince(clientIpProvinceCode);
                }
                if(!JudgeUtils.isEmpty(rechargeIpProvinceName)){
                    paramInfoDO.setParamExpress(rechargeIpProvinceName);
                    String rechargeIpProvinceCode = paramInfoDao.getParamValue(paramInfoDO);
                    alipayRiskgoNotifyDO.setRechargeIpProvince(rechargeIpProvinceCode);
                    logger.info("rechargeIpProvinceName : {} rechargeIpProvinceCode : {}",rechargeIpProvinceName,rechargeIpProvinceCode);
                }
                alipayRiskgoNotifyDO.setRiskType(RiskConfigConstants.IP_OTHER);
                alipayRiskgoNotifyDO.setRiskLevel(RiskConfigConstants.IP_OTHER);
                //插入风险订单表
                alipayRiskgoNotifyDO.setTmSmp(DateTimeUtils.getCurrentDateTimeStr());
                logger.info("INTERCEPT_SUCCESS alipayRiskgoNotifyDO.toString() : {}",alipayRiskgoNotifyDO.toString());
                alipayRiskgoNotifyDao.insert(alipayRiskgoNotifyDO);
                //插入订单表
                payOrderBO.setPaymentRout(rateBO.getPaymentRout());
                String status = OrderStatusEnum.TRADE_FAIL.name();
                logger.info("status : {}",status);
                payOrderBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                payOrderBO.setErrMsgCd(MsgCodeEnum.TRADE_IP_EXCEPTION.getMsgCd());
                payOrderBO.setErrMsgInfo(MsgCodeEnum.TRADE_IP_EXCEPTION.getMsgInfo());
                logger.info("payOrderBO.toString() : {}",payOrderBO.toString());
                payOrderService.insertByNewTranscation(payOrderBO);
                BusinessException.throwBusinessException(MsgCodeEnum.TRADE_IP_EXCEPTION);
            }
        }
    }
    /**
     * 调用风控事后接口
     *
     * @param tradeOrderDO tradeOrderDO
     */
    @Async
    @Override
    public void riskAfterPay(TradeOrderDO tradeOrderDO){
        //查询风控配置
        RiskgoConfigDO riskgoConfigDO = new RiskgoConfigDO();
        riskgoConfigDO.setMerchantNo(tradeOrderDO.getMerchantNo());
        riskgoConfigDO.setPayProductCode(tradeOrderDO.getPayProductCode());
        riskgoConfigDO.setAimProductCode(tradeOrderDO.getAimProductCode());
        riskgoConfigDO.setPayWayCode(tradeOrderDO.getPayWayCode());
        String provinceCode = tradeOrderDO.getOutTradeNo().substring(0,4);
        riskgoConfigDO.setProvinceCode(provinceCode);
        riskgoConfigDO.setBusType(tradeOrderDO.getGoodsDesc());
        //判断风控配置是否开启
        int RiskgoConfigCount = riskgoConfigDao.getRiskgoConfigCount(riskgoConfigDO);
        logger.info("RiskgoConfigCount : {}",RiskgoConfigCount);
        if(RiskgoConfigCount == 1){
            RiskAfterPayReq riskAfterPayReq = new RiskAfterPayReq();
            riskAfterPayReq.setTradeNo(tradeOrderDO.getTradeOrderNo());
            if (JudgeUtils.isNotNull(tradeOrderDO.getOrderCompleteTime())){
                riskAfterPayReq.setFinishDateTime(Long.parseLong(tradeOrderDO.getOrderCompleteTime()));
            }
            riskAfterPayReq.setPaymentOrderNo(tradeOrderDO.getOutTradeNo());
            riskAfterPayReq.setUserId(tradeOrderDO.getMobileNumber());
            riskAfterPayReq.setStatus(tradeOrderDO.getStatus());
            logger.info("riskAfterPayReq.toString() : {}",riskAfterPayReq.toString());
            //发起请求
            this.paymentCgwOutClient.request(PaymentUtils.buildRequest(RiskyChannelEnum.RISKY_AFTER, RiskyChannelEnum.RISK_AFTER_PAY.getName(), RiskyChannelEnum.RISKY_AFTER, riskAfterPayReq));
        }
    }
}
