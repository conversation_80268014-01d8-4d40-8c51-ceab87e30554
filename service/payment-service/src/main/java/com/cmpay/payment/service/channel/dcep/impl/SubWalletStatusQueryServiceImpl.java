package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.SubWalletStatusQueryBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.SubWalletStatusQueryReq;
import com.cmpay.payment.dto.dcep.SubWalletStatusQueryRsp;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.SubWalletStatusQueryService;
import com.cmpay.payment.util.AES256Util;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/5/5
 */
@Service
public class SubWalletStatusQueryServiceImpl extends AbstractDcepRequestServiceImpl implements SubWalletStatusQueryService {

    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;

    @Override
    public void handlerWalletStatusQuery(SubWalletStatusQueryBO statusQueryBO) {
        statusQueryBO.setPayWayCode(PaymentSceneEnum.TOKEN.name().toLowerCase());
        SubWalletStatusQueryReq subWalletStatusQueryReq = new SubWalletStatusQueryReq();
        buildRequestDto(subWalletStatusQueryReq, statusQueryBO, statusQueryBO.getChannelNo());
        subWalletStatusQueryReq.setMobile(AES256Util.encode(subWalletStatusQueryReq.getSmxKey(), statusQueryBO.getMobileNo()));
        buildDcepRequest(subWalletStatusQueryReq, statusQueryBO, DcepPaymentChannelEnum.SUB_WALLET_STATUS_QUERY.getName());
    }

    @Override
    public SubWalletStatusQueryBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        if (!StringUtils.equals(abstractDcepRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            BusinessException.throwBusinessException(MsgCodeEnum.DCEP_PAY_QUERY_FAIL);
        }
        SubWalletStatusQueryRsp statusQueryData = (SubWalletStatusQueryRsp) abstractDcepRsp;
        SubWalletStatusQueryBO statusQueryBO = (SubWalletStatusQueryBO) baseDcepHandlerBO;
        statusQueryBO.setStatus(statusQueryData.getData().getStatus());
        return statusQueryBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }

    @Override
    public void send(SubWalletStatusQueryBO statusQueryBO) {
        handlerWalletStatusQuery(statusQueryBO);
    }
}
