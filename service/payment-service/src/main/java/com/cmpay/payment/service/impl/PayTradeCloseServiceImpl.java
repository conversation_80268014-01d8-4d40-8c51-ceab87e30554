package com.cmpay.payment.service.impl;


import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.PaymentCloseOrderBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.PayTradeCloseService;
import com.cmpay.payment.service.channel.aplipay.AlipayTradeCloseService;
import com.cmpay.payment.service.channel.wechat.WeChatCloseOrderService;
import com.cmpay.payment.service.ext.ExtCloseOrderService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class PayTradeCloseServiceImpl implements PayTradeCloseService {
    @Autowired
    private ExtPayOrderService paymentOrderService;
    @Autowired
    private WeChatCloseOrderService weChatCloseOrderService;
    @Autowired
    private AlipayTradeCloseService alipayTradeCloseService;
    @Autowired
    private ExtCloseOrderService closeOrderService;

    @Override
    public PaymentCloseOrderBO closer(PaymentCloseOrderBO paymentCloseOrderBO){
        //查询订单
        TradeOrderDO paymentOrder= paymentOrderService.getOrderByNo(paymentCloseOrderBO.getOutTradeNo());
        //判断订单是否为空
        if (JudgeUtils.isNull(paymentOrder)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        paymentCloseOrderBO.setOutTradeNo(paymentOrder.getOutTradeNo());
        paymentCloseOrderBO.setAimProductCode(paymentOrder.getAimProductCode());
        paymentCloseOrderBO.setMerchantId(paymentOrder.getMerchantNo());
        paymentCloseOrderBO.setPayProductCode(paymentOrder.getPayProductCode());
        paymentCloseOrderBO.setScene(paymentOrder.getPayWayCode());
        paymentCloseOrderBO.setAimProductCode(paymentOrder.getAimProductCode());
        paymentCloseOrderBO.setBusinessType(paymentOrder.getBusinessType());
        paymentCloseOrderBO.setBusinessTypeDesc(paymentOrder.getBusinessTypeDesc());
        paymentCloseOrderBO.setOrderAmount(paymentOrder.getOrderAmount());
        paymentCloseOrderBO.setRealAmount(paymentOrder.getRealAmount());
        paymentCloseOrderBO.setOrderFeeAmount(paymentOrder.getOrderFeeAmount());
        paymentCloseOrderBO.setOrderData(paymentOrder.getOrderDate());
        paymentCloseOrderBO.setOrderTime(paymentOrder.getOrderTime());
        paymentCloseOrderBO.setRequestData(paymentOrder.getRequestDate());
        if(!JudgeUtils.equals(paymentOrder.getStatus(), OrderStatusEnum.WAIT_PAY.name())){
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_STATUS_CANNOT_CLOSE);
        }
        if (JudgeUtils.equals(paymentOrder.getStatus(), OrderStatusEnum.TRADE_CLOSED.name())){
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_STATUS_CANNOT_CLOSE);
        }
        //添加关单记录
        closeOrderService.addIfNot(paymentCloseOrderBO);
        //判断
        if (JudgeUtils.equalsIgnoreCase(paymentOrder.getPayProductCode(), PaymentWayEnum.WECHAT.name())){
           return weChatCloseOrderService.weChatPaymentCloseOrder(paymentCloseOrderBO);
        }else if(JudgeUtils.equalsIgnoreCase(paymentOrder.getPayProductCode(), PaymentWayEnum.ALIPAY.name())){
            return alipayTradeCloseService.alipayTradeClose(paymentCloseOrderBO);
        }else {
            BusinessException.throwBusinessException(MsgCodeEnum.CLOSER_NOT_ALI_ADN_WX);
        }
        return null;
    }
}
