package com.cmpay.payment.service.channel.cmpay.impl;

/**
 * Created on 2018/12/3
 *
 * @author: li_zhen
 */

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dto.cmpay.CmpayPosPaymentQueryReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayPosPaymentQueryRspDTO;
import com.cmpay.payment.service.channel.cmpay.CmpayCloudPaymentQueryChannelService;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayCloudPaymentQueryChannelServiceImpl implements CmpayCloudPaymentQueryChannelService {
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;

    @Override
    public void send(PaymentQueryBO paymentQueryBO) {
        cmpayCloudPaymentQuery(paymentQueryBO);
    }

    @Override
    public void cmpayCloudPaymentQuery(PaymentQueryBO paymentQueryBO) {
        CmpayPosPaymentQueryReqDTO paymentQueryReqDTO = new CmpayPosPaymentQueryReqDTO();
        paymentQueryReqDTO.setMerchantId(paymentQueryBO.getPaymentOrder().getBankMerchantNo());
        paymentQueryReqDTO.setRequestId(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.CMPAY_JRN_NO, 18));
        paymentQueryReqDTO.setSignType(paymentQueryBO.getSignMethod());
        paymentQueryReqDTO.setType(CmpayConstants.CPOS_QUERY_PAY);
        paymentQueryReqDTO.setVersion(CmpayConstants.CPOS_VERSION);
        paymentQueryReqDTO.setOrderId(paymentQueryBO.getPaymentOrder().getTradeOrderNo());
        paymentQueryReqDTO.setHmac(paymentQueryBO.getContractSecureValue());
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(CmpayPayChannelEnum.CMPAY_POS);
        request.setBusiType(CmpayPayChannelEnum.CMPAYPOS_PAYMENT_QUERY.getName());
        request.setSource(CmpayPayChannelEnum.CMPAY_POS);
        request.setTarget(paymentQueryReqDTO);
        GenericRspDTO<Response> genericRspDTO;
        if (StringUtils.equalsAny(paymentQueryBO.getSourceApp(), AppEnum.integrationbatch.name(), AppEnum.integrationshecdule.name())) {
            genericRspDTO =nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        } else {
            genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        }
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .map(result -> handleCposOrderQuery(paymentQueryBO, (CmpayPosPaymentQueryRspDTO) result))
                .orElseThrow(() -> new BusinessException(MsgCodeEnum.PAYMENT_STATUS_UNDEFINED));
    }

    private PaymentQueryBO handleCposOrderQuery(PaymentQueryBO paymentQueryBO, CmpayPosPaymentQueryRspDTO paymentQueryRspDTO) {
        if (!StringUtils.equals(paymentQueryRspDTO.getReturnCode(), CmpayConstants.RETURN_CODE)) {
            BusinessException.throwBusinessException(paymentQueryRspDTO.getReturnCode());
        }
        paymentQueryBO.setBnkTraceNo(paymentQueryRspDTO.getBankTraceNo());
        String status = getOrderStatus(CloudOrderStatusEnum.valueOf(paymentQueryRspDTO.getStatus()));
        if (JudgeUtils.isNull(status)) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_STATUS_UNDEFINED);
        }
        if (OrderStatusEnum.TRADE_FAIL.name().equals(status)) {
            paymentQueryBO.setErrMsgCd(paymentQueryRspDTO.getReturnCode());
            paymentQueryBO.setErrMsgInfo(paymentQueryRspDTO.getMessage());
        }
        paymentQueryBO.setStatus(status);
        if (JudgeUtils.isNotBlank(paymentQueryRspDTO.getSpeciesCard())) {
            paymentQueryBO.setCrdType(paymentQueryRspDTO.getSpeciesCard());
        } else {
            paymentQueryBO.setCrdType("2");
        }
        paymentQueryBO.getPaymentOrder().setAccountDate(paymentQueryRspDTO.getAccountDate());
        paymentQueryBO.getPaymentOrder().setBnkTraceNo(paymentQueryRspDTO.getBankTraceNo());
        paymentQueryBO.setUnionpayDiscountInfo(paymentQueryRspDTO.getUnionSaleInfo());
        if (JudgeUtils.isNotEmpty(paymentQueryRspDTO.getTradeNo())) {
            paymentQueryBO.getPaymentOrder().setThirdOrdNo(paymentQueryRspDTO.getSystemTrackingNumber());
            paymentQueryBO.getPaymentOrder().setThirdOrdDt(paymentQueryRspDTO.getAccountDate());
        } else {
            paymentQueryBO.getPaymentOrder().setThirdOrdNo(paymentQueryRspDTO.getFillingMoneyOrderNo());
            paymentQueryBO.getPaymentOrder().setThirdOrdDt(paymentQueryRspDTO.getAccountDate());
        }
        return paymentQueryBO;
    }

    private String getOrderStatus(CloudOrderStatusEnum cloudOrderStatusEnum) {
        switch (cloudOrderStatusEnum) {
            case SUCCESS:
                return OrderStatusEnum.TRADE_SUCCESS.name();
            case OVERDUE:
                return OrderStatusEnum.TRADE_CLOSED.name();
            case CANCLE:
            case CLOSED:
            case FAILED:
                return OrderStatusEnum.TRADE_FAIL.name();
            default:
                return null;
        }
    }
}
