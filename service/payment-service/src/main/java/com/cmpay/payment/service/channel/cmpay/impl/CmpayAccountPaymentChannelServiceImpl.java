package com.cmpay.payment.service.channel.cmpay.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.JkThirdPayParamBO;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.CmpayConstants;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.dto.cmpay.CmpayAccountPaymentReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayAccountPaymentRspDTO;
import com.cmpay.payment.service.channel.cmpay.CmpayAccountPaymentChannelService;
import com.cmpay.payment.service.channel.cmpay.CmpayCommonService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.service.ext.ExtThirdPayParamService;
import com.cmpay.payment.service.ext.utils.Sm4;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created on 2024/04/27
 *
 * @author: lb
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayAccountPaymentChannelServiceImpl implements CmpayAccountPaymentChannelService {

    private static final Logger logger = LoggerFactory.getLogger(CmpayAccountPaymentChannelServiceImpl.class);

    @Value("${cmpay.cmpayAccountBackendNotifyUrl:}")
    private String cmpayAccountBackendNotifyUrl;
    @Value("${payment.sm4Key:}")
    private String sm4Key;
    @Value("${cmpay.channelId:}")
    private String channelId;

    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;

    @Autowired
    private CmpayCommonService cmpayCommonService;
    @Autowired
    private ExtThirdPayParamService thirdPayParamService;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Override
    public void send(PayOrderBO payOrderBO) {
        cmpayAccountPayment(payOrderBO);
    }

    @Override
    public void cmpayAccountPayment(PayOrderBO payOrderBO) {

        try {
            CmpayAccountPaymentReqDTO cmpayAccountPaymentReqDTO = setReqDTO(payOrderBO);
            Request request = new Request();
            request.setRequestId(LemonUtils.getRequestId());
            request.setRoute(CmpayPayChannelEnum.CMPAY_ACCOUNT);
            request.setBusiType(CmpayPayChannelEnum.CMPAY_ACCOUNT_PAYMENT.getName());
            request.setSource(CmpayPayChannelEnum.CMPAY_ACCOUNT);
            request.setTarget(cmpayAccountPaymentReqDTO);
            GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            if (JudgeUtils.isNotSuccess(genericRspDTO)) {
                cmpayCommonService.cgwReturnInfoCheck(genericRspDTO);
                BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_ORDER_PAY_FAIL);
            }
            Optional.of(genericRspDTO)
                    .map(GenericRspDTO::getBody)
                    .map(Response::getResult)
                    .map(result -> handleCmpayAccountPayment((CmpayAccountPaymentRspDTO) result,payOrderBO))
                    .orElseThrow(() ->new BusinessException(MsgCodeEnum.CMPAY_ORDER_PAY_FAIL));

        } catch (Exception e) {
            throw e;
        }
    }

    private PayOrderBO handleCmpayAccountPayment (CmpayAccountPaymentRspDTO accountPaymentRsp,PayOrderBO payOrderBO) {
        logger.info("cmpayAccountPaymentRspDTO{}", JSONObject.toJSONString(accountPaymentRsp));
        if (JudgeUtils.isNotNull(accountPaymentRsp) && JudgeUtils.isNotSuccess(accountPaymentRsp.getMsgCd())) {
            payOrderBO.setErrMsgCd(accountPaymentRsp.getMsgCd());
            payOrderBO.setErrMsgInfo(accountPaymentRsp.getMsgInfo());
            //判断是否SYS开头转换错误码
            if(JudgeUtils.isNotNull(accountPaymentRsp.getMsgCd())
                    && accountPaymentRsp.getMsgCd().toUpperCase().contains(CommonConstant.SYS)){
                BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_SYS_FAIL);
            }
            BusinessException.throwBusinessException(accountPaymentRsp.getMsgCd());
        }
        payOrderBO.setPayUrl(accountPaymentRsp.getOrderNo());
        return payOrderBO;
    }

    /**
     * 将支付订单业务对象(PayOrderBO)转换成Cmpay账户支付请求数据传输对象(CmpayAccountPaymentReqDTO)。
     *
     * @param payOrderBO 支付订单业务对象，包含了需要转换的信息。
     * @return 已经填充好数据的Cmpay账户支付请求DTO对象。
     */
    private CmpayAccountPaymentReqDTO setReqDTO(PayOrderBO payOrderBO) {
        CmpayAccountPaymentReqDTO cmpayAccountPaymentReqDTO = new CmpayAccountPaymentReqDTO();
        cmpayAccountPaymentReqDTO.setVersion(CmpayConstants.VERSION);
        cmpayAccountPaymentReqDTO.setMerchantId(payOrderBO.getPaymentId());

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                cmpayAccountPaymentReqDTO.setBackReturnUrl(UrlUtils.replaceDomainOrIp(cmpayAccountBackendNotifyUrl,host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                cmpayAccountPaymentReqDTO.setBackReturnUrl(cmpayAccountBackendNotifyUrl);
            }
        } else {
            cmpayAccountPaymentReqDTO.setBackReturnUrl(cmpayAccountBackendNotifyUrl);
        }

        cmpayAccountPaymentReqDTO.setMerchantOrderDate(payOrderBO.getOrderDate());
        cmpayAccountPaymentReqDTO.setMerchantOrderNo(payOrderBO.getTradeOrderNo());
        cmpayAccountPaymentReqDTO.setMobileNo(Sm4.sm4encrypt(payOrderBO.getMobileNo(),sm4Key));
        cmpayAccountPaymentReqDTO.setPayMode(payOrderBO.getPayMode());
        cmpayAccountPaymentReqDTO.setRequestNo(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.CMPAY_JRN_NO, 18));
        cmpayAccountPaymentReqDTO.setValidNum(payOrderBO.getValidityNumber());
        cmpayAccountPaymentReqDTO.setValidUnit(payOrderBO.getValidityUnit());
        cmpayAccountPaymentReqDTO.setOrderAmount(payOrderBO.getRealAmount());
        cmpayAccountPaymentReqDTO.setBankAbbr(payOrderBO.getBankAbbreviation());
        cmpayAccountPaymentReqDTO.setCardAccountType(payOrderBO.getCrdAcTyp());
        cmpayAccountPaymentReqDTO.setType(CmpayConstants.TYPE);
        cmpayAccountPaymentReqDTO.setChannelNo(channelId);
        cmpayAccountPaymentReqDTO.setUserIp(payOrderBO.getClientIp());
        cmpayAccountPaymentReqDTO.setUserSmsNotify(payOrderBO.getUserSmsNotify());
        cmpayAccountPaymentReqDTO.setGoodsName(payOrderBO.getProductName());
        cmpayAccountPaymentReqDTO.setGoodsNo(payOrderBO.getProductCode());
        if (JudgeUtils.isNotBlank(payOrderBO.getAgreementNo())) {
            cmpayAccountPaymentReqDTO.setAgreementNo(payOrderBO.getAgreementNo());
        }
        // 配置营销工具字段，备注字段1，备注字段1
        JkThirdPayParamBO thirdPayParamBO = thirdPayParamService.queryMarketingToolField(payOrderBOTOThirdPayParamBO(payOrderBO));
        cmpayAccountPaymentReqDTO.setCouponsFlag(thirdPayParamBO.getParamValue());
        if (JudgeUtils.isBlank(payOrderBO.getRemark1())) {
            cmpayAccountPaymentReqDTO.setRemark1(CmpayConstants.JKJH);
        } else {
        cmpayAccountPaymentReqDTO.setRemark1(CmpayConstants.JKJH + payOrderBO.getRemark1());
        }
        cmpayAccountPaymentReqDTO.setRemark2(payOrderBO.getRemark2());
        return cmpayAccountPaymentReqDTO;
    }

    /**
     * 将PayOrderBO对象转换为JkThirdPayParamBO对象。
     * 此方法用于从支付订单业务对象中提取相关信息，并将其填充到第三方支付参数业务对象中。
     *
     * @param payOrderBO PayOrderBO类型的支付订单业务对象，包含支付相关的各种信息。
     * @return JkThirdPayParamBO类型的第三方支付参数业务对象。
     */
    private JkThirdPayParamBO payOrderBOTOThirdPayParamBO(PayOrderBO payOrderBO) {
        JkThirdPayParamBO thirdPayParamBO = new JkThirdPayParamBO();
        thirdPayParamBO.setAimProductCode(payOrderBO.getPaymentRout());
        thirdPayParamBO.setPayProductCode(payOrderBO.getPayWay());
        thirdPayParamBO.setPayWayCode(payOrderBO.getScene());
        return thirdPayParamBO;
    }
}
