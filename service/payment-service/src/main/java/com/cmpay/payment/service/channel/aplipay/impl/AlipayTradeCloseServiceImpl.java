package com.cmpay.payment.service.channel.aplipay.impl;


import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.PaymentCloseOrderBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.AlipayTradeStatusEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.dto.alipay.TradeCloseReq;
import com.cmpay.payment.dto.alipay.TradeCloseRsp;
import com.cmpay.payment.dto.alipay.TradeQueryReq;
import com.cmpay.payment.dto.alipay.TradeQueryRsp;
import com.cmpay.payment.service.channel.aplipay.AlipayTradeCloseService;
import com.cmpay.payment.service.ext.ExtCloseOrderService;
import com.cmpay.payment.util.PaymentUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;


/**
 * Created on 2021/9/7
 *
 * @author: deng_qian
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class AlipayTradeCloseServiceImpl implements AlipayTradeCloseService {
    private static final Logger logger = LoggerFactory.getLogger(AlipayTradeCloseServiceImpl.class);
    /**
     * 接口调用成功，调用结果请参考具体的API文档所对应的业务返回参数
     *
     * @see <a href="https://docs.open.alipay.com/common/105806">公共错误码 </a>
     */
    private static final String CODE_SUCCESS = "10000";
    /**
     * 业务异常
     *
     * @see <a href="https://docs.open.alipay.com/common/105806">公共错误码 </a>
     */
    private static final String CODE_BUSINESS_ERROR = "40004";
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private ExtCloseOrderService closeOrderService;

    @Override
    public PaymentCloseOrderBO alipayTradeClose(PaymentCloseOrderBO paymentCloseOrderBO) {
        logger.info("=====================Alipay Trade Close Start!!=====================");
        //调用alipay查询接口,查询状态是否可以关单
        TradeQueryReq tradeQueryReq = new TradeQueryReq();
        tradeQueryReq.setOutTradeNo(paymentCloseOrderBO.getOutTradeNo());
        GenericRspDTO<Response> res = paymentCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_QUERY.getName(), tradeQueryReq));
        logger.info("closeOrderResponse:",res);
        TradeQueryRsp tradeQueryRsp = (TradeQueryRsp) res.getBody().getResult();
        if(!JudgeUtils.equals(AlipayTradeStatusEnum.WAIT_BUYER_PAY.name(),tradeQueryRsp.getTradeStatus())){
            //修改第三方报错代码
            paymentCloseOrderBO.setErrMsgCd(tradeQueryRsp.getCode());
            //修改第三方错误描述
            paymentCloseOrderBO.setErrMsgInfo(tradeQueryRsp.getSubMsg());
            //把关单状态设置为失败
            paymentCloseOrderBO.setStatus(OrderStatusEnum.CLOSE_FAIL.name());
            if(JudgeUtils.equals(tradeQueryRsp.getCode(),CODE_BUSINESS_ERROR)){
                paymentCloseOrderBO.setTradeStatus(OrderStatusEnum.TRADE_NOT.name());
                return paymentCloseOrderBO;
            }else{
                paymentCloseOrderBO.setTradeStatus(tradeQueryRsp.getTradeStatus());
                logger.info("tradeStatus:",paymentCloseOrderBO.getTradeStatus());
                closeOrderService.updateByNewTransaction(paymentCloseOrderBO);
            return paymentCloseOrderBO;}
        }
        if (JudgeUtils.equals(AlipayTradeStatusEnum.TRADE_CLOSED,tradeQueryRsp.getTradeStatus())){
            //把关单状态设置为成功
            paymentCloseOrderBO.setStatus(OrderStatusEnum.TRADE_CLOSED.name());
            paymentCloseOrderBO.setTradeStatus(tradeQueryRsp.getTradeStatus());
            closeOrderService.updateByNewTransaction(paymentCloseOrderBO);
            return paymentCloseOrderBO;
        }
        // 调用alipay关单接口
        TradeCloseReq tradeCloseReq = new TradeCloseReq();
        tradeCloseReq.setOutTradeNo(paymentCloseOrderBO.getOutTradeNo());
        GenericRspDTO<Response> response = paymentCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_CLOSE.getName(), tradeCloseReq));
        logger.info("response:",response);
        TradeCloseRsp tradePayRsp = (TradeCloseRsp) response.getBody().getResult();
        paymentCloseOrderBO.setErrMsgCd(tradePayRsp.getCode());//修改第三方报错代码
        paymentCloseOrderBO.setErrMsgInfo(tradePayRsp.getSubMsg());//修改第三方错误描述
        if (JudgeUtils.isNotSuccess(response)) {
            BusinessException.throwBusinessException(response);
        }
        if (JudgeUtils.isSuccess(response.getMsgCd())) {
            if (StringUtils.equals(CODE_SUCCESS,paymentCloseOrderBO.getErrMsgCd())) {
                //把关单状态设置为成功
                paymentCloseOrderBO.setStatus(OrderStatusEnum.TRADE_CLOSED.name());
            } else  {
                //把关单状态设置为失败
                paymentCloseOrderBO.setStatus(OrderStatusEnum.CLOSE_FAIL.name());
            }
        }
        closeOrderService.updateByNewTransaction(paymentCloseOrderBO);
        return paymentCloseOrderBO;
    }


    @Override
    public void send(PaymentCloseOrderBO paymentCloseOrderBO) {
        alipayTradeClose(paymentCloseOrderBO);
    }
}
