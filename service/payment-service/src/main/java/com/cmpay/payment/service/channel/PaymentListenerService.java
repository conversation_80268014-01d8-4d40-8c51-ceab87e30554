package com.cmpay.payment.service.channel;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.payment.constant.MsgCodeEnum;
import org.springframework.context.ApplicationContext;

/**
 * Created on 2018/11/30
 *
 * @author: sun_zhh
 */
public abstract class PaymentListenerService<EVENT> {
    /**
     * 检查该渠道是否能执行 返回true表示要该渠道执行
     *
     * @param event
     * @return
     */
    protected abstract boolean checkChannelExecutable(EVENT event);

    /**
     * 返回该渠道的处理接口
     *
     * @param event
     * @return
     */
    protected abstract <BEANTYPE extends PaymentChannelService> BEANTYPE determinateChannelExecuteBean(EVENT event);

    /**
     * 返回spring  ApplicationContext
     *
     * @return
     */
    protected abstract ApplicationContext getApplicationContext();

    public <BEANTYPE extends PaymentChannelService> void execute(EVENT eventBO) {
        //判断是不是由CMPAY渠道处理这个退款
        if (!checkChannelExecutable(eventBO)) {
            return;
        }
        BEANTYPE bean = determinateChannelExecuteBean(eventBO);
        if (bean == null) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_QUERY_INTERFACE_NOT_EXISTS);
        }
        //触发对应接口发起退款
        bean.send(eventBO);
        afterSend(eventBO);
    }

    protected <BEANTYPE extends PaymentChannelService> BEANTYPE getBean(Class<BEANTYPE> beanType) {
        return getApplicationContext().getBean(beanType);
    }

    public void afterSend(EVENT eventBO) {

    }
}
