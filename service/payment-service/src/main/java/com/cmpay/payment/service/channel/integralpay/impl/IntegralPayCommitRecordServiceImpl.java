package com.cmpay.payment.service.channel.integralpay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.IntegralPayCommitRecordBO;
import com.cmpay.payment.channel.IntegralPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.IntegralPaymentStatusEnum;
import com.cmpay.payment.constant.IntegralpayConstants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.dto.integralpay.IntegralFinishReq;
import com.cmpay.payment.dto.integralpay.IntegralFinishRsp;
import com.cmpay.payment.service.channel.integralpay.IntegralPayCommitRecordService;
import com.cmpay.payment.util.PaymentUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/12 15:57
 * @description ：
 */
@Service
public class IntegralPayCommitRecordServiceImpl implements IntegralPayCommitRecordService {
    private static final Logger logger = LoggerFactory.getLogger(IntegralPayCommitRecordServiceImpl.class);

    @Autowired
    IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Value("${integralpay.identity_id:}")
    private String identityId;
    @Override
    public void send(IntegralPayCommitRecordBO integralPayCommitRecordBO) {
        IntegralFinishReq integralFinishReq = new IntegralFinishReq();
        integralFinishReq.setVersion(IntegralpayConstants.VERSION);
        integralFinishReq.setIdentityId(integralPayCommitRecordBO.getIntegralMercNo());
        integralFinishReq.setSource(IntegralpayConstants.SOURCE);
        IntegralFinishReq.ReqBody reqBody = new IntegralFinishReq.ReqBody();
        List<IntegralFinishReq.ReqBody.Record> recordList = new ArrayList<>();
        IntegralFinishReq.ReqBody.Record record = new IntegralFinishReq.ReqBody.Record();
        record.setOrderId(integralPayCommitRecordBO.getBankOrderNo());
        record.setItemId(integralPayCommitRecordBO.getProductCode());
        record.setUseId(integralPayCommitRecordBO.getUseId());
        record.setVirtualCode(integralPayCommitRecordBO.getVcode());
        record.setVirtualCodePass(integralPayCommitRecordBO.getVcodePass());
        record.setUseAmount(integralPayCommitRecordBO.getUseAmount().toString());
        try {
            Date format = new SimpleDateFormat("yyyyMMddHHmmss").parse(integralPayCommitRecordBO.getUseOrderTime());
            String useOrderTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(format);
            record.setUseDatetime(useOrderTime);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgCodeEnum.POINTS_FINISH_ORDER_FAIL);
        }
        record.setUseContent(integralPayCommitRecordBO.getUseOrderContent());
        record.setPhone(integralPayCommitRecordBO.getMobile());
        recordList.add(record);
        reqBody.setRecordList(recordList);
        integralFinishReq.setData(reqBody);
        logger.info("IntegralFinishReq : {}",integralFinishReq.toString());

        GenericRspDTO<Response> genericRspDTO = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(IntegralPayChannelEnum.INTEGRAL_PAY, IntegralPayChannelEnum.INTEGRAL_FINISH.getName(), IntegralPayChannelEnum.INTEGRAL_PAY, integralFinishReq));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.POINTS_FINISH_TIMEOUT);
        }
        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(integralPayCommitRecordBO, (IntegralFinishRsp) result));
    }

    public void handleResult(IntegralPayCommitRecordBO integralPayCommitRecordBO, IntegralFinishRsp integralFinishRsp){
        logger.info("integralFinishRsp : {}",integralFinishRsp.toString());
        if(JudgeUtils.equals(integralFinishRsp.getCode(), IntegralpayConstants.VIRTUAL_SUCCESS_CODE)
                && JudgeUtils.equals(integralFinishRsp.getMsg(), IntegralpayConstants.VIRTUAL_SUCCESS_MSG)
                && JudgeUtils.equals(integralPayCommitRecordBO.getBankOrderNo(),integralFinishRsp.getData().getSuccessful().getRecords().get(0).getOrderId())){
            integralPayCommitRecordBO.setStatus(IntegralPaymentStatusEnum.SHIP_FINISH.name());
        }else{
            integralPayCommitRecordBO.setStatus(IntegralPaymentStatusEnum.SHIP_FAIL.name());
            integralPayCommitRecordBO.setErrMsgCd(integralFinishRsp.getCode());
            integralPayCommitRecordBO.setErrMsgInfo(integralFinishRsp.getData().getFailed().getRecords().get(0).getInfo());
            BusinessException.throwBusinessException(MsgCodeEnum.POINTS_FINISH_ORDER_FAIL);
        }
    }
}
