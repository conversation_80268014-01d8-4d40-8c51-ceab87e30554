package com.cmpay.payment.service.channel.listener.cmbaion;

import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.cmbaion.CmbaionAppPayChannelService;
import com.cmpay.payment.service.channel.cmbaion.CmbaionWapPayChannelService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @author： PengAnHai
 * @date： 2024-08-09
 * @description： 招行一网通支付处理监听类
 * @modifiedBy：
 * @version: 1.0
 */
@Service
public class CmbaionPaymentListenerServiceImpl extends PaymentListenerService<PayOrderBO> {

    @Autowired
    ApplicationContext applicationContext;

    @EventListener
    @Override
    public void execute(PayOrderBO eventBO) {
        super.execute(eventBO);
    }

    @Override
    protected boolean checkChannelExecutable(PayOrderBO payOrderBO) {
        return Optional.ofNullable(payOrderBO)
                .map(PayOrderBO::getPaymentRout)
                .map(route -> StringUtils.equalsIgnoreCase(PaymentWayEnum.CMBAION.name(), route))
                .orElse(false);
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(PayOrderBO payOrderBO) {
        if (payOrderBO == null || payOrderBO.getPaymentRout() == null) {
            return null;
        }
        String scene = payOrderBO.getScene().toUpperCase();
        PaymentSceneEnum sceneEnum = PaymentSceneEnum.valueOf(scene);
        switch (sceneEnum) {
            case WAP:
                return getBean(CmbaionWapPayChannelService.class);
            case APP:
                return getBean(CmbaionAppPayChannelService.class);
            default:
                return null;
        }
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
