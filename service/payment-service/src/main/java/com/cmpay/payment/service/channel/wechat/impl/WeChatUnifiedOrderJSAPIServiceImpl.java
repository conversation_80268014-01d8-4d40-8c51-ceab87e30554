package com.cmpay.payment.service.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.AlertCapable;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.constant.wechat.WeChatTradeTypeEnum;
import com.cmpay.payment.dto.wechat.WXPayJsapiSignRequest;
import com.cmpay.payment.dto.wechat.WXPayJsapiSignResponse;
import com.cmpay.payment.dto.wechat.WXPayUnifiedorderRequest;
import com.cmpay.payment.dto.wechat.WXPayUnifiedorderResponse;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.channel.wechat.WeChatUnifiedOrderJSAPIService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatUnifiedOrderJSAPIServiceImpl implements WeChatUnifiedOrderJSAPIService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatUnifiedOrderJSAPIServiceImpl.class);

    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    ExtParamInfoService paramInfoService;

    /**
     * 微信JSAPI支付或小程序支付统一下单
     *
     * @param payOrderBO
     */
    @Override
    public void unifiedOrderJSAPI(PayOrderBO payOrderBO) {

        if (StringUtils.isBlank(payOrderBO.getWechatOpenId())) {
            BusinessException.throwBusinessException(MsgCodeEnum.REQUEST_PARAM_CANNOT_BE_EMPTY);
        }
        // 校验openid的合法性
        if(JudgeUtils.equals(CommonConstant.WECHAT_OPENID_UNDEFINED, payOrderBO.getWechatOpenId())){
            BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_OPENID_UNDEFINED);
        }
        WXPayUnifiedorderRequest wxPayUnifiedorderRequest = new WXPayUnifiedorderRequest();
        // 微信对body（商品描述）字段格式按使用场景有特殊要求，H5支付的商品字段规则：浏览器打开的移动网页的主页title名-商品概述，样例：腾讯充值中心-QQ会员充值
        wxPayUnifiedorderRequest.setBody(payOrderBO.getSubject());
        wxPayUnifiedorderRequest.setOutTradeNo(payOrderBO.getOutTradeNo());
        // 金额单位：分
        wxPayUnifiedorderRequest.setTotalFee(payOrderBO.getRealAmount().multiply(new BigDecimal(100)).intValue());
        wxPayUnifiedorderRequest.setSpbillCreateIp(payOrderBO.getClientIps());
        Optional.ofNullable(payOrderBO.getTradeDate() + payOrderBO.getTradeTime()).ifPresent(timeStart -> wxPayUnifiedorderRequest.setTimeStart(timeStart));
        Optional.ofNullable(payOrderBO.getExpireTime()).ifPresent(expireTime -> wxPayUnifiedorderRequest.setTimeExpire(expireTime));

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                wxPayUnifiedorderRequest.setNotifyUrl(UrlUtils.replaceDomainOrIp(weChatProperties.getNotifyUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                wxPayUnifiedorderRequest.setNotifyUrl(weChatProperties.getNotifyUrl());
            }
        } else {
            wxPayUnifiedorderRequest.setNotifyUrl(weChatProperties.getNotifyUrl());
        }

        wxPayUnifiedorderRequest.setTradeType(WeChatTradeTypeEnum.JSAPI.name());
        Optional.ofNullable(payOrderBO.getLimitPay()).ifPresent(limitPay -> wxPayUnifiedorderRequest.setLimitPay(limitPay));
        wxPayUnifiedorderRequest.setOpenid(payOrderBO.getWechatOpenId());
        wxPayUnifiedorderRequest.setAppid(payOrderBO.getAppId());
        wxPayUnifiedorderRequest.setMchId(payOrderBO.getPaymentId());
        wxPayUnifiedorderRequest.setKey(payOrderBO.getContractSecureValue());


        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.UNIFIEDORDER.getSource());
        request.setRoute(WXPayChannel.UNIFIEDORDER.getRoute());
        request.setBusiType(WXPayChannel.UNIFIEDORDER.getBusType());
        request.setTarget(wxPayUnifiedorderRequest);
        LocalDateTime sendTimeFirst = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        logger.info("WeChatUnifiedOrderJSAPIServiceImpl Source : {} Route : {} BusiType : {}",request.getSource(),request.getRoute(),request.getBusiType());

        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        LocalDateTime responseTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        int count= 1;
        while (count <= Constants.WECHAT_ORDER_RESEND_NUMBER
                && JudgeUtils.equals(genericRspDTO.getMsgCd(), Constants.WECHAT_ORDER_MSGCD)
                && sendTimeFirst.plusSeconds(1).compareTo(responseTime) >= 0){
            logger.info("wechatOrderFail"+count +"JSAPI");
            count++;
            genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            responseTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
            if (JudgeUtils.isSuccess(genericRspDTO)) {
                logger.info("wechatunifiedorderSuccessJSAPI");
                break;
            }
        }
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(payOrderBO, (WXPayUnifiedorderResponse) result));
    }

    @Override
    public void send(PayOrderBO payOrderBO) {
        unifiedOrderJSAPI(payOrderBO);
    }

    private void handleResult(PayOrderBO payOrderBO, WXPayUnifiedorderResponse response) {
        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                payOrderBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
                payOrderBO.setPrepayId(response.getPrepayId());

                // 生成JSAPI页面调用的支付参数并签名
                generateJSAPISign(payOrderBO);
            } else {
                logger.error("unified order failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                payOrderBO.setErrMsgCd(response.getErrCode());
                payOrderBO.setErrMsgInfo(response.getErrCodeDes());
                WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getErrCode());
                AlertCapable alertCapable;
                switch (errorCodeEnum) {
                    case NOAUTH:
                        alertCapable = MsgCodeEnum.WECHAT_NO_AUTH;
                        break;
                    case NOTENOUGH:
                        alertCapable = MsgCodeEnum.WECHAT_NOT_ENOUGH;
                        break;
                    case ORDERPAID:
                        alertCapable = MsgCodeEnum.WECHAT_ORDER_PAID;
                        break;
                    case ORDERCLOSED:
                        alertCapable = MsgCodeEnum.WECHAT_ORDER_CLOSED;
                        break;
                    case OUT_TRADE_NO_USED:
                        alertCapable = MsgCodeEnum.WECHAT_OUT_TRADE_NO_USED;
                        break;
                    default:
                        alertCapable = MsgCodeEnum.WECHAT_UNIFIED_ORDER_FAILURE;
                        break;
                }
                BusinessException.throwBusinessException(alertCapable);
            }
        } else {
            logger.error("unified order failure, weChat return message is : {}", response.getReturnMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_UNIFIED_ORDER_FAILURE);
        }
    }

    /**
     * check out {//pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=7_7&index=6}.
     *
     * @param payOrderBO
     */
    private void generateJSAPISign(PayOrderBO payOrderBO) {
        WXPayJsapiSignRequest wxPayJsapiSignRequest = new WXPayJsapiSignRequest();
        wxPayJsapiSignRequest.setFrontPackage(String.join("=", "prepay_id", payOrderBO.getPrepayId()));
        wxPayJsapiSignRequest.setAppid(payOrderBO.getAppId());
        wxPayJsapiSignRequest.setKey(payOrderBO.getContractSecureValue());
        wxPayJsapiSignRequest.setMchId(payOrderBO.getPaymentId());


        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.JSAPI_SIGN.getSource());
        request.setRoute(WXPayChannel.JSAPI_SIGN.getRoute());
        request.setBusiType(WXPayChannel.JSAPI_SIGN.getBusType());
        request.setTarget(wxPayJsapiSignRequest);

        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> {
                    WXPayJsapiSignResponse response = (WXPayJsapiSignResponse) result;
                    payOrderBO.setWechatAppId(response.getAppid());
                    payOrderBO.setWechatTmStamp(response.getTimeStamp());
                    payOrderBO.setNonceStr(response.getNonceStr());
                    payOrderBO.setWechatPackage(response.getFrontPackage());
                    payOrderBO.setWechatSignType(response.getSignType());
                    payOrderBO.setWechatSign(response.getSign());
                });
    }
}
