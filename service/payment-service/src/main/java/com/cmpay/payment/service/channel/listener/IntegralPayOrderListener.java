package com.cmpay.payment.service.channel.listener;

import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.IntegralPayOrderBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.integralpay.IntegralPayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created on 2021/08/31
 * 积分支付监听类
 *
 * @author: luo wenzhan
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class IntegralPayOrderListener extends PaymentListenerService<IntegralPayOrderBO> {
	@Autowired
    ApplicationContext applicationContext;

	@Override
	protected ApplicationContext getApplicationContext() {
		return applicationContext;
	}

	@EventListener
	@Override
	public void execute(IntegralPayOrderBO integralPayOrderBO) {
		super.execute(integralPayOrderBO);
	}
	@Override
	protected boolean checkChannelExecutable(IntegralPayOrderBO integralPayOrderBO) {
		return integralPayOrderBO != null && integralPayOrderBO.getPayWay() != null &&
				StringUtils.equalsIgnoreCase(integralPayOrderBO.getPayWay(), PaymentWayEnum.INTEGRALPAY.name());
	}

	@Override
	protected PaymentChannelService determinateChannelExecuteBean(IntegralPayOrderBO integralPayOrderBO) {
		return getBean(IntegralPayService.class);
	}
}
