package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.config.ContractDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.wechat.WeChatRefundService;
import com.cmpay.payment.service.ext.config.IExtContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
public class WeChatRefundListener extends PaymentListenerService<RefundBO> {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    private IExtContractService contractService;

    /**
     * 微信H5支付申请退款
     *
     * @param refundBO
     */
    @EventListener
    public void handleWeChatRefund(RefundBO refundBO) {
        execute(refundBO);
    }

    @Override
    protected boolean checkChannelExecutable(RefundBO refundBO) {
        return Optional.ofNullable(refundBO)
                .map(RefundBO::getOriginalPayTradeOrder)
                .filter(tradeOrder -> StringUtils.equals(PaymentWayEnum.WECHAT.name().toLowerCase(), tradeOrder.getAimProductCode()))
                .isPresent();
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(RefundBO refundBO) {
        ContractDO contract = contractService.getContract(refundBO.getOriginalPayTradeOrder().getBankMerchantNo());
        refundBO.setContractSecureValue(contract.getSecretKey());
        return getBean(WeChatRefundService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
