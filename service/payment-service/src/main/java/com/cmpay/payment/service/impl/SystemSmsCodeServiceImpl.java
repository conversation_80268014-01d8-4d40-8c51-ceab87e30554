package com.cmpay.payment.service.impl;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.exception.ErrorMsgCode;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.cache.CacheConstants;
import com.cmpay.lemon.framework.cache.redis.RedisCachePut;
import com.cmpay.lemon.framework.cache.redis.RedisCacheable;
import com.cmpay.payment.bo.UserLoginBO;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.SMSConstants;
import com.cmpay.payment.service.SystemSmsCodeService;
import com.cmpay.payment.utils.JhIdGenUtils;
import com.cmpay.sms.gw.client.GeneralSmsDispatcherGwClient;
import com.cmpay.sms.gw.dto.SendMessageReqDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date ：Created in 2021/1/20 15:12
 * @description ：下发短信验证码实现类
 */
@Service
public class SystemSmsCodeServiceImpl implements SystemSmsCodeService {

    private static final Logger logger = LoggerFactory.getLogger(SystemSmsCodeServiceImpl.class);

    @Autowired
    private GeneralSmsDispatcherGwClient smsDispatcherGwClient;
    @Value("${sms-verification.application_id:}")
    private String applicationId;
    @Value("${sms-verification.template_id:}")
    private String templateId;

    @Override
    @RedisCachePut(cacheNames = "SMS", key = "'CODE:'+#userLoginBO.mobileNo+#userLoginBO.flag+'messageCode'")
    public UserLoginBO lssueVerificationCode(UserLoginBO userLoginBO) {

        SecureRandom random;
        String messageCode = "";
        try {
            random = SecureRandom.getInstanceStrong();
            int randNum = random.nextInt(1000000);
            messageCode = StringUtils.leftPad(Integer.toString(randNum), 6, "0");
        } catch (NoSuchAlgorithmException e) {
            BusinessException.throwBusinessException(ErrorMsgCode.SYS_ERROR);
        }

        LocalDateTime localDateTime = DateTimeUtils.getCurrentLocalDateTime();
        userLoginBO.setMessageCode(messageCode);
        SendMessageReqDTO smsReqDTO = getSmsCodeInfo(userLoginBO);
        smsReqDTO.setMobileNo(userLoginBO.getMobileNo());
        smsReqDTO.setSmsSendSerialNumber(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.SMS_JRN_NO, 18));
        smsReqDTO.setApplicationId(applicationId);
        smsReqDTO.setSmsChannel(SMSConstants.SMS_CHANNEL);
        logger.info("request info:{}", smsReqDTO.toString());
        GenericRspDTO rspDTO = smsDispatcherGwClient.sendMessage(smsReqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            BusinessException.throwBusinessException(rspDTO.getMsgCd());
        }
        logger.info("response info:{}", rspDTO.toString());
        userLoginBO.setMobileNo(userLoginBO.getMobileNo());
        userLoginBO.setMessageCode(messageCode);
        userLoginBO.setEffectTime(localDateTime.plusMinutes(3).toString());
        return userLoginBO;
    }


    @Override
    @RedisCacheable(cacheNames = "SMS", key = "'CODE:'+#userLoginBO.mobileNo+#userLoginBO.flag+'messageCode'")
    public UserLoginBO checkIdentifyCode(UserLoginBO userLoginBO) {

        return userLoginBO;
    }

    @Override
    @CacheEvict(cacheNames = "SMS", key = "'CODE:'+#userLoginBO.mobileNo+#userLoginBO.flag+'messageCode'", cacheResolver = CacheConstants.CACHE_RESOLVER_REDIS)
    public void deleteIdentifyCode(UserLoginBO userLoginBO) {
    }

    private SendMessageReqDTO getSmsCodeInfo(UserLoginBO userLoginBO) {
        SendMessageReqDTO sendSmsReqDTO = new SendMessageReqDTO();
        switch (userLoginBO.getFlag().toUpperCase(Locale.ENGLISH)) {
            case "APP_DOWNLOAD":
                sendSmsReqDTO.setReplaceValues(userLoginBO.getMessageCode().concat(SMSConstants.PARAMETRIC_DIVIDER));
                sendSmsReqDTO.setSmsCode(templateId);
                break;
            default:
        }
        return sendSmsReqDTO;
    }

    @Override
    public boolean checkSmsCode(UserLoginBO userLoginBO, String smsCode) {
        LocalDateTime localDateTime = DateTimeUtils.getCurrentLocalDateTime();
        if (!JudgeUtils.isBlank(userLoginBO.getEffectTime())) {
            if (localDateTime.isAfter(LocalDateTime.parse(userLoginBO.getEffectTime()))) {
                deleteIdentifyCode(userLoginBO);
                return false;
            }
            if (JudgeUtils.notEquals(smsCode, userLoginBO.getMessageCode())) {
                return false;
            }
        } else {
            return false;
        }
        deleteIdentifyCode(userLoginBO);
        return true;
    }
}

