package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.AppEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.dao.config.ITrdContractDao;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.entity.config.TrdContractDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/5/11
 */
@Service
public abstract class AbstractDcepRequestServiceImpl {

    @Autowired
    protected IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private ITrdContractDao trdContractDao;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;


    protected void buildDcepRequest(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO, String requestBusiType) {
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(DcepPaymentChannelEnum.PAYMENT);
        request.setRoute(DcepPaymentChannelEnum.DCEP_PAYMENT);
        request.setBusiType(requestBusiType);
        request.setTarget(abstractDcepReq);
        GenericDTO<Response> responseGenericDTO ;
        if (StringUtils.equalsAny(baseDcepHandlerBO.getSourceApp(), AppEnum.integrationbatch.name(), AppEnum.integrationshecdule.name())) {
            responseGenericDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        } else {
            responseGenericDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        }
        Optional.ofNullable(responseGenericDTO)
                .map(GenericDTO::getBody)
                .map(Response::getResult)
                .map(result -> handlerDcepResponse((AbstractDcepRsp) result, baseDcepHandlerBO))
                .orElseThrow(() -> new BusinessException(MsgCodeEnum.REQUEST_TIME_OUT));
    }

    /**
     * 处理数字货币响应参数
     *
     * @param abstractDcepRsp
     * @param baseDcepHandlerBO
     * @return
     */
    protected abstract BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO);

    /**
     * 构建公共请求参数
     *
     * @param abstractDcepReq
     * @param baseDcepHandlerBO
     */
    protected void buildRequestDto(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO, String merchantNumber) {
        TrdContractDO trdContractDO = trdContractDao.getContract(merchantNumber);
        if (JudgeUtils.isNull(trdContractDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ROUT_CANNOT_VALIDITY);
        }
        abstractDcepReq.setChannelNo(trdContractDO.getMerchantNumber());
        abstractDcepReq.setSmxKey(trdContractDO.getSecretKey());
        abstractDcepReq.setSignType(trdContractDO.getSignMethod());
        abstractDcepReq.setVersion(DcepConstants.VERSION);
        if (!JudgeUtils.equalsAny(baseDcepHandlerBO.getPayWayCode(), PaymentSceneEnum.TOKEN.name().toLowerCase())) {
            specificBuildRequestReq(abstractDcepReq, baseDcepHandlerBO);
        }
    }

    /**
     * 特殊场景特殊参数构建
     *
     * @param abstractDcepReq
     * @param baseDcepHandlerBO
     */
    protected abstract void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO);
}
