package com.cmpay.payment.service.channel.aplipay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.dto.alipay.TradePagePayReq;
import com.cmpay.payment.dto.alipay.TradePagePayRsp;
import com.cmpay.payment.entity.AlipayPaymentJrnDO;
import com.cmpay.payment.properties.AlipayProperties;
import com.cmpay.payment.service.channel.aplipay.AlipayWebPaymentChannelService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.PaymentUtils;
import com.cmpay.payment.util.UrlUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;

/**
 * Created on 2018/12/13
 *
 * @author: wulinfeng
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class AlipayWebPaymentChannelServiceImpl extends AlipayBasePaymentChannelServiceImpl implements AlipayWebPaymentChannelService {

    Logger logger = LoggerFactory.getLogger(AlipayWebPaymentChannelServiceImpl.class);
    @Autowired
    AlipayProperties alipayProperties;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Override
    public void alipayWebPayment(PayOrderBO payOrderBO) {
        super.alipayBasePayment(payOrderBO);
    }

    @Override
    public boolean getStatus(PayOrderBO payOrderBO) {
        TradePagePayReq wapPayReq = new TradePagePayReq();
        wapPayReq.setOutTradeNo(payOrderBO.getTradeOrderNo());
        wapPayReq.setSubject(payOrderBO.getSubject());
        wapPayReq.setTotalAmount(payOrderBO.getRealAmount().toString());
        wapPayReq.setProductCode("FAST_INSTANT_TRADE_PAY");

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                wapPayReq.setNotifyUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getNotifyUrl(),host));
                wapPayReq.setReturnUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getReturnUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                wapPayReq.setNotifyUrl(alipayProperties.getNotifyUrl());
                wapPayReq.setReturnUrl(alipayProperties.getReturnUrl());
            }
        } else {
            wapPayReq.setNotifyUrl(alipayProperties.getNotifyUrl());
            wapPayReq.setReturnUrl(alipayProperties.getReturnUrl());
        }

        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            Date expireTime=simpleDateFormat.parse(payOrderBO.getExpireTime());
            wapPayReq.setTimeExpire(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(expireTime));
        } catch (ParseException e) {
            logger.info(e.getMessage());
        }
        if(StringUtils.isNotBlank(payOrderBO.getTimeoutExpress())){
            wapPayReq.setTimeoutExpress(payOrderBO.getTimeoutExpress());
        }
        try{
            GenericRspDTO<Response> response = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_PAGE_PAY.getName(), wapPayReq));

            Response body=response.getBody();
            Optional.ofNullable(body)
                    .ifPresent(payOrderBO::setAlipayPayRsp);

            if(StringUtils.contains(response.getMsgCd(),"00000")){

                Response rsp = payOrderBO.getAlipayPayRsp();

                //处理流水表
                AlipayPaymentJrnDO alipayPaymentJrnDO = new AlipayPaymentJrnDO();

                Optional.ofNullable(rsp)
                        .map(Response::getMsgCode)
                        .ifPresent(alipayPaymentJrnDO::setReturnMsg);

                Optional.ofNullable(rsp)
                        .map(Response::getMsgInfo)
                        .ifPresent(alipayPaymentJrnDO::setReturnMsgInfo);

                alipayPaymentJrnDO.setTradeOrderNo(payOrderBO.getTradeOrderNo());

                Optional.ofNullable(rsp)
                        .map(Response::getResult)
                        .map(result->{return ((TradePagePayRsp) result).getBody();})
                        .ifPresent(b->{payOrderBO.setPayUrl((String) b);});

                payOrderBO.setForm(((TradePagePayRsp) rsp.getResult()).getForm());
                payOrderBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
                return true;
            }
        }catch (Exception e){
            return false;
        }
        return false;
    }

    @Override
    public void send(PayOrderBO payOrderBO) {
        alipayWebPayment(payOrderBO);
    }

}
