package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.exception.ErrorMsgCode;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import com.cmpay.payment.bo.*;
import com.cmpay.payment.bo.config.SubMerchantRefreshAmountBO;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.dao.IJkThirdPayParamExtDao;
import com.cmpay.payment.entity.JkThirdPayParamDO;
import com.cmpay.payment.entity.OrderPaymentAttachDO;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.*;
import com.cmpay.payment.service.ext.ExtPayOrderAttachService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.ExtRefundOrderService;
import com.cmpay.payment.service.ext.ExtThirdPayParamService;
import com.cmpay.payment.utils.CloneableUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Created on 2018/11/30
 *
 * @author: sun_zhh
 */

@Transactional(propagation = Propagation.SUPPORTS)
@Service
public class RefundServcieImpl implements RefundServcie {
    private static final DateTimeFormatter COMMON_YEAR_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final Logger logger = LoggerFactory.getLogger(RefundServcieImpl.class);
    private static final String PRI_FIX = "REF";
    @Autowired
    private IJkThirdPayParamExtDao jkThirdPayParamExtDao;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private ExtRefundOrderService refundOrderService;
    @Autowired
    private ExtPayOrderAttachService payOrderAttachService;
    @Autowired
    private SensorsDataService sensorsDataService;
    @Autowired
    private SubMerchantIncomeService redisService;
    @Autowired
    private TradeNotifyService tradeNotifyService;

    @Autowired
    private CompleteOrderSplitService splitService;
    @Autowired
    private ExtThirdPayParamService thirdPayParamService;

    @Autowired
    private SubOrderInfosResolveService resolveService;
    @Autowired
    private AsynCommonService asynCommonService;

    @Override
    @DistributedLocked(lockName = "'OrderRefund:'+#refundParam.getMerchantId()+#refundParam.getOutTradeNo()", leaseTime = 60, waitTime = 1, ignoreUnableToAcquiredLockException = false)
    public RefundBO refund(RefundBO refundParam) {
        LocalDateTime refundStartTime = DateTimeUtils.getCurrentLocalDateTime();
        // 驾驶舱埋点建设，退款请求埋点(接口)
        sensorsDataService.functionRequest(CommonConstant.REFUND);
        TradeOrderDO originalPayTradeOrderDO = null;
        BigDecimal totalRefundMoney = null;
        BigDecimal totalRefundFeeAmount = null;
        String payOrderStatus = null;
        RefundOrderDO refundOrderDO = null;
        try {
            // 检查传入参数
            if (JudgeUtils.isNull(refundParam.getMerchantId())) {
                BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NO_CANNOT_BE_EMPTY);
            }
            if (JudgeUtils.isNull(refundParam.getOutRequestNo())) {
                BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ORDER_NO_CANNOT_BE_EMPTY);
            }
            if (JudgeUtils.isNull(refundParam.getTradeDate())) {
                BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_REQUEST_DATE_CANNOT_BE_EMPTY);
            }
            if (JudgeUtils.isNull(refundParam.getOutTradeNo())) {
                BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ORDER_NO_CANNOT_BE_EMPTY);
            }
            refundParam.setChannelType(ChannelTypeEnum.INTERFACE.name().toLowerCase());
            refundParam.setChannelTypeDesc(ChannelTypeEnum.INTERFACE.getDesc());

            refundParam.setMsgCode(MsgCodeEnum.REFUND_ERROR.getMsgCd());
            //检查退款单号是否存在
            RefundOrderDO refundOrderQueryParam = new RefundOrderDO();
            refundOrderQueryParam.setMerchantNo(refundParam.getMerchantId());
            refundOrderQueryParam.setOutTradeNo(refundParam.getOutRequestNo());
            RefundOrderDO refundOrderQuery = refundOrderService.load(refundOrderQueryParam);
            if (JudgeUtils.isNotNull(refundOrderQuery)) {
                //如果存在说明该款单号已发起过退款
                BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_ALREADY_EXIST);
            }
            //查询原支付订单
            TradeOrderDO payOrderQuery = new TradeOrderDO();
            payOrderQuery.setMerchantNo(refundParam.getMerchantId());
            payOrderQuery.setOutTradeNo(refundParam.getOutTradeNo());
            originalPayTradeOrderDO = payOrderService.load(payOrderQuery);
            // 校验是否满足退款条件
            checkRefundReq(originalPayTradeOrderDO, refundParam);
            if (StringUtils.equals(originalPayTradeOrderDO.getAimProductCode(), PaymentWayEnum.ICBCPAY.name().toLowerCase())) {
                refundParam.setTradeOrderNo(JhIdGenUtils.generateJhIdWithDate(DcepConstants.PAYMENT_REFUND, PRI_FIX, 15));
            }
            // 此退款接口不支持分账退款
            if (StringUtils.isNotEmpty(originalPayTradeOrderDO.getSplitFlag()) && StringUtils.equals(originalPayTradeOrderDO.getSplitFlag(), Constants.Y)) {
                BusinessException.throwBusinessException(MsgCodeEnum.ORDER_TYPE_IS_NOT_SUPPORTED);
            }

            //检测是否超过退款时长
            JkThirdPayParamDO refundOrderParamDO = new JkThirdPayParamDO();

            refundOrderParamDO.setAimProductCode(originalPayTradeOrderDO.getAimProductCode());
            refundOrderParamDO.setPayProductCode(originalPayTradeOrderDO.getPayProductCode());
            refundOrderParamDO.setPayWayCode(originalPayTradeOrderDO.getPayWayCode());
            refundOrderParamDO.setParamMark(ThirdPayWayParamEnum.REFUND_PARAM.name());
            JkThirdPayParamDO refund = jkThirdPayParamExtDao.findRecord(refundOrderParamDO);
            if (JudgeUtils.isNotNull(refund)) {
                logger.info("退款时长检测===");
                String orderDate = originalPayTradeOrderDO.getOrderDate();
                Long refundValue = Long.parseLong(refund.getParamValue());
                String nowDate = refundParam.getTradeDate();
                String afterDate = plusDays(orderDate, refundValue);
                logger.info("======================afterDate is {}=========================", afterDate);
                if (nowDate.compareTo(afterDate) > 0) {
                    BusinessException.throwBusinessException(MsgCodeEnum.ORDER_REFUND_PARAM_TIME_OUT);
                }
            }
            //检查通过后开始锁表，进行后面的操作
            try {
                payOrderService.lock(originalPayTradeOrderDO);
            } catch (Exception e) {
                BusinessException.throwBusinessException(MsgCodeEnum.ORDER_EXIST_UNFINISHED_REDUND);
            }
            //保存订单记录到参数对像里，以便后面使用
            refundParam.setOriginalPayTradeOrder(CloneableUtils.gsonDeepClone(originalPayTradeOrderDO, TradeOrderCloneBO.class));
            //保存订单附加表信息
            if (DcepConstants.DCEP.equals(originalPayTradeOrderDO.getDcepFlag()) ||
                    PaymentWayEnum.ICBCPAY.name().equalsIgnoreCase(originalPayTradeOrderDO.getPayProductCode())) {
                OrderPaymentAttachDO attachDO = new OrderPaymentAttachDO();
                attachDO.setOutTradeNo(originalPayTradeOrderDO.getOutTradeNo());
                attachDO.setOrderDate(originalPayTradeOrderDO.getRequestDate());
                OrderPaymentAttachDO orderPaymentAttachDO = payOrderAttachService.load(attachDO);
                refundParam.setOrderPaymentAttachDO(orderPaymentAttachDO);
                refundParam.setDcepFlag(originalPayTradeOrderDO.getDcepFlag());
            }

            //原支付订单状态不为支付成功并且不为部分退款，不允许退款
            if (!OrderStatusEnum.TRADE_SUCCESS.name().equals(originalPayTradeOrderDO.getStatus())
                    && !OrderStatusEnum.REFUND_PART.name().equals(originalPayTradeOrderDO.getStatus())
                    && !OrderStatusEnum.REFUND_WAIT.name().equals(originalPayTradeOrderDO.getStatus())) {
                BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_STATUS_CANNOT_REFUND);
            }
            //检查当前已登记退款金额
            RefundOrderDO refundOrder = new RefundOrderDO();
            refundOrder.setMerchantNo(refundParam.getMerchantId());
            refundOrder.setOrgOrderNo(refundParam.getOutTradeNo());
            refundOrder.setOrderDate(originalPayTradeOrderDO.getOrderDate());
            refundOrder.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            List<RefundOrderDO> refundOrderList = refundOrderService.refundOrderList(refundOrder);
            if (refundOrderList != null) {
                {
                    //计算退款已登记总金额
                    BigDecimal refundRegistMoney = refundOrderList.stream().map(RefundOrderDO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal refundMoney = refundRegistMoney.add(refundParam.getRefundAmount());
                    if (refundMoney.compareTo(originalPayTradeOrderDO.getRealAmount()) > 0) {
                        BusinessException.throwBusinessException(MsgCodeEnum.REFUND_AMOUNT_ERROR);
                    }
                }
            }
            if (refundParam.getRefundAmount().compareTo(originalPayTradeOrderDO.getRealAmount()) == 0) {
                //如退款金额等于订单金科则为全额退款
                refundParam.setRefundFlag("0");
            } else {
                BigDecimal settlement = originalPayTradeOrderDO.getInstDiscountSettlementAmount();
                BigDecimal unsettled = originalPayTradeOrderDO.getInstDiscountUnsettledAmount();
                if (settlement.add(unsettled).compareTo(BigDecimal.ZERO) > 0) {
                    BusinessException.throwBusinessException(MsgCodeEnum.ORDER_REFUND_NOT_PART);
                }
                //否则为部分退款
                refundParam.setRefundFlag("1");
                // 检测是否存在退款未完成订单订单
                checkRefundOrderStatus(refundOrderList);
                refundFrequencyLimit(originalPayTradeOrderDO, refundParam);
            }
            RefundOrderDO refundOrderLsQuery = new RefundOrderDO();
            refundOrderLsQuery.setMerchantNo(refundParam.getMerchantId());
            refundOrderLsQuery.setOrgOrderNo(refundParam.getOutTradeNo());
            refundOrderLsQuery.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            List<RefundOrderDO> refundOrderLs = refundOrderService.find(refundOrderLsQuery);
            totalRefundMoney = BigDecimal.ZERO;
            totalRefundFeeAmount = BigDecimal.ZERO;
            if (refundOrderLs != null) {
                //计算退款总金额
                totalRefundMoney = refundOrderLs.stream().map(RefundOrderDO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                totalRefundFeeAmount = refundOrderLs.stream().map(RefundOrderDO::getOrderFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            }
            // 本次退款总金额 =已退款金额+本次退款金额
            BigDecimal totalRefundAmountCurrent = totalRefundMoney.add(refundParam.getRefundAmount());
            payOrderStatus = originalPayTradeOrderDO.getStatus();
            if (totalRefundAmountCurrent.compareTo(originalPayTradeOrderDO.getRealAmount()) > 0) {
                BusinessException.throwBusinessException(MsgCodeEnum.REFUND_AMOUNT_ERROR);
            } else if (totalRefundAmountCurrent.compareTo(originalPayTradeOrderDO.getRealAmount()) == 0) {
                // 若退款金额等于支付订单金额则全额退款
                payOrderStatus = OrderStatusEnum.REFUND_SUCCESS.name();
            } else {
                // 若退款金额小于支付订单金额则部分退款
                payOrderStatus = OrderStatusEnum.REFUND_PART.name();
            }
            // 包含子商户号的订单，进行退款总金额限制
            if (StringUtils.isNotEmpty(originalPayTradeOrderDO.getSubMerchantNo())) {
                if (!StringUtils.equals(originalPayTradeOrderDO.getAimProductCode(), PaymentWayEnum.CMPAY.name().toLowerCase())) {
                    SubMerchantRefreshAmountBO refreshAmountBO = new SubMerchantRefreshAmountBO();
                    refreshAmountBO.setSubMerchantNo(originalPayTradeOrderDO.getSubMerchantNo());
                    refreshAmountBO.setAmount(refundParam.getRefundAmount());
                    redisService.refundConfine(refreshAmountBO);
                }
            }
            //创建退款实例
            refundOrderDO = refundOrderService.insert(refundParam, originalPayTradeOrderDO);
            //保存退款订单记录到参数对像里，以便后面使用
            refundParam.setRefundOrder(refundOrderDO);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                sensorsDataService.functionDelay(CommonConstant.REFUND, refundStartTime);
                sensorsDataService.requestSuccess(CommonConstant.REFUND);
            }
//            系统异常不算请求成功
            throw e;
        }
        // 驾驶舱建设埋点,接口调用时延
        sensorsDataService.functionDelay(CommonConstant.REFUND, refundStartTime);
        sensorsDataService.requestSuccess(CommonConstant.REFUND);
        try {
            //和包退款先登记再发往
            if (JudgeUtils.equals(originalPayTradeOrderDO.getAimProductCode(), PaymentWayEnum.CMPAY.name().toLowerCase())) {
                // 退款订单状态修改为退款待处理
                refundOrderDO.setStatus(OrderStatusEnum.REFUND_PEND.name());
                // 更新退款订单
                refundOrderService.update(refundOrderDO);
                refundParam.setMsgCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
                return refundParam;
            }
            refundParam.setSourceApp(AppEnum.integrationpaymnet.name());
            applicationContext.publishEvent(refundParam);
            // 是否异步退款
            if (refundParam.isAsync()) {
                // 原支付订单设置状态为REFUND_WAIT
                originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
                // 更新支付订单
                payOrderService.update(originalPayTradeOrderDO);
                refundParam.setMsgCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            }

        } catch (Exception e) {
            if (e instanceof BusinessException) {
                //记录业务抛出的错误码 及错误信息
                refundParam.setMsgCode(((BusinessException) e).getMsgCd());
                // 退款订单状态修改为退款失败
                refundOrderDO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
                //修改退款时间
                refundOrderDO.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                refundOrderDO.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
                //更新报错信息
                refundOrderDO.setErrMsgCd(refundParam.getErrMsgCd());
                refundOrderDO.setErrMsgInfo(refundParam.getErrMsgInfo());
                // 更新退款订单
                refundOrderService.update(refundOrderDO);
                // 调用退款通知处理
                RefundOrderCloneBO refundOrderCloneBO = new RefundOrderCloneBO();
                // 明确退款失败，且退款请求日期是今天，那么自增回去
                splitService.refundFailIncrement(refundOrderDO, originalPayTradeOrderDO);
                BeanUtils.copyProperties(refundOrderDO, refundOrderCloneBO);
                tradeNotifyService.handleRefundNotify(refundOrderCloneBO);
            } else {
                //如果是未知的系统错误，比如网络请求异常  原支付订单状态修改为退款中
                originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
            }
        } finally {
            // 更新返回值
            refundParam.setTotalAmount(originalPayTradeOrderDO.getOrderAmount());
        }
        return refundParam;
    }

    @Override
    public void checkRefundReq(TradeOrderDO originalPayTradeOrderDO, RefundBO refundParam) {
        if (JudgeUtils.isNull(originalPayTradeOrderDO)) {
            //如果原支付订单不存在，则不能发起退款
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORG_ORDER_NOT_EXISTS);
        }
        if (originalPayTradeOrderDO.getRealAmount().compareTo(refundParam.getRefundAmount()) != 0
                && JudgeUtils.equals(PaymentWayEnum.CMPAY.name().toLowerCase(), originalPayTradeOrderDO.getPayProductCode())
                && JudgeUtils.equals(PaymentSceneEnum.CONTRACTPAY.name().toLowerCase(), originalPayTradeOrderDO.getPayWayCode())) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_NOT_PART);
        }
        // 如果是积分退款，积分商品商户号不能为空
        if (JudgeUtils.equals(PaymentWayEnum.INTEGRALPAY.name().toLowerCase(), originalPayTradeOrderDO.getAimProductCode())) {
            if (JudgeUtils.isBlank(refundParam.getIntegralMercNo())) {
                BusinessException.throwBusinessException(MsgCodeEnum.INTEGRAL_MERCNO_BE_EMPTY);
            } else {
                originalPayTradeOrderDO.setBankMerchantNo(refundParam.getIntegralMercNo());
            }
            // 积分不支持部分退款
            if (originalPayTradeOrderDO.getRealAmount().compareTo(refundParam.getRefundAmount()) != 0) {
                BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
            }
        }
    }

    @Override
    public RefundOrderBO refundOrder(RefundOrderBO refundParam) {
        refundParam.setChannelType(ChannelTypeEnum.INTERFACE.name().toLowerCase());
        refundParam.setChannelTypeDesc(ChannelTypeEnum.INTERFACE.getDesc());

        //创建流水实例
//        TradeRecordDO merchantJournalInfoDO = merchantJournalInfoService.insertByNewTranscation(refundParam);
        //查询原支付订单
        TradeOrderDO payOrderQuery = new TradeOrderDO();
        payOrderQuery.setMerchantNo(refundParam.getMerchantId());
        payOrderQuery.setOutTradeNo(refundParam.getOutTradeNo());
        TradeOrderDO originalPayTradeOrderDO = payOrderService.load(payOrderQuery);
        if (originalPayTradeOrderDO == null) {
            //如果原支付订单不存在，则不能发起退款
            refundParam.setReturnMsg(MsgCodeEnum.REFUND_ORG_ORDER_NOT_EXISTS.getMsgCd());
            refundParam.setReturnMsgInfo(MsgCodeEnum.REFUND_ORG_ORDER_NOT_EXISTS.getMsgInfo());
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORG_ORDER_NOT_EXISTS);
        }
        //检查通过后开始锁表，进行后面的操作
        payOrderService.lock(originalPayTradeOrderDO);
        //保存订单记录到参数对像里，以便后面使用
        refundParam.setOriginalPayTradeOrder(CloneableUtils.clone(originalPayTradeOrderDO, TradeOrderCloneBO.class));
        BigDecimal totalRefundMoney = BigDecimal.ZERO;
        // 原支付订单状态不为支付成功并且不为部分退款，不允许退款
        if (!OrderStatusEnum.REFUND_PART.name().equals(originalPayTradeOrderDO.getStatus())) {
            RefundOrderDO refundOrderLsQuery = new RefundOrderDO();
            refundOrderLsQuery.setMerchantNo(refundParam.getMerchantId());
            refundOrderLsQuery.setOrgOrderNo(refundParam.getOutTradeNo());
            refundOrderLsQuery.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            List<RefundOrderDO> refundOrderLs = refundOrderService.find(refundOrderLsQuery);
            if (refundOrderLs != null) {
                //计算退款总金额
                totalRefundMoney = refundOrderLs.stream().map(RefundOrderDO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            }
        } else {
            // 新增商户请求流水
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_STATUS_CANNOT_REFUND);
        }
        // 本次退款总金额 =已退款金额+本次退款金额
        BigDecimal totalRefundAmountCurrent = totalRefundMoney.add(refundParam.getRefundAmount());
        String payOrderStatus = originalPayTradeOrderDO.getStatus();
        if (totalRefundAmountCurrent.compareTo(originalPayTradeOrderDO.getOrderAmount()) > 0) {
            // 新增商户请求流水
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_AMOUNT_ERROR);
        } else if (totalRefundAmountCurrent.compareTo(originalPayTradeOrderDO.getOrderAmount()) == 0) {
            // 若退款金额等于支付订单金额则全额退款
            payOrderStatus = OrderStatusEnum.REFUND_SUCCESS.name();
            refundParam.setRefundFlag("0");
        } else {
            // 若退款金额小于支付订单金额则部分退款
            payOrderStatus = OrderStatusEnum.REFUND_PART.name();
            refundParam.setRefundFlag("1");
        }
        //创建退款实例
        RefundOrderDO refundOrderDO = refundOrderService.insert(refundParam, originalPayTradeOrderDO);
        //保存退款订单记录到参数对像里，以便后面使用
        refundParam.setRefundOrder(refundOrderDO);
        try {

            applicationContext.publishEvent(refundParam);

            // 是否异步退款
            if (!refundParam.isAsync()) {

                // 退款订单状态修改为退款成功
                refundOrderDO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
                //修改退款时间
                refundOrderDO.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                // 更新退款订单
                refundOrderService.update(refundOrderDO);
                //修改退款次数
                originalPayTradeOrderDO.setRefundTimes(originalPayTradeOrderDO.getRefundTimes() + 1);
                //修改退款金额
                originalPayTradeOrderDO.setSuccessRefundAmount(originalPayTradeOrderDO.getSuccessRefundAmount().add(refundParam.getRefundAmount()));
                // 原支付订单设置状态
                originalPayTradeOrderDO.setStatus(payOrderStatus);
                // 更新支付订单
                payOrderService.update(originalPayTradeOrderDO);
                refundParam.setReturnMsg(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
                refundParam.setReturnMsgInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
                // 登记通知
                registerNotify(refundOrderDO);
            } else {

                // 原支付订单设置状态为REFUND_WAIT
                originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
                // 更新支付订单
                payOrderService.update(originalPayTradeOrderDO);
                refundParam.setReturnMsg(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
                refundParam.setReturnMsgInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());

            }


        } catch (Exception e) {
            if (e instanceof BusinessException) {

                //记录业务抛出的错误码 及错误信息
                refundParam.setReturnMsg(((BusinessException) e).getMsgCd());
                refundParam.setReturnMsgInfo(((BusinessException) e).getMsgInfo());
                // 退款订单状态修改为退款失败
                refundOrderDO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
                //修改退款时间
                refundOrderDO.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                // 更新退款订单
                refundOrderService.update(refundOrderDO);
            } else {
                //如果是未知的系统错误，比如网络请求异常  原支付订单状态修改为退款中
                originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
                //设置错误码 及错误信息为系统异常
                refundParam.setReturnMsg(ErrorMsgCode.SYS_ERROR.getMsgCd());
                refundParam.setReturnMsgInfo(ErrorMsgCode.SYS_ERROR.getMsgInfo());
            }
        } finally {
            refundParam.setReturnDate(DateTimeUtils.getCurrentDateStr());
            refundParam.setReturnTime(DateTimeUtils.getCurrentTimeStr());
            // 更新返回值
            refundParam.setTotalAmount(originalPayTradeOrderDO.getOrderAmount());
            refundParam.setMsgCode(refundParam.getReturnMsg());
        }
        return refundParam;
    }

    @Override
    public boolean refundFrequencyLimit(TradeOrderDO tradeOrderDO, RefundBO refundBO) {
        // 校验退款成功次数限制，超出最大次数，抛出异常
        boolean flag = refundSuccessFrequencyCheck(tradeOrderDO, refundBO);
        if (!flag) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_FREQUENCY_IS_LIMIT);
        }
        // 未达最大退款次数，返回true
        return true;
    }

    @Override
    public void checkRefundOrderStatus(List<RefundOrderDO> refundOrderList) {
        for (RefundOrderDO refundOrder : refundOrderList) {
            if (JudgeUtils.equalsAny(refundOrder.getStatus(),
                    OrderStatusEnum.REFUND_PEND.name(),
                    OrderStatusEnum.REFUND_WAIT.name())) {
                // 抛出异常
                BusinessException.throwBusinessException(MsgCodeEnum.ORDER_EXIST_UNFINISHED_REDUND);
            }
        }
    }


    /**
     * 退款成功次数限制校验
     *
     * @param tradeOrderDO
     * @param refundBO
     * @return true：通过校验，false：未通过校验
     */
    private boolean refundSuccessFrequencyCheck(TradeOrderDO tradeOrderDO, RefundBO refundBO) {
        // 获取第三方支付查询参数
        JkThirdPayParamBO thirdPayParam = getThirdPayParam(tradeOrderDO);
        // 查询退款成功次数限制参数
        thirdPayParam = thirdPayParamService.queryRefundSuccessFrequencyLimit(thirdPayParam);
        Long refundSuccessLimit = Long.valueOf(thirdPayParam.getParamValue());
        // 检查退款次数是否在允许的范围内
        return tradeOrderDO.getRefundTimes().compareTo(refundSuccessLimit) < 0;
    }

    /**
     * 根据订单信息获取查询第三方支付机构参数的条件对象
     *
     * @param tradeOrderDO
     * @return
     */
    private JkThirdPayParamBO getThirdPayParam(TradeOrderDO tradeOrderDO) {
        JkThirdPayParamBO jkThirdPayParamBO = new JkThirdPayParamBO();
        jkThirdPayParamBO.setAimProductCode(tradeOrderDO.getAimProductCode());
        jkThirdPayParamBO.setPayProductCode(tradeOrderDO.getPayProductCode());
        jkThirdPayParamBO.setPayWayCode(tradeOrderDO.getPayWayCode());
        return jkThirdPayParamBO;
    }

    /**
     * 登记通知
     *
     * @param refundOrderDO
     */
    private void registerNotify(RefundOrderDO refundOrderDO) {
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setMerchantNo(refundOrderDO.getMerchantNo());
        tradeNotifyBO.setTradeOrderNo(refundOrderDO.getTradeOrderNo());
        tradeNotifyBO.setOutOrderNo(refundOrderDO.getOrgOrderNo());
        tradeNotifyBO.setOutRequestNo(refundOrderDO.getOutTradeNo());
        tradeNotifyBO.setTradeDate(refundOrderDO.getRequestDate());
        tradeNotifyBO.setFinishDate(refundOrderDO.getOrderCompleteTime());
        tradeNotifyBO.setTradeAmount(refundOrderDO.getOrderAmount());
        tradeNotifyBO.setNotifyDate(DateTimeUtils.getCurrentDateStr());
        tradeNotifyBO.setNotifyTime(DateTimeUtils.getCurrentTimeStr());
        tradeNotifyBO.setNotifyType(TradeTypeEnum.REFUND.name().toLowerCase());
        tradeNotifyBO.setNotifyUrl(refundOrderDO.getNotifyUrl());
        tradeNotifyBO.setExtra(refundOrderDO.getRemark());
        tradeNotifyBO.setSecretIndex(refundOrderDO.getSecretIndex());
        asynCommonService.asyncNotify(tradeNotifyBO);
    }

    /**
     * 某个日期基础上,增加天数
     *
     * @param date
     * @param days
     * @return
     */
    private static String plusDays(String date, long days) {
        LocalDate localDate = LocalDate.parse(date, COMMON_YEAR_FORMAT);
        localDate = localDate.plusDays(days);
        return localDate.format(COMMON_YEAR_FORMAT);
    }
}
