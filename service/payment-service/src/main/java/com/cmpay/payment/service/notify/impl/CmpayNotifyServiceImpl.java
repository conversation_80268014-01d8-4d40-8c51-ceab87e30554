package com.cmpay.payment.service.notify.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.RefundNotifyBO;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.TradeOrderAndNotifyBO;
import com.cmpay.payment.bo.notify.AccountNotifyBO;
import com.cmpay.payment.bo.notify.CmpayNotifyBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.TradeNotifyService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.notify.ICmpayNotifyService;
import com.cmpay.payment.util.PaymentUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/14 13:09
 */
@Service
public class CmpayNotifyServiceImpl implements ICmpayNotifyService {
    @Autowired
    private ExtParamInfoService paramInfoService;
    @Autowired
    private AsynCommonService asynCommonService;
    @Autowired
    private TradeNotifyService tradeNotifyService;
    @Autowired
    private ExtPayOrderService payOrderService;

    @Override
    public void backendNotifyAccount(AccountNotifyBO accountNotifyBO) {
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setTradeOrderNo(accountNotifyBO.getMerchantOrderNo());
        tradeNotifyBO.setBnkTradeNo(accountNotifyBO.getOrderNo());
        tradeNotifyBO.setSplOrderNo(accountNotifyBO.getOrderNo());
        String paymentDateTime = accountNotifyBO.getPayDate();
        String accountDate = accountNotifyBO.getAccountDate();
        if (StringUtils.isBlank(paymentDateTime) || paymentDateTime.length() != 14) {
            paymentDateTime = DateTimeUtils.getCurrentDateTimeStr();
        }
        if (JudgeUtils.isEmpty(accountDate)) {
            accountDate = DateTimeUtils.getCurrentDateStr();
        }
        tradeNotifyBO.setFinishDate(paymentDateTime);
        tradeNotifyBO.setPaymentRout(PaymentWayEnum.CMPAY.name().toLowerCase());
        tradeNotifyBO.setAccountDate(accountDate);
        if (!StringUtils.equals(accountNotifyBO.getOrderStatus(), OrderStatusEnum.SUCCESS.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_NOTIFY_FAILURE);
        }
        // 记录支付金额明细
        tradeNotifyBO.setPayAmountList(PaymentUtils.setAmountInfoBOList(accountNotifyBO.getPayAmountList(), paramInfoService.getAmountListParam()));
        tradeNotifyBO.setInstPaidAmount(accountNotifyBO.getInstPaidAmount());
        tradeNotifyBO.setInstDiscountSettlementAmount(accountNotifyBO.getInstDiscountSettlementAmount());
        tradeNotifyBO.setInstDiscountUnsettledAmount(accountNotifyBO.getInstDiscountUnsettledAmount());
        if (JudgeUtils.isNull(tradeNotifyService.backendNotify(tradeNotifyBO))) {
            return;
        }
        //后台通知
        asynCommonService.asyncNotify(tradeNotifyBO);
    }

    @Override
    public void backendNotify(CmpayNotifyBO cmpayNotifyBO) {
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        BeanUtils.copyProperties(tradeNotifyBO, cmpayNotifyBO);
        String paymentDateTime = cmpayNotifyBO.getPayDate();
        String accountDate = cmpayNotifyBO.getAccountDate();
        tradeNotifyBO.setTradeOrderNo(cmpayNotifyBO.getOrderId());
        tradeNotifyBO.setUnionCardType(cmpayNotifyBO.getUnionCardType());
        tradeNotifyBO.setBnkTraceNo(cmpayNotifyBO.getBnkTraceNo());
        tradeNotifyBO.setBnkTradeNo(cmpayNotifyBO.getBnkTradeNo());
        tradeNotifyBO.setSplOrderNo(cmpayNotifyBO.getSplOrderNo());
        if (StringUtils.isBlank(paymentDateTime) || paymentDateTime.length() != 14) {
            paymentDateTime = DateTimeUtils.getCurrentDateTimeStr();
        }
        if (JudgeUtils.isEmpty(accountDate)) {
            accountDate = DateTimeUtils.getCurrentDateStr();
        }
        tradeNotifyBO.setFinishDate(paymentDateTime);
        tradeNotifyBO.setPaymentRout(PaymentWayEnum.CMPAY.name().toLowerCase());
        tradeNotifyBO.setAccountDate(accountDate);
        tradeNotifyBO.setUnionpayDiscountInfo(cmpayNotifyBO.getUnionSaleInfo());
        if (!StringUtils.equals(cmpayNotifyBO.getStatus(), OrderStatusEnum.SUCCESS.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_NOTIFY_FAILURE);
        }
        tradeNotifyBO.setPayAmountList(PaymentUtils.convertToAmountInfoList(tradeNotifyBO));
        TradeNotifyBO notifyBO = tradeNotifyService.backendNotify(tradeNotifyBO);
        //后台通知
        asynCommonService.asyncNotify(notifyBO);
    }

    @Override
    public void backendNotifyPos(CmpayNotifyBO cmpayNotifyBO) {
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setTradeOrderNo(cmpayNotifyBO.getOrderId());
        tradeNotifyBO.setUnionCardType(cmpayNotifyBO.getUnionCardType());
        tradeNotifyBO.setBnkTraceNo(cmpayNotifyBO.getBnkTraceNo());
        tradeNotifyBO.setBnkTradeNo(cmpayNotifyBO.getBnkTradeNo());
        tradeNotifyBO.setSplOrderNo(cmpayNotifyBO.getSplOrderNo());
        String paymentDateTime = cmpayNotifyBO.getUsrpaydt() + cmpayNotifyBO.getUsrpaytm();
        if (JudgeUtils.isEmpty(paymentDateTime)) {
            paymentDateTime = DateTimeUtils.getCurrentDateTimeStr();
        }
        String accountDate = cmpayNotifyBO.getAccountDate();
        if (JudgeUtils.isEmpty(accountDate)) {
            accountDate = DateTimeUtils.getCurrentDateStr();
        }
        if (!StringUtils.equals(cmpayNotifyBO.getStatus(), OrderStatusEnum.SUCCESS.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_NOTIFY_FAILURE);
        }
        TradeOrderDO tradeOrderDO = payOrderService.get(cmpayNotifyBO.getOrderId());
        if (JudgeUtils.isNull(tradeOrderDO)) {
            return;
        }
        tradeNotifyBO.setPaymentRout(PaymentWayEnum.CMPAY.name().toLowerCase());
        tradeOrderDO.setOrderCompleteTime(paymentDateTime);
        tradeOrderDO.setAccountDate(accountDate);
        tradeOrderDO.setBnkTraceNo(cmpayNotifyBO.getBnkTraceNo());
        TradeOrderAndNotifyBO orderAndNotifyBO = tradeNotifyService.backendQueryOrder(tradeOrderDO);
        if (JudgeUtils.isNull(orderAndNotifyBO) || JudgeUtils.isNull(orderAndNotifyBO.getTradeOrderDO())) {
            return;
        }
        //后台通知
        asynCommonService.asyncNotify(orderAndNotifyBO.getTradeNotifyBO());
    }

    @Override
    public void backendNotifyRefund(CmpayNotifyBO cmpayNotifyBO) {
        RefundOrderDO refundOrderDO = new RefundOrderDO();
        refundOrderDO.setOutTradeNo(cmpayNotifyBO.getMerchantRequestId());
        refundOrderDO.setOrgOrderNo(cmpayNotifyBO.getMerchantOrderNo());
        refundOrderDO.setAccountDate(cmpayNotifyBO.getRefundAccountDate());
        if (JudgeUtils.isEmpty(refundOrderDO.getAccountDate())) {
            refundOrderDO.setAccountDate(DateTimeUtils.getCurrentDateStr());
        }
        RefundOrderQueryBO refundOrderQueryBO = new RefundOrderQueryBO();
        refundOrderQueryBO.setStatus(cmpayNotifyBO.getStatus());
        refundOrderQueryBO.setRefundOrder(refundOrderDO);
        RefundNotifyBO refundNotifyBO = new RefundNotifyBO();
        refundNotifyBO.setRoute(PaymentWayEnum.CMPAY);
        refundNotifyBO.setRefundOrderQueryBO(refundOrderQueryBO);
        tradeNotifyService.refundResultNotify(refundNotifyBO);
    }

    @Override
    public void backendNotifyPosRefund(CmpayNotifyBO cmpayNotifyBO) {
        RefundOrderDO refundOrderDO = new RefundOrderDO();
        refundOrderDO.setOutTradeNo(cmpayNotifyBO.getMercRequestNo());
        refundOrderDO.setOrgOrderNo(cmpayNotifyBO.getOrderId());
        refundOrderDO.setAccountDate(cmpayNotifyBO.getAcDt());
        if (JudgeUtils.isEmpty(refundOrderDO.getAccountDate())) {
            refundOrderDO.setAccountDate(DateTimeUtils.getCurrentDateStr());
        }
        RefundOrderQueryBO refundOrderQueryBO = new RefundOrderQueryBO();
        refundOrderQueryBO.setStatus(cmpayNotifyBO.getRtSts());
        refundOrderQueryBO.setRefundOrder(refundOrderDO);
        RefundNotifyBO refundNotifyBO = new RefundNotifyBO();
        refundNotifyBO.setRoute(PaymentWayEnum.CMPAY);
        refundNotifyBO.setRefundOrderQueryBO(refundOrderQueryBO);
        tradeNotifyService.refundResultNotify(refundNotifyBO);
    }
}
