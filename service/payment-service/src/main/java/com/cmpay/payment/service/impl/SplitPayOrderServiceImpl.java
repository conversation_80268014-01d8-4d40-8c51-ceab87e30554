package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import com.cmpay.payment.bo.*;
import com.cmpay.payment.bo.config.PayServiceBeanBO;
import com.cmpay.payment.bo.config.SplitSettlementBO;
import com.cmpay.payment.bo.config.SubMerchantRefreshAmountBO;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.dao.IJkThirdPayParamExtDao;
import com.cmpay.payment.entity.JkThirdPayParamDO;
import com.cmpay.payment.entity.OrderPaymentAttachDO;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.*;
import com.cmpay.payment.service.ext.ExtPayOrderAttachService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.ExtRefundOrderService;
import com.cmpay.payment.service.ext.config.IExtRateService;
import com.cmpay.payment.service.ext.data.IExtSplitSettlementService;
import com.cmpay.payment.utils.CloneableUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import com.cmpay.payment.utils.TypeCheckUtils;
import feign.RetryableException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import sun.net.util.IPAddressUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * @date 2023-11-23 17:00
 * <AUTHOR>
 * @Version 1.0
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class SplitPayOrderServiceImpl implements SplitPayOrderService {
    private static final Logger logger = LoggerFactory.getLogger(SplitPayOrderServiceImpl.class);
    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    private IExtRateService rateService;
    @Autowired
    private AsynCommonService asynCommonService;
    @Autowired
    private RiskControlService riskControlService;
    @Autowired
    private PayOrderExtFunctionService extFunctionService;
    @Autowired
    private SubOrderInfosResolveService resolveService;
    @Autowired
    private IJkThirdPayParamExtDao jkThirdPayParamExtDao;
    @Autowired
    private ExtRefundOrderService refundOrderService;
    @Autowired
    private ExtPayOrderAttachService payOrderAttachService;
    @Autowired
    private SubMerchantIncomeService refundConfineService;
    @Autowired
    private IExtSplitSettlementService splitSettlementService;
    @Autowired
    private CompleteOrderSplitService splitService;
    @Autowired
    private TradeNotifyService tradeNotifyService;
    @Autowired
    private RefundServcie refundServcie;

    private static final DateTimeFormatter COMMON_YEAR_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final String B2B = "B2B";
    private static final String IPV4 = "IPV4";
    private static final String IPV6 = "IPV6";

    private static final String PRI_FIX = "REF";

    @Override
    public PayOrderBO payOrder(PayOrderBO payOrderBO) {
        try {
            paymentProcess(payOrderBO);
        } catch (Exception e) {
            String msg;
            if (e instanceof BusinessException) {
                msg = ((BusinessException) e).getMsgCd();
                payOrderBO.setMsgCode(msg);
            } else if ((e instanceof RetryableException) && e.getMessage().contains(CommonConstant.READ_TIMED_OUT)) {
                msg = MsgCodeEnum.REQUEST_RETURN_TIME_OUT.getMsgCd();
                logger.error("Exception:.", e);
            } else {
                msg = MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd();
                logger.error("Exception:.", e);
            }
            BusinessException.throwBusinessException(msg);
        }

        return payOrderBO;
    }

    /**
     * 支付处理
     *
     * @param payOrderBO
     */
    private void paymentProcess(PayOrderBO payOrderBO) {
        PayServiceBeanBO rateBO = new PayServiceBeanBO();
        extFunctionService.inputCannotEmptyCheck(payOrderBO);
        paramCannotEmptyCheck(payOrderBO);
        // 判断子商户号是否存在
        extFunctionService.subMerchantCheck(payOrderBO.getSubMerchant());
        //计算子订单总金额是否与实付金额相等
        if (payOrderBO.getRealAmount().compareTo(resolveService.sumSubOrderAmount(payOrderBO.getSubOrderInfos())) != Constants.ZERO_NUMBER) {
            BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
        }

        if (StringUtils.equals(payOrderBO.getScene(), CmpayConstants.APPLEPAY)) {
            payOrderBO.setScene(CmpayConstants.ALE);
        }
        extFunctionService.checkAmount(payOrderBO.getTotalAmount(), payOrderBO.getRealAmount(), payOrderBO.getDiscountableAmount());
        if (TypeCheckUtils.checkPaymentWay(PaymentWayEnum.valueOf(payOrderBO.getPayWay().toUpperCase()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_WAY_ERROR);
        }
        if (TypeCheckUtils.checkPaymentScene(PaymentSceneEnum.valueOf(payOrderBO.getScene().toUpperCase()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_SCENE_ERROR);
        }
        //设置支付场景为企业网银
        if (JudgeUtils.equals(PaymentSceneEnum.NETBANK.name().toLowerCase(), payOrderBO.getScene())
                || JudgeUtils.equals(PaymentSceneEnum.NETBANKB2B.name().toLowerCase(), payOrderBO.getScene())) {
            if (JudgeUtils.isBlank(payOrderBO.getBankAbbreviation())) {
                BusinessException.throwBusinessException(MsgCodeEnum.BANK_ABBREVIATION_CANNOT_BE_EMPTY);
            }
            if (payOrderBO.getBankAbbreviation().endsWith(B2B)) {
                payOrderBO.setScene(PaymentSceneEnum.NETBANKB2B.name().toLowerCase());
            }
            //设置银行简称
            payOrderBO.setHallAreaCode(payOrderBO.getBankAbbreviation());
        }
        //指定渠道花呗，且支付场景wap 和 app，修改支付场景XXXpcredit
        if (JudgeUtils.equals(payOrderBO.getSpecifiedChannel(), CommonConstant.ALIPAY_PCREDIT) &&
                JudgeUtils.equalsAny(payOrderBO.getScene(), PaymentSceneEnum.WAP.name().toLowerCase(), PaymentSceneEnum.APP.name().toLowerCase())) {
            payOrderBO.setScene(payOrderBO.getScene().concat(CommonConstant.ALIPAY_PCREDIT));
        }
        //查询商户配置费率
        rateBO.setMerchantNo(payOrderBO.getMerchantId());
        rateBO.setPaymentChannl(payOrderBO.getPayWay());
        rateBO.setOrderScene(payOrderBO.getScene());
        rateBO.setOrderAmount(payOrderBO.getRealAmount());
        rateBO.setProvinceCode(JudgeUtils.isBlank(payOrderBO.getProvinceCode()) ?
                StringUtils.substring(payOrderBO.getOutTradeNo(), 0, 4) : payOrderBO.getProvinceCode());
        rateBO.setOrderDate(DateTimeUtils.getCurrentDateStr());
        rateBO = rateService.findPaymentRout(rateBO);
        if (JudgeUtils.isNull(rateBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ROUT_CANNOT_VALIDITY);
        }
        payOrderBO.setOrderRate(rateBO.getRate());
        payOrderBO.setServiceCharge(rateBO.getServiceCharge());
        payOrderBO.setPaymentId(rateBO.getBankMerchantNo());
        payOrderBO.setPaymentType(rateBO.getBusinessType());
        payOrderBO.setPaymentRout(rateBO.getPaymentRout());
        payOrderBO.setRefundFeeWay(rateBO.getRefundFeeWay());
        payOrderBO.setContractSecureValue(rateBO.getSecureValue());
        payOrderBO.setSignMethod(rateBO.getSignMethod());
        extFunctionService.checkAuthCode(payOrderBO.getAuthCode(), payOrderBO.getPayWay(), payOrderBO.getScene());

        //分账订单计算服务费（子订单向上取整后求和）
        TradeOrderDO tradeOrderDO = new TradeOrderDO();
        tradeOrderDO.setSplitFlag(Constants.Y);
        tradeOrderDO.setSubOrderInfos(payOrderBO.getSubOrderInfos());
        tradeOrderDO.setOutTradeNo(payOrderBO.getOutTradeNo());
        tradeOrderDO.setOrderRate(payOrderBO.getOrderRate());
        splitService.splitOrderFeeCalculate(tradeOrderDO);
        payOrderBO.setOrderFeeAmount(tradeOrderDO.getOrderFeeAmount());
        //查询银联配置活动
        //unionActivityQuery(payOrderBO);
        payOrderBO.setBusinessCode(payOrderBO.getDiscountCode());
        payOrderBO.setTradeOrderNo(payOrderBO.getOutTradeNo());
        payOrderBO.setTradeTime(DateTimeUtils.getCurrentTimeStr());
        payOrderBO.setTimeoutExpress(extFunctionService.getTimeoutExpress(payOrderBO));
        payOrderBO.setExpireTime(extFunctionService.getExpireTime(payOrderBO));
        if (JudgeUtils.equals(payOrderBO.getPaymentRout(), PaymentWayEnum.WECHAT.name().toLowerCase())
                && JudgeUtils.equalsAny(payOrderBO.getScene(), PaymentSceneEnum.SCAN.name().toLowerCase(), PaymentSceneEnum.BARCODE.name().toLowerCase(),
                PaymentSceneEnum.APP.name().toLowerCase(), PaymentSceneEnum.WAP.name().toLowerCase(), PaymentSceneEnum.JSAPI.name().toLowerCase(),
                PaymentSceneEnum.APPLET.name().toLowerCase())) {
            if (JudgeUtils.isBlank(payOrderBO.getAppId())) {
                payOrderBO.setAppId(weChatProperties.getAppid());
            }
        } else {
            payOrderBO.setAppId("");
        }
//        tradeOrderDO.setRefundFeeWay(payOrderBO.getRefundFeeWay());
        extFunctionService.obtainProvinceOrderNo(payOrderBO);
        //判断IP地址类型
        if (IPAddressUtil.isIPv4LiteralAddress(payOrderBO.getClientIp())) {
            payOrderBO.setIpType(IPV4);
        } else if (IPAddressUtil.isIPv6LiteralAddress(payOrderBO.getClientIp())) {
            payOrderBO.setIpType(IPV6);
        }
        //进行风控拦截
        riskControlService.riskIntercept(payOrderBO, rateBO);
        // 默认分账订单
        payOrderBO.setSplitFlag(Constants.Y);
        TradeOrderDO tradeOrder = payOrderService.insertByNewTranscation(payOrderBO);

        long startTime = System.currentTimeMillis();
        try {
            applicationContext.publishEvent(payOrderBO);
            logger.info("RequestExternalInterfaceMonitoringLog{tc='{}',dur='{}',mc='{}'}", payOrderBO.getPaymentRout() + "-" + payOrderBO.getPayWay(), System.currentTimeMillis() - startTime, MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            if (JudgeUtils.equals(PaymentSceneEnum.NETBANK.name().toLowerCase(), payOrderBO.getScene())
                    || JudgeUtils.equals(PaymentSceneEnum.NETBANKB2B.name().toLowerCase(), payOrderBO.getScene())) {
                extFunctionService.analyzeBankUrl(payOrderBO);
            }
            tradeOrder.setThirdOrdNo(payOrderBO.getThirdOrdNo());
            tradeOrder.setThirdOrdDt(payOrderBO.getThirdOrdDt());
        } catch (Exception e) {
            // 微信付款码支付同步返回支付确认失败
            if (StringUtils.equals(payOrderBO.getPaymentRout(), PaymentWayEnum.WECHAT.name().toLowerCase())
                    && StringUtils.equals(payOrderBO.getScene(), PaymentSceneEnum.BARCODE.name().toLowerCase())
                    && StringUtils.equals(payOrderBO.getStatus(), OrderStatusEnum.TRADE_FAIL.name())) {
                tradeOrder.setStatus(payOrderBO.getStatus());
                tradeOrder.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                tradeOrder.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
            } else {
                tradeOrder.setStatus(null);
                tradeOrder.setOrderCompleteTime(null);
                tradeOrder.setReceiveNotifyTime(null);
            }
            //设置返回码
            if (e instanceof BusinessException) {
                payOrderBO.setMsgCode(((BusinessException) e).getMsgCd());
            } else if ((e instanceof RetryableException) && e.getMessage().contains(CommonConstant.READ_TIMED_OUT)) {
                payOrderBO.setMsgCode(MsgCodeEnum.REQUEST_RETURN_TIME_OUT.getMsgCd());
            } else {
                payOrderBO.setMsgCode(MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd());
            }
            if (JudgeUtils.isNull(payOrderBO.getErrMsgCd())) {
                payOrderBO.setErrMsgCd(payOrderBO.getMsgCode());
            }
            logger.info("RequestExternalInterfaceMonitoringLog{tc='{}',dur='{}',mc='{}'}", payOrderBO.getPaymentRout() + "-" + payOrderBO.getPayWay(), System.currentTimeMillis() - startTime, payOrderBO.getErrMsgCd());
            tradeOrder.setErrMsgCd(payOrderBO.getErrMsgCd());
            tradeOrder.setErrMsgInfo(payOrderBO.getErrMsgInfo());
            payOrderService.updateByNewTranscation(tradeOrder);
            riskControlService.riskAfterPay(tradeOrder);
            throw e;
        }
        payOrderBO.setMsgCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
        if (JudgeUtils.equals(payOrderBO.getScene(), PaymentSceneEnum.BARCODE.name().toLowerCase()) &&
                JudgeUtils.equals(payOrderBO.getPaymentRout(), PaymentWayEnum.CMPAY.name().toLowerCase())) {

            if (StringUtils.equals(payOrderBO.getStatus(), CommonConstant.BARCODE_SUCCESS)) {

                if (JudgeUtils.isEmpty(payOrderBO.getFinishDateTime())) {
                    payOrderBO.setFinishDateTime(DateTimeUtils.getCurrentDateTimeStr());
                }
                if (JudgeUtils.isEmpty(payOrderBO.getAccountDate())) {
                    payOrderBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
                }
                tradeOrder.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                tradeOrder.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                tradeOrder.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
                tradeOrder.setAccountDate(payOrderBO.getAccountDate());
                payOrderService.updateByNewTranscation(tradeOrder);
                riskControlService.riskAfterPay(tradeOrder);
                TradeNotifyBO tradeNotifyBO = assembleNotifyBO(payOrderBO);
                asynCommonService.asyncNotify(tradeNotifyBO);
            } else {
                BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_ORDER_PAY_FAIL);
            }
        } else if (JudgeUtils.equals(payOrderBO.getPaymentRout(), PaymentWayEnum.ALIPAY.name().toLowerCase())) {
            tradeOrder.setStatus(OrderStatusEnum.WAIT_PAY.name());

            if (StringUtils.isNotBlank(payOrderBO.getStatus())) {
                tradeOrder.setStatus(payOrderBO.getStatus());
                if (StringUtils.equals(OrderStatusEnum.TRADE_SUCCESS.name(), payOrderBO.getStatus())) {
                    tradeOrder.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
                    if (JudgeUtils.isEmpty(payOrderBO.getFinishDateTime())) {
                        payOrderBO.setFinishDateTime(DateTimeUtils.getCurrentDateTimeStr());
                    }
                    if (JudgeUtils.isEmpty(payOrderBO.getAccountDate())) {
                        payOrderBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
                    }
                    tradeOrder.setAccountDate(payOrderBO.getAccountDate());
                    tradeOrder.setMobileNumber(payOrderBO.getMobileNumber());
                    TradeNotifyBO tradeNotifyBO = assembleNotifyBO(payOrderBO);
                    asynCommonService.asyncNotify(tradeNotifyBO);
                }
            }
            tradeOrder.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
            tradeOrder.setErrMsgCd(payOrderBO.getErrMsgCd());
            tradeOrder.setErrMsgInfo(payOrderBO.getErrMsgInfo());
            payOrderService.updateByNewTranscation(tradeOrder);
            if (JudgeUtils.equalsAny(payOrderBO.getStatus(), OrderStatusEnum.TRADE_SUCCESS.name(), OrderStatusEnum.TRADE_FAIL.name(), OrderStatusEnum.TRADE_CLOSED.name())) {
                riskControlService.riskAfterPay(tradeOrder);
            }
        } else if (StringUtils.equals(payOrderBO.getPaymentRout(), PaymentWayEnum.WECHAT.name().toLowerCase())
                && StringUtils.equals(payOrderBO.getScene(), PaymentSceneEnum.BARCODE.name().toLowerCase())
                && StringUtils.equals(payOrderBO.getStatus(), OrderStatusEnum.TRADE_SUCCESS.name())) {
            // 微信付款码支付同步返回支付成功
            if (JudgeUtils.isEmpty(payOrderBO.getAccountDate())) {
                payOrderBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
            }
            tradeOrder.setStatus(payOrderBO.getStatus());
            tradeOrder.setOrderCompleteTime(payOrderBO.getFinishDateTime());
            tradeOrder.setReceiveNotifyTime(payOrderBO.getFinishDateTime());
            tradeOrder.setAccountDate(payOrderBO.getAccountDate());
            tradeOrder.setMobileNumber(payOrderBO.getMobileNumber());
            payOrderService.updateByNewTranscation(tradeOrder);
            riskControlService.riskAfterPay(tradeOrder);
            TradeNotifyBO tradeNotifyBO = assembleNotifyBO(payOrderBO);
            asynCommonService.asyncNotify(tradeNotifyBO);
        }
        payOrderBO.setMsgCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
    }

    @Override
    @DistributedLocked(lockName = "'OrderRefund:'+#refundParam.getMerchantId()+#refundParam.getOutTradeNo()", leaseTime = 60, waitTime = 1, ignoreUnableToAcquiredLockException = false)
    public RefundBO refund(RefundBO refundParam) {
        // 检查传入参数
        if (JudgeUtils.isNull(refundParam.getMerchantId())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isNull(refundParam.getOutRequestNo())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ORDER_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isNull(refundParam.getTradeDate())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_REQUEST_DATE_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isNull(refundParam.getOutTradeNo())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ORDER_NO_CANNOT_BE_EMPTY);
        }
        refundParam.setChannelType(ChannelTypeEnum.INTERFACE.name().toLowerCase());
        refundParam.setChannelTypeDesc(ChannelTypeEnum.INTERFACE.getDesc());

        refundParam.setMsgCode(MsgCodeEnum.REFUND_ERROR.getMsgCd());
        //检查退款单号是否存在
        RefundOrderDO refundOrderQueryParam = new RefundOrderDO();
        refundOrderQueryParam.setMerchantNo(refundParam.getMerchantId());
        refundOrderQueryParam.setOutTradeNo(refundParam.getOutRequestNo());
        RefundOrderDO refundOrderQuery = refundOrderService.load(refundOrderQueryParam);
        if (JudgeUtils.isNotNull(refundOrderQuery)) {
            //如果存在说明该款单号已发起过退款
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_ALREADY_EXIST);
        }
        //查询原支付订单
        TradeOrderDO payOrderQuery = new TradeOrderDO();
        payOrderQuery.setMerchantNo(refundParam.getMerchantId());
        payOrderQuery.setOutTradeNo(refundParam.getOutTradeNo());
        TradeOrderDO originalPayTradeOrderDO = payOrderService.load(payOrderQuery);
        //校验是否满足退款条件
        refundServcie.checkRefundReq(originalPayTradeOrderDO, refundParam);

        if (StringUtils.equals(originalPayTradeOrderDO.getAimProductCode(), PaymentWayEnum.ICBCPAY.name().toLowerCase())) {
            refundParam.setTradeOrderNo(JhIdGenUtils.generateJhIdWithDate(DcepConstants.PAYMENT_REFUND, PRI_FIX, 15));
        }
        // 此退款接口仅支持分账退款
        if (StringUtils.isEmpty(originalPayTradeOrderDO.getSplitFlag()) || StringUtils.isEmpty(originalPayTradeOrderDO.getSubMerchantNo())
                || !StringUtils.equals(originalPayTradeOrderDO.getSplitFlag(), Constants.Y)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_TYPE_IS_NOT_SUPPORTED);
        }
        //检测是否超过退款时长
        JkThirdPayParamDO refundOrderParamDO = new JkThirdPayParamDO();

        refundOrderParamDO.setAimProductCode(originalPayTradeOrderDO.getAimProductCode());
        refundOrderParamDO.setPayProductCode(originalPayTradeOrderDO.getPayProductCode());
        refundOrderParamDO.setPayWayCode(originalPayTradeOrderDO.getPayWayCode());
        refundOrderParamDO.setParamMark(ThirdPayWayParamEnum.REFUND_PARAM.name());
        JkThirdPayParamDO refund = jkThirdPayParamExtDao.findRecord(refundOrderParamDO);
        if (JudgeUtils.isNotNull(refund)) {
            logger.info("退款时长检测===");
            String orderDate = originalPayTradeOrderDO.getOrderDate();
            Long refundValue = Long.parseLong(refund.getParamValue());
            String nowDate = refundParam.getTradeDate();
            String afterDate = plusDays(orderDate, refundValue);
            logger.info("======================afterDate is {}=========================", afterDate);
            if (nowDate.compareTo(afterDate) > 0) {
                BusinessException.throwBusinessException(MsgCodeEnum.ORDER_REFUND_PARAM_TIME_OUT);
            }
        }
        //检查通过后开始锁表，进行后面的操作
        try {
            payOrderService.lock(originalPayTradeOrderDO);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_EXIST_UNFINISHED_REDUND);
        }
        //保存订单记录到参数对像里，以便后面使用
        refundParam.setOriginalPayTradeOrder(CloneableUtils.gsonDeepClone(originalPayTradeOrderDO, TradeOrderCloneBO.class));
        //保存订单附加表信息
        if (DcepConstants.DCEP.equals(originalPayTradeOrderDO.getDcepFlag()) ||
                PaymentWayEnum.ICBCPAY.name().equalsIgnoreCase(originalPayTradeOrderDO.getPayProductCode())) {
            OrderPaymentAttachDO attachDO = new OrderPaymentAttachDO();
            attachDO.setOutTradeNo(originalPayTradeOrderDO.getOutTradeNo());
            attachDO.setOrderDate(originalPayTradeOrderDO.getRequestDate());
            OrderPaymentAttachDO orderPaymentAttachDO = payOrderAttachService.load(attachDO);
            refundParam.setOrderPaymentAttachDO(orderPaymentAttachDO);
            refundParam.setDcepFlag(originalPayTradeOrderDO.getDcepFlag());
        }

        //原支付订单状态不为支付成功并且不为部分退款，不允许退款
        if (!OrderStatusEnum.TRADE_SUCCESS.name().equals(originalPayTradeOrderDO.getStatus())
                && !OrderStatusEnum.REFUND_PART.name().equals(originalPayTradeOrderDO.getStatus())
                && !OrderStatusEnum.REFUND_WAIT.name().equals(originalPayTradeOrderDO.getStatus())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_STATUS_CANNOT_REFUND);
        }
        //检查当前已登记退款金额
        RefundOrderDO refundOrder = new RefundOrderDO();
        refundOrder.setMerchantNo(refundParam.getMerchantId());
        refundOrder.setOrgOrderNo(refundParam.getOutTradeNo());
        refundOrder.setOrderDate(originalPayTradeOrderDO.getOrderDate());
        refundOrder.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
        List<RefundOrderDO> refundOrderList = refundOrderService.refundOrderList(refundOrder);
        if (refundOrderList != null) {
            {
                //计算退款已登记总金额
                BigDecimal refundRegistMoney = refundOrderList.stream().map(RefundOrderDO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal refundMoney = refundRegistMoney.add(refundParam.getRefundAmount());
                if (refundMoney.compareTo(originalPayTradeOrderDO.getRealAmount()) > 0) {
                    BusinessException.throwBusinessException(MsgCodeEnum.REFUND_AMOUNT_ERROR);
                }
            }
        }
        // 全额退款，需要解析原支付订单的分账参数，在退款表登记子订单号列表；
        if (refundParam.getRefundAmount().compareTo(originalPayTradeOrderDO.getRealAmount()) == Constants.ZERO_NUMBER) {
            //如退款金额等于订单金科则为全额退款
            refundParam.setSubOrderNo(null);
            refundParam.setRefundFlag(Constants.ZERO);
        } else {
            // 检测是否存在退款未完成订单订单
            refundServcie.checkRefundOrderStatus(refundOrderList);
            //校验退款次数
            refundServcie.refundFrequencyLimit(originalPayTradeOrderDO, refundParam);
            if (StringUtils.isBlank(refundParam.getSubOrderNo())) {
                BusinessException.throwBusinessException(MsgCodeEnum.REFUND_PART_SUBORDER_BE_EMPTY);
            }
            RefundOrderDO queryRefundWait = new RefundOrderDO();
            queryRefundWait.setOrderDate(originalPayTradeOrderDO.getOrderDate());
            queryRefundWait.setMerchantNo(refundParam.getMerchantId());
            queryRefundWait.setOrgOrderNo(refundParam.getOutTradeNo());
            queryRefundWait.setSubOrderNo(refundParam.getSubOrderNo());
            List<RefundOrderDO> refundWaitResult = refundOrderService.queryRefundWait(queryRefundWait);
            if (JudgeUtils.isNotEmpty(refundWaitResult)) {
                //如果存在说明该款子订单号有退款中，退款待发起，退款成功的退款记录
                BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_ALREADY_EXIST);
            }
            //部分退款：从分账结算表查询该支付子订单 判断退款金额是否与原子订单支付金额相等
            SplitSettlementBO querySettlementBO = new SplitSettlementBO();
            querySettlementBO.setOutTradeNo(originalPayTradeOrderDO.getOutTradeNo());
            querySettlementBO.setSubOrderNo(refundParam.getSubOrderNo());
            querySettlementBO.setSettlementStatus(Constants.S);
            SplitSettlementBO oriSubOrder = splitSettlementService.findBySubOrder(querySettlementBO);
            if (JudgeUtils.isNull(oriSubOrder)) {
                BusinessException.throwBusinessException(MsgCodeEnum.ORI_SUB_ORDER_ISNULL);
            }
            if (refundParam.getRefundAmount().compareTo(oriSubOrder.getSubOrderAmount()) != Constants.ZERO_NUMBER) {
                BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
            }
            refundParam.setRefundFlag(Constants.STR_ONE);
        }
        RefundOrderDO refundOrderLsQuery = new RefundOrderDO();
        refundOrderLsQuery.setMerchantNo(refundParam.getMerchantId());
        refundOrderLsQuery.setOrgOrderNo(refundParam.getOutTradeNo());
        refundOrderLsQuery.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
        List<RefundOrderDO> refundOrderLs = refundOrderService.find(refundOrderLsQuery);
        BigDecimal totalRefundMoney = BigDecimal.ZERO;
        BigDecimal totalRefundFeeAmount = BigDecimal.ZERO;
        if (refundOrderLs != null) {
            //计算退款总金额
            totalRefundMoney = refundOrderLs.stream().map(RefundOrderDO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            totalRefundFeeAmount = refundOrderLs.stream().map(RefundOrderDO::getOrderFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        }
        // 本次退款总金额 =已退款金额+本次退款金额
        BigDecimal totalRefundAmountCurrent = totalRefundMoney.add(refundParam.getRefundAmount());
        String payOrderStatus = originalPayTradeOrderDO.getStatus();
        if (totalRefundAmountCurrent.compareTo(originalPayTradeOrderDO.getRealAmount()) > 0) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_AMOUNT_ERROR);
        } else if (totalRefundAmountCurrent.compareTo(originalPayTradeOrderDO.getRealAmount()) == 0) {
            // 若退款金额等于支付订单金额则全额退款
            payOrderStatus = OrderStatusEnum.REFUND_SUCCESS.name();
        } else {
            // 若退款金额小于支付订单金额则部分退款
            payOrderStatus = OrderStatusEnum.REFUND_PART.name();
        }
        // 包含子商户号的订单，进行退款总金额限制
        if (StringUtils.isNotEmpty(originalPayTradeOrderDO.getSubMerchantNo())) {
            if (!StringUtils.equals(originalPayTradeOrderDO.getAimProductCode(), PaymentWayEnum.CMPAY.name().toLowerCase())) {
                SubMerchantRefreshAmountBO refreshAmountBO = new SubMerchantRefreshAmountBO();
                refreshAmountBO.setSubMerchantNo(originalPayTradeOrderDO.getSubMerchantNo());
                refreshAmountBO.setAmount(refundParam.getRefundAmount());
                refundConfineService.refundConfine(refreshAmountBO);
            }
        }
        refundParam.setSplitFlag(Constants.Y);
        //创建退款实例
        RefundOrderDO refundOrderDO = refundOrderService.insert(refundParam, originalPayTradeOrderDO);
        //保存退款订单记录到参数对像里，以便后面使用
        refundParam.setRefundOrder(refundOrderDO);

        try {
            //和包退款先登记再发往
            if (JudgeUtils.equals(originalPayTradeOrderDO.getAimProductCode(), PaymentWayEnum.CMPAY.name().toLowerCase())) {
                // 退款订单状态修改为退款待处理
                refundOrderDO.setStatus(OrderStatusEnum.REFUND_PEND.name());
                // 更新退款订单
                refundOrderService.update(refundOrderDO);
                refundParam.setMsgCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
                return refundParam;
            }
            applicationContext.publishEvent(refundParam);
            // 是否异步退款
            if (refundParam.isAsync()) {
                // 原支付订单设置状态为REFUND_WAIT
                originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
                // 更新支付订单
                payOrderService.update(originalPayTradeOrderDO);
                refundParam.setMsgCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            }

        } catch (Exception e) {
            if (e instanceof BusinessException) {
                //记录业务抛出的错误码 及错误信息
                refundParam.setMsgCode(((BusinessException) e).getMsgCd());
                // 退款订单状态修改为退款失败
                refundOrderDO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
                //修改退款时间
                refundOrderDO.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                refundOrderDO.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
                //更新报错信息
                refundOrderDO.setErrMsgCd(refundParam.getErrMsgCd());
                refundOrderDO.setErrMsgInfo(refundParam.getErrMsgInfo());
                // 更新退款订单
                refundOrderService.update(refundOrderDO);
                // 明确退款失败，且退款请求日期是今天，那么自增回去
                splitService.refundFailIncrement(refundOrderDO, originalPayTradeOrderDO);
                // 调用退款通知处理
                RefundOrderCloneBO refundOrderCloneBO = new RefundOrderCloneBO();
                BeanUtils.copyProperties(refundOrderDO, refundOrderCloneBO);
                tradeNotifyService.handleRefundNotify(refundOrderCloneBO);
            } else {
                //如果是未知的系统错误，比如网络请求异常  原支付订单状态修改为退款中
                originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
            }
        } finally {
            // 更新返回值
            refundParam.setTotalAmount(originalPayTradeOrderDO.getOrderAmount());
        }
        return refundParam;
    }

    /**
     * 某个日期基础上,增加天数
     *
     * @param date
     * @param days
     * @return
     */
    private static String plusDays(String date, long days) {
        LocalDate localDate = LocalDate.parse(date, COMMON_YEAR_FORMAT);
        localDate = localDate.plusDays(days);
        return localDate.format(COMMON_YEAR_FORMAT);
    }

    /**
     * 参数检查
     *
     * @param payOrderBO
     */
    private void paramCannotEmptyCheck(PayOrderBO payOrderBO) {
        if (JudgeUtils.isBlank(payOrderBO.getSubMerchant())) {
            BusinessException.throwBusinessException(MsgCodeEnum.SUB_MERCHANT_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(payOrderBO.getSubOrderInfos())) {
            BusinessException.throwBusinessException(MsgCodeEnum.SUB_ORDER_INFOS_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(payOrderBO.getMerchantChannelType())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_CHANNEL_TYPE_CANNOT_BE_EMPTY);
        }
    }

    /**
     * 从订单表，赋值成通知的业务对象
     *
     * @param payOrderBO
     */
    public TradeNotifyBO assembleNotifyBO(PayOrderBO payOrderBO) {
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setMerchantNo(payOrderBO.getMerchantId());
        tradeNotifyBO.setTradeOrderNo(payOrderBO.getTradeOrderNo());
        tradeNotifyBO.setOutOrderNo(payOrderBO.getOutTradeNo());
        tradeNotifyBO.setTradeDate(payOrderBO.getTradeDate());
        tradeNotifyBO.setTradeAmount(payOrderBO.getTotalAmount());
        tradeNotifyBO.setDiscountableAmount(payOrderBO.getDiscountableAmount());
        tradeNotifyBO.setNotifyType(TradeTypeEnum.PAYMENT.name().toLowerCase());
        tradeNotifyBO.setNotifyUrl(payOrderBO.getNotifyUrl());
        tradeNotifyBO.setExtra(payOrderBO.getExtra());
        tradeNotifyBO.setSecretIndex(payOrderBO.getSecretIndex());
        tradeNotifyBO.setFinishDate(payOrderBO.getFinishDateTime());
        return tradeNotifyBO;
    }
}
