package com.cmpay.payment.service.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.bo.RpmOnlineRefundBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.dto.cmpay.CmpayOnlineRefundQueryReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayOnlineRefundQueryRspDTO;
import com.cmpay.payment.entity.CmpayRefundJrnDO;
import com.cmpay.payment.service.TradeCommonService;
import com.cmpay.payment.service.channel.cmpay.CmpayCommonService;
import com.cmpay.payment.service.channel.cmpay.CmpayOnlineRefundQueryChannelService;
import com.cmpay.payment.service.ext.ExtCmpayRefundJrnService;
import com.cmpay.payment.util.PaymentUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created on 2018/12/4
 *
 * @author: chen_lan
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayOnlineRefundQueryChannelServiceImpl implements CmpayOnlineRefundQueryChannelService {

    private static final Logger logger = LoggerFactory.getLogger(CmpayCloudRefundQueryChannelServiceImpl.class);

    @Autowired
    ExtCmpayRefundJrnService extCmpayRefundJrnService;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Autowired
    private CmpayCommonService cmpayCommonService;
    @Autowired
    private TradeCommonService tradeCommonService;

    @Override
    public void send(RefundOrderQueryBO refundOrderQueryBO) {
        cmpayOnlineRefundQuery(refundOrderQueryBO);
    }

    @Override
    public void cmpayOnlineRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {

        CmpayRefundJrnDO querycmpayJournalDO = new CmpayRefundJrnDO();
        querycmpayJournalDO.setTradeOrderNo(refundOrderQueryBO.getRefundOrder().getTradeOrderNo());
        //  查询和包退款流水
        CmpayRefundJrnDO cmpayRefundJrnDO = extCmpayRefundJrnService.load(querycmpayJournalDO);
        if (JudgeUtils.isNull(cmpayRefundJrnDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_JOURNAL_INFO_GET_ERROR);
        }
        // 和包退款流水加锁
        extCmpayRefundJrnService.lock(cmpayRefundJrnDO);
        try {
            // 调用和包远程退款查询接口
            RpmOnlineRefundBO cmpayOnlineRefundBO = onlineRefundQuery(refundOrderQueryBO);
            logger.info("Cmpay Cloud Order Query Success,tradeOrderNo is {}", cmpayRefundJrnDO.getTradeOrderNo());
            cmpayRefundJrnDO.setRemark(cmpayOnlineRefundBO.getOrderStatus());
            logger.info("Cmpay Cloud Order Result,tradeOrderNo is {},orderStatus is {}",
                    cmpayRefundJrnDO.getTradeOrderNo(), cmpayOnlineRefundBO.getOrderStatus());
            cmpayRefundJrnDO.setReturnMsg(cmpayOnlineRefundBO.getMessageCode());
            logger.info("Cmpay Cloud Order Result,tradeOrderNo is {},messageCode is {}",
                    cmpayRefundJrnDO.getTradeOrderNo(), cmpayOnlineRefundBO.getMessageCode());
            cmpayRefundJrnDO.setReturnMsgInfo(cmpayOnlineRefundBO.getMessageInfo());

            // 退款状态
            if (OrderStatusEnum.REFUND_SUCCESS.name().equals(cmpayOnlineRefundBO.getOrderStatus())
                    || OrderStatusEnum.REFUND_FAIL.name().equals(cmpayOnlineRefundBO.getOrderStatus())) {
                refundOrderQueryBO.setStatus(cmpayOnlineRefundBO.getOrderStatus());
                if (OrderStatusEnum.REFUND_FAIL.name().equals(cmpayOnlineRefundBO.getOrderStatus())) {
                    refundOrderQueryBO.setErrMsgCd(cmpayOnlineRefundBO.getErrMsgCd());
                    refundOrderQueryBO.setErrMsgInfo(cmpayOnlineRefundBO.getErrMsgInfo());
                }
                refundOrderQueryBO.getRefundOrder().setAccountDate(cmpayOnlineRefundBO.getAccountDate());
                refundOrderQueryBO.setRefAmountList(cmpayOnlineRefundBO.getRefAmountList());
            } else {
                // 若返回错误码为OLA00018，则判断是否超过30分钟，若超过则当作失败处理
                if (MsgCodeEnum.CMPAY_ONLINE_REFUND_NOT_EXISTS.getMsgCd().equals(cmpayOnlineRefundBO.getMessageCode())) {
                    tradeCommonService.handleCmpayRefundOrderNotExist(refundOrderQueryBO,cmpayRefundJrnDO,MsgCodeEnum.CMPAY_ONLINE_REFUND_NOT_EXISTS);
                }
            }
        } catch (Exception e) {
            logger.info("Cmpay Cloud Order Query Exception,tradeOrderNo is {},exception is {}",
                    cmpayRefundJrnDO.getTradeOrderNo(), e);
            //系统异常，比如网络异常 记录异常日志
            cmpayCommonService.cmapyThrowClassHandle(cmpayRefundJrnDO,e);
            throw e;
        } finally {
            cmpayRefundJrnDO.setReturnDate(DateTimeUtils.getCurrentDateStr());
            cmpayRefundJrnDO.setReturnTime(DateTimeUtils.getCurrentTimeStr());
            //将异常信息更新到 cmpay退款流水记录
            extCmpayRefundJrnService.updateByNewTranscation(cmpayRefundJrnDO);
            logger.info("Cmpay Cloud Journal Record Update Success,tradeOrderNo is {}",
                    cmpayRefundJrnDO.getTradeOrderNo());
            if (!MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd().equals(cmpayRefundJrnDO.getReturnMsg())) {
                // 报错退出
                BusinessException.throwBusinessException(cmpayRefundJrnDO.getReturnMsg());
            }
        }

        logger.info("=====================Cmpay Cloud Refund Order Query End!!=====================");
    }


    private RpmOnlineRefundBO onlineRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {
        RpmOnlineRefundBO refundQueryBO = new RpmOnlineRefundBO();
        CmpayOnlineRefundQueryReqDTO onlineRefundQueryReqDTO = new CmpayOnlineRefundQueryReqDTO();
        onlineRefundQueryReqDTO.setMerchantOrderNo(refundOrderQueryBO.getOriginalPayTradeOrder().getBankOrderNo());
        onlineRefundQueryReqDTO.setMerchantRefundSequence(refundOrderQueryBO.getRefundOrder().getBankOrderNo());
        onlineRefundQueryReqDTO.setMerchantId(refundOrderQueryBO.getRefundOrder().getBankMerchantNo());
        onlineRefundQueryReqDTO.setMerchantOrderDate(refundOrderQueryBO.getOriginalPayTradeOrder().getOrderDate());
        onlineRefundQueryReqDTO.setSignType(refundOrderQueryBO.getSignMethod());
        onlineRefundQueryReqDTO.setHmac(refundOrderQueryBO.getContractSecureValue());
        onlineRefundQueryReqDTO.setVersion(CommonConstant.CMPAY_ONLINE_PAYMENT_VERSION);
        onlineRefundQueryReqDTO.setType(CommonConstant.CMPAY_REFUND_QUERY_TYPE);
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(CmpayPayChannelEnum.CMPAY_ONLINE);
        request.setBusiType(CmpayPayChannelEnum.CMPAY_ONLINE_REFUND_QUERY.getName());
        request.setSource(CmpayPayChannelEnum.CMPAY_ONLINE);
        request.setTarget(onlineRefundQueryReqDTO);
        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            cmpayCommonService.cgwReturnInfoCheck(genericRspDTO);
            BusinessException.throwBusinessException(genericRspDTO);
        }
        Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleOnlineRefundQuery(refundQueryBO, (CmpayOnlineRefundQueryRspDTO) result));
        return refundQueryBO;
    }

    private void handleOnlineRefundQuery(RpmOnlineRefundBO refundQueryBO, CmpayOnlineRefundQueryRspDTO onlineRefundQueryRspDTO) {
        if (JudgeUtils.isNull(onlineRefundQueryRspDTO)) {
            refundQueryBO.setMessageCode(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgCd());
            refundQueryBO.setMessageInfo(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgInfo());
            return;
        }
        refundQueryBO.setOrderStatus(onlineRefundQueryRspDTO.getRefundStatus());
        if (OrderStatusEnum.REFUND_SUCCESS.name().equals(onlineRefundQueryRspDTO.getRefundStatus())) {
            refundQueryBO.setRefAmountList(PaymentUtils.convertToAmountInfoList(onlineRefundQueryRspDTO));
            refundQueryBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            refundQueryBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
        } else if (OrderStatusEnum.REFUND_FAIL.name().equals(onlineRefundQueryRspDTO.getRefundStatus())) {
            refundQueryBO.setErrMsgCd(onlineRefundQueryRspDTO.getMsgCd());
            refundQueryBO.setErrMsgInfo(onlineRefundQueryRspDTO.getMsgInfo());
            refundQueryBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            refundQueryBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
        } else if (OrderStatusEnum.REFUND_WAIT.name().equals(onlineRefundQueryRspDTO.getRefundStatus())
                || OrderStatusEnum.REFUND_ACCEPT.name().equals(onlineRefundQueryRspDTO.getRefundStatus())) {
            refundQueryBO.setOrderStatus(OrderStatusEnum.REFUND_WAIT.name());
            refundQueryBO.setMessageCode(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgCd());
            refundQueryBO.setMessageInfo(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgInfo());
        } else {
            if (MsgCodeEnum.CMPAY_ONLINE_REFUND_NOT_EXISTS.getMsgCd().equals(onlineRefundQueryRspDTO.getMsgCd())) {
                refundQueryBO.setMessageCode(MsgCodeEnum.CMPAY_ONLINE_REFUND_NOT_EXISTS.getMsgCd());
                refundQueryBO.setMessageInfo(MsgCodeEnum.CMPAY_ONLINE_REFUND_NOT_EXISTS.getMsgInfo());
            } else {
                refundQueryBO.setMessageCode(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgCd());
                refundQueryBO.setMessageInfo(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgInfo());
            }
        }
    }
}
