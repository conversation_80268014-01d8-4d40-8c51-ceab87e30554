package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.config.SplitSettlementBO;
import com.cmpay.payment.bo.config.SubMerchantRefreshAmountBO;
import com.cmpay.payment.bo.config.SubOrderInfoBO;
import com.cmpay.payment.bo.data.OrderFeeBO;
import com.cmpay.payment.constant.Constants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.CompleteOrderSplitService;
import com.cmpay.payment.service.SubMerchantIncomeService;
import com.cmpay.payment.service.SubOrderInfosResolveService;
import com.cmpay.payment.service.data.IDataFeeService;
import com.cmpay.payment.service.ext.data.IExtSplitSettlementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * @date 2023-11-25 10:00
 * <AUTHOR>
 * @Version 1.0
 */
@Service
public class CompleteOrderSplitServiceImpl implements CompleteOrderSplitService {
    private static final Logger logger = LoggerFactory.getLogger(CompleteOrderSplitServiceImpl.class);

    @Autowired
    SubMerchantIncomeService subMerchantIncomeService;
    @Autowired
    SubOrderInfosResolveService resolveService;
    @Autowired
    IDataFeeService feeService;
    @Autowired
    IExtSplitSettlementService splitSettlementService;

    /**
     * 分账支付订单，计算服务费
     *
     * @param tradeOrderDO 原订单的DO
     * @return
     */
    @Override
    public TradeOrderDO splitOrderFeeCalculate(TradeOrderDO tradeOrderDO) {
        if (JudgeUtils.isNull(tradeOrderDO)) {
            return tradeOrderDO;
        }

        // 如果原订单不为分账订单，或者分账参数为空，无法分账，直接返回
        if (!StringUtils.equals(tradeOrderDO.getSplitFlag(), Constants.Y)
                || StringUtils.isEmpty(tradeOrderDO.getSubOrderInfos())) {
            return tradeOrderDO;
        }
        //解析分账参数，遍历登记子结算表
        try {
            List<SubOrderInfoBO> subOrderInfoBOList = resolveService.resolve(tradeOrderDO.getSubOrderInfos());
            if (JudgeUtils.isEmpty(subOrderInfoBOList)) {
                BusinessException.throwBusinessException(MsgCodeEnum.SUB_ORDER_INFOS_CANNOT_BE_EMPTY);
            }
            //保留两位小数向上取整后，累加得出的服务费，只会比原订单乘费率后四舍五入多，不会比之前少
            BigDecimal totalFeeAmount = BigDecimal.ZERO;
            for (SubOrderInfoBO subOrderInfoBO : subOrderInfoBOList) {
                BigDecimal subOrderAmount = new BigDecimal(subOrderInfoBO.getSubOrderAmount());
                OrderFeeBO feeBO = new OrderFeeBO();
                feeBO.setOrderAmount(subOrderAmount);
                feeBO.setOrderRate(tradeOrderDO.getOrderRate());
                BigDecimal subFeeAmount = feeService.feeCalculateRoundUp(feeBO).getOrderFeeAmount();
                totalFeeAmount = totalFeeAmount.add(subFeeAmount);
            }
            //所有子订单总服务费之和
            tradeOrderDO.setOrderFeeAmount(totalFeeAmount);
        } catch (BusinessException e) {
            logger.info("分账服务费计算支付异常:" + e);
        } catch (Exception e) {
            logger.info("分账服务费计算支付系统异常,订单号:{}", tradeOrderDO.getOutTradeNo());
        }
        return tradeOrderDO;
    }


    /**
     * 分账退款订单，计算服务费
     *
     * @param tradeOrderDO 原订单的DO
     * @return
     */
    @Override
    public RefundOrderDO splitOrderRefundFee(RefundOrderDO refundOrderDO, TradeOrderDO tradeOrderDO) {
        try {
            //  全额退款: 从子结算表查询原支付主订单的所有子订单，登记退款请求号，订单标识改成R，登记进子结算表
            if (StringUtils.equals(refundOrderDO.getRefundFlag(), Constants.ZERO)) {
                refundOrderDO.setOrderFeeAmount(tradeOrderDO.getOrderFeeAmount());
            } else {
                SplitSettlementBO queryBO = new SplitSettlementBO();
                queryBO.setOutTradeNo(tradeOrderDO.getOutTradeNo());
                queryBO.setSubOrderNo(refundOrderDO.getSubOrderNo());
                queryBO.setSettlementStatus(Constants.S);
                SplitSettlementBO paySettlementBO = splitSettlementService.findBySubOrder(queryBO);
                if (JudgeUtils.isNull(paySettlementBO)) {
                    BusinessException.throwBusinessException(MsgCodeEnum.ORI_SUB_ORDER_ISNULL);
                }
                refundOrderDO.setOrderFeeAmount(paySettlementBO.getSubOrderFee());
            }
        } catch (Exception e) {
            logger.info("分账参数异常，解析失败，无法进行分账处理,订单号:{}", tradeOrderDO.getOutTradeNo());
        }

        return refundOrderDO;
    }


    /**
     * 明确退款失败，则将金额自增回来
     *
     * @param refundOrderDO
     * @param tradeOrderDO
     * @return
     */
    @Override
    public RefundOrderDO refundFailIncrement(RefundOrderDO refundOrderDO, TradeOrderDO tradeOrderDO) {
        if (JudgeUtils.isNullAny(refundOrderDO, tradeOrderDO)) {
            return null;
        }
        if (JudgeUtils.isEmpty(tradeOrderDO.getSubMerchantNo())) {
            return refundOrderDO;
        }
        // 如果不是今天发起的退款，自增到今天会导致今天的支付金额变大，自增到昨天也没意义，直接退出
        if (JudgeUtils.notEquals(refundOrderDO.getOrderDate(), DateTimeUtils.getCurrentDateStr())) {
            return refundOrderDO;
        }
        // 刷新子商户支付金额
        SubMerchantRefreshAmountBO refreshAmountBO = new SubMerchantRefreshAmountBO();
        refreshAmountBO.setSubMerchantNo(tradeOrderDO.getSubMerchantNo());
        refreshAmountBO.setAmount(refundOrderDO.getOrderAmount());
        refreshAmountBO.setCountDate(refundOrderDO.getOrderDate());
        subMerchantIncomeService.refundFailSubtractAmount(refreshAmountBO);
        return refundOrderDO;
    }

    /**
     * 检查订单与退款订单是否为分账订单
     *
     * @param refundOrderDO
     * @param tradeOrderDO
     * @return
     */
    @Override
    public boolean checkSplitOrder(RefundOrderDO refundOrderDO, TradeOrderDO tradeOrderDO) {
        if (JudgeUtils.isNullAny(refundOrderDO, tradeOrderDO)) {
            return false;
        }
        // 如果不为分账订单，或者原支付订单分账参数为空
        if (!StringUtils.equals(refundOrderDO.getSplitFlag(), Constants.Y)) {
            return false;
        }
        // 如果原订单不为分账订单，或者分账参数为空，无法分账，直接返回
        if (!StringUtils.equals(tradeOrderDO.getSplitFlag(), Constants.Y)
                || StringUtils.isEmpty(tradeOrderDO.getSubOrderInfos())) {
            return false;
        }
        return true;
    }

}
