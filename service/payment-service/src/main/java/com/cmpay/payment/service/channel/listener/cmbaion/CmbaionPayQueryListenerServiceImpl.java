package com.cmpay.payment.service.channel.listener.cmbaion;

import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.cmbaion.CmbaionPayQueryChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @author： PengAnHai
 * @date： 2024-08-21
 * @description：招行一网通支付查询处理监听类
 * @modifiedBy：
 * @version: 1.0
 */
@Service
public class CmbaionPayQueryListenerServiceImpl extends PaymentListenerService<PaymentQueryBO> {
    @Autowired
    ApplicationContext applicationContext;

    @EventListener
    @Override
    public void execute(PaymentQueryBO paymentQueryBO) {
        super.execute(paymentQueryBO);
    }

    @Override
    protected boolean checkChannelExecutable(PaymentQueryBO paymentQueryBO) {
        return Optional.ofNullable(paymentQueryBO)
                .map(PaymentQueryBO::getPaymentOrder)
                .map(TradeOrderDO::getAimProductCode)
                .map(route -> StringUtils.equalsIgnoreCase(PaymentWayEnum.CMBAION.name(), route))
                .orElse(false);
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(PaymentQueryBO paymentQueryBO) {
        return getBean(CmbaionPayQueryChannelService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return this.applicationContext;
    }

}
