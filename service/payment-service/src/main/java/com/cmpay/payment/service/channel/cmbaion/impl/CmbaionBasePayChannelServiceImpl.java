package com.cmpay.payment.service.channel.cmbaion.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.channel.common.utils.JudgeUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.constant.Constants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.cmbaion.CmbaionConstants;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.PaymentOrderHandleService;
import com.cmpay.payment.service.RefundOrderHandleService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.StringJoiner;

/**
 * @author： PengAnHai
 * @date： 2024-08-13
 * @description： 招行一网通公共支付处理实现类
 * @modifiedBy：
 * @version: 1.0
 */
public abstract class CmbaionBasePayChannelServiceImpl {

    @Autowired
    private PaymentOrderHandleService paymentOrderHandleService;

    @Autowired
    private RefundOrderHandleService refundOrderHandleService;

    /**
     * 构建包含请求数据的 JSONObject。
     * 此方法用于创建一个包含基本请求参数的 JSONObject，包括版本号、字符集、签名和签名类型。
     *
     * @param sign 签名字符串，由调用者提供，用于验证请求的合法性。
     * @return 返回一个填充了基本请求参数的 JSONObject。
     */
    protected JSONObject setRequestData(String sign) {
        JSONObject requestData = new JSONObject();
        // 设置版本号，设置字符集,设置签名,设置签名类型
        requestData.put("version", CmbaionConstants.VERSION);
        requestData.put("charset", CmbaionConstants.CHARSET);
        requestData.put("sign", sign);
        requestData.put("signType", CmbaionConstants.SHA256);
        return requestData;
    }

    /**
     * 将 JSONObject 转换为使用指定分隔符拼接的字符串。
     * 此方法遍历 JSONObject 中的所有键值对，并将它们转换为 "key=value" 格式，
     * 然后使用指定的分隔符将它们拼接成一个字符串。
     *
     * @param jsonObject   要转换的 JSONObject。
     * @param spliceSymbol 用于拼接各个键值对的字符串，例如 "&"。
     * @return 返回拼接后的字符串，例如形式为 "key1=value1&key2=value2&..."。
     */
    protected String convertToString(JSONObject jsonObject, String spliceSymbol) {
        // 创建一个 StringJoiner，使用提供的分隔符
        StringJoiner joiner = new StringJoiner(spliceSymbol);
        // 遍历 JSONObject 的所有键
        for (String key : jsonObject.keySet()) {
            // 将每个键值对转换为 "key=value" 格式并添加到 StringJoiner 中
            joiner.add(key + Constants.EQUAL_SIGN + jsonObject.get(key));
        }
        // 返回最终拼接的字符串
        return joiner.toString();
    }

    /**
     * 检查支付订单的参数是否有效。
     *
     * @param payOrderBO 支付订单业务对象，包含需要验证的参数。
     * @return 如果参数验证通过，则返回 true。
     * @throws BusinessException 如果任何参数验证失败，则抛出业务异常。
     */
    protected boolean checkParams(PayOrderBO payOrderBO) {
        if (JudgeUtils.isBlank(payOrderBO.getTimeoutExpress())) {
            // 如果超时表达式为空，抛出业务异常
            BusinessException.throwBusinessException(MsgCodeEnum.TIMEOUTEXPRESS_IS_EMPTY);
        } else if (JudgeUtils.notEquals(getUnitsOfTime(payOrderBO.getTimeoutExpress()), Constants.MINUTE)) {
            // 如果单位不是分钟，抛出业务异常
            BusinessException.throwBusinessException(MsgCodeEnum.UNITS_OF_TIME_IS_MINUTE);
        } else if (payOrderBO.getTimeoutExpress().length() > 3) {
            // 如果时间长度超出3为，抛出业务异常
            BusinessException.throwBusinessException(MsgCodeEnum.TIMEOUT_RANGE);
        }
        // 如果所有检查都通过，返回 true
        return true;
    }

    /**
     * 从超时表达式中提取时间跨度部分。
     * 假设超时表达式的格式为 "30m"，其中 "30" 是时间跨度，"m" 是单位。
     *
     * @param timeoutExpress 超时表达式字符串。
     * @return 返回超时表达式中的时间跨度部分。
     */
    protected String getExpireTimeSpan(String timeoutExpress) {
        int len = timeoutExpress.length();
        return timeoutExpress.substring(0, len - 1);
    }

    /**
     * 从超时表达式中提取时间单位。
     * 假设超时表达式的格式为 "30m"，其中 "30" 是时间跨度，"m" 是单位。
     *
     * @param timeoutExpress 超时表达式字符串。
     * @return 返回超时表达式中的最后一个字符，即时间单位。
     */
    protected String getUnitsOfTime(String timeoutExpress) {
        int len = timeoutExpress.length();
        return timeoutExpress.substring(len - 1);
    }

    /**
     * 从银行商户号中提取分行号。
     * 分行号通常是银行商户号的前4个字符。
     *
     * @param bankMerchantNo 银行商户号，包含分行号和商户号。
     * @return 分行号，为银行商户号的前4个字符。
     */
    protected String getBranchNo(String bankMerchantNo) {
        return bankMerchantNo.substring(0,4);
    }

    /**
     * 从银行商户号中提取商户号。
     * 商户号是银行商户号除了前4个字符之外的所有字符。
     *
     * @param bankMerchantNo 银行商户号，包含分行号和商户号。
     * @return 商户号，为银行商户号去除前4个字符后的部分。
     */
    protected String getMerchantNo(String bankMerchantNo) {
        return bankMerchantNo.substring(4);
    }

    /**
     * 配置订单的费率信息
     *
     * @param tradeOrder
     * @return
     */
    protected TradeOrderDO setPayOrderRate(TradeOrderDO tradeOrder) {
        return paymentOrderHandleService.setPayOrderRate(tradeOrder);
    }

    protected boolean notifyMerchant(TradeOrderDO tradeOrder) {
        return paymentOrderHandleService.notifyMerchant(tradeOrder);
    }


    protected RefundOrderQueryBO setPayOrderStatus(RefundOrderQueryBO refundQueryBO) {
        return refundOrderHandleService.setPayOrderStatus(refundQueryBO);
    }

    protected RefundOrderQueryBO setPayOrderStatusRefFail(RefundOrderQueryBO refundQueryBO) {
        return refundOrderHandleService.setPayOrderStatusRefFail(refundQueryBO);
    }

    protected RefundOrderQueryBO setOrderFeeAmount(RefundOrderQueryBO refundQueryBO) {
        return refundOrderHandleService.setOrderFeeAmount(refundQueryBO);
    }


    protected boolean notifyMerchant(RefundOrderDO refundOrderDO) {
        return refundOrderHandleService.notifyMerchant(refundOrderDO);
    }

}
