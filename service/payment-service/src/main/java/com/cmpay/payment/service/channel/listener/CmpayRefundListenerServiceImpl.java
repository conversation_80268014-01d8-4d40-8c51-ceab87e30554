package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.config.ContractDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.cmpay.*;
import com.cmpay.payment.service.ext.config.IExtContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Locale;

/**
 * Created on 2018/11/26
 * cmpay渠道 监听类
 *
 * @author: sun_zhh
 */
@Service
@Transactional(propagation = Propagation.REQUIRES_NEW)
public class CmpayRefundListenerServiceImpl extends PaymentListenerService<RefundBO> {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    private IExtContractService contractService;
    /**
     * cmpay退款交易
     *
     * @param eventBO
     */
    @EventListener
    @Override
    public void execute(RefundBO eventBO) {
        super.execute(eventBO);
    }

    @Override
    protected boolean checkChannelExecutable(RefundBO refundBO) {

        return refundBO != null && refundBO.getOriginalPayTradeOrder() != null &&  StringUtils.equalsIgnoreCase(PaymentWayEnum.CMPAY.name(),
                refundBO.getOriginalPayTradeOrder().getAimProductCode());
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(RefundBO refundBO) {

        if (refundBO == null || refundBO.getRefundOrder() == null) {
            return null;
        }
        ContractDO contract = contractService.getContract(refundBO.getOriginalPayTradeOrder().getBankMerchantNo());
        refundBO.setContractSecureValue(contract.getSecretKey());
        refundBO.setSignMethod(contract.getSignMethod());
        //和包免密签约退款需要公钥
        refundBO.setPublicKey(contract.getPublicKey());
        String payWayCode = refundBO.getRefundOrder().getPayWayCode().toUpperCase(Locale.ENGLISH);
        if (PaymentSceneEnum.BARCODE.name().equals(payWayCode)
                || PaymentSceneEnum.SCAN.name().equals(payWayCode)) {
            //返回cmpay Cloud退款接口
            return getBean(CmpayCloudRefundChannelService.class) ;
        }else if(PaymentSceneEnum.CONTRACTPAY.name().equals(payWayCode)){
            return getBean(CmpayContractRefundChannelService.class);
        }else if(PaymentSceneEnum.CMPAYACCOUNTPAY.name().equals(payWayCode)){
            return getBean(CmpayAccountRefundChannelService.class);
        }else {
            // 返回cmpay 远程退款接口
            return getBean(CmpayOnlineRefundChannelService.class) ;
        }
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
