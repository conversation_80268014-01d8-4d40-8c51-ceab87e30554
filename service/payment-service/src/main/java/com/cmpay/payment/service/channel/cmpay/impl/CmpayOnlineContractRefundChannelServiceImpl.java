package com.cmpay.payment.service.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.ContractOnlineRefundBO;
import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.bo.config.SubMerchantRefreshAmountBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dto.cmpay.CmpayOnlineContractRefundReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayOnlineContractRefundRspDTO;
import com.cmpay.payment.entity.CmpayRefundJrnDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.CompleteOrderSplitService;
import com.cmpay.payment.service.channel.cmpay.CmpayCommonService;
import com.cmpay.payment.service.channel.cmpay.CmpayContractRefundChannelService;
import com.cmpay.payment.service.ext.ExtCmpayRefundJrnService;
import com.cmpay.payment.service.SubMerchantIncomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Created on 2018/12/4
 *
 * @author: lixiang
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayOnlineContractRefundChannelServiceImpl implements CmpayContractRefundChannelService {
    @Autowired
    ExtCmpayRefundJrnService extCmpayRefundJrnService;

    @Autowired
    private CmpayCommonService cmpayCommonService;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Autowired
    private CompleteOrderSplitService splitService;
    @Autowired
    private SubMerchantIncomeService refundConfineService;
    @Override
    public void send(RefundBO refundBO) {
        cmpayOnlineContractRefund(refundBO);
    }

    @Override
    public void cmpayOnlineContractRefund(RefundBO refundBO) {
        // 记录和包免密流水
        CmpayRefundJrnDO cmpayRefundJrnDO=new CmpayRefundJrnDO();
        cmpayRefundJrnDO.setTradeOrderNo(refundBO.getOutRequestNo());
        List<CmpayRefundJrnDO> find=extCmpayRefundJrnService.find(cmpayRefundJrnDO);
        if(find.size()==0) {
            TradeOrderDO originalPayTradeOrderDO = refundBO.getOriginalPayTradeOrder();;
            String subMerchant = StringUtils.isEmpty(refundBO.getSubMerchant()) ? originalPayTradeOrderDO.getSubMerchantNo() : refundBO.getSubMerchant();
            //原订单号子商户不为空，进行退款限制
            if(StringUtils.isNotEmpty(subMerchant)){
                // 查询和包退款流水，如果没有这笔退款，说明是第一次发起，则进行限制
                SubMerchantRefreshAmountBO refreshAmountBO = new SubMerchantRefreshAmountBO();
                refreshAmountBO.setSubMerchantNo(subMerchant);
                refreshAmountBO.setAmount(refundBO.getRefundOrder().getOrderAmount());
                refundConfineService.refundConfine(refreshAmountBO);
            }

            try {
                cmpayRefundJrnDO = extCmpayRefundJrnService.insertByNewTranscation(refundBO);
            } catch (Exception e) {
                // 插入失败，报错，则自增回去
                splitService.refundFailIncrement(refundBO.getRefundOrder(), originalPayTradeOrderDO);
                // 报错退出
                BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ERROR);
            }
        }else {
            cmpayRefundJrnDO=find.get(0);
        }
        try {
            ContractOnlineRefundBO contractOnlineRefundBO = onlineRefund(refundBO);
            cmpayRefundJrnDO.setTradeStatus(contractOnlineRefundBO.getStatus());
            cmpayRefundJrnDO.setReturnMsg(contractOnlineRefundBO.getReturnCode());
            cmpayRefundJrnDO.setReturnMsgInfo(contractOnlineRefundBO.getMessage());
            refundBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
            refundBO.setStatus(contractOnlineRefundBO.getStatus());
            refundBO.setErrMsgCd(contractOnlineRefundBO.getReturnCode());
            refundBO.setErrMsgInfo(contractOnlineRefundBO.getMessage());
            refundBO.getRefundOrder().setRefundId(contractOnlineRefundBO.getPayNo());
        } catch (Exception e) {
            //系统异常，比如网络异常 记录异常日志
            cmpayCommonService.cmapyThrowClassHandle(cmpayRefundJrnDO,e);
            throw e;
        } finally {
            //将异常信息更新到 cmpay退款流水记录
            extCmpayRefundJrnService.updateByNewTranscation(cmpayRefundJrnDO);
        }
    }

    private ContractOnlineRefundBO onlineRefund(RefundBO refundBO){
        ContractOnlineRefundBO contractOnlineRefundBO = new ContractOnlineRefundBO();
        CmpayOnlineContractRefundReqDTO refundReqDTO = new CmpayOnlineContractRefundReqDTO();
        refundReqDTO.setMerchantId(refundBO.getOriginalPayTradeOrder().getBankMerchantNo());
        refundReqDTO.setRequestId(refundBO.getRefundOrder().getOutTradeNo());
        refundReqDTO.setSignType(refundBO.getSignMethod());
        //外面查过一次contract表，直接取公钥
        refundReqDTO.setMerchantCert(refundBO.getPublicKey());
        refundReqDTO.setHmac(refundBO.getContractSecureValue());
        refundReqDTO.setVersion(CommonConstant.CMPAY_ONLINE_CONTRACT_REFUND_VERSION);
        refundReqDTO.setType(CommonConstant.CMPAY_ONLINE_CONTRACT_REFUND_TYPE);
        refundReqDTO.setOrderId(refundBO.getOriginalPayTradeOrder().getTradeOrderNo());
        refundReqDTO.setAmount(refundBO.getRefundAmount().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_DOWN));
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(CmpayPayChannelEnum.CMPAY_ONLINE);
        request.setBusiType(CmpayPayChannelEnum.CMPAY_ONLINE_CONTRACT_REFUND.getName());
        request.setSource(CmpayPayChannelEnum.CMPAY_ONLINE);
        request.setTarget(refundReqDTO);
        GenericRspDTO<Response> genericRspDTO;
        if (StringUtils.equalsAny(refundBO.getSourceApp(), AppEnum.integrationbatch.name(), AppEnum.integrationshecdule.name())) {
            genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        } else {
            genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        }
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .map(result -> {
                    handleOnlineRefund(contractOnlineRefundBO,(CmpayOnlineContractRefundRspDTO) result);
                    return result;
                }).orElseThrow(() ->new BusinessException(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR));
        return contractOnlineRefundBO;
    }


    private void handleOnlineRefund(ContractOnlineRefundBO contractOnlineRefundBO, CmpayOnlineContractRefundRspDTO cmpayOnlineContractRefundRspDTO){
        if (JudgeUtils.isNull(cmpayOnlineContractRefundRspDTO)) {
            contractOnlineRefundBO.setReturnCode(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgCd());
            contractOnlineRefundBO.setMessage(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgInfo());
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR);
        }
        if (!StringUtils.equals(CmpayConstants.RETURN_CODE,cmpayOnlineContractRefundRspDTO.getReturnCode())) {
            contractOnlineRefundBO.setReturnCode(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgCd());
            contractOnlineRefundBO.setMessage(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgInfo());
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR);
        }
        //和包返回退款流水号
        if (JudgeUtils.isBlank(cmpayOnlineContractRefundRspDTO.getPayNo())) {
            contractOnlineRefundBO.setReturnCode(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgCd());
            contractOnlineRefundBO.setMessage(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgInfo());
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR);
        }
        if (StringUtils.equals(CmpayConstants.RETURN_CODE,cmpayOnlineContractRefundRspDTO.getReturnCode())) {
            contractOnlineRefundBO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            contractOnlineRefundBO.setReturnCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            contractOnlineRefundBO.setMessage(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
            contractOnlineRefundBO.setPayNo(cmpayOnlineContractRefundRspDTO.getPayNo());
            contractOnlineRefundBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
        } else {
            contractOnlineRefundBO.setReturnCode(cmpayOnlineContractRefundRspDTO.getReturnCode());
            contractOnlineRefundBO.setMessage(cmpayOnlineContractRefundRspDTO.getReturnCode());
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR);
        }
    }
}
