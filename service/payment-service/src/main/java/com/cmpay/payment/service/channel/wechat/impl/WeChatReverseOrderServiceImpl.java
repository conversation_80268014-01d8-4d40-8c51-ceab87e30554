package com.cmpay.payment.service.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PaymentCloseOrderBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentStatusEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.constant.wechat.WeChatTradeStatusEnum;
import com.cmpay.payment.dto.wechat.WXPayOrderqueryRequest;
import com.cmpay.payment.dto.wechat.WXPayOrderqueryResponse;
import com.cmpay.payment.dto.wechat.WXPayReverseRequest;
import com.cmpay.payment.dto.wechat.WXPayReverseResponse;
import com.cmpay.payment.service.channel.wechat.WeChatReverseOrderService;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatReverseOrderServiceImpl implements WeChatReverseOrderService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatReverseOrderServiceImpl.class);

    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;

    private static final String NEED_RECALL = "Y";

    /**
     * 微信支付撤销订单
     *
     * @param paymentCloseOrderBO
     */
    @Override
    public void weChatReverseOrder(PaymentCloseOrderBO paymentCloseOrderBO) {
        // 撤销订单前先查询订单状态
        WXPayOrderqueryRequest wxPayOrderqueryRequest = new WXPayOrderqueryRequest();
        wxPayOrderqueryRequest.setOutTradeNo(paymentCloseOrderBO.getOutTradeNo());
        wxPayOrderqueryRequest.setKey(paymentCloseOrderBO.getContractSecureValue());
        wxPayOrderqueryRequest.setMchId(paymentCloseOrderBO.getBankMerchantNo());

        Request orderQuery = new Request();
        orderQuery.setRequestId(UUID.randomUUID().toString());
        orderQuery.setSource(WXPayChannel.ORDERQUERY.getSource());
        orderQuery.setRoute(WXPayChannel.ORDERQUERY.getRoute());
        orderQuery.setBusiType(WXPayChannel.ORDERQUERY.getBusType());
        orderQuery.setTarget(wxPayOrderqueryRequest);
        logger.info("WeChatReverseOrderServiceImpl Source : {} Route : {} BusiType : {}",orderQuery.getSource(),orderQuery.getRoute(),orderQuery.getBusiType());

        GenericRspDTO<Response> orderQueryResponse = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, orderQuery));

        if (JudgeUtils.isNotSuccess(orderQueryResponse)) {
            BusinessException.throwBusinessException(orderQueryResponse);
        }

        Optional.ofNullable(orderQueryResponse)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> {
                    WXPayOrderqueryResponse response = (WXPayOrderqueryResponse) result;
                    if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
                        if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                            if (Objects.isNull(response.getTradeState())) {
                                // 支付状态未确认不允许关闭订单
                                logger.error("query order failure, weChat return message is : {}", response.getReturnMsg());
                                BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_QUERY_ORDER_FAILURE);
                            } else {
                                WeChatTradeStatusEnum status = EnumUtils.getEnum(WeChatTradeStatusEnum.class, response.getTradeState());
                                switch (status) {
                                    case PAYERROR:
                                    case USERPAYING:
                                    case NOTPAY:
                                        // 允许撤销订单
                                        break;
                                    case SUCCESS:
                                    case REFUND:
                                    case CLOSED:
                                    case REVOKED:
                                    default:
                                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_QUERY_ORDER_FAILURE);
                                }
                            }
                        } else {
                            logger.error("query order failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                            WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getErrCode());
                            switch (errorCodeEnum) {
                                default:
                                    BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_QUERY_ORDER_FAILURE);
                            }
                        }
                    } else {
                        // 支付状态未确认不允许关闭订单
                        logger.error("query order failure, weChat return message is : {}", response.getReturnMsg());
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_QUERY_ORDER_FAILURE);
                    }
                });

        // 撤单
        WXPayReverseRequest wxPayReverseRequest = new WXPayReverseRequest();
        wxPayReverseRequest.setOutTradeNo(paymentCloseOrderBO.getOutTradeNo());
        wxPayReverseRequest.setKey(paymentCloseOrderBO.getContractSecureValue());
        wxPayReverseRequest.setMchId(paymentCloseOrderBO.getBankMerchantNo());

        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.REVERSE.getSource());
        request.setRoute(WXPayChannel.REVERSE.getRoute());
        request.setBusiType(WXPayChannel.REVERSE.getBusType());
        request.setTarget(wxPayReverseRequest);

        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(paymentCloseOrderBO, (WXPayReverseResponse) result));

    }

    @Override
    public void send(PaymentCloseOrderBO paymentCloseOrderBO) {
        weChatReverseOrder(paymentCloseOrderBO);
    }

    private void handleResult(PaymentCloseOrderBO paymentCloseOrderBO, WXPayReverseResponse response) {
        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                if (StringUtils.equals(NEED_RECALL, response.getRecall())) {
                    logger.warn("reverse order need recall");
                    paymentCloseOrderBO.setTradeStatus(PaymentStatusEnum.WAIT_PAY.name());
                } else {
                    paymentCloseOrderBO.setTradeStatus(PaymentStatusEnum.TRADE_CLOSED.name());
                }
            } else {
                logger.error("reverse order failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                paymentCloseOrderBO.setTradeStatus(PaymentStatusEnum.WAIT_PAY.name());
            }
        } else {
            logger.error("reverse order failure, weChat return message is : {}", response.getReturnMsg());
            paymentCloseOrderBO.setTradeStatus(PaymentStatusEnum.WAIT_PAY.name());
        }
    }
}
