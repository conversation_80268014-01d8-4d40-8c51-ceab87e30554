package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.ProtocolPaymentBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.constant.protocol.DcepUnifiedOrderTypeEnum;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.DcepUnifiedOrderReq;
import com.cmpay.payment.dto.dcep.DcepUnifiedOrderRsp;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.DcepPaymentScanPayService;
import com.cmpay.payment.util.LemonAmount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/8/3
 */
@Service
public class DcepPaymentScanPayServiceImpl extends AbstractDcepRequestServiceImpl implements DcepPaymentScanPayService {

    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;

    @Override
    public void send(ProtocolPaymentBO protocolPaymentBO) {
        dcepScanPayment(protocolPaymentBO);
    }

    @Override
    public void dcepScanPayment(ProtocolPaymentBO protocolPaymentBO) {
        DcepUnifiedOrderReq unifiedOrderReq = new DcepUnifiedOrderReq();
        buildRequestDto(unifiedOrderReq, protocolPaymentBO, protocolPaymentBO.getChannelNo());
        unifiedOrderReq.setOrgNo(dcepPaymentProperties.getNewOrgNo());
        unifiedOrderReq.setSubMerchantNo(protocolPaymentBO.getSubMerchantId());
        unifiedOrderReq.setGoodsInfo(protocolPaymentBO.getProductDesc());
        unifiedOrderReq.setBusiType(protocolPaymentBO.getBusinessType());
        unifiedOrderReq.setBusiCode(protocolPaymentBO.getBusinessCode());
        unifiedOrderReq.setOutTranNo(protocolPaymentBO.getOutTradeNo());
        unifiedOrderReq.setExpireMinutes(Integer.parseInt(protocolPaymentBO.getExpireMinutes()));
        unifiedOrderReq.setOutTranDate(protocolPaymentBO.getOrderDate());
        unifiedOrderReq.setOutTranTime(protocolPaymentBO.getOrderTime());
        unifiedOrderReq.setAmount(Long.parseLong(new LemonAmount(protocolPaymentBO.getRealAmount()).yuan2fen()));
        unifiedOrderReq.setCurrentType(DcepConstants.CURRENT_TYPE);
        unifiedOrderReq.setTransType(DcepUnifiedOrderTypeEnum.valueOf(protocolPaymentBO.getScene().toUpperCase()).getOrderType());
        unifiedOrderReq.setNotifyUrl(dcepPaymentProperties.getNotifyUrl());
        unifiedOrderReq.setOpenId(protocolPaymentBO.getOpenId());
        buildDcepRequest(unifiedOrderReq, protocolPaymentBO, DcepPaymentChannelEnum.PAYMENT_UNIFIED_ORDER.getName());
    }

    @Override
    protected BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        DcepUnifiedOrderRsp unifiedOrderRsp = (DcepUnifiedOrderRsp) abstractDcepRsp;
        ProtocolPaymentBO protocolPaymentBO = (ProtocolPaymentBO) baseDcepHandlerBO;
        if (StringUtils.equals(unifiedOrderRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            DcepUnifiedOrderRsp.Data data = unifiedOrderRsp.getData();
            if (!StringUtils.isBlank(data.getCodeUrl())) {
                protocolPaymentBO.setCodeUrl(data.getCodeUrl());
                return protocolPaymentBO;
            }
            Optional.ofNullable(data.getErrCode()).ifPresent(errCode -> protocolPaymentBO.setErrCode(data.getErrCode()));
            Optional.ofNullable(data.getErrMsg()).ifPresent(errMsg -> protocolPaymentBO.setErrCodeDes(data.getErrMsg()));
            protocolPaymentBO.setTradeStatus(OrderStatusEnum.TRADE_FAIL.name());
        } else {
            protocolPaymentBO.setErrCode(unifiedOrderRsp.getCode());
            protocolPaymentBO.setErrCodeDes(unifiedOrderRsp.getMsg());
            protocolPaymentBO.setTradeStatus(OrderStatusEnum.TRADE_FAIL.name());
        }
        protocolPaymentBO.setErrMsgCd(protocolPaymentBO.getErrCode());
        protocolPaymentBO.setErrMsgInfo(protocolPaymentBO.getErrCodeDes());
        return protocolPaymentBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }
}
