package com.cmpay.payment.service.channel.dcep;

import com.cmpay.payment.bo.protocol.ProtocolApplyBO;
import com.cmpay.payment.service.channel.PaymentChannelService;

/**
 * Created on 2020/5/12
 *
 * @author: huang_yh1
 */
public interface DcepUserProtocolApplyService extends PaymentChannelService<ProtocolApplyBO> {
    /**
     * 协议申请
     *
     * @param protocolApplyBO
     */
    void protocolApply(ProtocolApplyBO protocolApplyBO);
}
