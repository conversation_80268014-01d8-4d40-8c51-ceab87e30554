package com.cmpay.payment.service;

import java.time.LocalDateTime;

/**
 * @date 2023-03-24 14:58
 * <AUTHOR>
 * @Version 1.0
 */
public interface SensorsDataService {

    /**
     * 时延处理
     * @param serviceName 接口名
     * @param startTime 接口接受请求时间
     */
    void functionDelay(String serviceName, LocalDateTime startTime);

    /**
     * 接口调用埋点，接口调用成功埋点
     * @param serviceName 接口名
     */
    void functionRequest(String serviceName);

    /**
     *
     * @param serviceName 接口名
     */
    void requestSuccess(String serviceName);

}
