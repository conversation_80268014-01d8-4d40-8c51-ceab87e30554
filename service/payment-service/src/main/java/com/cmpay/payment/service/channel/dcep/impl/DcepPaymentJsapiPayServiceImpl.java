package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.ProtocolPaymentBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.constant.protocol.DcepUnifiedOrderTypeEnum;
import com.cmpay.payment.dto.dcep.*;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.DcepPaymentJsapiPayService;
import com.cmpay.payment.util.AES256Util;
import com.cmpay.payment.util.LemonAmount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/7/13
 */
@Service
public class DcepPaymentJsapiPayServiceImpl extends AbstractDcepRequestServiceImpl implements DcepPaymentJsapiPayService {
    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;
    private static final String BIZ_CONTENT = "?biz_content=%7b%22orderInfo%22:%7b%22out_trade_no%22:%22";
    private static final String CONTENT = "%22%7d%7d";
    private static final String DEFAULT_IMEI = "7F1EA4C3-951B-4E88-BED2-D25ED9A4F837";
    private static final String DEFAULT_IP = "**************";
    private static final String DEFAULT_MAC = "4c:7c:5f:20:11:a9";

    @Override
    public void dcepJsapiPayment(ProtocolPaymentBO protocolPaymentBO) {
        getUserOpenId(protocolPaymentBO);
    }

    @Override
    public void send(ProtocolPaymentBO protocolPaymentBO) {
        dcepJsapiPayment(protocolPaymentBO);
    }

    @Override
    protected BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        ProtocolPaymentBO protocolPaymentBO = (ProtocolPaymentBO) baseDcepHandlerBO;
        if (StringUtils.equals(protocolPaymentBO.getTradeStatus(), OrderStatusEnum.TRADE_FAIL.name())) {
            return baseDcepHandlerBO;
        }
        if (abstractDcepRsp instanceof DcepUserGetOpenIdRsp) {
            handlerUserOpenId(abstractDcepRsp, baseDcepHandlerBO);
            unifiedPayment((ProtocolPaymentBO) baseDcepHandlerBO);
        }
        if (abstractDcepRsp instanceof DcepUnifiedOrderRsp) {
            handleUnifiedOrderResult(abstractDcepRsp, baseDcepHandlerBO);
            webMobilePay((ProtocolPaymentBO) baseDcepHandlerBO);
        }
        if (abstractDcepRsp instanceof DcepWapPayRsp) {
            handleWebMobilePay(abstractDcepRsp, baseDcepHandlerBO);
        }
        return baseDcepHandlerBO;
    }

    private void getUserOpenId(ProtocolPaymentBO protocolPaymentBO) {
        DcepUserGetOpenIdReq getOpenIdReq = new DcepUserGetOpenIdReq();
        buildRequestDto(getOpenIdReq, protocolPaymentBO, protocolPaymentBO.getChannelNo());
        getOpenIdReq.setOrgNo(dcepPaymentProperties.getNewOrgNo());
        getOpenIdReq.setSubMerchantNo(protocolPaymentBO.getSubMerchantId());
        getOpenIdReq.setAuthType(Integer.parseInt(protocolPaymentBO.getAuthType()));
        getOpenIdReq.setAuthCode(protocolPaymentBO.getAuthCode());
        buildDcepRequest(getOpenIdReq, protocolPaymentBO, DcepPaymentChannelEnum.USER_OPEN_ID_GET.getName());
    }

    private void handlerUserOpenId(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        DcepUserGetOpenIdRsp userGetOpenIdRsp = (DcepUserGetOpenIdRsp) abstractDcepRsp;
        ProtocolPaymentBO protocolPaymentBO = (ProtocolPaymentBO) baseDcepHandlerBO;
        if (StringUtils.equals(userGetOpenIdRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            DcepUserGetOpenIdRsp.Data data = userGetOpenIdRsp.getData();
            if (StringUtils.isBlank(data.getOpenId())) {
                Optional.ofNullable(data.getErrCode()).ifPresent(errCode -> protocolPaymentBO.setErrCode(data.getErrCode()));
                Optional.ofNullable(data.getErrMsg()).ifPresent(errMsg -> protocolPaymentBO.setErrCodeDes(data.getErrMsg()));
                protocolPaymentBO.setTradeStatus(OrderStatusEnum.TRADE_FAIL.name());
                return;
            }
            protocolPaymentBO.setOpenId(data.getOpenId());
        } else {
            protocolPaymentBO.setErrCode(userGetOpenIdRsp.getCode());
            protocolPaymentBO.setErrCodeDes(userGetOpenIdRsp.getMsg());
            protocolPaymentBO.setTradeStatus(OrderStatusEnum.TRADE_FAIL.name());
        }
    }

    private void unifiedPayment(ProtocolPaymentBO protocolPaymentBO) {
        DcepUnifiedOrderReq unifiedOrderReq = new DcepUnifiedOrderReq();
        buildRequestDto(unifiedOrderReq, protocolPaymentBO, protocolPaymentBO.getChannelNo());
        unifiedOrderReq.setOrgNo(dcepPaymentProperties.getNewOrgNo());
        unifiedOrderReq.setSubMerchantNo(protocolPaymentBO.getSubMerchantId());
        unifiedOrderReq.setMobile(AES256Util.encode(unifiedOrderReq.getSmxKey(), protocolPaymentBO.getMobileNo()));
        unifiedOrderReq.setGoodsInfo(protocolPaymentBO.getProductDesc());
        unifiedOrderReq.setBusiType(protocolPaymentBO.getBusinessType());
        unifiedOrderReq.setBusiCode(protocolPaymentBO.getBusinessCode());
        unifiedOrderReq.setOutTranNo(protocolPaymentBO.getOutTradeNo());
        unifiedOrderReq.setExpireMinutes(Integer.parseInt(protocolPaymentBO.getExpireMinutes()));
        unifiedOrderReq.setOutTranDate(protocolPaymentBO.getOrderDate());
        unifiedOrderReq.setOutTranTime(protocolPaymentBO.getOrderTime());
        unifiedOrderReq.setAmount(Long.parseLong(new LemonAmount(protocolPaymentBO.getRealAmount()).yuan2fen()));
        unifiedOrderReq.setCurrentType(DcepConstants.CURRENT_TYPE);
        unifiedOrderReq.setTransType(DcepUnifiedOrderTypeEnum.valueOf(protocolPaymentBO.getScene().toUpperCase()).getOrderType());
        unifiedOrderReq.setNotifyUrl(dcepPaymentProperties.getNotifyUrl());
        unifiedOrderReq.setOpenId(protocolPaymentBO.getOpenId());
        buildDcepRequest(unifiedOrderReq, protocolPaymentBO, DcepPaymentChannelEnum.PAYMENT_UNIFIED_ORDER.getName());
    }

    private void handleUnifiedOrderResult(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        DcepUnifiedOrderRsp unifiedOrderRsp = (DcepUnifiedOrderRsp) abstractDcepRsp;
        ProtocolPaymentBO protocolPaymentBO = (ProtocolPaymentBO) baseDcepHandlerBO;
        if (StringUtils.equals(unifiedOrderRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            DcepUnifiedOrderRsp.Data data = unifiedOrderRsp.getData();
            if (!StringUtils.isBlank(data.getPrepayId())) {
                protocolPaymentBO.setPrepayId(data.getPrepayId());
                return;
            }
            Optional.ofNullable(data.getErrCode()).ifPresent(errCode -> protocolPaymentBO.setErrCode(data.getErrCode()));
            Optional.ofNullable(data.getErrMsg()).ifPresent(errMsg -> protocolPaymentBO.setErrCodeDes(data.getErrMsg()));
            protocolPaymentBO.setTradeStatus(OrderStatusEnum.TRADE_FAIL.name());
        } else {
            protocolPaymentBO.setErrCode(unifiedOrderRsp.getCode());
            protocolPaymentBO.setErrCodeDes(unifiedOrderRsp.getMsg());
            protocolPaymentBO.setTradeStatus(OrderStatusEnum.TRADE_FAIL.name());
        }
        protocolPaymentBO.setErrMsgCd(protocolPaymentBO.getErrCode());
        protocolPaymentBO.setErrMsgInfo(protocolPaymentBO.getErrCodeDes());
    }

    private void webMobilePay(ProtocolPaymentBO protocolPaymentBO) {
        DcepWapPayReq wapPayReq = new DcepWapPayReq();
        buildRequestDto(wapPayReq, protocolPaymentBO, protocolPaymentBO.getChannelNo());
        wapPayReq.setOrgNo(dcepPaymentProperties.getNewOrgNo());
        wapPayReq.setSubMerchantNo(protocolPaymentBO.getSubMerchantId());
        wapPayReq.setVersion(DcepConstants.VERSION);
        wapPayReq.setPrepayId(protocolPaymentBO.getPrepayId());
        wapPayReq.setOutTranNo(protocolPaymentBO.getOutTradeNo());
        wapPayReq.setTermImei(DEFAULT_IMEI);
        wapPayReq.setTermIp(DEFAULT_IP);
        wapPayReq.setTermMac(DEFAULT_MAC);
        Optional.ofNullable(protocolPaymentBO.getTerminalImei()).ifPresent(imei -> wapPayReq.setTermImei(protocolPaymentBO.getTerminalImei()));
        Optional.ofNullable(protocolPaymentBO.getTerminalIp()).ifPresent(ip -> wapPayReq.setTermIp(protocolPaymentBO.getTerminalIp()));
        Optional.ofNullable(protocolPaymentBO.getTerminalMac()).ifPresent(mac -> wapPayReq.setTermMac(protocolPaymentBO.getTerminalMac()));
        wapPayReq.setJumpBackUrl(dcepPaymentProperties.getPageNotifyUrl() + BIZ_CONTENT + protocolPaymentBO.getTradeOrderNo() + CONTENT);
        buildDcepRequest(wapPayReq, protocolPaymentBO, DcepPaymentChannelEnum.PAYMENT_WAP_PAY.getName());
    }

    private void handleWebMobilePay(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        DcepWapPayRsp wapPayRsp = (DcepWapPayRsp) abstractDcepRsp;
        ProtocolPaymentBO protocolPaymentBO = (ProtocolPaymentBO) baseDcepHandlerBO;
        protocolPaymentBO.setErrCode(wapPayRsp.getCode());
        protocolPaymentBO.setErrCodeDes(wapPayRsp.getMsg());
        if (StringUtils.equals(wapPayRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            DcepWapPayRsp.Data data = wapPayRsp.getData();
            if (StringUtils.isBlank(data.getPayForm())) {
                Optional.ofNullable(data.getErrCode()).ifPresent(errCode -> protocolPaymentBO.setErrCode(data.getErrCode()));
                Optional.ofNullable(data.getErrMsg()).ifPresent(errMsg -> protocolPaymentBO.setErrCodeDes(data.getErrMsg()));
                protocolPaymentBO.setTradeStatus(OrderStatusEnum.TRADE_FAIL.name());
                return;
            }
            protocolPaymentBO.setPayUrl(data.getPayForm());
        } else {
            protocolPaymentBO.setTradeStatus(OrderStatusEnum.TRADE_FAIL.name());
        }
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }
}
