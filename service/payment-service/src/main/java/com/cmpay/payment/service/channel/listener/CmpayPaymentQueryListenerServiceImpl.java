package com.cmpay.payment.service.channel.listener;

import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.config.ContractDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.cmpay.CmpayAccountPaymentQueryChannelService;
import com.cmpay.payment.service.channel.cmpay.CmpayCloudPaymentQueryChannelService;
import com.cmpay.payment.service.channel.cmpay.CmpayOnlinePaymentQueryChannelService;
import com.cmpay.payment.service.ext.config.IExtContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created on 2018/12/03
 * cmpay支付查询渠道 监听类
 *
 * @author: li_zhen
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayPaymentQueryListenerServiceImpl extends PaymentListenerService<PaymentQueryBO> {
	@Autowired
	ApplicationContext applicationContext;
	@Autowired
	private IExtContractService contractService;

	@EventListener
	@Override
	public void execute(PaymentQueryBO paymentQueryBO) {
		super.execute(paymentQueryBO);
	}
	@Override
	protected boolean checkChannelExecutable(PaymentQueryBO paymentQueryBO) {
		return paymentQueryBO != null && paymentQueryBO.getPaymentOrder() != null &&
				StringUtils.equalsIgnoreCase(paymentQueryBO.getPaymentOrder().getAimProductCode(),PaymentWayEnum.CMPAY.name());
	}

	@Override
	protected PaymentChannelService determinateChannelExecuteBean(PaymentQueryBO paymentQueryBO) {
		if (paymentQueryBO == null || paymentQueryBO.getPaymentOrder() == null) {
			return null;
		}
		ContractDO contract = contractService.getContract(paymentQueryBO.getPaymentOrder().getBankMerchantNo());
		paymentQueryBO.setContractSecureValue(contract.getSecretKey());
		paymentQueryBO.setSignMethod(contract.getSignMethod());
		switch (PaymentSceneEnum.valueOf(paymentQueryBO.getPaymentOrder().getPayWayCode().toUpperCase())){
			case SCAN:
			case BARCODE:
				return getBean(CmpayCloudPaymentQueryChannelService.class);
			case CMPAYACCOUNTPAY:
				return getBean(CmpayAccountPaymentQueryChannelService.class);
			default:
				return getBean(CmpayOnlinePaymentQueryChannelService.class);
		}
	}

	@Override
	protected ApplicationContext getApplicationContext() {
		return applicationContext;
	}
}
