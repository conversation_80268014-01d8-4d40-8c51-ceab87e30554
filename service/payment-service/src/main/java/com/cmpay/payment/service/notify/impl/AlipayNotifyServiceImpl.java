package com.cmpay.payment.service.notify.impl;

import com.cmpay.framework.data.utils.GwaUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.RefundNotifyBO;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.notify.AlipayNotifyBO;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.TradeNotifyService;
import com.cmpay.payment.service.notify.IAlipayNotifyService;
import com.cmpay.payment.util.PaymentUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/14 13:34
 */
@Service
@Slf4j
public class AlipayNotifyServiceImpl implements IAlipayNotifyService {
    @Autowired
    private AsynCommonService asynCommonService;
    @Autowired
    private TradeNotifyService tradeNotifyService;
    @Override
    public void backendNotifyWithStatus(AlipayNotifyBO alipayNotifyBO) {
        // 不处理支付宝的交易结束通知
        if (JudgeUtils.equalsIgnoreCase(alipayNotifyBO.getTrade_status().name(), AlipayTradeStatusEnum.TRADE_FINISHED.name())) {
            return;
        }
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setTradeOrderNo(alipayNotifyBO.getOut_trade_no());
        tradeNotifyBO.setResult(alipayNotifyBO.getTrade_status().name());
        String paymentDateTime=alipayNotifyBO.getGmt_payment();
        if (JudgeUtils.isNotEmpty(paymentDateTime)) {
            paymentDateTime = PaymentUtils.getStringDate(paymentDateTime);
        } else {
            paymentDateTime = DateTimeUtils.getCurrentDateTimeStr();
        }
        tradeNotifyBO.setFinishDate(paymentDateTime);
        tradeNotifyBO.setPaymentRout(PaymentWayEnum.ALIPAY.name().toLowerCase());
        tradeNotifyBO.setAccountDate(paymentDateTime.substring(0, 8));
        tradeNotifyBO.setBnkTradeNo(alipayNotifyBO.getTrade_no());
        tradeNotifyBO.setMobileNumber(alipayNotifyBO.getBuyer_id());
        TradeNotifyBO notifyBO = tradeNotifyService.backendNotify(tradeNotifyBO);
        //后台通知
        asynCommonService.asyncNotify(notifyBO);
    }


    @Override
    public ResponseEntity<byte[]> alipayRefundNotify(AlipayNotifyBO alipayNotifyBO) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_HTML);
        try {
            String accountDate = PaymentUtils.getStringDate(alipayNotifyBO.getGmt_refund()).substring(0, 8);
            RefundOrderDO refundOrderDO = new RefundOrderDO();
            refundOrderDO.setOutTradeNo(alipayNotifyBO.getOut_biz_no());
            refundOrderDO.setOrgOrderNo(alipayNotifyBO.getOut_trade_no());
            refundOrderDO.setAccountDate(accountDate);
            RefundOrderQueryBO refundOrderQueryBO = new RefundOrderQueryBO();
            refundOrderQueryBO.setStatus(alipayNotifyBO.getTrade_status().name());
            refundOrderQueryBO.setRefundOrder(refundOrderDO);
            if (!StringUtils.equals(refundOrderQueryBO.getStatus(), OrderStatusEnum.TRADE_CLOSED.name())) {
                GwaUtils.setMsgCd(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
                return new ResponseEntity<>(CommonConstant.FAIL.getBytes(), headers, HttpStatus.OK);
            }
            //支付宝退款会传TRADE_CLOSED，要转成退款成功
            refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            RefundNotifyBO refundNotifyBO = new RefundNotifyBO();
            refundNotifyBO.setRoute(PaymentWayEnum.ALIPAY);
            refundNotifyBO.setRefundOrderQueryBO(refundOrderQueryBO);
            tradeNotifyService.refundResultNotify(refundNotifyBO);
        } catch (Exception e) {
            if(e instanceof BusinessException){
                log.warn("notifyAlipayRefund modify status fail:{}", e.getMessage());
            }else{
                log.error("notifyAlipayRefund modify status fail:", e);
            }
            GwaUtils.setMsgCd((e instanceof BusinessException) ? ((BusinessException) e).getMsgCd() : e.getMessage());
            return new ResponseEntity<>(e.getMessage().getBytes(), headers, HttpStatus.OK);
        }
        GwaUtils.setMsgCd(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
        return new ResponseEntity<>(CommonConstant.SUCCESS.getBytes(), headers, HttpStatus.OK);
    }
}
