package com.cmpay.payment.service.channel.cmpay.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dto.cmpay.CmpayPosScanPamentReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayPosScanPamentRspDTO;
import com.cmpay.payment.service.channel.cmpay.CmpayCommonService;
import com.cmpay.payment.service.channel.cmpay.CmpayScanPaymentChannelService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;

/**
 * Created on 2018/12/03
 * cmapy 云支付渠道  支付业务类
 *
 * @author: li_zhen
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayScanPaymentChannelServiceImpl implements CmpayScanPaymentChannelService {
    private static final Logger logger = LoggerFactory.getLogger(CmpayScanPaymentChannelServiceImpl.class);

    @Value("${cmpay.cpsBackendNotifyUrl:}")
    private String cpsBackendNotifyUrl;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private CmpayCommonService cmpayCommonService;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Override
    public void send(PayOrderBO payOrderBO) {
        cmpayScanPayment(payOrderBO);
    }

    @Override
    public void cmpayScanPayment(PayOrderBO payOrderBO) {
        CmpayPosScanPamentReqDTO scanCodePaymentReqDTO = new CmpayPosScanPamentReqDTO();
        scanCodePaymentReqDTO.setCharacterSet(CmpayConstants.CHARACTER_SET2);

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                scanCodePaymentReqDTO.setNotifyUrl(UrlUtils.replaceDomainOrIp(cpsBackendNotifyUrl,host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                scanCodePaymentReqDTO.setNotifyUrl(cpsBackendNotifyUrl);
            }
        } else {
            scanCodePaymentReqDTO.setNotifyUrl(cpsBackendNotifyUrl);
        }

        scanCodePaymentReqDTO.setMerchantId(payOrderBO.getPaymentId());
        scanCodePaymentReqDTO.setRequestId(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.CMPAY_JRN_NO, 18));
        scanCodePaymentReqDTO.setSignType(payOrderBO.getSignMethod());
        scanCodePaymentReqDTO.setType(CmpayConstants.CPOS_SCAN_PAY);
        scanCodePaymentReqDTO.setVersion(CmpayConstants.CPOS_VERSION);
        scanCodePaymentReqDTO.setAmount(payOrderBO.getRealAmount().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_DOWN));
        scanCodePaymentReqDTO.setOrderDate(payOrderBO.getOrderDate());
        scanCodePaymentReqDTO.setOrderId(payOrderBO.getTradeOrderNo());
        scanCodePaymentReqDTO.setProductName(payOrderBO.getSubject());
        scanCodePaymentReqDTO.setPayWay(getVersion(PaymentWayEnum.valueOf(payOrderBO.getPayWay().toUpperCase())));
        scanCodePaymentReqDTO.setPeriod(new BigDecimal(payOrderBO.getValidityNumber()));
        scanCodePaymentReqDTO.setPeriodUnit(payOrderBO.getValidityUnit());
        scanCodePaymentReqDTO.setStoreBrand(payOrderBO.getHallCode());
        scanCodePaymentReqDTO.setCityBranchNm(payOrderBO.getHallWindowCode());
        scanCodePaymentReqDTO.setDeviceInfo(payOrderBO.getTerminalCode());
        scanCodePaymentReqDTO.setSpbillCreateIp(payOrderBO.getClientIps());
        scanCodePaymentReqDTO.setHmac(payOrderBO.getContractSecureValue());
        if (JudgeUtils.equals(payOrderBO.getPayWay(), PaymentWayEnum.UNIONPAY.name().toLowerCase())) {
            scanCodePaymentReqDTO.setDiscountCode(payOrderBO.getDiscountCode());
        }
        scanCodePaymentReqDTO.setReconciliationCheckFlg(CmpayConstants.SUPPLY_MODE);
        logger.info("scanCodePaymentReqDTO{}", JSONObject.toJSONString(scanCodePaymentReqDTO));
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setRoute(CmpayPayChannelEnum.CMPAY_POS);
        request.setBusiType(CmpayPayChannelEnum.CMPAYPOS_SCAN.getName());
        request.setSource(CmpayPayChannelEnum.CMPAY_POS);
        request.setTarget(scanCodePaymentReqDTO);
        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            cmpayCommonService.cgwReturnInfoCheck(genericRspDTO);
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_ORDER_PAY_FAIL);
        }
        Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .map(result -> {
                    CmpayPosScanPamentRspDTO scanCodePaymentRspDTO = (CmpayPosScanPamentRspDTO) result;
                    logger.info("scanCodePaymentRspDTO{}", JSONObject.toJSONString(scanCodePaymentRspDTO));
                    if (JudgeUtils.isNotNull(scanCodePaymentRspDTO) && !StringUtils.equals(scanCodePaymentRspDTO.getReturnCode(), CmpayConstants.RETURN_CODE)) {
                        payOrderBO.setErrMsgCd(scanCodePaymentRspDTO.getReturnCode());
                        payOrderBO.setErrMsgInfo(scanCodePaymentRspDTO.getMessage());
                        //判断是否SYS开头转换错误码
                        if(JudgeUtils.isNotNull(scanCodePaymentRspDTO.getReturnCode())
                                &&scanCodePaymentRspDTO.getReturnCode().toUpperCase().contains(CommonConstant.SYS)){
                            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_SYS_FAIL);
                        }
                        BusinessException.throwBusinessException(scanCodePaymentRspDTO.getReturnCode());
                    }
                    payOrderBO.setPayUrl(scanCodePaymentRspDTO.getSessionId());
                    return result;
                }).orElseThrow(() ->new BusinessException(MsgCodeEnum.CMPAY_ORDER_PAY_FAIL));
    }

    private String getVersion(PaymentWayEnum paymentWayEnum) {
        switch (paymentWayEnum) {
            case WECHAT:
                return CloudPaymentVersionEnum.WECHANT_WAY.getVersion();
            case ALIPAY:
                return CloudPaymentVersionEnum.ALIPAY_WAY.getVersion();
            case UNIONPAY:
                return CloudPaymentVersionEnum.UNIONPAY_WAY.getVersion();
            default:
                return CloudPaymentVersionEnum.CMPAY_WAY.getVersion();
        }
    }
}
