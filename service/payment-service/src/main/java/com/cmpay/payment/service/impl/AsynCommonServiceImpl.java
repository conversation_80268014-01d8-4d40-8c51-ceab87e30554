package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.client.AsynPaymentClient;
import com.cmpay.payment.constant.contract.ContractNofityTypeEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.ext.ExtNotifyRecordService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author： pengAnHai
 * @date： 2023-12-04
 * @description：异步公共服务类
 */
@Slf4j
@Service
public class AsynCommonServiceImpl implements AsynCommonService {

    @Resource
    private AsynPaymentClient asynPaymentClient;
    @Autowired
    private ExtParamInfoService paramInfoService;
    @Autowired
    private ExtNotifyRecordService notifyRecordService;

    /**
     * 异步新增第三方订单号和订单号的对应关系
     *
     * @param tradeOrderDO
     */
    @Override
    public void asynConnectThirdNo(TradeOrderDO tradeOrderDO) {
        if (JudgeUtils.isNotBlankAll(tradeOrderDO.getTradeOrderNo(), tradeOrderDO.getThirdOrdNo())) {
            asynPaymentClient.asynConnectThirdNo(tradeOrderDO);
        }
    }

    /**
     * 调用异步通知
     *
     * @param tradeNotifyBO
     */
    @Override
    public void asyncNotify(TradeNotifyBO tradeNotifyBO) {
        if (JudgeUtils.isNull(tradeNotifyBO)) {
            return;
        }
        if (StringUtils.isEmpty(tradeNotifyBO.getNotifyDate())) {
            tradeNotifyBO.setNotifyDate(DateTimeUtils.getCurrentDateStr());
        }
        // 待异步通知稳定后，取消进行判断
        if (paramInfoService.getRegisNotifyParam()&&JudgeUtils.isNotNull(tradeNotifyBO.getNotifyUrl())) {
            tradeNotifyBO.setFirstNotify(false);
            try {
                notifyRecordService.insertByNewTranscation(tradeNotifyBO);
            } catch (Exception e) {
                log.error(" notifyRecordService.insertByNewTranscation fail,tradeNotifyBO：{}，{}", tradeNotifyBO, e);
            }
        } else {
            tradeNotifyBO.setFirstNotify(true);
        }
        // 调用异步通知的，都是第一次通知
        try {
            asynPaymentClient.asynNotify(tradeNotifyBO);
        } catch (Exception e) {
            log.error("asynNotify fail,tradeNotifyBO：{}，{}", tradeNotifyBO, e);
            notifyRecordService.insertByNewTranscation(tradeNotifyBO);
        }
    }

    @Override
    public void asyncContractNotify(ContractWithholdBO contractWithholdBO) {
        if (JudgeUtils.isNull(contractWithholdBO)) {
            return;
        }
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setMerchantNo(contractWithholdBO.getMerchantId());
        tradeNotifyBO.setTradeOrderNo(contractWithholdBO.getContractCode());
        tradeNotifyBO.setOutOrderNo(contractWithholdBO.getContractCode());
        if (JudgeUtils.equals(contractWithholdBO.getNotifyType(), ContractNofityTypeEnum.DELETE.name().toLowerCase())) {
            tradeNotifyBO.setOutRequestNo(contractWithholdBO.getContractCode());
        }
        tradeNotifyBO.setTradeDate(DateTimeUtils.getCurrentDateStr());
        tradeNotifyBO.setNotifyType(contractWithholdBO.getNotifyType());
        tradeNotifyBO.setNotifyUrl(contractWithholdBO.getNotifyUrl());
        tradeNotifyBO.setExtra(contractWithholdBO.getExtra());
        tradeNotifyBO.setFinishDate(contractWithholdBO.getContractSignedTime());
        this.asyncNotify(tradeNotifyBO);
    }


}
