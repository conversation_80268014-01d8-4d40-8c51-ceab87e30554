package com.cmpay.payment.service.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.bo.RpmOnlineRefundBO;
import com.cmpay.payment.bo.config.SubMerchantRefreshAmountBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dto.cmpay.CmpayOnlineRefundReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayOnlineRefundRspDTO;
import com.cmpay.payment.entity.CmpayRefundJrnDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.CompleteOrderSplitService;
import com.cmpay.payment.service.channel.cmpay.CmpayCommonService;
import com.cmpay.payment.service.channel.cmpay.CmpayOnlineRefundChannelService;
import com.cmpay.payment.service.ext.ExtCmpayRefundJrnService;
import com.cmpay.payment.service.SubMerchantIncomeService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Created on 2018/12/4
 *
 * @author: chen_lan
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayOnlineRefundChannelServiceImpl implements CmpayOnlineRefundChannelService {
    private static final Logger logger = LoggerFactory.getLogger(CmpayOnlineRefundChannelServiceImpl.class);
    @Autowired
    ExtCmpayRefundJrnService extCmpayRefundJrnService;
    @Value("${cmpay.backendRefundNotifyUrl:}")
    private String backendRefundNotifyUrl;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Autowired
    private CmpayCommonService cmpayCommonService;
    @Autowired
    private CompleteOrderSplitService splitService;
    @Autowired
    private SubMerchantIncomeService refundConfineService;
    @Autowired
    ExtParamInfoService paramInfoService;
    @Override
    public void send(RefundBO refundBO) {
        cmpayOnlineRefund(refundBO);
    }

    @Override
    public void cmpayOnlineRefund(RefundBO refundBO) {
        //查询是否存在一条
        CmpayRefundJrnDO cmpayRefundJrnDO=new CmpayRefundJrnDO();
        cmpayRefundJrnDO.setTradeOrderNo(refundBO.getOutRequestNo());
        List<CmpayRefundJrnDO> find=extCmpayRefundJrnService.find(cmpayRefundJrnDO);
        if(find.size()==0) {
            TradeOrderDO originalPayTradeOrderDO = refundBO.getOriginalPayTradeOrder();;
            String subMerchant = StringUtils.isEmpty(refundBO.getSubMerchant()) ? originalPayTradeOrderDO.getSubMerchantNo() : refundBO.getSubMerchant();
            //原订单号子商户不为空，进行退款限制
            if(StringUtils.isNotEmpty(subMerchant)){
                // 查询和包退款流水，如果没有这笔退款，说明是第一次发起，则进行限制
                SubMerchantRefreshAmountBO refreshAmountBO = new SubMerchantRefreshAmountBO();
                refreshAmountBO.setSubMerchantNo(subMerchant);
                refreshAmountBO.setAmount(refundBO.getRefundOrder().getOrderAmount());
                refundConfineService.refundConfine(refreshAmountBO);
            }

            try {
            cmpayRefundJrnDO=extCmpayRefundJrnService.insertByNewTranscation(refundBO);
            } catch (Exception e) {
                // 插入失败，报错，则自增回去
                splitService.refundFailIncrement(refundBO.getRefundOrder(), originalPayTradeOrderDO);
                // 报错退出
                BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ERROR);
            }
        }else {
            cmpayRefundJrnDO=find.get(0);
        }
        try {
            // 调用和包云支付退款接口
            RpmOnlineRefundBO rpmOnlineRefundBO = onlineRefund(refundBO);
            cmpayRefundJrnDO.setTradeStatus(rpmOnlineRefundBO.getJrnStatus());
            cmpayRefundJrnDO.setReturnMsg(rpmOnlineRefundBO.getMessageCode());
            cmpayRefundJrnDO.setReturnMsgInfo(rpmOnlineRefundBO.getMessageInfo());
            refundBO.setAccountDate(rpmOnlineRefundBO.getAccountDate());
            refundBO.setStatus(rpmOnlineRefundBO.getJrnStatus());
            refundBO.setErrMsgCd(rpmOnlineRefundBO.getErrMsgCd());
            refundBO.setErrMsgInfo(rpmOnlineRefundBO.getErrMsgInfo());
        } catch (Exception e) {
            //系统异常，比如网络异常 记录异常日志
            cmpayCommonService.cmapyThrowClassHandle(cmpayRefundJrnDO,e);
            throw e;
        } finally {
            //将异常信息更新到 cmpay退款流水记录
            extCmpayRefundJrnService.updateByNewTranscation(cmpayRefundJrnDO);
        }
        if (!MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd().equals(cmpayRefundJrnDO.getReturnMsg())) {
            // 报错退出
            BusinessException.throwBusinessException(cmpayRefundJrnDO.getReturnMsg());
        }
    }

    private RpmOnlineRefundBO onlineRefund(RefundBO refundBO){
        RpmOnlineRefundBO onlineRefundBO = new RpmOnlineRefundBO();
        CmpayOnlineRefundReqDTO onlineRefundReqDTO = new CmpayOnlineRefundReqDTO();
        onlineRefundReqDTO.setMerchantId(refundBO.getOriginalPayTradeOrder().getBankMerchantNo());
        onlineRefundReqDTO.setMerchantRequestNo(refundBO.getRefundOrder().getOutTradeNo());
        onlineRefundReqDTO.setMerchantOrderNo(refundBO.getOriginalPayTradeOrder().getTradeOrderNo());
        onlineRefundReqDTO.setMerchantRefundAmount(refundBO.getRefundAmount());

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                onlineRefundReqDTO.setBankNotifyUrl(UrlUtils.replaceDomainOrIp(backendRefundNotifyUrl,host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                onlineRefundReqDTO.setBankNotifyUrl(backendRefundNotifyUrl);
            }
        } else {
            onlineRefundReqDTO.setBankNotifyUrl(backendRefundNotifyUrl);
        }

        onlineRefundReqDTO.setInterfaceType(CommonConstant.CMPAY_REFUND_INTERFACE_TYPE);
        onlineRefundReqDTO.setSignType(refundBO.getSignMethod());
        onlineRefundReqDTO.setHmac(refundBO.getContractSecureValue());
        onlineRefundReqDTO.setVersion(CommonConstant.CMPAY_ONLINE_PAYMENT_VERSION);
        onlineRefundReqDTO.setType(CommonConstant.CMPAY_ONLINE_REFUND_TYPE);
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(CmpayPayChannelEnum.CMPAY_ONLINE);
        request.setBusiType(CmpayPayChannelEnum.CMPAY_ONLINE_REFUND.getName());
        request.setSource(CmpayPayChannelEnum.CMPAY_ONLINE);
        request.setTarget(onlineRefundReqDTO);
        GenericRspDTO<Response> genericRspDTO;
        if (StringUtils.equalsAny(refundBO.getSourceApp(), AppEnum.integrationbatch.name(), AppEnum.integrationshecdule.name())) {
            genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        } else {
            genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        }
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            cmpayCommonService.cgwReturnInfoCheck(genericRspDTO);
            BusinessException.throwBusinessException(genericRspDTO);
        }
        Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleOnlineRefund(onlineRefundBO, (CmpayOnlineRefundRspDTO) result));
        return onlineRefundBO;
    }


    private void handleOnlineRefund(RpmOnlineRefundBO onlineRefundBO, CmpayOnlineRefundRspDTO onlineRefundRspDTO){
        if (JudgeUtils.isNull(onlineRefundRspDTO)) {
            onlineRefundBO.setMessageCode(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgCd());
            onlineRefundBO.setMessageInfo(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgInfo());
            return;
        }
        if (JudgeUtils.isSuccess(onlineRefundRspDTO.getMsgCd())) {
            onlineRefundBO.setJrnStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            onlineRefundBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            onlineRefundBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
            onlineRefundBO.setAccountDate(onlineRefundRspDTO.getRefundAccountDate());
        } else if (StringUtils.equalsIgnoreCase(MsgCodeEnum.CMPAY_ONLINE_REFUND_ORDER_ACCEPT.getMsgCd(), onlineRefundRspDTO.getMsgCd())
                || StringUtils.equalsIgnoreCase(MsgCodeEnum.CMPAY_ONLINE_REFUND_ORDER_EXISTS_REFUND.getMsgCd(), onlineRefundRspDTO.getMsgCd())) {
            onlineRefundBO.setJrnStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            onlineRefundBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            onlineRefundBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
            onlineRefundBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
        } else if (StringUtils.equalsIgnoreCase(MsgCodeEnum.CMPAY_ONLINE_REFUND_ORDER_FAIL.getMsgCd(), onlineRefundRspDTO.getMsgCd())
                || StringUtils.equalsIgnoreCase(MsgCodeEnum.ONLINE_REFUND_ORDER_NOT_EXISTS.getMsgCd(), onlineRefundRspDTO.getMsgCd())
                || StringUtils.equalsIgnoreCase(MsgCodeEnum.MERCHANT_BALANCE_ENOUGH.getMsgCd(), onlineRefundRspDTO.getMsgCd())) {
            onlineRefundBO.setJrnStatus(OrderStatusEnum.REFUND_FAIL.name());
            onlineRefundBO.setMessageCode(onlineRefundRspDTO.getMsgCd());
            onlineRefundBO.setMessageInfo(onlineRefundRspDTO.getMsgInfo());
            //保存报错信息
            onlineRefundBO.setErrMsgCd(onlineRefundRspDTO.getMsgCd());
            onlineRefundBO.setErrMsgInfo(onlineRefundRspDTO.getMsgInfo());
        } else {
            onlineRefundBO.setMessageCode(onlineRefundRspDTO.getMsgCd());
            onlineRefundBO.setMessageInfo(onlineRefundRspDTO.getMsgInfo());
        }
    }
}
