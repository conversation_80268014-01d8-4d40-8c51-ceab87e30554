package com.cmpay.payment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.*;
import com.cmpay.payment.bo.config.MerchantBO;
import com.cmpay.payment.bo.config.PayServiceBeanBO;
import com.cmpay.payment.bo.config.SecretBO;
import com.cmpay.payment.bo.data.AlipayRiskgoNotifyBO;
import com.cmpay.payment.bo.data.OrderFeeBO;
import com.cmpay.payment.bo.protocol.ProtocolAbolishBO;
import com.cmpay.payment.bo.protocol.ProtocolApplyBO;
import com.cmpay.payment.bo.protocol.ProtocolNotifyReq;
import com.cmpay.payment.bo.withhold.ContractNotifyReq;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.channel.PaymentOgwOutEnum;
import com.cmpay.payment.channel.dto.TradeNotifyReq;
import com.cmpay.payment.channel.dto.TradeNotifyRsp;
import com.cmpay.payment.client.AsynPaymentClient;
import com.cmpay.payment.client.IntegrationPaymentOgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.constant.contract.ContractWayEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.entity.config.MerchantDO;
import com.cmpay.payment.entity.config.SecretDO;
import com.cmpay.payment.entity.protocol.ProtocolDO;
import com.cmpay.payment.entity.protocol.ProtocolDeleteDO;
import com.cmpay.payment.entity.withhold.ContractWithholdDO;
import com.cmpay.payment.service.*;
import com.cmpay.payment.service.data.IDataFeeService;
import com.cmpay.payment.service.ext.ExtNotifyRecordService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.ExtRefundOrderService;
import com.cmpay.payment.service.ext.config.IExtMerchantService;
import com.cmpay.payment.service.ext.config.IExtRateService;
import com.cmpay.payment.service.ext.config.IExtSecretService;
import com.cmpay.payment.service.ext.data.IExtAlipayRiskgoNotifyService;
import com.cmpay.payment.service.ext.protocol.IExtProtocolAbolishService;
import com.cmpay.payment.service.ext.protocol.IExtProtocolService;
import com.cmpay.payment.service.ext.withhold.IExtContractWithholdService;
import com.cmpay.payment.util.DateUtils;
import com.cmpay.payment.util.PaymentUtils;
import com.cmpay.register.client.PaymentRegisterClient;
import com.cmpay.register.dto.PaymentNotifyReqDTO;
import com.cmpay.register.dto.PaymentNotifyRspDTO;
import com.cmpay.register.dto.RefundResultNotifyReqDTO;
import com.cmpay.register.dto.RefundResultNotifyRspDTO;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Created on 2018/12/04
 *
 * @author: li_zhen
 */
@Service
public class TradeNotifyServiceImpl implements TradeNotifyService {
    private static final Logger logger = LoggerFactory.getLogger(TradeNotifyServiceImpl.class);
    @Autowired
    private IntegrationPaymentOgwOutClient paymentOgwOutClient;
    @Autowired
    private ExtNotifyRecordService notifyRecordService;
    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private ExtRefundOrderService refundOrderService;
    @Autowired
    private IDataFeeService dataFeeService;
    @Autowired
    private IExtSecretService secretService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IExtMerchantService merchantService;
    @Autowired
    private TradeSynService tradeSynService;
    @Autowired
    private IExtRateService rateService;
    @Autowired
    private IDataFeeService feeService;
    @Autowired
    private IExtProtocolService protocolService;
    @Autowired
    private IExtProtocolAbolishService protocolAbolishService;
    @Autowired
    private IExtAlipayRiskgoNotifyService alipayRiskgoNotifyService;
    @Autowired
    private IExtContractWithholdService contractWithholdService;
    @Autowired
    private RiskControlService riskControlService;
    @Autowired
    private PaymentRegisterClient paymentRegisterClient;
    @Autowired
    private AsynCommonService asynCommonService;
    @Autowired
    private CompleteOrderSplitService splitService;
    @Autowired
    private IExtMerchantService extMerchantService;
    @Autowired
    private AsynPaymentClient asynPaymentClient;

    /**
     * 通知商户：返回通知结果
     *
     * @param tradeNotifyBO
     * @return
     */
    private boolean notifyMerchant(TradeNotifyBO tradeNotifyBO) {
        boolean notifyStatus = false;
        try {
            switch (TradeTypeEnum.valueOf(tradeNotifyBO.getNotifyType().toUpperCase())) {
                case PAYMENT:
                    notifyStatus = paymentNotify(tradeNotifyBO);
                    break;
                case REFUND:
                    notifyStatus = refundNotify(tradeNotifyBO);
                    break;
                case SIGN:
                    notifyStatus = protocolNotify(tradeNotifyBO, CommonConstant.PROTOCOL_SING_NOTIFY_METHOD);
                    break;
                case ABOLISH:
                    notifyStatus = protocolNotify(tradeNotifyBO, CommonConstant.PROTOCOL_ABOLISH_NOTIFY_METHOD);
                    break;
                case ADD:
                    notifyStatus = contractNotify(tradeNotifyBO, CommonConstant.CONTRACT_NOTIFY_METHOD);
                    break;
                case DELETE:
                    notifyStatus = contractNotify(tradeNotifyBO, CommonConstant.CONTRACT_DELETE_NOTIFY_METHOD);
                    break;
                default:
                    BusinessException.throwBusinessException(MsgCodeEnum.TREAD_TYPE_ERROR);
            }
        } catch (Exception e) {
            return false;
        }
        return notifyStatus;
    }

    @Override
    public TradeOrderAndNotifyBO backendQueryOrder(TradeOrderDO tradeOrderDO) {

        PaymentQueryBO paymentQueryBO = new PaymentQueryBO();
        paymentQueryBO.setPaymentOrder(tradeOrderDO);
        paymentQueryBO.setFinishDateTime(tradeOrderDO.getOrderCompleteTime());
        paymentQueryBO.setBnkTraceNo(tradeOrderDO.getBnkTraceNo());
        paymentQueryBO.setCmpayPosFlag(Constants.CMPAY_POS_FLG);
        paymentQueryBO.setSourceApp(AppEnum.integrationpaymnet.name());
        try {

            return tradeSynService.paymentOrderSyn(paymentQueryBO);
        } catch (Exception e) {
            logger.error("backendQueryOrder error:", e);
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_QUERY_ERROR);
        }
        return null;
    }

    /**
     * 退款结果通知
     *
     * @param refundNotifyBO
     */
    @Override
    public void handleRefundResultNotification(RefundNotifyBO refundNotifyBO) {
        applicationContext.publishEvent(refundNotifyBO);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void refundResultNotify(RefundNotifyBO refundBO) {

        RefundOrderDO refundOrderDO = refundOrderService.get(refundBO.getRefundOrderQueryBO().getRefundOrder().getOutTradeNo());
        if (com.cmpay.lemon.common.utils.StringUtils.endsWithAny(refundOrderDO.getStatus(), OrderStatusEnum.REFUND_SUCCESS.name())) {
            return;
        }
        RefundOrderCloneBO refundOrderCloneBO = new RefundOrderCloneBO();
        BeanUtils.copyProperties(refundOrderDO, refundOrderCloneBO);

        // 查询原支付订单信息
        refundOrderDO.setAccountDate(refundBO.getRefundOrderQueryBO().getRefundOrder().getAccountDate());
        if (StringUtils.isBlank(refundOrderDO.getAccountDate()) || refundOrderDO.getAccountDate().length() != 8) {
            refundOrderDO.setAccountDate(DateTimeUtils.getCurrentDateStr());
        }
        TradeOrderDO originalPayTradeOrderDO = payOrderService.get(refundOrderDO.getOrgOrderNo());
        if (JudgeUtils.isNull(originalPayTradeOrderDO)) {
            logger.info("Org Payment Order of Refund Order Not Exists,tradeOrderNo is {},orgOrderNo is {},msgcd is {}",
                    refundOrderDO.getTradeOrderNo(), refundOrderDO.getOrgOrderNo(), MsgCodeEnum.REFUND_ORG_ORDER_NOT_EXISTS.getMsgCd());
            return;
        }
        if (!StringUtils.equalsAny(originalPayTradeOrderDO.getStatus(), OrderStatusEnum.REFUND_WAIT.name(), OrderStatusEnum.REFUND_FAIL.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_EXIST_UNFINISHED_REDUND);
        }
        RefundOrderDO queryRefundOrderDO = new RefundOrderDO();
        queryRefundOrderDO.setMerchantNo(refundOrderDO.getMerchantNo());
        queryRefundOrderDO.setOrgOrderNo(refundOrderDO.getOrgOrderNo());
        queryRefundOrderDO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
        List<RefundOrderDO> refundOrderDOList = refundOrderService.find(queryRefundOrderDO);

        BigDecimal alreadyRefundTotalAmount = BigDecimal.ZERO;
        BigDecimal alreadyRefundTotalFeeAmount = BigDecimal.ZERO;

        if (refundOrderDOList.size() > 0) {
            //计算退款总金额
            alreadyRefundTotalAmount = refundOrderDOList.stream().map(RefundOrderDO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            alreadyRefundTotalFeeAmount = refundOrderDOList.stream().map(RefundOrderDO::getOrderFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 本次退款总金额 =已退款金额+本次退款金额
        BigDecimal currentRefundTotalAmount = alreadyRefundTotalAmount.add(refundOrderDO.getOrderAmount());

        if (currentRefundTotalAmount.compareTo(originalPayTradeOrderDO.getRealAmount()) == 0) {
            originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            originalPayTradeOrderDO.setRefundTimes(originalPayTradeOrderDO.getRefundTimes() + 1);
            originalPayTradeOrderDO.setSuccessRefundAmount(originalPayTradeOrderDO.getSuccessRefundAmount().add(refundOrderDO.getOrderAmount()));
        } else if (currentRefundTotalAmount.compareTo(originalPayTradeOrderDO.getRealAmount()) < 0) {
            originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_PART.name());
            originalPayTradeOrderDO.setRefundTimes(originalPayTradeOrderDO.getRefundTimes() + 1);
            originalPayTradeOrderDO.setSuccessRefundAmount(originalPayTradeOrderDO.getSuccessRefundAmount().add(refundOrderDO.getOrderAmount()));
        } else {
            logger.error("refund amount error, current refund amount: {}, already refund amount: {}, origin order amount: {}",
                    refundOrderDO.getOrderAmount(), currentRefundTotalAmount, originalPayTradeOrderDO.getOrderAmount());
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_AMOUNT_ERROR);
        }

        //计算退款手续费
        BigDecimal refundFeeAmount;
        if (originalPayTradeOrderDO.getOrderFeeAmount().compareTo(BigDecimal.ZERO) > 0
                && !JudgeUtils.equals(originalPayTradeOrderDO.getRefundFeeWay(), RefundFeeWayEnum.NO_REFUND_FEE_WAY.getDesc())) {
            // 如果是分账订单
            if (splitService.checkSplitOrder(refundOrderDO, originalPayTradeOrderDO)) {
                //  计算退款分账服务费
                splitService.splitOrderRefundFee(refundOrderDO, originalPayTradeOrderDO);
            } else {
                OrderFeeBO orderFeeBO = new OrderFeeBO();
                orderFeeBO.setOrderAmount(originalPayTradeOrderDO.getOrderAmount());
                orderFeeBO.setRefundAmount(refundOrderDO.getOrderAmount());
                orderFeeBO.setSuccessRefundFeeAmount(alreadyRefundTotalFeeAmount);
                orderFeeBO.setSuccessRefundAmount(alreadyRefundTotalAmount);
                orderFeeBO.setRefundFeeAmount(BigDecimal.ZERO);
                orderFeeBO.setOrderFeeAmount(originalPayTradeOrderDO.getOrderFeeAmount());
                orderFeeBO.setOrderRate(originalPayTradeOrderDO.getOrderRate());
                orderFeeBO = feeService.refundFeeCalculate(orderFeeBO);
                refundFeeAmount = orderFeeBO.getRefundFeeAmount();
                refundOrderDO.setOrderFeeAmount(refundFeeAmount);
            }
            refundOrderCloneBO.setOrderFeeAmount(refundOrderDO.getOrderFeeAmount());
        }
        refundOrderDO.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
        refundOrderDO.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
        refundOrderDO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
        // 更新退款订单
        if (refundOrderService.updateRefundStatus(refundOrderDO) != 1) {
            return;
        }
        logger.info("Refund Order Update Success,tradeOrderNo is {},orgTradeNO is {}",
                refundOrderDO.getTradeOrderNo(), refundOrderDO.getOrgOrderNo());
        // 更新支付订单
        payOrderService.update(originalPayTradeOrderDO);
        try {
            refundOrderCloneBO.setStatus(refundOrderDO.getStatus());
            handleRefundNotify(refundOrderCloneBO);
        } catch (Exception e) {
            logger.info("refund notify merchant error, outTradeNo:{}, error{}", refundOrderDO.getOutTradeNo(), e);
        }
    }

    /**
     * 修改本地订单状态，异步通知
     *
     * @param tradeNotifyBO
     * @return
     */
    @Override
    public TradeNotifyBO backendNotify(TradeNotifyBO tradeNotifyBO) {
        logger.info("tradeNotifyBO:{}", tradeNotifyBO.toString());
        TradeOrderDO tradeOrder = payOrderService.getOrderByNo(tradeNotifyBO.getTradeOrderNo());
        if (JudgeUtils.isNull(tradeOrder)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        //设置系统跟踪号
        tradeOrder.setBnkTraceNo(tradeNotifyBO.getBnkTraceNo());
        if (JudgeUtils.equalsAny(tradeOrder.getStatus(), OrderStatusEnum.WAIT_PAY.name(), OrderStatusEnum.TRADE_CLOSED.name())
                || updateIntegralSuccessCondit(tradeOrder)) {
            if (JudgeUtils.equals(tradeOrder.getPayWayCode(), PaymentSceneEnum.WAP.name().toLowerCase())
                    && !JudgeUtils.equals(tradeOrder.getAimProductCode(), tradeNotifyBO.getPaymentRout())) {
                //H5支付支付渠道与登记不一致更新订单信息
                tradeOrder.setAimProductCode(tradeNotifyBO.getPaymentRout());
                tradeOrder.setPayProductCode(tradeNotifyBO.getPaymentRout());
            }
            if (StringUtils.isEmpty(tradeOrder.getDcepFlag())) {
                if (JudgeUtils.notEquals(tradeOrder.getAimProductCode(), PaymentWayEnum.ICBCPAY.name().toLowerCase())) {
                    // 查询费率
                    PayServiceBeanBO rateBO = new PayServiceBeanBO();
                    rateBO.setMerchantNo(tradeOrder.getMerchantNo());
                    rateBO.setPaymentChannl(tradeOrder.getPayProductCode());
                    rateBO.setOrderScene(tradeOrder.getPayWayCode());
                    rateBO.setOrderAmount(tradeOrder.getRealAmount());
                    rateBO.setProvinceCode(tradeOrder.getProvinceCode());
                    rateBO.setOrderDate(DateTimeUtils.getCurrentDateStr());
                    if (JudgeUtils.equals(tradeOrder.getPayProductCode(), PaymentWayEnum.UNIONPAY.name().toLowerCase())) {
                        rateBO.setCardType(CardTypeEnum.CEBIT_CARD_TYPE.getDesc());
                        if (JudgeUtils.isNotNull(tradeNotifyBO.getUnionCardType())
                                && (JudgeUtils.equals(tradeNotifyBO.getUnionCardType(), CardTypeEnum.DEBIT_CARD_TYPE.getDesc())
                                || JudgeUtils.equals(tradeNotifyBO.getUnionCardType(), CardTypeEnum.CEBIT_CARD_TYPE.getDesc()))) {
                            rateBO.setCardType(tradeNotifyBO.getUnionCardType());
                        }
                        rateBO = rateService.findUnionPayPaymentRout(rateBO);
                    } else {
                        rateBO = rateService.findPaymentRout(rateBO);
                    }
                    if (JudgeUtils.isNull(rateBO)) {
                        BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ROUT_CANNOT_VALIDITY);
                    }
                    tradeOrder.setOrderRate(rateBO.getRate());
                    tradeOrder.setBusinessType(rateBO.getBusinessType());
                    //计算服务费
                    OrderFeeBO orderFeeBO = new OrderFeeBO();
                    orderFeeBO.setOrderAmount(tradeOrder.getRealAmount());
                    orderFeeBO.setOrderRate(tradeOrder.getOrderRate());
                    orderFeeBO.setMinFeeLimit(rateBO.getMinFeeLimit());
                    orderFeeBO.setMaxFeeLimit(rateBO.getMaxFeeLimit());
                    orderFeeBO.setInstDiscountUnsettledAmount(tradeNotifyBO.getInstDiscountUnsettledAmount());
                    orderFeeBO = dataFeeService.feeCalculate(orderFeeBO);
                    orderFeeBO.setMaxFeeLimit(rateBO.getMaxFeeLimit());
                    orderFeeBO.setMinFeeLimit(rateBO.getMinFeeLimit());
                    tradeOrder.setOrderFeeAmount(orderFeeBO.getOrderFeeAmount());
                    //处理银联优惠信息
                    unionpayDiscountInfoDeal(tradeNotifyBO, tradeOrder);
                    //订单支付成功，进行分账订单服务费计算
                    splitService.splitOrderFeeCalculate(tradeOrder);
                }
            }
            // 校验支付机构传入账期的并设置账期
            String accountDate = DateUtils.verifiAndSetAccountDate((tradeNotifyBO.getAccountDate()));
            if (JudgeUtils.equals(tradeOrder.getAimProductCode(), PaymentWayEnum.CMPAY.name().toLowerCase())) {
                tradeOrder.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                tradeOrder.setAccountDate(accountDate);
                tradeOrder.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                tradeOrder.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
                //收到支付通知更新卡种
                if (tradeNotifyBO.getUnionCardType() == null || JudgeUtils.isBlank(tradeNotifyBO.getUnionCardType())) {
                    tradeOrder.setCrdAcTyp("2");
                } else {
                    tradeOrder.setCrdAcTyp(tradeNotifyBO.getUnionCardType());
                }
                if (JudgeUtils.isNotEmpty(tradeNotifyBO.getBnkTradeNo())) {
                    tradeOrder.setThirdOrdNo(tradeNotifyBO.getBnkTradeNo());
                    tradeOrder.setThirdOrdDt(tradeNotifyBO.getAccountDate());
                } else {
                    tradeOrder.setThirdOrdNo(tradeNotifyBO.getSplOrderNo());
                    tradeOrder.setThirdOrdDt(tradeNotifyBO.getAccountDate());
                }
                if (JudgeUtils.isNotEmpty(tradeNotifyBO.getPayAmountList())) {
                    // 支付金额明细集合转换成json字符串存入订单表
                    tradeOrder.setPayAmountList(JSONObject.toJSONString(tradeNotifyBO.getPayAmountList()));
                }
                if (JudgeUtils.isNotEmpty(tradeNotifyBO.getActivityId())) {
                    tradeOrder.setActivityId(tradeNotifyBO.getActivityId());
                }
                if (JudgeUtils.isNotEmpty(tradeNotifyBO.getActivityName())) {
                    tradeOrder.setActivityName(tradeNotifyBO.getActivityName());
                }
                if (JudgeUtils.isNotNull(tradeNotifyBO.getInstDiscountSettlementAmount())) {
                    tradeOrder.setInstDiscountSettlementAmount(tradeNotifyBO.getInstDiscountSettlementAmount());
                }
                if (JudgeUtils.isNotNull(tradeNotifyBO.getInstPaidAmount())) {
                    tradeOrder.setInstPaidAmount(tradeNotifyBO.getInstPaidAmount());
                }
                if (JudgeUtils.isNotNull(tradeNotifyBO.getInstDiscountUnsettledAmount())) {
                    tradeOrder.setInstDiscountUnsettledAmount(tradeNotifyBO.getInstDiscountUnsettledAmount());
                }
                if (0 == payOrderService.updatePaymentStatus(tradeOrder)) {
                    return null;
                }
                riskControlService.riskAfterPay(tradeOrder);
            } else if (JudgeUtils.equals(tradeOrder.getAimProductCode(), PaymentWayEnum.ALIPAY.name().toLowerCase())) {
                Optional.ofNullable(tradeNotifyBO)
                        .map(TradeNotifyBO::getResult)
                        .ifPresent(result -> {
                            tradeOrder.setThirdOrdNo(tradeNotifyBO.getBnkTradeNo());
                            tradeOrder.setThirdOrdDt(tradeNotifyBO.getAccountDate());
                            AlipayTradeStatusEnum statusEnum = EnumUtils.getEnum(AlipayTradeStatusEnum.class, result);
                            switch (statusEnum) {
                                case TRADE_CLOSED:
                                    tradeOrder.setStatus(OrderStatusEnum.TRADE_CLOSED.name());
                                    tradeOrder.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                                    tradeOrder.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
                                    if (0 == payOrderService.updateWithStuts(tradeOrder)) {
                                        return;
                                    }
                                    riskControlService.riskAfterPay(tradeOrder);
                                    break;
                                case TRADE_FINISHED:
                                case TRADE_SUCCESS:
                                    tradeOrder.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                                    tradeOrder.setAccountDate(accountDate);
                                    tradeOrder.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                                    tradeOrder.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
                                    tradeOrder.setMobileNumber(tradeNotifyBO.getMobileNumber());
                                    if (0 == payOrderService.updateWithStuts(tradeOrder)) {
                                        return;
                                    }
                                    riskControlService.riskAfterPay(tradeOrder);
                                    break;
                                default:
                                    break;
                            }
                        });
            } else if (JudgeUtils.equals(tradeOrder.getAimProductCode(), PaymentWayEnum.WECHAT.name().toLowerCase())) {
                if (JudgeUtils.isNotNull(tradeNotifyBO) && JudgeUtils.isNotBlank(tradeNotifyBO.getResult())) {
                    String result = tradeNotifyBO.getResult();
                    WeChatCommonResponseEnum.ResultCode resultCode = EnumUtils.getEnum(WeChatCommonResponseEnum.ResultCode.class, result);
                    switch (resultCode) {
                        case SUCCESS:
                            if (tradeNotifyBO.getTradeAmount().compareTo(tradeOrder.getRealAmount()) == 0) {
                                tradeOrder.setThirdOrdNo(tradeNotifyBO.getBnkTradeNo());
                                tradeOrder.setThirdOrdDt(tradeNotifyBO.getAccountDate());
                                tradeOrder.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                                tradeOrder.setAccountDate(accountDate);
                                tradeOrder.setOrderCompleteTime(tradeNotifyBO.getFinishDate());
                                tradeOrder.setReceiveNotifyTime(tradeNotifyBO.getFinishDate());
                                tradeOrder.setMobileNumber(tradeNotifyBO.getMobileNumber());
                                if (0 == payOrderService.updateWithStuts(tradeOrder)) {
                                    return null;
                                }
                                riskControlService.riskAfterPay(tradeOrder);
                            }
                            break;
                        case FAIL:
                            tradeOrder.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                            tradeOrder.setThirdOrdDt(tradeNotifyBO.getAccountDate());
                            tradeOrder.setOrderCompleteTime(tradeNotifyBO.getFinishDate());
                            tradeOrder.setErrMsgCd(tradeNotifyBO.getErrCode());
                            tradeOrder.setErrMsgInfo(tradeNotifyBO.getErrCodeDes());
                            if (0 == payOrderService.updateWithStuts(tradeOrder)) {
                                return null;
                            }
                            riskControlService.riskAfterPay(tradeOrder);
                            break;
                        default:
                            break;
                    }
                }
            } else if (JudgeUtils.equalsAny(tradeOrder.getAimProductCode(), PaymentWayEnum.ICBCPAY.name().toLowerCase(), PaymentWayEnum.DCEPPAY.name().toLowerCase())) {
                Optional.ofNullable(tradeNotifyBO)
                        // 检查订单金额是否与通知的订单金额一致
                        .filter(notifyBO -> notifyBO.getTradeAmount().compareTo(tradeOrder.getRealAmount()) == 0)
                        .map(TradeNotifyBO::getResult)
                        .ifPresent(result -> {
                            tradeOrder.setThirdOrdNo(tradeNotifyBO.getBnkTradeNo());
                            tradeOrder.setThirdOrdDt(tradeNotifyBO.getAccountDate());
                            tradeOrder.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                            tradeOrder.setAccountDate(accountDate);
                            tradeOrder.setOrderCompleteTime(tradeNotifyBO.getFinishDate());
                            tradeOrder.setReceiveNotifyTime(tradeNotifyBO.getFinishDate());
                            tradeOrder.setMobileNumber(tradeNotifyBO.getMobileNumber());
                            if (0 == payOrderService.updatePaymentStatus(tradeOrder)) {
                                return;
                            }
                            riskControlService.riskAfterPay(tradeOrder);
                        });
            } else if (JudgeUtils.equals(tradeOrder.getAimProductCode(), PaymentWayEnum.INTEGRALPAY.name().toLowerCase())) {
                tradeOrder.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                tradeOrder.setAccountDate(accountDate);
                tradeOrder.setBankOrderNo(tradeNotifyBO.getBankOrderNo());
                tradeOrder.setThirdOrdNo(tradeNotifyBO.getBnkTradeNo());
                tradeOrder.setOrderCompleteTime(tradeNotifyBO.getFinishDate());
                tradeOrder.setReceiveNotifyTime(tradeNotifyBO.getFinishDate());
                if (0 == payOrderService.updatePaymentSuccessStatus(tradeOrder)) {
                    return null;
                }
            }
            asynCommonService.asynConnectThirdNo(tradeOrder);
            boolean ifNotify = StringUtils.equals(tradeOrder.getStatus(), OrderStatusEnum.TRADE_SUCCESS.name())
                    || (
                    JudgeUtils.equalsIgnoreCase(tradeOrder.getPayWayCode(), PaymentSceneEnum.CONTRACTPAY.name())
                            && JudgeUtils.notEquals(tradeOrder.getStatus(), OrderStatusEnum.WAIT_PAY.name())
            );
            if (ifNotify) {
                tradeNotifyBO.setMerchantNo(tradeOrder.getMerchantNo());
                tradeNotifyBO.setTradeOrderNo(tradeOrder.getTradeOrderNo());
                tradeNotifyBO.setOutOrderNo(tradeOrder.getOutTradeNo());
                tradeNotifyBO.setTradeDate(tradeOrder.getRequestDate());
                tradeNotifyBO.setTradeAmount(tradeOrder.getOrderAmount());
                tradeNotifyBO.setDiscountableAmount(tradeOrder.getDiscountableAmount());
                tradeNotifyBO.setNotifyType(TradeTypeEnum.PAYMENT.name().toLowerCase());
                tradeNotifyBO.setNotifyUrl(tradeOrder.getNotifyUrl());
                tradeNotifyBO.setExtra(tradeOrder.getRemark());
                tradeNotifyBO.setSecretIndex(tradeOrder.getSecretIndex());
                tradeNotifyBO.setFinishDate(tradeNotifyBO.getFinishDate());
            } else {
                return null;
            }
        } else if (updateIntegralThirdOrdNoCondit(tradeOrder)) {
            tradeOrder.setThirdOrdNo(tradeNotifyBO.getBnkTradeNo());
            payOrderService.updateIntegralThirdOrdNo(tradeOrder);
            return null;
        } else {
            return null;
        }
        return tradeNotifyBO;
    }

    @Override
    public void backendalipayRiskgoNotify(AlipayRiskgoNotifyBO alipayRiskgoNotifyBO) {
        if (StringUtils.isBlank(alipayRiskgoNotifyBO.getPid())) {
            BusinessException.throwBusinessException(MsgCodeEnum.ALIPAY_RISKGO_NOTIFY_PID_EMPTY);
        }
        if (StringUtils.isBlank(alipayRiskgoNotifyBO.getRisktype())) {
            BusinessException.throwBusinessException(MsgCodeEnum.ALIPAY_RISKGO_NOTIFY_RISKTYPE_EMPTY);
        }
        if (StringUtils.isBlank(alipayRiskgoNotifyBO.getRisklevel())) {
            BusinessException.throwBusinessException(MsgCodeEnum.ALIPAY_RISKGO_NOTIFY_RISKTYPE_LEVEL);
        }
        LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
        TradeOrderDO tradeOrderDO = new TradeOrderDO();
        tradeOrderDO.setOutTradeNo(alipayRiskgoNotifyBO.getTradeNos());
        tradeOrderDO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
        tradeOrderDO.setAccountDate(DateTimeUtils.formatLocalDate(localDate.plusDays(1)));
        TradeOrderDO tradeOrder = payOrderService.queryAlipayOrder(tradeOrderDO);
        if (JudgeUtils.isNull(tradeOrder)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        MerchantBO merchantBO = new MerchantBO();
        merchantBO.setMerchantNumber(tradeOrder.getMerchantNo());
        MerchantDO merchantDO = merchantService.get(merchantBO);
        alipayRiskgoNotifyBO.setContractId(merchantDO.getMerchantNumber());
        alipayRiskgoNotifyBO.setContractName(merchantDO.getMerchantName());
        alipayRiskgoNotifyBO.setProvinceCode(tradeOrder.getProvinceCode());
        alipayRiskgoNotifyBO.setPaymentOrderNo(tradeOrder.getThirdOrdNo());
        alipayRiskgoNotifyBO.setOutTradeNo(tradeOrder.getOutTradeNo());
        alipayRiskgoNotifyBO.setPayProductCode(tradeOrder.getPayProductCode());
        alipayRiskgoNotifyBO.setPayWayCode(tradeOrder.getPayWayCode());
        alipayRiskgoNotifyBO.setAimProductCode(tradeOrder.getAimProductCode());
        alipayRiskgoNotifyBO.setBusinessType(tradeOrder.getBusinessType());
        alipayRiskgoNotifyBO.setOrder_date(tradeOrder.getOrderDate());
        alipayRiskgoNotifyBO.setOrder_time(tradeOrder.getOrderTime());
        alipayRiskgoNotifyBO.setClientIp(tradeOrder.getOrderIp());
        alipayRiskgoNotifyBO.setInterceptType(RiskConfigConstants.PAYMENT_INSTITUTION);
        alipayRiskgoNotifyService.insertDate(alipayRiskgoNotifyBO);
    }

    @Override
    public boolean handleRefundNotify(RefundOrderCloneBO refundOrderCloneBO) {
        TradeNotifyBO notifyBO = new TradeNotifyBO();
        if (JudgeUtils.equals(OrderStatusEnum.REFUND_SUCCESS.name(), refundOrderCloneBO.getStatus())) {
            notifyBO = registerRefundNotify(refundOrderCloneBO);
        } else if (JudgeUtils.equals(OrderStatusEnum.REFUND_FAIL.name(), refundOrderCloneBO.getStatus())) {
            notifyBO = registerRefundFailNotify((refundOrderCloneBO));
        }
        if (JudgeUtils.isNull(notifyBO)) {
            // notifyBO为空，不通知
            return false;
        }
        notifyBO.setInstPaidAmount(refundOrderCloneBO.getInstPaidAmount());
        notifyBO.setInstDiscountSettlementAmount(refundOrderCloneBO.getInstDiscountSettlementAmount());
        notifyBO.setInstDiscountUnsettledAmount(refundOrderCloneBO.getInstDiscountUnsettledAmount());
        //异步调用通知
        asynCommonService.asyncNotify(notifyBO);
        return true;
    }

    @Override
    public boolean managerOrderNotify(TradeNotifyBO tradeNotifyBO) {
        // 运管仅支持近2天的通知重发
        tradeNotifyBO.setNotifyDate(DateTimeUtils.formatLocalDate(DateTimeUtils.getCurrentLocalDate().minusDays(1)));
        boolean flag = notifyRecordService.queryWaitNotifyRecord(tradeNotifyBO);
        if (flag) {
            tradeNotifyBO.setFirstNotify(false);
            asynPaymentClient.asynNotify(tradeNotifyBO);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 处理银联优惠信息
     *
     * @param tradeNotifyBO
     */
    private void unionpayDiscountInfoDeal(TradeNotifyBO tradeNotifyBO, TradeOrderDO tradeOrder) {
        if (JudgeUtils.isBlank(tradeNotifyBO.getUnionpayDiscountInfo())) {
            return;
        }
        try {
            String[] strs = tradeNotifyBO.getUnionpayDiscountInfo().split(Constants.HASH);
            if (strs.length < 3) {
                return;
            }
            tradeOrder.setDiscountAmount(new BigDecimal(strs[0]));
            tradeOrder.setActivityId(strs[1]);
            tradeOrder.setActivityName(strs[2]);
        } catch (Exception e) {
            logger.error("unionPayDiscountInfoDeal, unionDiscountInfo:{}, {}, ", tradeNotifyBO.getUnionpayDiscountInfo(), e);
        }
    }

    /**
     * 支付通知
     *
     * @param tradeNotifyBO
     */
    private boolean paymentNotify(TradeNotifyBO tradeNotifyBO) {
        TradeNotifyReq notifyBaseReq = new TradeNotifyReq();
        TradeOrderDO tradeOrderDO = payOrderService.get(tradeNotifyBO.getTradeOrderNo());
        if (JudgeUtils.isNull(tradeOrderDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        if (JudgeUtils.notEquals(tradeNotifyBO.getNotifyUrl(), IntegralpayConstants.NOTIFYURL)) {
            //密钥索引不建议写死，写到配置文件里或数据库里
            notifyBaseReq.setSecureIndex(tradeNotifyBO.getSecretIndex());
            //查询密钥
            SecretBO secretBO = new SecretBO();
            secretBO.setSecretIndex(tradeNotifyBO.getSecretIndex());
            SecretDO secretDO = secretService.get(secretBO);
            notifyBaseReq.setSecureIndex(tradeNotifyBO.getSecretIndex());
            notifyBaseReq.setSecureValue(secretDO.getSecretNumber());
            notifyBaseReq.setSignType(secretDO.getSecretType());
            notifyBaseReq.setNotifyUrl(tradeNotifyBO.getNotifyUrl());
            notifyBaseReq.setMerchantId(tradeNotifyBO.getMerchantNo());
            notifyBaseReq.setMethod(CommonConstant.PAYMENT_NOTIFY_METHOD);
        }
        PaymentNotifyReq paymentNotifyReq = new PaymentNotifyReq();
        paymentNotifyReq.setOutTradeNo(tradeNotifyBO.getOutOrderNo());
        paymentNotifyReq.setTotalAmount(tradeNotifyBO.getTradeAmount());
        paymentNotifyReq.setRealAmount(tradeNotifyBO.getTradeAmount().subtract(tradeNotifyBO.getDiscountableAmount()));
        if (!StringUtils.equals(tradeOrderDO.getStatus(), OrderStatusEnum.TRADE_SUCCESS.name())
                && StringUtils.equalsIgnoreCase(tradeOrderDO.getPayWayCode(), PaymentSceneEnum.CONTRACTPAY.name())) {
            paymentNotifyReq.setStatus(OrderStatusEnum.TRADE_FAIL.name());
        } else {
            paymentNotifyReq.setStatus(tradeOrderDO.getStatus());
        }
        paymentNotifyReq.setStatusMsg(tradeOrderDO.getErrMsgInfo());
        if (tradeNotifyBO.getDiscountableAmount().compareTo(BigDecimal.ZERO) == 0) {
            paymentNotifyReq.setDiscountableAmount("0.00");
        } else {
            paymentNotifyReq.setDiscountableAmount(tradeNotifyBO.getDiscountableAmount().toString());
        }
        paymentNotifyReq.setTradeDate(tradeNotifyBO.getTradeDate());
        paymentNotifyReq.setFinishDateTime(tradeNotifyBO.getFinishDate());
        if (JudgeUtils.isEmpty(paymentNotifyReq.getFinishDateTime())) {
            paymentNotifyReq.setFinishDateTime(DateTimeUtils.getCurrentDateTimeStr());
        }
        paymentNotifyReq.setExtra(tradeNotifyBO.getExtra());
        paymentNotifyReq.setPayWay(tradeOrderDO.getPayProductCode());
        paymentNotifyReq.setPaymentRout(tradeOrderDO.getAimProductCode());
        paymentNotifyReq.setAccountDate(tradeOrderDO.getAccountDate());
        paymentNotifyReq.setScene(alipayPcreditSceneConversion(tradeOrderDO.getPayWayCode()));
        if (JudgeUtils.equalsAny(tradeOrderDO.getAimProductCode(), PaymentWayEnum.ALIPAY.name().toLowerCase(), PaymentWayEnum.WECHAT.name().toLowerCase())) {
            paymentNotifyReq.setPaymentOrderNo(tradeOrderDO.getThirdOrdNo());
        }
        if (JudgeUtils.isNotNull(tradeOrderDO.getDiscountAmount())) {
            if (tradeOrderDO.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
                paymentNotifyReq.setDiscountAmount(tradeOrderDO.getDiscountAmount());
            }
        }
        if (JudgeUtils.isNotBlank(tradeOrderDO.getPayAmountList())) {
            // 记录支付金额明细
            paymentNotifyReq.setPayAmountList(JSONObject.parseArray(tradeOrderDO.getPayAmountList(), AmountInfoBO.class));
        }
        if (JudgeUtils.isNotEmpty(tradeOrderDO.getActivityId())) {
            paymentNotifyReq.setActivityId(tradeOrderDO.getActivityId());
        }
        if (JudgeUtils.isNotEmpty(tradeOrderDO.getActivityName())) {
            paymentNotifyReq.setActivityName(tradeOrderDO.getActivityName());
        }
        if (JudgeUtils.isNotNull(tradeOrderDO.getInstDiscountSettlementAmount())) {
            paymentNotifyReq.setInstDiscountSettlementAmount(tradeOrderDO.getInstDiscountSettlementAmount());
        }
        if (JudgeUtils.isNotNull(tradeOrderDO.getInstPaidAmount())) {
            paymentNotifyReq.setInstPaidAmount(tradeOrderDO.getInstPaidAmount());
        }
        if (JudgeUtils.isNotNull(tradeOrderDO.getInstDiscountUnsettledAmount())) {
            paymentNotifyReq.setInstDiscountUnsettledAmount(tradeOrderDO.getInstDiscountUnsettledAmount());
        }
        if (JudgeUtils.equals(tradeNotifyBO.getNotifyUrl(), IntegralpayConstants.NOTIFYURL)) {
            //通知积分特殊处理,调用积分client
            PaymentNotifyReqDTO paymentNotifyReqDTO = new PaymentNotifyReqDTO();
            paymentNotifyReqDTO.setMerchantId(tradeNotifyBO.getMerchantNo());
            paymentNotifyReqDTO.setOutTradeNo(paymentNotifyReq.getOutTradeNo());
            paymentNotifyReqDTO.setTotalAmount(paymentNotifyReq.getTotalAmount());
            paymentNotifyReqDTO.setRealAmount(paymentNotifyReq.getRealAmount());
            paymentNotifyReqDTO.setDiscountableAmount(paymentNotifyReq.getDiscountableAmount());
            paymentNotifyReqDTO.setTradeDate(paymentNotifyReq.getTradeDate());
            paymentNotifyReqDTO.setFinishDateTime(paymentNotifyReq.getFinishDateTime());
            paymentNotifyReqDTO.setPayWay(paymentNotifyReq.getPayWay());
            paymentNotifyReqDTO.setPaymentRout(paymentNotifyReq.getPaymentRout());
            paymentNotifyReqDTO.setScene(paymentNotifyReq.getScene());
            paymentNotifyReqDTO.setAccountDate(paymentNotifyReq.getAccountDate());
            paymentNotifyReqDTO.setDiscountAmount(paymentNotifyReq.getDiscountAmount());
            paymentNotifyReqDTO.setPaymentOrderNo(paymentNotifyReq.getPaymentOrderNo());
            paymentNotifyReqDTO.setOrderId(tradeOrderDO.getThirdOrdNo());
            PaymentNotifyRspDTO paymentNotifyRspDTO = paymentRegisterClient.paymentSuccessNotify(paymentNotifyReqDTO);
            if (JudgeUtils.isNotNull(paymentNotifyRspDTO) &&
                    StringUtils.equals(CommonConstant.NOTIFY_SUCCESS, paymentNotifyRspDTO.getResult())) {
                tradeNotifyBO.setResult(CommonConstant.NOTIFY_SUCCESS);
                return true;
            } else {
                logger.info("Payment Notify Failure, outTradeNo is {}, Result is {}",
                        paymentNotifyReq.getOutTradeNo(), paymentNotifyRspDTO == null ? "null" : paymentNotifyRspDTO.toString());
                tradeNotifyBO.setResult(CommonConstant.NOTIFY_FAILURE);
                return false;
            }
        } else {
            GenericRspDTO<Response> response = this.paymentOgwOutClient.request(PaymentUtils.buildRequest(PaymentOgwOutEnum.TRADE_NOTIFY.getName(), notifyBaseReq, paymentNotifyReq));
            TradeNotifyRsp rs = (TradeNotifyRsp) response.getBody().getResult();
            if (JudgeUtils.isNotNull(rs) &&
                    StringUtils.equals(CommonConstant.NOTIFY_SUCCESS, rs.getResult())) {
                tradeNotifyBO.setResult(CommonConstant.NOTIFY_SUCCESS);
                return true;
            } else {
                logger.info("Payment Notify Failure, outTradeNo is {}, Result is {}",
                        paymentNotifyReq.getOutTradeNo(), rs == null ? "null" : rs.toString());
                tradeNotifyBO.setResult(CommonConstant.NOTIFY_FAILURE);
                return false;
            }
        }
    }

    /**
     * 退款通知
     *
     * @param tradeNotifyBO
     */
    private boolean refundNotify(TradeNotifyBO tradeNotifyBO) {
        RefundOrderDO refundOrderDO = new RefundOrderDO();
        refundOrderDO.setTradeOrderNo(tradeNotifyBO.getTradeOrderNo());
        refundOrderDO = refundOrderService.load(refundOrderDO);
        //金科收银台退款通知直接通知
        if (JudgeUtils.equals(tradeNotifyBO.getNotifyUrl(), IntegralpayConstants.NOTIFYURL)) {
            return integralRefundNotify(refundOrderDO, tradeNotifyBO);
        }
        /**
         * 由原业的RefundNotifyReq继承NotifyBaseReq改成NotifyBaseReq依赖RefundNotifyReq
         */
        TradeNotifyReq notifyBaseReq = new TradeNotifyReq();
        //密钥索引不建议写死，写到配置文件里或数据库里
        //查询密钥
        SecretBO secretBO = new SecretBO();
        secretBO.setSecretIndex(tradeNotifyBO.getSecretIndex());
        SecretDO secretDO = secretService.get(secretBO);
        notifyBaseReq.setSecureIndex(tradeNotifyBO.getSecretIndex());
        if (JudgeUtils.isNull(refundOrderDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        notifyBaseReq.setSecureValue(secretDO.getSecretNumber());
        notifyBaseReq.setSignType(secretDO.getSecretType());
        notifyBaseReq.setMerchantId(tradeNotifyBO.getMerchantNo());
        notifyBaseReq.setNotifyUrl(tradeNotifyBO.getNotifyUrl());
        notifyBaseReq.setMethod(CommonConstant.REFUND_NOTIFY_METHOD);
        RefundNotifyReq refundNotifyReq = new RefundNotifyReq();
        refundNotifyReq.setOutTradeNo(tradeNotifyBO.getOutOrderNo());
        refundNotifyReq.setOutRequestNo(tradeNotifyBO.getOutRequestNo());
        refundNotifyReq.setRefundAmount(tradeNotifyBO.getTradeAmount());
        refundNotifyReq.setTradeDate(tradeNotifyBO.getTradeDate());
        refundNotifyReq.setFinishDateTime(tradeNotifyBO.getFinishDate());
        refundNotifyReq.setPayWay(refundOrderDO.getPayProductCode());
        refundNotifyReq.setPaymentRout(refundOrderDO.getAimProductCode());
        refundNotifyReq.setAccountDate(refundOrderDO.getAccountDate());
        refundNotifyReq.setScene(alipayPcreditSceneConversion(refundOrderDO.getPayWayCode()));
        refundNotifyReq.setInstPaidAmount(refundOrderDO.getInstPaidAmount());
        refundNotifyReq.setInstDiscountSettlementAmount(refundOrderDO.getInstDiscountSettlementAmount());
        refundNotifyReq.setInstDiscountUnsettledAmount(refundOrderDO.getInstDiscountUnsettledAmount());
        if (JudgeUtils.equals(OrderStatusEnum.REFUND_SUCCESS.name(), refundOrderDO.getStatus())) {
            refundNotifyReq.setRefundResult(OrderStatusEnum.REFUND_SUCCESS.name());
        } else if (JudgeUtils.equals(OrderStatusEnum.REFUND_FAIL.name(), refundOrderDO.getStatus())) {
            refundNotifyReq.setRefundResult(OrderStatusEnum.REFUND_FAIL.name());
        }
        if (JudgeUtils.isEmpty(refundNotifyReq.getFinishDateTime())) {
            refundNotifyReq.setFinishDateTime(DateTimeUtils.getCurrentDateTimeStr());
        }
        refundNotifyReq.setExtra(tradeNotifyBO.getExtra());
        if (JudgeUtils.isNotBlank(refundOrderDO.getRefAmountList())) {
            // 记录退款金额明细
            refundNotifyReq.setRefAmountList(JSONObject.parseArray(refundOrderDO.getRefAmountList(), AmountInfoBO.class));
        }
        GenericRspDTO<Response> response = this.paymentOgwOutClient.request(PaymentUtils.buildRequest(PaymentOgwOutEnum.TRADE_NOTIFY.getName(), notifyBaseReq, refundNotifyReq));
        TradeNotifyRsp rs = (TradeNotifyRsp) response.getBody().getResult();
        if (JudgeUtils.isNotNull(rs) &&
                StringUtils.equals(CommonConstant.NOTIFY_SUCCESS, rs.getResult())) {
            tradeNotifyBO.setResult(CommonConstant.NOTIFY_SUCCESS);
            return true;
        } else {
            logger.info("Payment Notify Failure, outRequestNo is {}, Result is {}",
                    tradeNotifyBO.getOutOrderNo(), rs == null ? "null" : rs.toString());
            tradeNotifyBO.setResult(CommonConstant.NOTIFY_FAILURE);
            return false;
        }
    }

    /**
     * 收银台退款通知
     */
    private boolean integralRefundNotify(RefundOrderDO refundOrderDO, TradeNotifyBO tradeNotifyBO) {
        RefundResultNotifyReqDTO refundResultNotifyReqDTO = new RefundResultNotifyReqDTO();
        refundResultNotifyReqDTO.setMerchantId(tradeNotifyBO.getMerchantNo());
        refundResultNotifyReqDTO.setOutTradeNo(tradeNotifyBO.getOutOrderNo());
        refundResultNotifyReqDTO.setOutRequestNo(tradeNotifyBO.getOutRequestNo());
        refundResultNotifyReqDTO.setRefundAmount(tradeNotifyBO.getTradeAmount());
        refundResultNotifyReqDTO.setTradeDate(tradeNotifyBO.getTradeDate());
        refundResultNotifyReqDTO.setFinishDateTime(tradeNotifyBO.getFinishDate());
        refundResultNotifyReqDTO.setPayWay(refundOrderDO.getPayProductCode());
        refundResultNotifyReqDTO.setPaymentRout(refundOrderDO.getAimProductCode());
        refundResultNotifyReqDTO.setScene(refundOrderDO.getPayWayCode());
        refundResultNotifyReqDTO.setAccountDate(refundOrderDO.getAccountDate());
        refundResultNotifyReqDTO.setExtra(tradeNotifyBO.getExtra());
        RefundResultNotifyRspDTO paymentNotifyRspDTO = paymentRegisterClient.refundSuccessNotify(refundResultNotifyReqDTO);
        if (JudgeUtils.isNotNull(paymentNotifyRspDTO) &&
                StringUtils.equals(CommonConstant.NOTIFY_SUCCESS, paymentNotifyRspDTO.getResult())) {
            tradeNotifyBO.setResult(CommonConstant.NOTIFY_SUCCESS);
            return true;
        } else {
            logger.info("Payment Notify Failure, outTradeNo is {}, Result is {}",
                    tradeNotifyBO.getOutOrderNo(), paymentNotifyRspDTO == null ? "null" : paymentNotifyRspDTO.toString());
            tradeNotifyBO.setResult(CommonConstant.NOTIFY_FAILURE);
            return false;
        }
    }

    /**
     * 支付场景通知转换
     */
    private String alipayPcreditSceneConversion(String payWayCode) {
        String scene;
        if (StringUtils.equals(payWayCode, CmpayConstants.ALE)) {
            scene = CmpayConstants.APPLEPAY;
        } else if (JudgeUtils.equals(PaymentSceneEnum.NETBANKB2B.name().toLowerCase(), payWayCode)) {
            scene = PaymentSceneEnum.NETBANK.name().toLowerCase();
        } else if (JudgeUtils.equalsAny(payWayCode.toUpperCase(), PaymentSceneEnum.APPPCREDIT.name(),
                PaymentSceneEnum.WAPPCREDIT.name())) {
            scene = payWayCode.substring(0, 3);
        } else {
            scene = payWayCode;
        }
        return scene;
    }

    /**
     * 签约通知
     *
     * @param tradeNotifyBO
     */
    private boolean contractNotify(TradeNotifyBO tradeNotifyBO, String method) {
        TradeNotifyReq notifyBaseReq = new TradeNotifyReq();
        //查询密钥
        SecretBO secretBO = new SecretBO();
        secretBO.setMerchantNumber(tradeNotifyBO.getMerchantNo());
        SecretDO secretDO = secretService.getSecret(secretBO);
        notifyBaseReq.setSecureIndex(secretDO.getSecretIndex());
        notifyBaseReq.setSecureValue(secretDO.getSecretNumber());
        notifyBaseReq.setSignType(secretDO.getSecretType());
        notifyBaseReq.setMerchantId(tradeNotifyBO.getMerchantNo());
        notifyBaseReq.setNotifyUrl(tradeNotifyBO.getNotifyUrl());
        ContractWithholdBO contractWithholdBO = new ContractWithholdBO();
        ContractNotifyReq contractNotifyReq = new ContractNotifyReq();
        contractWithholdBO.setContractCode(tradeNotifyBO.getOutOrderNo());
        ContractWithholdDO contractWithholdDO = contractWithholdService.findByContractCode(contractWithholdBO);
        if (CommonConstant.CONTRACT_NOTIFY_METHOD.equals(method)) {
            notifyBaseReq.setMethod(CommonConstant.CONTRACT_NOTIFY_METHOD);
            contractNotifyReq.setChangeType(TradeTypeEnum.ADD.name());
        } else {
            notifyBaseReq.setMethod(CommonConstant.CONTRACT_DELETE_NOTIFY_METHOD);
            contractNotifyReq.setChangeType(TradeTypeEnum.DELETE.name());
        }
        contractNotifyReq.setPlanId(contractWithholdDO.getPlanId());
        if (JudgeUtils.equals(contractWithholdDO.getContractWay(), ContractWayEnum.WECHAT.name().toLowerCase())) {
            contractNotifyReq.setAppId(contractWithholdDO.getAppId());
            contractNotifyReq.setOpenid(contractWithholdDO.getOpenid());
        }
        contractNotifyReq.setContractCode(contractWithholdDO.getContractCode());
        contractNotifyReq.setContractId(contractWithholdDO.getContractId());
        contractNotifyReq.setExtra(contractWithholdDO.getExtra());
        contractNotifyReq.setContractSignedTime(contractWithholdDO.getSignedTime());
        contractNotifyReq.setContractExpiredTime(contractWithholdDO.getExpiredTime());
        contractNotifyReq.setContractTerminatedTime(contractWithholdDO.getTerminatedTime());
        GenericRspDTO<Response> response = this.paymentOgwOutClient.request(PaymentUtils.buildRequest(PaymentOgwOutEnum.TRADE_NOTIFY.getName(), notifyBaseReq, contractNotifyReq));
        TradeNotifyRsp rs = (TradeNotifyRsp) response.getBody().getResult();
        if (JudgeUtils.isNotNull(rs) &&
                StringUtils.equals(CommonConstant.NOTIFY_SUCCESS, rs.getResult())) {
            tradeNotifyBO.setResult(CommonConstant.NOTIFY_SUCCESS);
            return true;
        } else {
            logger.info("Payment Notify Failure, outTradeNo is {}, Result is {}",
                    tradeNotifyBO.getOutOrderNo(), rs == null ? "null" : rs.toString());
            tradeNotifyBO.setResult(CommonConstant.NOTIFY_FAILURE);
            return false;
        }
    }

    /**
     * 登记退款失败通知
     *
     * @param refundOrderQueryParam
     * @return
     */
    private TradeNotifyBO registerRefundFailNotify(RefundOrderCloneBO refundOrderQueryParam) {
        String notifyFlag = extMerchantService.getRefundFailNotifyFlag(refundOrderQueryParam.getMerchantNo());
        if (JudgeUtils.notEquals(notifyFlag, Constants.Y)) {
            return null;
        }
        return registerRefundNotify(refundOrderQueryParam);
    }


    /**
     * 登记退款通知
     *
     * @param refundOrderQueryParam
     */
    private TradeNotifyBO registerRefundNotify(RefundOrderCloneBO refundOrderQueryParam) {
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setMerchantNo(refundOrderQueryParam.getMerchantNo());
        tradeNotifyBO.setTradeOrderNo(refundOrderQueryParam.getTradeOrderNo());
        tradeNotifyBO.setOutOrderNo(refundOrderQueryParam.getOrgOrderNo());
        tradeNotifyBO.setOutRequestNo(refundOrderQueryParam.getOutTradeNo());
        tradeNotifyBO.setTradeDate(refundOrderQueryParam.getRequestDate());
        tradeNotifyBO.setFinishDate(refundOrderQueryParam.getOrderCompleteTime());
        tradeNotifyBO.setTradeAmount(refundOrderQueryParam.getOrderAmount());
        tradeNotifyBO.setNotifyDate(DateTimeUtils.getCurrentDateStr());
        tradeNotifyBO.setNotifyTime(DateTimeUtils.getCurrentTimeStr());
        tradeNotifyBO.setNotifyType(TradeTypeEnum.REFUND.name().toLowerCase());
        tradeNotifyBO.setNotifyUrl(refundOrderQueryParam.getNotifyUrl());
        tradeNotifyBO.setExtra(refundOrderQueryParam.getRemark());
        tradeNotifyBO.setSecretIndex(refundOrderQueryParam.getSecretIndex());
        tradeNotifyBO.setFinishDate(refundOrderQueryParam.getOrderCompleteTime());
        return tradeNotifyBO;
    }


    /**
     * @param tradeNotifyBO
     * @param method
     * @return
     */
    private TradeNotifyReq getNotifyBaseReq(TradeNotifyBO tradeNotifyBO, String method) {
        TradeNotifyReq notifyBaseReq = new TradeNotifyReq();
        //查询密钥
        SecretBO secretBO = new SecretBO();
        secretBO.setMerchantNumber(tradeNotifyBO.getMerchantNo());
        SecretDO secretDO = secretService.getSecret(secretBO);
        notifyBaseReq.setSecureIndex(secretDO.getSecretIndex());
        notifyBaseReq.setSecureValue(secretDO.getSecretNumber());
        notifyBaseReq.setSignType(secretDO.getSecretType());
        notifyBaseReq.setNotifyUrl(tradeNotifyBO.getNotifyUrl());
        notifyBaseReq.setMerchantId(tradeNotifyBO.getMerchantNo());
        notifyBaseReq.setMethod(method);
        return notifyBaseReq;
    }

    /**
     * 免密协议通知
     *
     * @param tradeNotifyBO
     */
    private boolean protocolNotify(TradeNotifyBO tradeNotifyBO, String method) {
        TradeNotifyReq notifyBaseReq = getNotifyBaseReq(tradeNotifyBO, method);
        ProtocolNotifyReq protocolNotifyReq = new ProtocolNotifyReq();
        protocolNotifyReq.setChangeType(tradeNotifyBO.getNotifyType().toUpperCase());
        if (CommonConstant.PROTOCOL_SING_NOTIFY_METHOD.equals(method)) {
            ProtocolApplyBO protocolApplyBO = new ProtocolApplyBO();
            protocolApplyBO.setAgreementReqNo(tradeNotifyBO.getOutOrderNo());
            protocolApplyBO.setAgreementReqDate(tradeNotifyBO.getTradeDate());
            ProtocolDO protocolDO = protocolService.findByContractCode(protocolApplyBO);
            protocolNotifyReq.setTerminatedTime(protocolDO.getTerminatedTime());
            protocolNotifyReq.setSignedTime(protocolDO.getSignedTime());
            protocolNotifyReq.setAgreementReqNo(protocolDO.getAgreementReqNo());
            protocolNotifyReq.setAgreementId(protocolDO.getAgreementId());
            protocolNotifyReq.setExtra(protocolDO.getExtra());
        } else {
            ProtocolAbolishBO protocolAbolishBO = new ProtocolAbolishBO();
            protocolAbolishBO.setAgreementReqNo(tradeNotifyBO.getOutRequestNo());
            protocolAbolishBO.setAgreementReqDate(tradeNotifyBO.getTradeDate());
            ProtocolDeleteDO deleteDO = protocolAbolishService
                    .findByContractCode(protocolAbolishBO);
            protocolNotifyReq.setTerminatedTime(deleteDO.getTerminatedTime());
            protocolNotifyReq.setSignedTime(deleteDO.getSignedTime());
            protocolNotifyReq.setAgreementReqNo(deleteDO.getAgreementReqNo());
            protocolNotifyReq.setAgreementId(deleteDO.getAgreementId());
            protocolNotifyReq.setExtra(deleteDO.getExtra());
        }
        GenericRspDTO<Response> response = this.paymentOgwOutClient.request(PaymentUtils.buildRequest(PaymentOgwOutEnum.TRADE_NOTIFY.getName(), notifyBaseReq, protocolNotifyReq));
        TradeNotifyRsp rs = (TradeNotifyRsp) response.getBody().getResult();
        if (JudgeUtils.isNotNull(rs) &&
                StringUtils.equals(CommonConstant.NOTIFY_SUCCESS, rs.getResult())) {
            tradeNotifyBO.setResult(CommonConstant.NOTIFY_SUCCESS);
            return true;
        } else {
            logger.info("Payment Notify Failure, outTradeNo is {}, Result is {}",
                    tradeNotifyBO.getOutOrderNo(), rs == null ? "null" : rs.toString());
            tradeNotifyBO.setResult(CommonConstant.NOTIFY_FAILURE);
            return false;
        }
    }

    /**
     * 积分订单成功处理条件
     * 1. 订单的为积分订单 2.状态不为成功 3.第三方订单号为空
     *
     * @return
     */
    private boolean updateIntegralSuccessCondit(TradeOrderDO tradeOrderDO) {
        return JudgeUtils.equals(tradeOrderDO.getAimProductCode(), PaymentWayEnum.INTEGRALPAY.name().toLowerCase())
                && JudgeUtils.notEquals(tradeOrderDO.getStatus(), OrderStatusEnum.TRADE_SUCCESS.name())
                && JudgeUtils.isBlank(tradeOrderDO.getThirdOrdNo());
    }

    /**
     * 积分订单更新第三方订单号条件
     * 1. 订单的为积分订单 2.状态为成功 3.第三方订单号为空
     *
     * @return
     */
    private boolean updateIntegralThirdOrdNoCondit(TradeOrderDO tradeOrderDO) {
        return JudgeUtils.equals(tradeOrderDO.getAimProductCode(), PaymentWayEnum.INTEGRALPAY.name().toLowerCase())
                && JudgeUtils.equals(tradeOrderDO.getStatus(), OrderStatusEnum.TRADE_SUCCESS.name())
                && JudgeUtils.isBlank(tradeOrderDO.getThirdOrdNo());
    }
}
