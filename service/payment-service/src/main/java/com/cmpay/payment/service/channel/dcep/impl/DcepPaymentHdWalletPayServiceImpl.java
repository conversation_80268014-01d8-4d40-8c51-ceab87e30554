package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.ProtocolPaymentBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.constant.protocol.DcepOrderStatusEnum;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.DcepHdWalletPayOrderReq;
import com.cmpay.payment.dto.dcep.DcepHdWalletPayOrderRsp;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.DcepPaymentHdWalletPayService;
import com.cmpay.payment.util.LemonAmount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;
/**
 * <AUTHOR>
 */
@Service
public class DcepPaymentHdWalletPayServiceImpl extends AbstractDcepRequestServiceImpl implements DcepPaymentHdWalletPayService {

    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;
    private static final String TRADE_TYPE = "payment";

    @Override
    public void send(ProtocolPaymentBO protocolPaymentBO) {
        dcepHdWalletPayment(protocolPaymentBO);
    }

    @Override
    public void dcepHdWalletPayment(ProtocolPaymentBO protocolPaymentBO) {
        DcepHdWalletPayOrderReq dcepHdWalletPayOrderReq = new DcepHdWalletPayOrderReq();
        buildRequestDto(dcepHdWalletPayOrderReq, protocolPaymentBO, protocolPaymentBO.getChannelNo());
        // 渠道号待确认
        dcepHdWalletPayOrderReq.setOrgNo(dcepPaymentProperties.getHdWalletOrgNo());
        dcepHdWalletPayOrderReq.setSubMerchantNo(protocolPaymentBO.getSubMerchantId());
        dcepHdWalletPayOrderReq.setToken(protocolPaymentBO.getHdWalletToken());
        dcepHdWalletPayOrderReq.setOutTranNo(protocolPaymentBO.getOutTradeNo());
        dcepHdWalletPayOrderReq.setGoodsInfo(protocolPaymentBO.getProductDesc());
        dcepHdWalletPayOrderReq.setBusiScene(protocolPaymentBO.getBusiScene());
        dcepHdWalletPayOrderReq.setBusiType(protocolPaymentBO.getBusinessType());
        dcepHdWalletPayOrderReq.setBusiCode(protocolPaymentBO.getBusinessCode());
        dcepHdWalletPayOrderReq.setExpireMinutes(Integer.parseInt(protocolPaymentBO.getExpireMinutes()));
        dcepHdWalletPayOrderReq.setOutTranDate(protocolPaymentBO.getOrderDate());
        dcepHdWalletPayOrderReq.setOutTranTime(protocolPaymentBO.getOrderTime());
        dcepHdWalletPayOrderReq.setCurrType(DcepConstants.CURRENT_TYPE);
        dcepHdWalletPayOrderReq.setAmount(Long.parseLong(new LemonAmount(protocolPaymentBO.getRealAmount()).yuan2fen()));
        dcepHdWalletPayOrderReq.setNotifyUrl(dcepPaymentProperties.getNotifyUrl());
        dcepHdWalletPayOrderReq.setTermId(protocolPaymentBO.getTerminalCode());
        dcepHdWalletPayOrderReq.setTranplace(protocolPaymentBO.getTransPlace());
        buildDcepRequest(dcepHdWalletPayOrderReq, protocolPaymentBO, DcepPaymentChannelEnum.PAYMENT_HD_WALLET_PAY.getName());
    }

    @Override
    protected BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        DcepHdWalletPayOrderRsp hdWalletPayOrderRsp = (DcepHdWalletPayOrderRsp) abstractDcepRsp;
        ProtocolPaymentBO protocolPaymentBO = (ProtocolPaymentBO) baseDcepHandlerBO;
        if (StringUtils.equals(hdWalletPayOrderRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            DcepHdWalletPayOrderRsp.Data data = hdWalletPayOrderRsp.getData();
            DcepOrderStatusEnum orderStatusEnum = DcepOrderStatusEnum.getEnumName(TRADE_TYPE, data.getPayStatus());
            protocolPaymentBO.setTradeStatus(JudgeUtils.isNull(orderStatusEnum)? OrderStatusEnum.WAIT_PAY.name() : orderStatusEnum.name());
            if(JudgeUtils.notEquals(OrderStatusEnum.WAIT_PAY.name(), protocolPaymentBO.getTradeStatus())){
                String finishDateTime = data.getPaySuccessDate() + data.getPaySuccessTime();
                protocolPaymentBO.setFinishDateTime((StringUtils.isBlank(finishDateTime) || finishDateTime.length() != 14) ? DateTimeUtils.getCurrentDateTimeStr() : finishDateTime);
                protocolPaymentBO.setAccountDate(data.getPaySuccessDate());
            }
            Optional.ofNullable(data.getErrCode()).ifPresent(errCode -> protocolPaymentBO.setErrCode(data.getErrCode()));
            Optional.ofNullable(data.getErrMsg()).ifPresent(errMsg -> protocolPaymentBO.setErrCodeDes(data.getErrMsg()));
        } else {
            protocolPaymentBO.setErrCode(hdWalletPayOrderRsp.getCode());
            protocolPaymentBO.setErrCodeDes(hdWalletPayOrderRsp.getMsg());
            protocolPaymentBO.setTradeStatus(OrderStatusEnum.TRADE_FAIL.name());
        }
        protocolPaymentBO.setErrMsgCd(protocolPaymentBO.getErrCode());
        protocolPaymentBO.setErrMsgInfo(protocolPaymentBO.getErrCodeDes());
        return protocolPaymentBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }
}
