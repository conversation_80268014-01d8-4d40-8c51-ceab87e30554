package com.cmpay.payment.service;

import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.bo.config.PayServiceBeanBO;
import com.cmpay.payment.entity.TradeOrderDO;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/22 10:36
 * @description ：风控服务
 */
public interface RiskControlService {
    /**
     * 调用事中接口进行风控拦截
     *
     * @param payOrderBO payOrderBO
     * @param rateBO rateBO
     */
    void riskIntercept(PayOrderBO payOrderBO, PayServiceBeanBO rateBO);
    /**
     * 调用风控事后接口
     *
     * @param tradeOrderDO tradeOrderDO
     */
    void riskAfterPay(TradeOrderDO tradeOrderDO);
}
