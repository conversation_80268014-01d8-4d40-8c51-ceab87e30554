package com.cmpay.payment.service.channel.integralpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.RandomUtils;
import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.channel.IntegralPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dto.integralpay.IntegralOrderQueryReq;
import com.cmpay.payment.dto.integralpay.IntegralOrderQueryRsp;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.channel.integralpay.IntegralPayQueryChannelService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/6 14:36
 * @description ：
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class IntegralPayQueryChannelServiceImpl implements IntegralPayQueryChannelService {
    private static final Logger logger = LoggerFactory.getLogger(IntegralPayQueryChannelServiceImpl.class);

    private static final String CODE_SUCCESS = "0000";
    private static final String PAGE = "1";
    private static final String ROWS = "10";

    @Autowired
    IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Value("${integralpay.channelCode:}")
    private String channelCode;

    @Override
    public void integralBasePaymentQuery(PaymentQueryBO paymentQueryBO) {
        if (checkQueryParams(paymentQueryBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.OUT_TRADE_NO_CANNOT_BE_EMPTY);
        }
        IntegralOrderQueryReq integralOrderQueryReq = new IntegralOrderQueryReq();
        integralOrderQueryReq.setMobile(paymentQueryBO.getPaymentOrder().getMobileNumber());
        integralOrderQueryReq.setOrderId(paymentQueryBO.getPaymentOrder().getBankOrderNo());
        integralOrderQueryReq.setPage(PAGE);
        integralOrderQueryReq.setRows(ROWS);
        String jrn_no = RandomUtils.randomStringFixLength(16);
        integralOrderQueryReq.setTraceId(jrn_no);
        integralOrderQueryReq.setSpanId(jrn_no);
        integralOrderQueryReq.setServiceCode(IntegralpayConstants.S0004);
        integralOrderQueryReq.setChannelCode(channelCode);
        integralOrderQueryReq.setReqTime(DateFormatUtils.format(new Date(), "yyyyMMddHHmmss"));
        integralOrderQueryReq.setAccessChannel(IntegralpayConstants.API_APP);
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(IntegralPayChannelEnum.INTEGRAL_PAY);
        request.setRoute(IntegralPayChannelEnum.INTEGRAL_PAY);
        request.setBusiType(IntegralPayChannelEnum.INTEGRAL_ORDER_QUERY.getName());
        request.setTarget(integralOrderQueryReq);
        GenericRspDTO<Response> response;
        if (StringUtils.equalsAny(paymentQueryBO.getSourceApp(), AppEnum.integrationbatch.name(), AppEnum.integrationshecdule.name())) {
            response = this.nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        } else {
            response = this.paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        }
        if (JudgeUtils.isSuccess(response.getMsgCd())) {
            IntegralOrderQueryRsp integralOrderQueryRsp = (IntegralOrderQueryRsp) response.getBody().getResult();
            //登记积分商城订单号
            if (JudgeUtils.isNotNull(integralOrderQueryRsp)) {
                if (StringUtils.equals(CODE_SUCCESS, integralOrderQueryRsp.getRespCode())) {
                    if (JudgeUtils.isNotNull(integralOrderQueryRsp.getResult())) {
                        if (integralOrderQueryRsp.getResult().size() == 1) {
                            String status = integralOrderQueryRsp.getResult().get(0).getSubOrderStatus();
                            IntegralOrderStatusEnum statusEnum = IntegralOrderStatusEnum.getMsgCodeEnum(status);
                            switch (statusEnum) {
                                case TO_BE_SHIPPED:
                                    paymentQueryBO.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                                    break;
                                case SHIPPED:
                                    paymentQueryBO.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                                    break;
                                case CLOSED:
                                case RESCINDED:
                                default:
                                    break;
                            }
                        } else if (integralOrderQueryRsp.getResult().size() > 1) {
                            logger.error("=======查询到多笔订单, integralOrderQueryRsp：{}", integralOrderQueryRsp.toString());
                            BusinessException.throwBusinessException(MsgCodeEnum.INTEGRAL_ORDER_FIND_FALSE);
                        }
                    } else {
                        logger.error("=======查询成功,订单不存在, integralOrderQueryRsp：{}", integralOrderQueryRsp.toString());
                        paymentQueryBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                    }
                } else {
                    logger.error("=======integral order find false, RespCode：{}", integralOrderQueryRsp.getRespCode());
                    BusinessException.throwBusinessException(MsgCodeEnum.INTEGRAL_ORDER_FIND_FALSE);
                }
            } else {
                logger.error("=======integral order find false, integralOrderQueryRsp is null");
                BusinessException.throwBusinessException(MsgCodeEnum.INTEGRAL_ORDER_FIND_FALSE);
            }
        }
    }

    @Override
    public void send(PaymentQueryBO paymentQueryBO) {
        integralBasePaymentQuery(paymentQueryBO);
    }

    private boolean checkQueryParams(PaymentQueryBO paymentQueryBO) {
        return Optional.ofNullable(paymentQueryBO)
                .map(PaymentQueryBO::getPaymentOrder)
                .map(TradeOrderDO::getOutTradeNo)
                .map(StringUtils::isEmpty)
                .orElse(false);
    }
}
