package com.cmpay.payment.service.channel.cmbaion.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.channel.CmbaionPayNrtChannelEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.cmbaion.CmbaionConstants;
import com.cmpay.payment.dto.cmbaion.AbstractCmbaionDTO;
import com.cmpay.payment.dto.cmbaion.CmbaionPaymentQueryReqDTO;
import com.cmpay.payment.dto.cmbaion.CmbaionPaymentQueryRspDTO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.RiskControlService;
import com.cmpay.payment.service.channel.cmbaion.CmbaionPayQueryChannelService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.util.DateUtils;
import com.cmpay.payment.util.PaymentUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author： PengAnHai
 * @date： 2024-08-21
 * @description：招行一网通支付订单状态查询
 * @modifiedBy：
 * @version: 1.0
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmbaionPayQueryChannelServiceImpl extends CmbaionBaseRequestServiceImpl implements CmbaionPayQueryChannelService {

    @Autowired
    private RiskControlService riskControlService;
    @Autowired
    private AsynCommonService asynCommonService;
    @Autowired
    private ExtPayOrderService payOrderService;

    @Override
    public void send(PaymentQueryBO paymentQueryBO) {
        cmbaionPayQuery(paymentQueryBO);
    }

    @Override
    public void cmbaionPayQuery(PaymentQueryBO paymentQueryBO) {
        // 创建查询请求DTO
        CmbaionPaymentQueryReqDTO paymentQueryReqDTO = createPaymentQueryReqDTO(paymentQueryBO);
        // 构建并发送查询请求
        buildCmbaionRequest(paymentQueryReqDTO,paymentQueryBO, CmbaionPayNrtChannelEnum.CMBAION_PAYMENT_QUERY.getName());
    }

    /**
     * 处理来自Cmbaion的支付订单查询响应。
     *
     * @param result 响应DTO，包含响应数据。
     * @param baseHandlerBO 基础处理业务对象，用于进一步处理。
     * @return 处理后的业务对象。
     */
    @Override
    public BaseHandlerBO handlerCmbaionResponse(AbstractCmbaionDTO result, BaseHandlerBO baseHandlerBO) {
        // 强制类型转换为退款响应DTO
        CmbaionPaymentQueryRspDTO queryRspDTO = (CmbaionPaymentQueryRspDTO) result;
        // 强制类型转换为退款业务对象
        PaymentQueryBO paymentQueryBO = (PaymentQueryBO) baseHandlerBO;
        // 获取响应数据
        CmbaionPaymentQueryRspDTO.RsqData rsqData = queryRspDTO.getRsqData();
        // 检查响应代码是否不等于成功代码
        if (!JudgeUtils.equalsAny(rsqData.getRspCode(), CmbaionConstants.SUCCESS, CmbaionConstants.NO_ORDER)) {
            // 抛出业务异常，并传递响应代码和响应信息
            BusinessException.throwBusinessException(rsqData.getRspCode(), rsqData.getRspMsg());
        }
        if (JudgeUtils.equals(rsqData.getRspCode(), CmbaionConstants.NO_ORDER)) {
            // 订单不存在，处理订单失败的逻辑
            orderFailHandle(paymentQueryBO.getPaymentOrder());
        }else if (JudgeUtils.equals(rsqData.getOrderStatus(), CmbaionConstants.ORDER_SUCCESS)) {
            // 检查订单状态是否为成功
            // 处理订单成功的逻辑
            orderSuccessHandle(paymentQueryBO.getPaymentOrder(), rsqData);
        } else if (JudgeUtils.equals(rsqData.getOrderStatus(), CmbaionConstants.ORDER_FAIL)) {
            // 订单状态为失败，处理订单失败的逻辑
            orderFailHandle(paymentQueryBO.getPaymentOrder());
        }
        // 返回查询业务对象
        return paymentQueryBO;
    }

    /**
     * 处理订单失败的逻辑
     *
     * @param tradeOrder 支付订单对象
     * @return 如果处理成功则返回true，否则返回false
     */
    private boolean orderFailHandle(TradeOrderDO tradeOrder) {
        // 判断订单是否超时关闭
        if (DateUtils.compareTimeIsTimeOut(tradeOrder.getExpireTime(), 1)) {
            // 将订单状态设置为交易关闭
            tradeOrder.setStatus(OrderStatusEnum.TRADE_CLOSED.name());
            // 更新支付订单的状态，并检查更新是否成功
            if (0 == payOrderService.updatePaymentStatus(tradeOrder)) {
                // 更新失败，返回false
                return false;
            }
        }
        // 处理成功，返回true
        return true;
    }

    /**
     * 处理订单成功的逻辑
     *
     * @param tradeOrder 支付订单对象
     * @param rsqData 响应数据对象
     * @return 如果处理成功则返回true，否则返回false
     */
    private boolean orderSuccessHandle(TradeOrderDO tradeOrder, CmbaionPaymentQueryRspDTO.RsqData rsqData) {
        // 设置支付订单的费率
        setPayOrderRate(tradeOrder);
        // 设置交易订单的详细信息
        setTradeOder(tradeOrder, rsqData);
        // 更新支付订单的状态，并检查更新是否成功
        if (0 == payOrderService.updatePaymentStatus(tradeOrder)) {
            // 更新失败，返回false
            return false;
        }
        // 异步连接第三方订单号
        asynCommonService.asynConnectThirdNo(tradeOrder);
        // 处理支付后的风控逻辑
        riskControlService.riskAfterPay(tradeOrder);
        // 通知商户
        notifyMerchant(tradeOrder);
        // 处理成功，返回true
        return true;
    }

    /**
     * 设置交易订单的详细信息
     *
     * @param tradeOrder 支付订单对象
     * @param rsqData 响应数据对象，包含查询返回的支付信息
     * @return 更新后的支付订单对象
     */
    private TradeOrderDO setTradeOder(TradeOrderDO tradeOrder, CmbaionPaymentQueryRspDTO.RsqData rsqData) {
        tradeOrder.setStatus((OrderStatusEnum.TRADE_SUCCESS.name()));
        tradeOrder.setOrderCompleteTime(rsqData.getSettleTime());
        tradeOrder.setAccountDate(DateUtils.verifiAndSetAccountDate((rsqData.getSettleDate())));
        tradeOrder.setCrdAcTyp(PaymentUtils.setCmbaionCrdAcTyp(rsqData.getCardType()));
        tradeOrder.setThirdOrdNo(rsqData.getBankSerialNo());
        tradeOrder.setThirdOrdDt(tradeOrder.getAccountDate());
        return tradeOrder;
    }

    /**
     * 从支付订单查询业务对象创建查询请求DTO。
     * @param paymentQueryBO
     * @return
     */
    private CmbaionPaymentQueryReqDTO createPaymentQueryReqDTO(PaymentQueryBO paymentQueryBO) {
        // 获取原始支付交易订单
        TradeOrderDO tradeOrderDO = paymentQueryBO.getPaymentOrder();
        CmbaionPaymentQueryReqDTO paymentQueryReqDTO = new CmbaionPaymentQueryReqDTO();
        CmbaionPaymentQueryReqDTO.ReqData reqData = new CmbaionPaymentQueryReqDTO.ReqData();
        // 设置当前日期时间字符串
        reqData.setDateTime(DateTimeUtils.getCurrentDateTimeStr());
        // 设置分行号
        reqData.setBranchNo(getBranchNo(tradeOrderDO.getBankMerchantNo()));
        // 设置商户号
        reqData.setMerchantNo(getMerchantNo(tradeOrderDO.getBankMerchantNo()));
        // 设置类型
        reqData.setType(CmbaionConstants.PAY_QUERY_TYPE);
        // 设置订单日期
        reqData.setDate(tradeOrderDO.getOrderDate());
        // 设置交易订单号
        reqData.setOrderNo(tradeOrderDO.getTradeOrderNo());
        paymentQueryReqDTO.setReqData(reqData);
        setRequestDataDTO(paymentQueryReqDTO);
        return paymentQueryReqDTO;
    }

}
