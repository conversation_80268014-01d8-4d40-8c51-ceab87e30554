package com.cmpay.payment.service.channel.aplipay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.dto.alipay.TradePayReq;
import com.cmpay.payment.dto.alipay.TradePayRsp;
import com.cmpay.payment.entity.AlipayPaymentJrnDO;
import com.cmpay.payment.properties.AlipayProperties;
import com.cmpay.payment.service.channel.aplipay.AlipayBarcodePaymentChannelService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.PaymentUtils;
import com.cmpay.payment.util.UrlUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;

/**
 * Created on 2018/12/13
 *
 * @author: wulinfeng
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class AlipayBarcodePaymentChannelServiceImpl extends AlipayBasePaymentChannelServiceImpl implements AlipayBarcodePaymentChannelService {

    Logger logger = LoggerFactory.getLogger(AlipayBarcodePaymentChannelServiceImpl.class);

    @Autowired
    AlipayProperties alipayProperties;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;

    @Autowired
    ExtParamInfoService paramInfoService;

    /**
     * 接口调用成功，调用结果请参考具体的API文档所对应的业务返回参数
     */
    private static final String CODE_SUCCESS = "10000";

    @Override
    public boolean getStatus(PayOrderBO payOrderBO) {
        TradePayReq tradePayReq = new TradePayReq();
        tradePayReq.setOutTradeNo(payOrderBO.getTradeOrderNo());

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                tradePayReq.setNotifyUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getNotifyUrl(),host));
                tradePayReq.setReturnUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getReturnUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                tradePayReq.setNotifyUrl(alipayProperties.getNotifyUrl());
                tradePayReq.setReturnUrl(alipayProperties.getReturnUrl());
            }
        } else {
            tradePayReq.setNotifyUrl(alipayProperties.getNotifyUrl());
            tradePayReq.setReturnUrl(alipayProperties.getReturnUrl());
        }

        tradePayReq.setScene("bar_code");
        tradePayReq.setAuthCode(payOrderBO.getAuthCode());
        tradePayReq.setTotalAmount(payOrderBO.getRealAmount().toString());
        tradePayReq.setSubject(payOrderBO.getSubject());
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            Date expireTime=simpleDateFormat.parse(payOrderBO.getExpireTime());
            tradePayReq.setTimeExpire(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(expireTime));
        } catch (ParseException e) {
            logger.info(e.getMessage());
        }

        if(StringUtils.isNotBlank(payOrderBO.getTimeoutExpress())){
            tradePayReq.setTimeoutExpress(payOrderBO.getTimeoutExpress());
        }
        try {
            GenericRspDTO<Response> response = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_PAY.getName(), tradePayReq));

            Response body = response.getBody();
            Optional.ofNullable(body)
                    .ifPresent(payOrderBO::setAlipayPayRsp);

            if (StringUtils.contains(response.getMsgCd(), "00000")) {

                TradePayRsp tradePayRsp = (TradePayRsp) response.getBody().getResult();
                payOrderBO.setThirdOrdNo(tradePayRsp.getTradeNo());
                payOrderBO.setMobileNumber(tradePayRsp.getBuyerUserId());
                if (JudgeUtils.equals(tradePayRsp.getCode(), CODE_SUCCESS) && JudgeUtils.isNotEmpty(tradePayRsp.getTotalAmount())) {

                    Response rsp = payOrderBO.getAlipayPayRsp();

                    // 支付成功，修改流水状态为成功
                    AlipayPaymentJrnDO alipayPaymentJrnDO = new AlipayPaymentJrnDO();

                    Optional.ofNullable(rsp)
                            .map(Response::getMsgCode)
                            .ifPresent(alipayPaymentJrnDO::setReturnMsg);

                    Optional.ofNullable(rsp)
                            .map(Response::getMsgInfo)
                            .ifPresent(alipayPaymentJrnDO::setReturnMsgInfo);

                    alipayPaymentJrnDO.setTradeOrderNo(payOrderBO.getTradeOrderNo());
                    alipayPaymentJrnDO.setTradeStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                    payOrderBO.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                    payOrderBO.setFinishDateTime(DateTimeUtils.getCurrentDateTimeStr());
                    payOrderBO.setThirdOrdDt(DateTimeUtils.getCurrentDateStr());
                    return true;
                }
            }

            return false;

        } catch (Exception e) {
            logger.error("接出网关请求异常");
            return false;
        }

    }

    @Override
    public void send(PayOrderBO payOrderBO) {
        alipayBarcodePayment(payOrderBO);
    }

    @Override
    public void alipayBarcodePayment(PayOrderBO payOrderBO) {
        super.alipayBasePayment(payOrderBO);
    }
}
