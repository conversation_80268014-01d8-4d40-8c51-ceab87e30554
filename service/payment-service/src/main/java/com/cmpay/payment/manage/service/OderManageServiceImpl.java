package com.cmpay.payment.manage.service;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.*;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.entity.OrderPaymentAttachDO;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.manage.IOrderManageService;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.TradeSynService;
import com.cmpay.payment.service.UndefinedRefundQueryService;
import com.cmpay.payment.service.ext.ExtPayOrderAttachService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.ExtRefundOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/8/24 14:39
 */
@Service
@Slf4j
public class OderManageServiceImpl implements IOrderManageService {

    @Autowired
    private TradeSynService tradeSynService;
    @Autowired
    private ExtPayOrderService extPayOrderService;
    @Autowired
    private ExtRefundOrderService refundOrderService;
    @Autowired
    private UndefinedRefundQueryService undefinedRefundQueryService;
    @Autowired
    private ExtPayOrderAttachService payOrderAttachService;
    @Autowired
    private AsynCommonService asynCommonService;

    @Override
    public void orderStatusSync(FapOrderSyncBO orderSyncBO) {
        if (StringUtils.equals(orderSyncBO.getOrderType(), OrderTypeEnum.TRADE.getValue())) {
            //支付订单状态同步
            PayOrderBO orderBO = new PayOrderBO();
            orderBO.setTradeOrderNo(orderSyncBO.getTradeOrderNo());
            TradeOrderDO tradeOrderDO = extPayOrderService.get(orderBO);
            if (JudgeUtils.isNull(tradeOrderDO)) {
                log.warn("orderStatusSync error:order not exist");
                return;
            }
            if (!StringUtils.equals(PaymentStatusEnum.WAIT_PAY.getDesc(),tradeOrderDO.getStatus())){
                log.warn("orderStatusSync error:status error,{}",tradeOrderDO.getStatus());
                return;
            }
            PaymentQueryBO paymentQueryBO=new PaymentQueryBO();
            paymentQueryBO.setPaymentOrder(tradeOrderDO);
            if(DcepConstants.DCEP.equals(tradeOrderDO.getDcepFlag())
                    || PaymentWayEnum.ICBCPAY.name().equalsIgnoreCase(tradeOrderDO.getPayProductCode())){
                OrderPaymentAttachDO attachDO = new OrderPaymentAttachDO();
                attachDO.setOrderDate(tradeOrderDO.getRequestDate());
                attachDO.setOutTradeNo(tradeOrderDO.getOutTradeNo());
                OrderPaymentAttachDO orderPaymentAttachDO = payOrderAttachService.load(attachDO);
                if (JudgeUtils.isNotNull(orderPaymentAttachDO)) {
                    paymentQueryBO.setOrderPaymentAttachDO(orderPaymentAttachDO);
                } else {
                    BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
                }
            }
            try{
                paymentQueryBO.setSourceApp(AppEnum.integrationshecdule.name());
                TradeOrderAndNotifyBO orderAndNotifyBO =  tradeSynService.paymentOrderSyn(paymentQueryBO);
                if (JudgeUtils.isNull(orderAndNotifyBO) || JudgeUtils.isNull(orderAndNotifyBO.getTradeOrderDO())) {
                    return;
                }
                //后台通知
                asynCommonService.asyncNotify(orderAndNotifyBO.getTradeNotifyBO());
            } catch (BusinessException e) {
                if (StringUtils.equals(MsgCodeEnum.PAYMENT_STATUS_UNDEFINED.getMsgCd(),e.getMsgCd())) {
                    BusinessException.throwBusinessException(MsgCodeEnum.FAP_SUCCESSFUL);
                } else {
                    BusinessException.throwBusinessException(e.getMsgCd());
                }
            }

        } else if (StringUtils.equals(orderSyncBO.getOrderType(), OrderTypeEnum.REFUND.getValue())) {
            //退款订单状态同步
           RefundOrderDO refundOrderDO= refundOrderService.get(orderSyncBO.getTradeOrderNo());
            RefundOrderCloneBO refundOrderBO=new RefundOrderCloneBO();
            BeanUtils.copyProperties(refundOrderDO, refundOrderBO);
            undefinedRefundQueryService.undefinedRefundOrderQuery(refundOrderBO);
        } else {
            log.error("订单类型不合法，orderType：{}", orderSyncBO.getOrderType());
        }

    }
}
