package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.constant.protocol.DcepOrderStatusEnum;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.DcepPaymentOrderQueryReq;
import com.cmpay.payment.dto.dcep.DcepPaymentOrderQueryRsp;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.DcepPaymentQueryService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/5/7
 */
@Service
public class DcepPaymentQueryServiceImpl extends AbstractDcepRequestServiceImpl implements DcepPaymentQueryService {

    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;

    private static final String ORDER_TYPE = "payment";

    @Override
    public void handlerDcepPaymentQuery(PaymentQueryBO paymentQueryBO) {
        paymentQueryBO.setPayWayCode(paymentQueryBO.getPaymentOrder().getPayWayCode().toLowerCase());
        paymentQueryBO.setBusinessType(DcepPaymentChannelEnum.PAYMENT_ORDER_QUERY.getName());
        DcepPaymentOrderQueryReq paymentOrderQueryReq = new DcepPaymentOrderQueryReq();
        paymentOrderQueryReq.setOrgNo(dcepPaymentProperties.getOrgNo());
        buildRequestDto(paymentOrderQueryReq, paymentQueryBO, paymentQueryBO.getPaymentOrder().getBankMerchantNo());
        paymentOrderQueryReq.setSubMerchantNo(paymentQueryBO.getOrderPaymentAttachDO().getSubMerchantId());
        paymentOrderQueryReq.setOutTranNo(paymentQueryBO.getPaymentOrder().getOutTradeNo());
        paymentOrderQueryReq.setOutTranDate(paymentQueryBO.getPaymentOrder().getOrderDate());
        buildDcepRequest(paymentOrderQueryReq, paymentQueryBO, paymentQueryBO.getBusinessType());
    }

    @Override
    public void send(PaymentQueryBO paymentQueryBO) {
        handlerDcepPaymentQuery(paymentQueryBO);
    }

    @Override
    public PaymentQueryBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        if (!StringUtils.equals(abstractDcepRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            BusinessException.throwBusinessException(MsgCodeEnum.DCEP_PAY_QUERY_FAIL);
        }
        PaymentQueryBO paymentQueryBO = (PaymentQueryBO) baseDcepHandlerBO;
        DcepPaymentOrderQueryRsp paymentOrderQueryRsp = (DcepPaymentOrderQueryRsp) abstractDcepRsp;
        String finishDateTime = paymentOrderQueryRsp.getData().getPaySuccessDate() + paymentOrderQueryRsp.getData().getPaySuccessTime();
        paymentQueryBO.setFinishDateTime((StringUtils.isBlank(finishDateTime) || finishDateTime.length() != 14 ? DateTimeUtils.getCurrentDateStr() : finishDateTime));
        paymentQueryBO.getPaymentOrder().setAccountDate(paymentOrderQueryRsp.getData().getPaySuccessDate());
        DcepOrderStatusEnum orderStatusEnum = DcepOrderStatusEnum.getEnumName(ORDER_TYPE, paymentOrderQueryRsp.getData().getPayStatus());
        paymentQueryBO.setStatus(orderStatusEnum != null ? orderStatusEnum.name() : DcepOrderStatusEnum.WAIT_PAY.name());
        paymentQueryBO.getPaymentOrder().setThirdOrdNo(paymentOrderQueryRsp.getData().getPaySeq());
        return paymentQueryBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {
        DcepPaymentOrderQueryReq paymentOrderQueryReq = (DcepPaymentOrderQueryReq) abstractDcepReq;
        paymentOrderQueryReq.setOrgNo(dcepPaymentProperties.getNewOrgNo());
        baseDcepHandlerBO.setBusinessType(DcepPaymentChannelEnum.DCEP_PAYMENT_ORDER_QUERY.getName());
    }
}
