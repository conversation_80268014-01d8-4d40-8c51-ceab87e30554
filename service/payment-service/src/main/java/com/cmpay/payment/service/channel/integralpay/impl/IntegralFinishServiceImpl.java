package com.cmpay.payment.service.channel.integralpay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.IntegralPayFinishBO;
import com.cmpay.payment.channel.IntegralPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.IntegralPaymentStatusEnum;
import com.cmpay.payment.constant.IntegralpayConstants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.dto.integralpay.IntegralSubmitVirtualReq;
import com.cmpay.payment.dto.integralpay.IntegralSubmitVirtualRsp;
import com.cmpay.payment.service.channel.integralpay.IntegralFinishService;
import com.cmpay.payment.util.PaymentUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> wenzhan
 * @Date 2021-9-2 0002 11:42
 */
@Service
public class IntegralFinishServiceImpl implements IntegralFinishService {
    private static final Logger logger = LoggerFactory.getLogger(IntegralFinishServiceImpl.class);

    @Autowired
    IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Value("${integralpay.identity_id:}")
    private String identityId;
    @Override
    public void send(IntegralPayFinishBO integralPayFinishBO) {
        IntegralSubmitVirtualReq integralSubmitVirtualReq = new IntegralSubmitVirtualReq();
        integralSubmitVirtualReq.setVersion(IntegralpayConstants.VERSION);
        integralSubmitVirtualReq.setIdentityId(integralPayFinishBO.getIntegralMercNo());//移动分配
        integralSubmitVirtualReq.setSource(IntegralpayConstants.SOURCE);
        IntegralSubmitVirtualReq.ReqBody reqBody = new IntegralSubmitVirtualReq.ReqBody();
        reqBody.setOrderId(integralPayFinishBO.getBankOrderNo());
        reqBody.setItemId(integralPayFinishBO.getProductCode());
        List<IntegralSubmitVirtualReq.ReqBody.VirtualCode> virtualCodeList = new ArrayList<>();
        IntegralSubmitVirtualReq.ReqBody.VirtualCode virtualCode = new IntegralSubmitVirtualReq.ReqBody.VirtualCode();
        virtualCode.setVcode(integralPayFinishBO.getVcode());
        virtualCode.setVcodePass(integralPayFinishBO.getVcodePass());
        virtualCodeList.add(virtualCode);
        reqBody.setVirtualCodes(virtualCodeList);
        integralSubmitVirtualReq.setData(reqBody);
        logger.info("integralSubmitVirtualReq : {}",integralSubmitVirtualReq.toString());

        GenericRspDTO<Response> genericRspDTO = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(IntegralPayChannelEnum.INTEGRAL_PAY, IntegralPayChannelEnum.INTEGRAL_SUBMIT_VIRTUAL.getName(), IntegralPayChannelEnum.INTEGRAL_PAY, integralSubmitVirtualReq));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.POINTS_FINISH_TIMEOUT);
        }
        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(integralPayFinishBO, (IntegralSubmitVirtualRsp) result));
    }

    public void handleResult(IntegralPayFinishBO integralPayFinishBO, IntegralSubmitVirtualRsp integralSubmitVirtualRsp){
        logger.info("integralSubmitVirtualRsp : {}",integralSubmitVirtualRsp.toString());
        if(JudgeUtils.equals(integralSubmitVirtualRsp.getCode(), IntegralpayConstants.VIRTUAL_SUCCESS_CODE)&& JudgeUtils.equals(integralSubmitVirtualRsp.getMsg(), IntegralpayConstants.VIRTUAL_SUCCESS_MSG)){
            integralPayFinishBO.setStatus(IntegralPaymentStatusEnum.SEND_FINISH.name());
        }else{
            integralPayFinishBO.setStatus(IntegralPaymentStatusEnum.SEND_FAIL.name());
            integralPayFinishBO.setErrMsgInfo(integralSubmitVirtualRsp.getMsg());
            integralPayFinishBO.setErrMsgCd(integralSubmitVirtualRsp.getCode());
            BusinessException.throwBusinessException(MsgCodeEnum.POINTS_FINISH_ORDER_FAIL);

        }
    }
}
