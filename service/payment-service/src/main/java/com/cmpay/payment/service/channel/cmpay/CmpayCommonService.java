package com.cmpay.payment.service.channel.cmpay;


import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.BaseDO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.payment.entity.CmpayRefundJrnDO;

/**
 * Created on 2023/11/9
 *
 * @author: Lhm
 */
public interface CmpayCommonService {

    void cgwReturnInfoCheck(GenericRspDTO<Response> genericRspDTO);

    void cmapyThrowClassHandle(CmpayRefundJrnDO cmpayRefundJrnDO, Exception e);
}
