package com.cmpay.payment.util;


import com.cmpay.lemon.common.utils.JudgeUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Hex;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.SecureRandom;
import java.security.Security;

/**
 * 　　* @description: 国密 Sm4 对称加密
 * 　　* <AUTHOR>
 * 　　* @date 2023/12/10 10:41
 *
 */
public class GmwSm4Utils {

    private static final String ALGORITHM = "SM4/GCM/NoPadding";
    private static final String KEY_ALGORITHM = "SM4";
    private static final String PROVIDER = "BC";

    static {
        // 添加Bouncy Castle提供者
        Security.addProvider(new BouncyCastleProvider());
    }


    public static String genIv(){
        SecureRandom random = new SecureRandom();
        return Hex.toHexString(random.generateSeed(16));
    }

    public static String genKey() throws Exception {
        // 创建一个SM4密钥生成器
        KeyGenerator keyGenerator = KeyGenerator.getInstance(KEY_ALGORITHM, PROVIDER);
        // 设置密钥长度为128位
        keyGenerator.init(128);
        // 生成SM4密钥
        SecretKey secretKey = keyGenerator.generateKey();
        //编码 16进制
        return  Hex.toHexString(secretKey.getEncoded());
    }

    public static String encrypt(String plainText,String key, byte[] iv) throws Exception {
        //实例
        Cipher cipher = Cipher.getInstance(ALGORITHM, PROVIDER);
        //密钥
        Key keySpec = new SecretKeySpec(Hex.decode(key), KEY_ALGORITHM);
        //初始化
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, new IvParameterSpec(iv));
        //加密
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        //编码
        return Hex.toHexString(encryptedBytes);
    }

    public static String decrypt(String cipherText,String key,byte[] iv) throws Exception {
        if (JudgeUtils.isBlank(cipherText)) {
            return cipherText;
        }
        //实例
        Cipher cipher = Cipher.getInstance(ALGORITHM, PROVIDER);
        //密钥
        Key keySpec = new SecretKeySpec(Hex.decode(key), KEY_ALGORITHM);
        //初始化
        cipher.init(Cipher.DECRYPT_MODE, keySpec, new IvParameterSpec(iv));
        //解码
        byte[] ciphertext = Hex.decode(cipherText);
        //解密
        byte[] decryptedText = cipher.doFinal(ciphertext);
        //转字符串
        return new String(decryptedText, StandardCharsets.UTF_8);
    }
}
