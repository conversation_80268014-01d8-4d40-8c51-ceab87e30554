package com.cmpay.payment.service.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.CmpayAccountRefundBO;
import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.bo.config.SubMerchantRefreshAmountBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dto.cmpay.CmpayAccountRefundReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayAccountRefundRspDTO;
import com.cmpay.payment.entity.CmpayRefundJrnDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.CompleteOrderSplitService;
import com.cmpay.payment.service.channel.cmpay.CmpayAccountRefundChannelService;
import com.cmpay.payment.service.channel.cmpay.CmpayCommonService;
import com.cmpay.payment.service.ext.ExtCmpayRefundJrnService;
import com.cmpay.payment.service.SubMerchantIncomeService;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Created on 2024/04/27
 *
 * @author: lb
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayAccountRefundChannelServiceImpl implements CmpayAccountRefundChannelService {

    private static Logger logger = LoggerFactory.getLogger(CmpayAccountRefundChannelServiceImpl.class);

    @Value("${cmpay.channelId:}")
    private String channelId;
    @Autowired
    ExtCmpayRefundJrnService extCmpayRefundJrnService;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Autowired
    private CmpayCommonService cmpayCommonService;
    @Autowired
    private CompleteOrderSplitService splitService;
    @Autowired
    private SubMerchantIncomeService refundConfineService;

    @Override
    public void send(RefundBO refundBO) {
        cmpayAccountRefund(refundBO);
    }

    @Override
    public void cmpayAccountRefund(RefundBO refundBO) {
        //查询是否存在一条
        CmpayRefundJrnDO cmpayRefundJrnDO=new CmpayRefundJrnDO();
        cmpayRefundJrnDO.setTradeOrderNo(refundBO.getOutRequestNo());
        List<CmpayRefundJrnDO> find = extCmpayRefundJrnService.find(cmpayRefundJrnDO);
        if (find.size()==0) {
            TradeOrderDO originalPayTradeOrderDO = refundBO.getOriginalPayTradeOrder();
            String subMerchant = StringUtils.isEmpty(refundBO.getSubMerchant()) ? originalPayTradeOrderDO.getSubMerchantNo() : refundBO.getSubMerchant();
            //原订单号子商户不为空，进行退款限制
            if(StringUtils.isNotEmpty(subMerchant)){
                // 查询和包退款流水，如果没有这笔退款，说明是第一次发起，则进行限制
                SubMerchantRefreshAmountBO refreshAmountBO = new SubMerchantRefreshAmountBO();
                refreshAmountBO.setSubMerchantNo(subMerchant);
                refreshAmountBO.setAmount(refundBO.getRefundOrder().getOrderAmount());
                refundConfineService.refundConfine(refreshAmountBO);
            }

            try {
                cmpayRefundJrnDO = extCmpayRefundJrnService.insertByNewTranscation(refundBO);
            } catch (Exception e) {
                splitService.refundFailIncrement(refundBO.getRefundOrder(), originalPayTradeOrderDO);
                // 报错退出
                BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ERROR);
            }
        }
        try {
            // 调用和包账户退款接口
            CmpayAccountRefundBO cmpayAccountRefundBO = accountRefund(refundBO);
            cmpayRefundJrnDO.setTradeStatus(cmpayAccountRefundBO.getJrnStatus());
            cmpayRefundJrnDO.setReturnMsg(cmpayAccountRefundBO.getMessageCode());
            cmpayRefundJrnDO.setReturnMsgInfo(cmpayAccountRefundBO.getMessageInfo());
            refundBO.setAccountDate(cmpayAccountRefundBO.getAccountDate());
            refundBO.setStatus(cmpayAccountRefundBO.getJrnStatus());
            refundBO.setErrMsgCd(cmpayAccountRefundBO.getErrMsgCd());
            refundBO.setErrMsgInfo(cmpayAccountRefundBO.getErrMsgInfo());
        } catch (Exception e) {
            //系统异常，比如网络异常 记录异常日志
            cmpayCommonService.cmapyThrowClassHandle(cmpayRefundJrnDO, e);
            throw e;
        } finally {
            //将异常信息更新到 cmpay退款流水记录
            extCmpayRefundJrnService.updateByNewTranscation(cmpayRefundJrnDO);
        }
        if (!MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd().equals(cmpayRefundJrnDO.getReturnMsg())) {
            // 报错退出
            BusinessException.throwBusinessException(cmpayRefundJrnDO.getReturnMsg());
        }
    }

    private CmpayAccountRefundBO accountRefund(RefundBO refundBO){
        CmpayAccountRefundBO cmpayAccountRefundBO = new CmpayAccountRefundBO();

        CmpayAccountRefundReqDTO cmpayAccountRefundReqDTO = new CmpayAccountRefundReqDTO();
        cmpayAccountRefundReqDTO.setVersion(CmpayConstants.VERSION);
        cmpayAccountRefundReqDTO.setType(CmpayConstants.TYPE);
        cmpayAccountRefundReqDTO.setRequestNo(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.CMPAY_JRN_NO, 18));
        cmpayAccountRefundReqDTO.setChannelNo(channelId);
        cmpayAccountRefundReqDTO.setMerchantId(refundBO.getOriginalPayTradeOrder().getBankMerchantNo());
        cmpayAccountRefundReqDTO.setRefundAmount(refundBO.getRefundAmount());
        cmpayAccountRefundReqDTO.setMerchantOrderDate(refundBO.getOriginalPayTradeOrder().getOrderDate());
        cmpayAccountRefundReqDTO.setMerchantRefundNo(refundBO.getRefundOrder().getOutTradeNo());
        cmpayAccountRefundReqDTO.setMerchantOrderNo(refundBO.getOriginalPayTradeOrder().getTradeOrderNo());

        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(CmpayPayChannelEnum.CMPAY_ACCOUNT);
        request.setBusiType(CmpayPayChannelEnum.CMPAY_ACCOUNT_REFUND.getName());
        request.setSource(CmpayPayChannelEnum.CMPAY_ACCOUNT);
        request.setTarget(cmpayAccountRefundReqDTO);
        GenericRspDTO<Response> genericRspDTO ;
        if (StringUtils.equals(refundBO.getSourceApp(), AppEnum.integrationpaymnet.name())) {
            genericRspDTO =  paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        } else {
            genericRspDTO =  nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        }
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleOnlineRefund(cmpayAccountRefundBO, (CmpayAccountRefundRspDTO) result));
        return cmpayAccountRefundBO;
    }


    private void handleOnlineRefund(CmpayAccountRefundBO cmpayAccountRefundBO, CmpayAccountRefundRspDTO cmpayAccountRefundRspDTO){
        if (JudgeUtils.isNull(cmpayAccountRefundRspDTO)) {
            cmpayAccountRefundBO.setMessageCode(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgCd());
            cmpayAccountRefundBO.setMessageInfo(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgInfo());
            return;
        }
        logger.info("和包响应码:{}",cmpayAccountRefundRspDTO.getMsgCd());
        if (JudgeUtils.isSuccess(cmpayAccountRefundRspDTO.getMsgCd())) {
            cmpayAccountRefundBO.setJrnStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            cmpayAccountRefundBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            cmpayAccountRefundBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
        } else if (isRefundExistMessage(cmpayAccountRefundRspDTO)) {
            cmpayAccountRefundBO.setJrnStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            cmpayAccountRefundBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            cmpayAccountRefundBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
        } else if (JudgeUtils.isNull(cmpayAccountRefundRspDTO.getMsgCd())
                || cmpayAccountRefundRspDTO.getMsgCd().toUpperCase().contains(CommonConstant.SYS)
                || StringUtils.equalsIgnoreCase(MsgCodeEnum.CMPAY_ACCOUNT_ERROR.getMsgCd(), cmpayAccountRefundRspDTO.getMsgCd())) {
            cmpayAccountRefundBO.setMessageCode(cmpayAccountRefundRspDTO.getMsgCd());
            cmpayAccountRefundBO.setMessageInfo(cmpayAccountRefundRspDTO.getMsgInfo());
        } else {
            cmpayAccountRefundBO.setJrnStatus(OrderStatusEnum.REFUND_FAIL.name());
            cmpayAccountRefundBO.setMessageCode(cmpayAccountRefundRspDTO.getMsgCd());
            cmpayAccountRefundBO.setMessageInfo(cmpayAccountRefundRspDTO.getMsgInfo());
            //保存报错信息
            cmpayAccountRefundBO.setErrMsgCd(cmpayAccountRefundRspDTO.getMsgCd());
            cmpayAccountRefundBO.setErrMsgInfo(cmpayAccountRefundRspDTO.getMsgInfo());
        }
    }

    public static boolean isRefundExistMessage(CmpayAccountRefundRspDTO refundRspDTO) {
        // 将需要比较的消息码作为枚举值处理，确保类型安全和易于维护
        String[] refundExistCodes = {
                MsgCodeEnum.CMPAY_ONLINE_REFUND_ORDER_EXISTS_REFUND.getMsgCd(),
                MsgCodeEnum.CMPAY_ACCOUNT_EXISTS_REFUND.getMsgCd()
        };
        return StringUtils.equalsAnyIgnoreCase(refundRspDTO.getMsgCd(), refundExistCodes);
    }
}
