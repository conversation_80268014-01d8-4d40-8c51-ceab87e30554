package com.cmpay.payment.service.channel.cmpay.impl;

/**
 * Created on 2018/12/03
 * cmapy 云支付渠道  支付业务类
 *
 * @author: li_zhen
 */

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dto.cmpay.CmpayPosBarcodePaymentReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayPosBarcodePaymentRspDTO;
import com.cmpay.payment.service.channel.cmpay.CmpayBarcodePaymentChannelService;
import com.cmpay.payment.service.channel.cmpay.CmpayCommonService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Optional;

@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayBarcodePaymentChannelServiceImpl implements CmpayBarcodePaymentChannelService {
    private static final Logger logger = LoggerFactory.getLogger(CmpayBarcodePaymentChannelServiceImpl.class);
    @Value("${cmpay.cpsBackendNotifyUrl:}")
    private String cpsBackendNotifyUrl;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private CmpayCommonService cmpayCommonService;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Override
    public void send(PayOrderBO payOrderBO) {
        cmpayBarcodePayment(payOrderBO);
    }

    @Override
    public void cmpayBarcodePayment(PayOrderBO payOrderBO) {
        CmpayPosBarcodePaymentReqDTO cmpayBarcodePaymentReq = new CmpayPosBarcodePaymentReqDTO();
        cmpayBarcodePaymentReq.setCharacterSet(CmpayConstants.CHARACTER_SET2);

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(host)) {
            try {
                cmpayBarcodePaymentReq.setNotifyUrl(UrlUtils.replaceDomainOrIp(cpsBackendNotifyUrl,host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                cmpayBarcodePaymentReq.setNotifyUrl(cpsBackendNotifyUrl);
            }
        } else {
            cmpayBarcodePaymentReq.setNotifyUrl(cpsBackendNotifyUrl);
        }

        cmpayBarcodePaymentReq.setRequestId(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.CMPAY_JRN_NO, 18));
        cmpayBarcodePaymentReq.setMerchantId(payOrderBO.getPaymentId());
        cmpayBarcodePaymentReq.setSignType(payOrderBO.getSignMethod());
        cmpayBarcodePaymentReq.setType(CmpayConstants.CPOS_BARCODE_PAY);
        cmpayBarcodePaymentReq.setVersion(CmpayConstants.CPOS_VERSION);
        cmpayBarcodePaymentReq.setAmount(payOrderBO.getRealAmount().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_DOWN));
        cmpayBarcodePaymentReq.setCurrency(CommonConstant.CURRENCY);
        cmpayBarcodePaymentReq.setOrderDate(payOrderBO.getOrderDate());
        cmpayBarcodePaymentReq.setOrderId(payOrderBO.getTradeOrderNo());
        cmpayBarcodePaymentReq.setPeriod(new BigDecimal(payOrderBO.getValidityNumber()));
        cmpayBarcodePaymentReq.setPeriodUnit(payOrderBO.getValidityUnit());
        cmpayBarcodePaymentReq.setGoodsId(payOrderBO.getProductCode());
        cmpayBarcodePaymentReq.setGoodsQuantity(new BigDecimal(CommonConstant.DEFAULT_GOODS_QUANTITY));
        cmpayBarcodePaymentReq.setGoodsPrice(payOrderBO.getTotalAmount());
        cmpayBarcodePaymentReq.setProductName(payOrderBO.getSubject());
        cmpayBarcodePaymentReq.setUserToken(payOrderBO.getAuthCode());
        cmpayBarcodePaymentReq.setStoreBrand(payOrderBO.getHallCode());
        cmpayBarcodePaymentReq.setCityBranchNm(payOrderBO.getHallWindowCode());
        cmpayBarcodePaymentReq.setDeviceInfo(payOrderBO.getTerminalCode());
        cmpayBarcodePaymentReq.setSpbillCreateIp(payOrderBO.getClientIps());
        cmpayBarcodePaymentReq.setHmac(payOrderBO.getContractSecureValue());
        if (JudgeUtils.equals(payOrderBO.getPayWay(), PaymentWayEnum.UNIONPAY.name().toLowerCase())) {
            cmpayBarcodePaymentReq.setDiscountCode(payOrderBO.getDiscountCode());
        }
        cmpayBarcodePaymentReq.setReconciliationCheckFlg(CmpayConstants.SUPPLY_MODE);
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(CmpayPayChannelEnum.CMPAY_POS);
        request.setBusiType(CmpayPayChannelEnum.CMPAYPOS_BARCODE.getName());
        request.setSource(CmpayPayChannelEnum.CMPAY_POS);
        request.setTarget(cmpayBarcodePaymentReq);
        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            cmpayCommonService.cgwReturnInfoCheck(genericRspDTO);
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_ORDER_PAY_FAIL);
        }
        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .map(result -> {
                    CmpayPosBarcodePaymentRspDTO barcodePaymentRspDTO = (CmpayPosBarcodePaymentRspDTO) result;
                    if (JudgeUtils.isNotNull(barcodePaymentRspDTO) && !StringUtils.equals(barcodePaymentRspDTO.getReturnCode(), CmpayConstants.RETURN_CODE) &&
                            JudgeUtils.notEquals(barcodePaymentRspDTO.getReturnCode(), CommonConstant.PAYMENT_BEING_PROCESSED)) {
                        payOrderBO.setErrMsgCd(barcodePaymentRspDTO.getReturnCode());
                        payOrderBO.setErrMsgInfo(barcodePaymentRspDTO.getMessage());
                        //判断是否SYS开头转换错误码
                        if(JudgeUtils.isNotNull(barcodePaymentRspDTO.getReturnCode())
                            &&barcodePaymentRspDTO.getReturnCode().toUpperCase().contains(CommonConstant.SYS)){
                            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_SYS_FAIL);
                        }
                        BusinessException.throwBusinessException(barcodePaymentRspDTO.getReturnCode());
                    }
                    payOrderBO.setStatus(barcodePaymentRspDTO.getReturnCode());
                    payOrderBO.setAccountDate(barcodePaymentRspDTO.getAccountDate());
                    return result;
                }).orElseThrow(() ->new BusinessException(MsgCodeEnum.CMPAY_ORDER_PAY_FAIL));
    }
}
