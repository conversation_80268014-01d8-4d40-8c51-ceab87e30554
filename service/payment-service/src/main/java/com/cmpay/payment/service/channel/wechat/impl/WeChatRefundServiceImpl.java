package com.cmpay.payment.service.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.dto.wechat.WXPayRefundRequest;
import com.cmpay.payment.dto.wechat.WXPayRefundResponse;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.channel.wechat.WeChatRefundService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatRefundServiceImpl implements WeChatRefundService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatRefundServiceImpl.class);

    @Autowired
    private WeChatProperties weChatProperties;

    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    ExtParamInfoService paramInfoService;

    /**
     * 微信H5支付申请退款
     *
     * @param refundBO
     */
    @Override
    public void weChatRefund(RefundBO refundBO) {

        refundBO.setAsync(true);

        WXPayRefundRequest wxPayRefundRequest = new WXPayRefundRequest();
        wxPayRefundRequest.setOutTradeNo(refundBO.getOutTradeNo());
        wxPayRefundRequest.setOutRefundNo(refundBO.getOutRequestNo());
        wxPayRefundRequest.setTotalFee(refundBO.getOriginalPayTradeOrder().getRealAmount().multiply(new BigDecimal(100)).intValue());
        wxPayRefundRequest.setRefundFee(refundBO.getRefundAmount().multiply(new BigDecimal(100)).intValue());
        wxPayRefundRequest.setRefundDesc(refundBO.getRefundReason());

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (org.apache.commons.lang.StringUtils.isNotBlank(host)) {
            try {
                wxPayRefundRequest.setNotifyUrl(UrlUtils.replaceDomainOrIp(weChatProperties.getRefundNofityUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                wxPayRefundRequest.setNotifyUrl(weChatProperties.getRefundNofityUrl());
            }
        } else {
            wxPayRefundRequest.setNotifyUrl(weChatProperties.getRefundNofityUrl());
        }

        wxPayRefundRequest.setAppid(refundBO.getOriginalPayTradeOrder().getWechatAppid());
        wxPayRefundRequest.setMchId(refundBO.getOriginalPayTradeOrder().getBankMerchantNo());
        wxPayRefundRequest.setKey(refundBO.getContractSecureValue());

        Request request = new Request();
        if (JudgeUtils.equals(refundBO.getOriginalPayTradeOrder().getBankMerchantNo(), weChatProperties.getMchId())) {
            request.setSource(WXPayChannel.REFUND.getSource());
            request.setRoute(WXPayChannel.REFUND.getRoute());
            request.setBusiType(WXPayChannel.REFUND.getBusType());
        } else {
            request.setSource(WXPayChannel.NEWREFUND.getSource());
            request.setRoute(WXPayChannel.NEWREFUND.getRoute());
            request.setBusiType(WXPayChannel.NEWREFUND.getBusType());
        }
        request.setRequestId(UUID.randomUUID().toString());

        request.setTarget(wxPayRefundRequest);
        logger.info("WeChatRefundServiceImpl Source : {} Route : {} BusiType : {}",request.getSource(),request.getRoute(),request.getBusiType());

        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(refundBO, (WXPayRefundResponse) result));
    }

    @Override
    public void send(RefundBO refundBO) {
        weChatRefund(refundBO);
    }

    private void handleResult(RefundBO refundBO, WXPayRefundResponse response) {
        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            if (!StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                logger.error("refund order failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                refundBO.setErrMsgCd(response.getErrCode());
                refundBO.setErrMsgInfo(response.getErrCodeDes());
                WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getErrCode());
                switch (errorCodeEnum) {
                    // 系统超时等，请不要更换商户退款单号，请使用相同参数再次调用API。
                    case SYSTEMERROR:
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_SYSTEM_ERROR);
                    case BIZERR_NEED_RETRY:
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_BIZERR_NEED_RETRY);
                    case TRADE_OVERDUE:
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_TRADE_OVERDUE);
                    case ERROR:
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_REFUND_ERROR);
                    case USER_ACCOUNT_ABNORMAL:
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_USER_ACCOUNT_ABNORMAL);
                    case INVALID_REQ_TOO_MUCH:
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_INVALID_REQ_TOO_MUCH);
                    case NOTENOUGH:
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_NOT_ENOUGH);
                    case ORDERNOTEXIST:
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_ORDER_NOT_EXIST);
                    case FREQUENCY_LIMITED:
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_FREQUENCY_LIMITED);
                    default:
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_REFUND_FAILURE);
                }
            }
        } else {
            logger.error("refund order failure, weChat return message is : {}", response.getReturnMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_REFUND_FAILURE);
        }
    }
}
