package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.protocol.ProtocolAbolishBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.constant.protocol.DcepUserProtocolStatusEnum;
import com.cmpay.payment.constant.protocol.ProtocolStatusEnum;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.DcepUserProtocolCancelReq;
import com.cmpay.payment.dto.dcep.DcepUserProtocolCancelRsp;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.DcepUserProtocolAbolishService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created on 2020/5/12
 *
 * @author: huang_yh1
 */
@Service
public class DcepUserProtocolAbolishServiceImpl extends AbstractDcepRequestServiceImpl implements DcepUserProtocolAbolishService {
    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;

    private static final String SIGN_TYPE = "cancel";

    @Override
    public void protocolAbolish(ProtocolAbolishBO protocolAbolishBO) {
        DcepUserProtocolCancelReq userProtocolCancelReq = new DcepUserProtocolCancelReq();
        userProtocolCancelReq.setChannelNo(dcepPaymentProperties.getNewChannelNo());
        userProtocolCancelReq.setSignType(DcepConstants.SIGN_TYPE);
        userProtocolCancelReq.setVersion(DcepConstants.VERSION);
        userProtocolCancelReq.setOrgNo(dcepPaymentProperties.getNewOrgNo());
        userProtocolCancelReq.setSmxKey(dcepPaymentProperties.getNewAesKey());
        userProtocolCancelReq.setSubMerchantNo(protocolAbolishBO.getSubMerchantId());
        userProtocolCancelReq.setAgreementSeq(protocolAbolishBO.getAgreementId());
        buildDcepRequest(userProtocolCancelReq, protocolAbolishBO, DcepPaymentChannelEnum.USER_PROTOCOL_SIGN_CANCEL.getName());
    }

    @Override
    public void send(ProtocolAbolishBO protocolAbolishBO) {
        protocolAbolish(protocolAbolishBO);
    }

    @Override
    protected BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        DcepUserProtocolCancelRsp userProtocolCancelRsp = (DcepUserProtocolCancelRsp) abstractDcepRsp;
        ProtocolAbolishBO protocolAbolishBO = (ProtocolAbolishBO) baseDcepHandlerBO;
        protocolAbolishBO.setReturnCode(userProtocolCancelRsp.getCode());
        protocolAbolishBO.setReturnMsg(userProtocolCancelRsp.getMsg());
        if (JudgeUtils.equals(DcepConstants.SUCCESS_CODE, userProtocolCancelRsp.getCode())) {
            DcepUserProtocolCancelRsp.Data data = userProtocolCancelRsp.getData();
            DcepUserProtocolStatusEnum statusEnum = DcepUserProtocolStatusEnum.getMsgCodeEnum(data.getStatus(), SIGN_TYPE);
            protocolAbolishBO.setSignStatus(statusEnum == null ? ProtocolStatusEnum.CONTRACT_TERMINATED_WAIT.getDesc() : statusEnum.name());
        } else {
            protocolAbolishBO.setSignStatus(ProtocolStatusEnum.CONTRACT_TERMINATED_FAIL.getDesc());
        }
        return baseDcepHandlerBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }
}
