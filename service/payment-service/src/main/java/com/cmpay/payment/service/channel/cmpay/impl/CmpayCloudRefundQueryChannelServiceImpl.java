package com.cmpay.payment.service.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.CpsCloudRefundBO;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dto.cmpay.CmpayPosRefundQueryReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayPosRefundQueryRspDTO;
import com.cmpay.payment.entity.CmpayRefundJrnDO;
import com.cmpay.payment.service.TradeCommonService;
import com.cmpay.payment.service.channel.cmpay.CmpayCloudRefundQueryChannelService;
import com.cmpay.payment.service.channel.cmpay.CmpayCommonService;
import com.cmpay.payment.service.ext.ExtCmpayRefundJrnService;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created on 2018/12/3
 *
 * @author: chen_lan
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayCloudRefundQueryChannelServiceImpl implements CmpayCloudRefundQueryChannelService {

    private static final Logger logger = LoggerFactory.getLogger(CmpayCloudRefundQueryChannelServiceImpl.class);

    @Autowired
    ExtCmpayRefundJrnService extCmpayRefundJrnService;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Autowired
    private CmpayCommonService cmpayCommonService;
    @Autowired
    private TradeCommonService tradeCommonService;

    @Override
    public void send(RefundOrderQueryBO refundOrderQueryBO) {
        cmpayCloudRefundQuery(refundOrderQueryBO);
    }

    @Override
    public void cmpayCloudRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {
        logger.info("=====================Cmpay Cloud Refund Order Query Start!!=====================");
        CmpayRefundJrnDO querycmpayJournalDO = new CmpayRefundJrnDO();
        querycmpayJournalDO.setTradeOrderNo(refundOrderQueryBO.getRefundOrder().getTradeOrderNo());
        //  查询和包退款流水
        CmpayRefundJrnDO cmpayRefundJrnDO = extCmpayRefundJrnService.load(querycmpayJournalDO);
        if (JudgeUtils.isNull(cmpayRefundJrnDO)) {
            logger.info("Cmpay Cloud Journal Record Not Exists,tradeOrderNo is {},msgcd is {}", MsgCodeEnum.CMPAY_JOURNAL_INFO_GET_ERROR);
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_JOURNAL_INFO_GET_ERROR);
        }
        logger.info("Cmpay Cloud Journal Record Query Success,tradeOrderNo is {}", cmpayRefundJrnDO.getTradeOrderNo());
        // 和包退款流水加锁
        extCmpayRefundJrnService.lock(cmpayRefundJrnDO);
        logger.info("Cmpay Cloud Journal Record For Update Success,tradeOrderNo is {}", cmpayRefundJrnDO.getTradeOrderNo());
        try {
            // 调用和包云支付退款接口
            CpsCloudRefundBO cmpayCloudRefundBO = cmpayPosRefundQuery(refundOrderQueryBO);
            logger.info("Cmpay Cloud Order Query Success,tradeOrderNo is {}", cmpayRefundJrnDO.getTradeOrderNo());
            cmpayRefundJrnDO.setRemark(cmpayCloudRefundBO.getOrderStatus());
            logger.info("Cmpay Cloud Order Result,tradeOrderNo is {},orderStatus is {}",
                    cmpayRefundJrnDO.getTradeOrderNo(), cmpayCloudRefundBO.getOrderStatus());
            cmpayRefundJrnDO.setReturnMsg(cmpayCloudRefundBO.getMessageCode());
            logger.info("Cmpay Cloud Order Result,tradeOrderNo is {},messageCode is {}",
                    cmpayRefundJrnDO.getTradeOrderNo(), cmpayCloudRefundBO.getMessageCode());
            cmpayRefundJrnDO.setReturnMsgInfo(cmpayCloudRefundBO.getMessageInfo());

            // 退款状态
            if (CloudOrderStatusEnum.RF.name().equals(cmpayCloudRefundBO.getOrderStatus())
                    || CloudOrderStatusEnum.RP.name().equals(cmpayCloudRefundBO.getOrderStatus())) {
                refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
                cmpayRefundJrnDO.setTradeStatus(OrderStatusEnum.REFUND_SUCCESS.name());
                refundOrderQueryBO.getRefundOrder().setAccountDate(cmpayCloudRefundBO.getAccountDate());
            } else if (CloudOrderStatusEnum.RQ.name().equals(cmpayCloudRefundBO.getOrderStatus())
                    || CloudOrderStatusEnum.RA.name().equals(cmpayCloudRefundBO.getOrderStatus())) {
                refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
                cmpayRefundJrnDO.setTradeStatus(OrderStatusEnum.REFUND_WAIT.name());
            } else if (CloudOrderStatusEnum.RE.name().equals(cmpayCloudRefundBO.getOrderStatus())) {
                refundOrderQueryBO.setErrMsgCd(cmpayCloudRefundBO.getErrMsgCd());
                refundOrderQueryBO.setErrMsgInfo(cmpayCloudRefundBO.getErrMsgCd());
                refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
                cmpayRefundJrnDO.setTradeStatus(OrderStatusEnum.REFUND_FAIL.name());
            } else {
                // 若返回错误码为CPS13002，则判断是否超过30分钟，若超过则当作失败处理
                if (MsgCodeEnum.CMPAY_REFUND_ORDER_NOT_EXISTS.getMsgCd().equals(cmpayCloudRefundBO.getMessageCode())) {
                    tradeCommonService.handleCmpayRefundOrderNotExist(refundOrderQueryBO,cmpayRefundJrnDO,MsgCodeEnum.CMPAY_REFUND_ORDER_NOT_EXISTS);
                }
            }
        } catch (Exception e) {
            logger.info("Cmpay Cloud Order Query Exception,tradeOrderNo is {},exception is {}",
                    cmpayRefundJrnDO.getTradeOrderNo(), e);
            //系统异常，比如网络异常 记录异常日志
            cmpayCommonService.cmapyThrowClassHandle(cmpayRefundJrnDO,e);
            throw e;
        } finally {
            cmpayRefundJrnDO.setReturnDate(DateTimeUtils.getCurrentDateStr());
            cmpayRefundJrnDO.setReturnTime(DateTimeUtils.getCurrentTimeStr());
            //将异常信息更新到 cmpay退款流水记录
            extCmpayRefundJrnService.updateByNewTranscation(cmpayRefundJrnDO);
            logger.info("Cmpay Cloud Journal Record Update Success,tradeOrderNo is {}",
                    cmpayRefundJrnDO.getTradeOrderNo());
            if (!MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd().equals(cmpayRefundJrnDO.getReturnMsg())) {
                // 报错退出
                BusinessException.throwBusinessException(cmpayRefundJrnDO.getReturnMsg());
            }
        }
        logger.info("=====================Cmpay Cloud Refund Order Query End!!=====================");
    }

    private CpsCloudRefundBO cmpayPosRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {
        CpsCloudRefundBO cloudRefundBO = new CpsCloudRefundBO();
        CmpayPosRefundQueryReqDTO posRefundQueryReqDTO = new CmpayPosRefundQueryReqDTO();
        posRefundQueryReqDTO.setMerchantId(refundOrderQueryBO.getRefundOrder().getBankMerchantNo());
        posRefundQueryReqDTO.setRequestId(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.CMPAY_JRN_NO, 18));
        posRefundQueryReqDTO.setOrigRequestId(refundOrderQueryBO.getRefundOrder().getOutTradeNo());
        posRefundQueryReqDTO.setSignType(refundOrderQueryBO.getSignMethod());
        posRefundQueryReqDTO.setType(CmpayConstants.CPOS_REFUND_QUERY);
        posRefundQueryReqDTO.setVersion(CmpayConstants.CPOS_VERSION);
        posRefundQueryReqDTO.setHmac(refundOrderQueryBO.getContractSecureValue());
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(CmpayPayChannelEnum.CMPAY_POS);
        request.setBusiType(CmpayPayChannelEnum.CMPAYPOS_REFUND_QUERY.getName());
        request.setSource(CmpayPayChannelEnum.CMPAY_POS);
        request.setTarget(posRefundQueryReqDTO);
        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            cmpayCommonService.cgwReturnInfoCheck(genericRspDTO);
            BusinessException.throwBusinessException(genericRspDTO);
        }
        Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleCposRefundQuery(cloudRefundBO, (CmpayPosRefundQueryRspDTO) result));
        return cloudRefundBO;
    }

    private void handleCposRefundQuery(CpsCloudRefundBO cloudRefundBO, CmpayPosRefundQueryRspDTO refundQueryRspDTO) {
        if (JudgeUtils.isNull(refundQueryRspDTO)) {
            cloudRefundBO.setMessageCode(MsgCodeEnum.CMPAY_REFUND_QUERY_REQUEST_ERROR.getMsgCd());
            cloudRefundBO.setMessageInfo(MsgCodeEnum.CMPAY_REFUND_QUERY_REQUEST_ERROR.getMsgInfo());
            return;
        }
        // 更新退款流水
        if (CloudOrderStatusEnum.RF.name().equals(refundQueryRspDTO.getReturnState())
                || CloudOrderStatusEnum.RP.name().equals(refundQueryRspDTO.getReturnState())
                || CloudOrderStatusEnum.RQ.name().equals(refundQueryRspDTO.getReturnState())
                || CloudOrderStatusEnum.RA.name().equals(refundQueryRspDTO.getReturnState())
                || CloudOrderStatusEnum.RE.name().equals(refundQueryRspDTO.getReturnState())) {
            cloudRefundBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            cloudRefundBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
            cloudRefundBO.setOrderStatus(refundQueryRspDTO.getReturnState());
            cloudRefundBO.setAccountDate(refundQueryRspDTO.getRefundAccountDate());
        } else {
            // 若返回错误码为CPS13002，则判断是否超过30分钟，若超过则当作失败处理
            if (MsgCodeEnum.CMPAY_REFUND_ORDER_NOT_EXISTS.getMsgCd().equals(refundQueryRspDTO.getReturnCode())) {
                cloudRefundBO.setMessageCode(MsgCodeEnum.CMPAY_REFUND_ORDER_NOT_EXISTS.getMsgCd());
                cloudRefundBO.setMessageInfo(MsgCodeEnum.CMPAY_REFUND_ORDER_NOT_EXISTS.getMsgInfo());
            } else if (MsgCodeEnum.OFFLINE_REFUND_ORDER_NOT_EXISTS.getMsgCd().equals(refundQueryRspDTO.getReturnCode())) {
                cloudRefundBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
                cloudRefundBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
                cloudRefundBO.setOrderStatus(CloudOrderStatusEnum.RE.name());
                cloudRefundBO.setAccountDate(refundQueryRspDTO.getRefundAccountDate());
            } else {
                cloudRefundBO.setMessageCode(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgCd());
                cloudRefundBO.setMessageInfo(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgInfo());
            }
        }
    }
}
