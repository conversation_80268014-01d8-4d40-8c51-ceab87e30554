package com.cmpay.payment.service.channel.cmpay.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.bo.UnionpayBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dto.cmpay.CmpayOnlinePaymentReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayOnlinePaymentRspDTO;
import com.cmpay.payment.service.channel.cmpay.CmpayCommonService;
import com.cmpay.payment.service.channel.cmpay.CmpayOnlinePaymentChannelService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created on 2018/12/4
 *
 * @author: li_zhen
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayOnlinePaymentChannelServiceImpl implements CmpayOnlinePaymentChannelService {

    private static final Logger logger = LoggerFactory.getLogger(CmpayOnlinePaymentChannelServiceImpl.class);
    @Value("${cmpay.pageNotifyUrl:}")
    private String pageNotifyUrl;
    @Value("${cmpay.backendNotifyUrl:}")
    private String backendNotifyUrl;
    @Value("${cmpay.subAppId:}")
    private String subAppId;
    @Value("${cmpay.subMchId:}")
    private String subMchId;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private CmpayCommonService cmpayCommonService;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Override
    public void send(PayOrderBO payOrderBO) {
        cmpayOnlinePayment(payOrderBO);
    }

    @Override
    public void cmpayOnlinePayment(PayOrderBO payOrderBO) {
        if (StringUtils.equals(payOrderBO.getScene(), CmpayConstants.APPLEPAY)) {
            payOrderBO.setScene(CmpayConstants.ALE);
        }
        try {
            CmpayOnlinePaymentReqDTO onlinePaymentReq = new CmpayOnlinePaymentReqDTO();
            onlinePaymentReq.setMerchantRequestNo(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.CMPAY_JRN_NO, 18));
            onlinePaymentReq.setMerchantOrderNo(payOrderBO.getTradeOrderNo());
            onlinePaymentReq.setMerchantOrderDate(payOrderBO.getOrderDate());
            onlinePaymentReq.setOrderAmount(payOrderBO.getRealAmount());
            onlinePaymentReq.setCurrency(CommonConstant.CMPAY_CURRENCY);
            onlinePaymentReq.setValidNum(payOrderBO.getValidityNumber());
            onlinePaymentReq.setValidUnit(payOrderBO.getValidityUnit());
            onlinePaymentReq.setMerchantId(payOrderBO.getPaymentId());
            onlinePaymentReq.setGoodsNo(payOrderBO.getProductCode());
            onlinePaymentReq.setGoodsName(payOrderBO.getProductName());
            onlinePaymentReq.setGoodsDescription(payOrderBO.getProductDesc());
            onlinePaymentReq.setGoodShowUrl(payOrderBO.getProductUrl());
            onlinePaymentReq.setUniPayReserved(payOrderBO.getExtra());
            onlinePaymentReq.setReserveField1(payOrderBO.getExtra());
            if (StringUtils.isBlank(payOrderBO.getExtra())) {
                onlinePaymentReq.setUniPayReserved("cmpay");
                onlinePaymentReq.setReserveField1("cmpay");
            }
            if (StringUtils.equals(payOrderBO.getPayChannelFlag(),CommonConstant.CMPAY_PAYCHANNELFLAG)){
                onlinePaymentReq.setSceneCode(payOrderBO.getPayChannelFlag());
            }
            onlinePaymentReq.setUserIp(payOrderBO.getClientIps());

            String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(host)) {
                try {
                    onlinePaymentReq.setPageReturnUrl(UrlUtils.replaceDomainOrIp(pageNotifyUrl,host));
                    onlinePaymentReq.setBankReturnUrl(UrlUtils.replaceDomainOrIp(backendNotifyUrl,host));
                } catch (Exception e) {
                    logger.info("通知地址域名获取异常");
                    onlinePaymentReq.setPageReturnUrl(pageNotifyUrl);
                    onlinePaymentReq.setBankReturnUrl(backendNotifyUrl);
                }
            } else {
                onlinePaymentReq.setPageReturnUrl(pageNotifyUrl);
                onlinePaymentReq.setBankReturnUrl(backendNotifyUrl);
            }

            String sceneTemp = "";
            if (JudgeUtils.equals(payOrderBO.getScene().toUpperCase(), PaymentSceneEnum.APP.name())
                    && JudgeUtils.equals(payOrderBO.getPayWay().toUpperCase(), PaymentWayEnum.CMPAY.name())) {
                sceneTemp = PaymentSceneEnum.APP.name();
                payOrderBO.setScene(PaymentSceneEnum.WAP.name().toLowerCase());
            }
            onlinePaymentReq.setBankAbbreviation(payOrderBO.getPayWay().toUpperCase() + "_" + payOrderBO.getScene().toUpperCase());
            if (JudgeUtils.equals(payOrderBO.getScene(), PaymentSceneEnum.NETBANK.name().toLowerCase())
                    || JudgeUtils.equals(payOrderBO.getScene(), PaymentSceneEnum.NETBANKB2B.name().toLowerCase())) {
                onlinePaymentReq.setBankAbbreviation(payOrderBO.getBankAbbreviation());
                onlinePaymentReq.setCardAccountType(payOrderBO.getCrdAcTyp());
                if (JudgeUtils.equals(payOrderBO.getScene(), PaymentSceneEnum.NETBANKB2B.name().toLowerCase())) {
                    //对银行简称进行处理
                    onlinePaymentReq.setBankAbbreviation(payOrderBO.getBankAbbreviation().split("_")[0]);
                    onlinePaymentReq.setBankType("1");
                    //企业网银支付时，卡种设置为借记卡
                    onlinePaymentReq.setCardAccountType("0");
                }
            }
            onlinePaymentReq.setInterfaceType(CommonConstant.CMPAY_VERSION_TYPE);
            onlinePaymentReq.setStoreBrand(payOrderBO.getHallCode());
            onlinePaymentReq.setCityBranchName(payOrderBO.getHallWindowCode());
            onlinePaymentReq.setMerchantShowAbbreviation(CmpayConstants.MERCHANT_NAME);
            onlinePaymentReq.setInterfaceVersion(CommonConstant.CMPAY_ONLINE_PAYMENT_VERSION);
            onlinePaymentReq.setVersion(CommonConstant.CMPAY_ONLINE_PAYMENT_VERSION);
            onlinePaymentReq.setType(CommonConstant.CMPAY_ONLINE_PAYMENT_TYPE);
            if (JudgeUtils.equals(payOrderBO.getPayWay(), PaymentWayEnum.UNIONPAY.name().toLowerCase())) {
                onlinePaymentReq.setInterfaceType(CommonConstant.CMPAY_UNIONPAY_VERSION_TYPE);
            }
            if (JudgeUtils.equals(payOrderBO.getPayWay(), PaymentWayEnum.UNIONPAY.name().toLowerCase())) {
                onlinePaymentReq.setDiscountCode(payOrderBO.getDiscountCode());
                if (JudgeUtils.isNotBlank(payOrderBO.getBankIdNo())) {
                    onlinePaymentReq.setBankIdNo(payOrderBO.getBankIdNo());
                }
                if (JudgeUtils.isNotBlank(payOrderBO.getBankIdType())) {
                    onlinePaymentReq.setBankIdType(payOrderBO.getBankIdType());
                }
            }
            onlinePaymentReq.setSignType(payOrderBO.getSignMethod());
            onlinePaymentReq.setHmac(payOrderBO.getContractSecureValue());
            onlinePaymentReq.setWeChatAuthCode(payOrderBO.getWechatOpenId());
            onlinePaymentReq.setWeChatSubAppId(subAppId);
            onlinePaymentReq.setWeChatSubMerchantId(subMchId);
            onlinePaymentReq.setUniPayOrderShowFlag("Y");
            Request request = new Request();
            request.setRequestId(LemonUtils.getRequestId());
            request.setRoute(CmpayPayChannelEnum.CMPAY_ONLINE);
            request.setBusiType(CmpayPayChannelEnum.CMPAY_ONLINE_PAYMENT.getName());
            request.setSource(CmpayPayChannelEnum.CMPAY_ONLINE);
            request.setTarget(onlinePaymentReq);
            GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            if (JudgeUtils.isNotSuccess(genericRspDTO)) {
                cmpayCommonService.cgwReturnInfoCheck(genericRspDTO);
                BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_ORDER_PAY_FAIL);
            }
            String finalSceneTemp = sceneTemp;
            Optional.of(genericRspDTO)
                    .map(GenericRspDTO::getBody)
                    .map(Response::getResult)
                    .map(result -> handleOnlinePayment(payOrderBO, (CmpayOnlinePaymentRspDTO) result, finalSceneTemp))
                    .orElseThrow(() ->new BusinessException(MsgCodeEnum.CMPAY_ORDER_PAY_FAIL));
        } catch (Exception e) {
            throw e;
        }
    }

    private PayOrderBO handleOnlinePayment(PayOrderBO payOrderBO, CmpayOnlinePaymentRspDTO onlinePaymentRsp, String sceneTemp) {

        logger.info("paymentRsp:{}", JSONObject.toJSONString(onlinePaymentRsp));
        if (JudgeUtils.isNotSuccess(onlinePaymentRsp.getMsgCd())) {
            payOrderBO.setErrMsgCd(onlinePaymentRsp.getMsgCd());
            payOrderBO.setErrMsgInfo(onlinePaymentRsp.getMsgInfo());
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_BUSINESS_FAIL);
        }
        UnionpayBO unionpayBO = new UnionpayBO();
        unionpayBO.setReqReserved(onlinePaymentRsp.getUniPayReserved());
        unionpayBO.setParametersReserved(onlinePaymentRsp.getUniDiscountInfo());
        unionpayBO.setTxnType(onlinePaymentRsp.getUniTradeType());
        unionpayBO.setPayTimeout(onlinePaymentRsp.getUniPayTimeout());
        unionpayBO.setFrontUrl(onlinePaymentRsp.getUniFrontNotifyUrl());
        unionpayBO.setCurrencyCode(onlinePaymentRsp.getUniTradeCurrency());
        unionpayBO.setChannelType(onlinePaymentRsp.getUniChannelType());
        unionpayBO.setMerId(onlinePaymentRsp.getUniMerchantId());
        unionpayBO.setTxnSubType(onlinePaymentRsp.getUniSubTradeType());
        unionpayBO.setTxnAmt(onlinePaymentRsp.getUniTradeAmount());
        unionpayBO.setVersion(onlinePaymentRsp.getUniVersion());
        unionpayBO.setSignMethod(onlinePaymentRsp.getUniSignType());
        unionpayBO.setBackUrl(onlinePaymentRsp.getUniBackgroundNotifyUrl());
        unionpayBO.setCertId(onlinePaymentRsp.getUniCertificateId());
        unionpayBO.setEncoding(onlinePaymentRsp.getUniEncodingType());
        unionpayBO.setBizType(onlinePaymentRsp.getUniProductType());
        unionpayBO.setOrderId(onlinePaymentRsp.getUniMerchantOrderNo());
        unionpayBO.setSignature(onlinePaymentRsp.getUniSign());
        unionpayBO.setTxnTime(onlinePaymentRsp.getUniOrderSendTime());
        unionpayBO.setAccessType(onlinePaymentRsp.getUniAccessType());
        payOrderBO.setUnionpayBO(unionpayBO);
        String payUrl = "";
        payOrderBO.setPayUrl(onlinePaymentRsp.getPayUrl());
        if (!JudgeUtils.equalsAny(payOrderBO.getScene(), PaymentSceneEnum.NETBANK.name().toLowerCase(),
                PaymentSceneEnum.NETBANKB2B.name().toLowerCase()) && StringUtils.isNotBlank(onlinePaymentRsp.getPayUrl())) {
            payUrl = onlinePaymentRsp.getPayUrl().replaceAll(CmpayConstants.URL_SPLIT, "");
            String s = payUrl.replaceAll(CmpayConstants.GET_METHOD_SPLIT, "?");
            payOrderBO.setPayUrl(s.replaceAll(CmpayConstants.SPECIAL_CHAR1, "="));
        }
        if (JudgeUtils.equals(sceneTemp, PaymentSceneEnum.APP.name())) {
            payOrderBO.setPayUrl(payOrderBO.getPayUrl().split("=")[1]);
        }
        payOrderBO.setWechatAppId(onlinePaymentRsp.getAppletId());
        payOrderBO.setWechatTmStamp(onlinePaymentRsp.getTimeStamp());
        payOrderBO.setNonceStr(onlinePaymentRsp.getRandom());
        payOrderBO.setWechatPackage(onlinePaymentRsp.getOrderDetailExpand());
        payOrderBO.setWechatSignType(onlinePaymentRsp.getSignType());
        payOrderBO.setWechatSign(onlinePaymentRsp.getSign());
        payOrderBO.setPartnerId(onlinePaymentRsp.getPracticeMechanismId());
        payOrderBO.setPrepayId(onlinePaymentRsp.getPrePaySessionId());
        payOrderBO.setUnionpayTxnType(onlinePaymentRsp.getUniTradeType());
        payOrderBO.setUnionpayTxnSubType(onlinePaymentRsp.getUniSubTradeType());
        payOrderBO.setUnionpayFrontUrl(onlinePaymentRsp.getUniFrontNotifyUrl());
        payOrderBO.setUnionpayChannelType(onlinePaymentRsp.getUniChannelType());
        payOrderBO.setUnionpayCurrencyCode(onlinePaymentRsp.getUniTradeCurrency());
        payOrderBO.setUnionpayMerchantId(onlinePaymentRsp.getUniMerchantId());
        payOrderBO.setUnionpayTxnAmount(onlinePaymentRsp.getUniTradeAmount());
        payOrderBO.setUnionpayVersion(onlinePaymentRsp.getUniVersion());
        payOrderBO.setUnionpaySignMethod(onlinePaymentRsp.getUniSignType());
        payOrderBO.setUnionpaySign(onlinePaymentRsp.getUniSign());
        payOrderBO.setUnionpayBackUrl(onlinePaymentRsp.getUniBackgroundNotifyUrl());
        payOrderBO.setUnionpayCertId(onlinePaymentRsp.getUniCertificateId());
        payOrderBO.setUnionpayEncoding(onlinePaymentRsp.getUniEncodingType());
        payOrderBO.setUnionpayBizType(onlinePaymentRsp.getUniProductType());
        payOrderBO.setUnionpayOrderId(onlinePaymentRsp.getUniMerchantOrderNo());
        payOrderBO.setUnionpayTxnTime(onlinePaymentRsp.getUniOrderSendTime());
        payOrderBO.setUnionpayAccessType(onlinePaymentRsp.getUniAccessType());
        payOrderBO.setUnionpayReqReserved(payOrderBO.getExtra());
        payOrderBO.setUnionpayPayTimeout(onlinePaymentRsp.getUniPayTimeout());
        payOrderBO.setUnionpayTn(onlinePaymentRsp.getUniPayResponseInfo());
        payOrderBO.setUnionpayAPayMerchantId(onlinePaymentRsp.getUniPayMerchantId());
        return payOrderBO;
    }
}
