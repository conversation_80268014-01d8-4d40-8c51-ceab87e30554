package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.lock.DistributedLocker;
import com.cmpay.lemon.framework.lock.UnableToAcquireLockException;
import com.cmpay.payment.bo.SubWalletAddNewCardBO;
import com.cmpay.payment.bo.SubWalletOrderQueryBO;
import com.cmpay.payment.bo.SubWalletPayChannelQueryBO;
import com.cmpay.payment.bo.SubWalletPaymentBO;
import com.cmpay.payment.bo.utils.TripleDes;
import com.cmpay.payment.constant.AppEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.PaymentStatusEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.constant.protocol.SubWalletHandlerEnum;
import com.cmpay.payment.entity.OrderPaymentAttachDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.properties.ProtocolProperties;
import com.cmpay.payment.service.SubWalletCashierService;
import com.cmpay.payment.service.ext.ExtPayOrderAttachService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.util.LemonAmount;
import io.netty.channel.ConnectTimeoutException;
import io.netty.handler.timeout.ReadTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/5/5
 */
@Service
public class SubWalletCashierServiceImpl implements SubWalletCashierService {


    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private ExtPayOrderAttachService payOrderAttachService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ProtocolProperties protocolProperties;
    @Autowired
    private DistributedLocker distributedLocker;

    @Override
    public void queryOrderInfo(SubWalletOrderQueryBO orderQueryBO) {
        TradeOrderDO tradeOrderDO = queryTradeOrder(orderQueryBO.getOutTradeNo(), orderQueryBO.getTradeDate());
        OrderPaymentAttachDO orderPaymentAttach = getOrderPaymentAttach(orderQueryBO.getOutTradeNo(), orderQueryBO.getTradeDate(), orderQueryBO.getMobileNo());
        checkOrderExist(tradeOrderDO);
        checkOrderAttach(orderPaymentAttach);
        orderQueryBO.setTotalAmount(tradeOrderDO.getRealAmount());
        orderQueryBO.setProductName(tradeOrderDO.getGoodsName());
        orderQueryBO.setProductDesc(tradeOrderDO.getGoodsDesc());
        orderQueryBO.setTradeStatus(tradeOrderDO.getStatus());
        orderQueryBO.setTradeStatusDesc(OrderStatusEnum.valueOf(tradeOrderDO.getStatus()).getDesc());
        orderQueryBO.setOrderCompleteTime(tradeOrderDO.getOrderCompleteTime());
        orderQueryBO.setDcepPayChannel(orderPaymentAttach.getDcepPayChannel());
        orderQueryBO.setErrMsgInfo(tradeOrderDO.getErrMsgInfo());
        orderQueryBO.setPageNotifyUrl(tradeOrderDO.getReturnUrl());
    }


    @Override
    public void payChannelQuery(SubWalletPayChannelQueryBO payChannelQueryBO) {
        try {
            OrderPaymentAttachDO orderPaymentAttach = getOrderPaymentAttach(payChannelQueryBO.getOutTradeNo(), payChannelQueryBO.getTradeDate(), payChannelQueryBO.getMobileNo());
            if (JudgeUtils.isNull(orderPaymentAttach)) {
                BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
            }
            payChannelQueryBO.setMobileNo(TripleDes.decrypt(protocolProperties.getTripleDes(), payChannelQueryBO.getMobileNo()));
            payChannelQueryBO.setSubMerchantId(orderPaymentAttach.getSubMerchantId());
            payChannelQueryBO.setHandlerName(SubWalletHandlerEnum.SUB_WALLET_PAY_CHANNEL_QUERY.name());
            TradeOrderDO tradeOrderDO = queryTradeOrder(payChannelQueryBO.getOutTradeNo(), payChannelQueryBO.getTradeDate());
            payChannelQueryBO.setChannelNo(tradeOrderDO.getBankMerchantNo());
            applicationContext.publishEvent(payChannelQueryBO);
        } catch (Exception e) {
            handlerException(e, "payChannelQuery:" + payChannelQueryBO.getErrorMsg());
        }
    }

    @Override
    public void subWalletPayment(SubWalletPaymentBO walletPaymentBO) {
        try {
            distributedLocker.lock("subWalletPayment" + walletPaymentBO.getTradeDate() + walletPaymentBO.getOutTradeNo(), 60, 1, () -> {
                subWalletSurePayment(walletPaymentBO);
                return null;
            });
        } catch (UnableToAcquireLockException e) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_PAID);
        }
    }

    /**
     * 数币子钱包确认支付
     * @param walletPaymentBO
     */
    public void subWalletSurePayment(SubWalletPaymentBO walletPaymentBO) {
        TradeOrderDO tradeOrderDO = queryTradeOrder(walletPaymentBO.getOutTradeNo(), walletPaymentBO.getTradeDate());
        OrderPaymentAttachDO orderPaymentAttach = getOrderPaymentAttach(walletPaymentBO.getOutTradeNo(), walletPaymentBO.getTradeDate(), walletPaymentBO.getMobileNo());
        // 校验是否满足支付条件
        checkPaymentReq(tradeOrderDO, orderPaymentAttach);
        // 确保支付描述不为空
        if (JudgeUtils.isBlank(walletPaymentBO.getDcepPayChannel())) {
            walletPaymentBO.setDcepPayChannel(walletPaymentBO.getChannelBankCode());
        }
        try {
            buildPaymentRequest(walletPaymentBO, tradeOrderDO, orderPaymentAttach);
            walletPaymentBO.setSourceApp(AppEnum.integrationpaymnet.name());
            applicationContext.publishEvent(walletPaymentBO);
            // 请求成功记录支付描述
            orderPaymentAttach.setDcepPayChannel(walletPaymentBO.getDcepPayChannel());
        } catch (Exception e) {
            handlerException(e, "subWalletPayment:" + walletPaymentBO.getErrMsgInfo());
        } finally {
            updatePaymentAttachInfo(tradeOrderDO, orderPaymentAttach, walletPaymentBO);
        }
    }

    @Override
    public void subWalletAddNewCard(SubWalletAddNewCardBO walletAddNewCardBO) {
        OrderPaymentAttachDO orderPaymentAttach = getOrderPaymentAttach(walletAddNewCardBO.getOutTradeNo(), walletAddNewCardBO.getTradeDate(), walletAddNewCardBO.getMobileNo());
        if(JudgeUtils.isNull(orderPaymentAttach)){
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        walletAddNewCardBO.setMobileNo(TripleDes.decrypt(protocolProperties.getTripleDes(), walletAddNewCardBO.getMobileNo()));
        walletAddNewCardBO.setSubMerchantId(orderPaymentAttach.getSubMerchantId());
        walletAddNewCardBO.setHandlerName(SubWalletHandlerEnum.SUB_WALLET_GET_SCHEME.name());
        TradeOrderDO tradeOrderDO = queryTradeOrder(walletAddNewCardBO.getOutTradeNo(), walletAddNewCardBO.getTradeDate());
        walletAddNewCardBO.setChannelNo(tradeOrderDO.getBankMerchantNo());
        try {
            applicationContext.publishEvent(walletAddNewCardBO);
        } catch (Exception e) {
            handlerException(e, "subWalletAddNewCard:" + walletAddNewCardBO.getErrorMsg());
        }
    }


    /**
     * 校验是否满足支付条件
     * @param tradeOrderDO
     * @param orderPaymentAttach
     */
    private boolean checkPaymentReq(TradeOrderDO tradeOrderDO, OrderPaymentAttachDO orderPaymentAttach) {
        checkOrderExist(tradeOrderDO);
        checkOrderStatus(tradeOrderDO);
        checkOrderOverTime(tradeOrderDO);
        checkOrderAttach(orderPaymentAttach);
        checkOrderSubmitted(tradeOrderDO,orderPaymentAttach);
        return true;
    }

    /**
     * 校验是否已提交支付
     * @param tradeOrderDO
     * @param orderPaymentAttach
     * @return
     */
    private boolean checkOrderSubmitted(TradeOrderDO tradeOrderDO, OrderPaymentAttachDO orderPaymentAttach) {
        if (JudgeUtils.equals(OrderStatusEnum.WAIT_PAY.name(), tradeOrderDO.getStatus())
                && JudgeUtils.isNotBlank(orderPaymentAttach.getDcepPayChannel())) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_PAID);
        }
        return true;
    }

    private void handlerException(Exception e, String errMsg) {
        if (e instanceof BusinessException) {
            if (StringUtils.equals(((BusinessException) e).getMsgCd(), MsgCodeEnum.DCEP_REUQEST_FAIL.getMsgCd())) {
                BusinessException.throwBusinessException(MsgCodeEnum.DCEP_REUQEST_FAIL, new String[]{errMsg +":" + e.getMessage()});
            }
            BusinessException.throwBusinessException((BusinessException) e);
        } else if (e instanceof ConnectTimeoutException || e instanceof ReadTimeoutException) {
            BusinessException.throwBusinessException(MsgCodeEnum.REQUEST_TIME_OUT);
        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.DCEP_PAY_QUERY_FAIL);
        }
    }

    private TradeOrderDO queryTradeOrder(String outTradeNo, String tradeDate) {
        TradeOrderDO tradeOrderDO = new TradeOrderDO();
        tradeOrderDO.setOutTradeNo(outTradeNo);
        tradeOrderDO.setRequestDate(tradeDate);
        return payOrderService.getOrderByOutTradeNoTradeDate(tradeOrderDO);
    }

    private OrderPaymentAttachDO getOrderPaymentAttach(String outTradeNo, String tradeDate, String mobileNo) {
        OrderPaymentAttachDO attachDO = new OrderPaymentAttachDO();
        attachDO.setOutTradeNo(outTradeNo);
        attachDO.setOrderDate(tradeDate);
        attachDO.setMobileNo(mobileNo);
        return payOrderAttachService.load(attachDO);
    }

    private void checkOrderExist(TradeOrderDO tradeOrderDO) {
        if (JudgeUtils.isNull(tradeOrderDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
    }

    private void checkOrderOverTime(TradeOrderDO tradeOrderDO) {
        if (DateTimeUtils.getCurrentLocalDateTime().isAfter(DateTimeUtils.parseLocalDateTime(tradeOrderDO.getExpireTime()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_OVERDUE);
        }
    }

    private void checkOrderStatus(TradeOrderDO tradeOrderDO) {
        if (!StringUtils.equals(tradeOrderDO.getStatus(), PaymentStatusEnum.WAIT_PAY.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_STATUS_CANNOT_PAYMENT);
        }
    }

    private void checkOrderAttach(OrderPaymentAttachDO paymentAttachDO) {
        if (JudgeUtils.isNull(paymentAttachDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
    }

    private void buildPaymentRequest(SubWalletPaymentBO walletPaymentBO, TradeOrderDO tradeOrderDO, OrderPaymentAttachDO orderPaymentAttach) {
        walletPaymentBO.setChannelNo(tradeOrderDO.getBankMerchantNo());
        walletPaymentBO.setSubMerchantId(orderPaymentAttach.getSubMerchantId());
        walletPaymentBO.setProductDesc(tradeOrderDO.getGoodsDesc());
        walletPaymentBO.setPaymentScene(tradeOrderDO.getPayWayCode());
        walletPaymentBO.setBusinessType(orderPaymentAttach.getUpBusinessType());
        walletPaymentBO.setBusinessCode(orderPaymentAttach.getUpBusinessCode());
        walletPaymentBO.setExpireMinutes(Integer.parseInt(orderPaymentAttach.getExpireMinutes()));
        walletPaymentBO.setOrderDate(tradeOrderDO.getOrderDate());
        walletPaymentBO.setOrderTime(tradeOrderDO.getOrderTime());
        walletPaymentBO.setMobileNo(TripleDes.decrypt(protocolProperties.getTripleDes(), orderPaymentAttach.getMobileNo()));
        walletPaymentBO.setAmount(Long.parseLong(new LemonAmount(tradeOrderDO.getRealAmount()).yuan2fen()));
    }

    private void updatePaymentAttachInfo(TradeOrderDO tradeOrderDO, OrderPaymentAttachDO orderPaymentAttach, SubWalletPaymentBO walletPaymentBO) {
        if (JudgeUtils.isNotBlank(walletPaymentBO.getErrMsgCd())) {
            orderPaymentAttach.setDcepPayChannel(walletPaymentBO.getDcepPayChannel());
            // 数币返回除订单不存以外的错误，进行关单操作
            if (JudgeUtils.notEquals(DcepConstants.ORDER_ALREADY_EXIST, walletPaymentBO.getErrMsgCd())) {
                tradeOrderDO.setStatus(PaymentStatusEnum.TRADE_CLOSED.name());
                tradeOrderDO.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                tradeOrderDO.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
                tradeOrderDO.setErrMsgCd(walletPaymentBO.getErrMsgCd());
                tradeOrderDO.setErrMsgInfo(walletPaymentBO.getErrMsgInfo());
                payOrderService.updatePaymentStatus(tradeOrderDO);
            }
        }
        if (JudgeUtils.isNotBlank(orderPaymentAttach.getDcepPayChannel())) {
            payOrderAttachService.updateDcepPayChannel(orderPaymentAttach);
        }
    }
}
