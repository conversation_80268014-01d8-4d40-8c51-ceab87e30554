package com.cmpay.payment.service.channel.listener;

import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.BaseDcepPaymentHandlerBO;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.dcep.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/5/6
 */
@Service
public class DcepPaymentListenerServiceImpl extends PaymentListenerService<BaseDcepPaymentHandlerBO> {

    @Autowired
    private ApplicationContext applicationContext;

    @EventListener
    public void handlerDcepPayment(BaseDcepPaymentHandlerBO paymentHandlerBO) {
        super.execute(paymentHandlerBO);
    }

    @Override
    protected boolean checkChannelExecutable(BaseDcepPaymentHandlerBO paymentHandlerBO) {
        return Optional.ofNullable(paymentHandlerBO)
                .map(BaseDcepPaymentHandlerBO::getPaymentScene)
                .map(paymentScene->{return !StringUtils.equalsIgnoreCase(PaymentSceneEnum.TOKEN.name(), paymentScene);})
                .orElse(false);
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(BaseDcepPaymentHandlerBO paymentHandlerBO) {
        return Optional.ofNullable(paymentHandlerBO)
                .map(BaseDcepPaymentHandlerBO::getPaymentScene)
                .map(String::toUpperCase)
                .map(PaymentSceneEnum::valueOf)
                .map(paymentSceneEnum -> {
                    switch (paymentSceneEnum) {
                        case MMPAY:
                            return getBean(DcepPaymentMmpayService.class);
                        case MICROPAY:
                            return getBean(DcepPaymentMicroPayService.class);
                        case JSAPIPAY:
                            return getBean(DcepPaymentJsapiPayService.class);
                        case SCANPAY:
                            return getBean(DcepPaymentScanPayService.class);
                        case APP:
                            return getBean(DcepPaymentAppPayService.class);
                        case HDWALLET:
                            return getBean(DcepPaymentHdWalletPayService.class);
                        default:
                            return null;
                    }
                }).orElse(null);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
