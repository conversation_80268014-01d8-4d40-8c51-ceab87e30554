package com.cmpay.payment.service.channel.cmbaion;

import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.service.channel.PaymentChannelService;

/**
 * @author： PengAnHai
 * @date： 2024-08-21
 * @description：招行一网通退款订单查询处理类
 * @modifiedBy：
 * @version: 1.0
 */
public interface CmbaionRefundQueryChannelService extends PaymentChannelService<RefundOrderQueryBO> {

    /**
     * 招行一网通退款订单查询理
     * @param refundQueryBO
     */
    void cmbaionRefundQuery(RefundOrderQueryBO refundQueryBO);

}
