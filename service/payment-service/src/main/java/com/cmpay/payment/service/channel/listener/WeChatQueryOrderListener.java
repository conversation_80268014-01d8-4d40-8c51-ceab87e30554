package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.entity.config.ContractDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.wechat.WeChatQueryOrderService;
import com.cmpay.payment.service.ext.config.IExtContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatQueryOrderListener extends PaymentListenerService<PaymentQueryBO> {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    private IExtContractService contractService;

    /**
     * 微信H5支付查询订单
     *
     * @param paymentQueryBO
     */
    @EventListener
    public void handleWeChatOrderQuery(PaymentQueryBO paymentQueryBO) {
        execute(paymentQueryBO);
    }

    @Override
    protected boolean checkChannelExecutable(PaymentQueryBO paymentQueryBO) {
        return Optional.ofNullable(paymentQueryBO)
                .map(PaymentQueryBO::getPaymentOrder)
                .map(TradeOrderDO::getAimProductCode)
                .filter(aimProductCode -> StringUtils.equals(PaymentWayEnum.WECHAT.name().toLowerCase(), aimProductCode))
                .isPresent();
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(PaymentQueryBO paymentQueryBO) {
        ContractDO contract = contractService.getContract(paymentQueryBO.getPaymentOrder().getBankMerchantNo());
        paymentQueryBO.setContractSecureValue(contract.getSecretKey());
        return getBean(WeChatQueryOrderService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
