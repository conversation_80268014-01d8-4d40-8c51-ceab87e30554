package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.bo.config.SubMerchantBO;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.service.PayOrderExtFunctionService;
import com.cmpay.payment.service.ext.config.IExtSubMerchantService;
import com.cmpay.payment.util.EnumUtilsEx;
import com.cmpay.payment.util.PaymentUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.function.Function;

/**
 * @date 2023-11-23 19:21
 * <AUTHOR>
 * @Version 1.0
 */
@Service
public class PayOrderExtFunctionServiceImpl implements PayOrderExtFunctionService {
    private static final Logger logger = LoggerFactory.getLogger(SplitPayOrderServiceImpl.class);

    @Autowired
    private IExtSubMerchantService subMerchantService;
    private static final Function<String, ExpireTimeTypeEnum> EXPIRE_TIME_TYPE_ENUM_FUNCTION =
            EnumUtilsEx.lookupMap(ExpireTimeTypeEnum.class, ExpireTimeTypeEnum::getDesc);

    private static final String BANK_URL_INTERCEPTION = "<hi:\\$\\$>";
    private static final String BANK_URL_INTERCEPTION_INFO = "<hi:=>";
    private static final String BANK_URL_ADDRESS = "URL";
    private static final String BANK_URL_METHOD = "method";

    /**
     * 子商户校验
     * @param subMerchant
     */
    @Override
    public SubMerchantBO subMerchantCheck(String subMerchant){
        if(StringUtils.isEmpty(subMerchant)){
            BusinessException.throwBusinessException(MsgCodeEnum.SUB_MERCHANT_NOT_EXIST);
        }
        // 判断子商户号是否存在
        SubMerchantBO subMerchantBO = subMerchantService.querySubMerchant(subMerchant);
        if (JudgeUtils.isNull(subMerchantBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.SUB_MERCHANT_NOT_EXIST);
        }
        return subMerchantBO;
    }

    /**
     * 获取省侧订单号
     *
     * @param payOrderBO
     */
    @Override
    public void obtainProvinceOrderNo(PayOrderBO payOrderBO) {
        try {
            String provinceCode = StringUtils.substring(payOrderBO.getOutTradeNo(), ContractConstants.SUBSTR_ZERO, ContractConstants.SUBSTR_FOUR);
            if (!JudgeUtils.equals(provinceCode, CommonConstant.PROVINCE_CODE_MOBILE_MARKET) || JudgeUtils.isNull(payOrderBO.getExtra()) ||
                    !StringUtils.contains(payOrderBO.getExtra(), CommonConstant.PROVINCE_MOBILE_MARKET_ORDER_INFO)) {
                return;
            }
            int result1 = payOrderBO.getExtra().indexOf(CommonConstant.PROVINCE_MOBILE_MARKET_ORDER_INFO);
            payOrderBO.setProvinceOrderNo(StringUtils.substring(payOrderBO.getExtra(), result1 + Constants.INT_NINE, result1 + Constants.THIRTY_NINE));
        } catch (Exception e) {
            logger.error("obtainProvinceOrderNo:{}, error:{}", payOrderBO.getOutTradeNo(), e);
        }
    }

    /**
     * 输入不为空检查
     * 支付接口，与分账支付接口共用的不为空检查
     * @param payOrderBO
     */
    @Override
    public void inputCannotEmptyCheck(PayOrderBO payOrderBO) {
        if (JudgeUtils.isBlank(payOrderBO.getMerchantId())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(payOrderBO.getPayWay())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_WAY_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(payOrderBO.getScene())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_SCENE_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(payOrderBO.getOutTradeNo())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ORDER_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(payOrderBO.getTradeDate())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_REQUEST_DATE_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isNull(payOrderBO.getTotalAmount())) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_AMOUNT_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isNull(payOrderBO.getRealAmount())) {
            BusinessException.throwBusinessException(MsgCodeEnum.REAL_AMOUNT_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(payOrderBO.getSubject())) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_SUBJECT_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(payOrderBO.getNotifyUrl())) {
            BusinessException.throwBusinessException(MsgCodeEnum.NOTIFY_URL_CANNOT_BE_EMPTY);
        }
        //       当支付方式是alipay，promoParams参数json校验
        if (JudgeUtils.equalsIgnoreCase(payOrderBO.getPayWay(), PaymentWayEnum.ALIPAY.name())) {
            if (JudgeUtils.isNotBlank(payOrderBO.getPromoParams())) {
//                 如果不是json格式，报错
                if (!PaymentUtils.isJSON2(payOrderBO.getPromoParams())) {
                    BusinessException.throwBusinessException(MsgCodeEnum.ALIPAY_PROMO_PARAMS_ONLY_JSON);
                }
            }
        }
    }

    /**
     * 检查金额
     *
     * @param totalAmount
     * @param realAmount
     * @param discountableAmount
     */
    @Override
    public void checkAmount(BigDecimal totalAmount, BigDecimal realAmount, BigDecimal discountableAmount) {
        if (JudgeUtils.isNull(discountableAmount)) {
            if (totalAmount.compareTo(realAmount) != Constants.INT_ZERO) {
                BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
            }
        } else {
            if (discountableAmount.add(realAmount).compareTo(totalAmount) != Constants.INT_ZERO) {
                BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
            }
        }
        if (realAmount.compareTo(BigDecimal.ZERO) <= Constants.INT_ZERO) {
            BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
        }
        // 加两位小数判断
        if(!PaymentUtils.isTwoDecimalPlaces(realAmount)){
            BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
        }
    }

    /**
     * 检查支付条码
     *
     * @param authCode
     * @param payWay
     * @param scene
     */
    @Override
    public void checkAuthCode(String authCode, String payWay, String scene) {
        if (JudgeUtils.notEquals(scene, PaymentSceneEnum.BARCODE.name().toLowerCase())) {
            return;
        }
        if (JudgeUtils.isEmpty(authCode)) {
            BusinessException.throwBusinessException(MsgCodeEnum.AUTHCODE_CANNOT_BE_EMPTY);
        }
        if (authCode.length() < CommonConstant.AUTH_CODE_TYPE_LENGTH) {
            BusinessException.throwBusinessException(MsgCodeEnum.AUTHCODE_ILLEGAL);
        }
        if (JudgeUtils.notEquals(payWay, getPaymentWay(StringUtils.substring(authCode, Constants.INT_ZERO, CommonConstant.AUTH_CODE_TYPE_LENGTH)))) {
            BusinessException.throwBusinessException(MsgCodeEnum.AUTHCODE_ILLEGAL);
        }

    }

    /**
     * 得到支付方式
     *
     * @param code
     * @return String
     */
    @Override
    public String getPaymentWay(String code) {
        if (JudgeUtils.equalsAny(code, AuthCodeTypeEnum.WECHANT_ZERO.getDesc(), AuthCodeTypeEnum.WECHANT_ONE.getDesc(),
                AuthCodeTypeEnum.WECHANT_TWO.getDesc(), AuthCodeTypeEnum.WECHANT_THREE.getDesc(),
                AuthCodeTypeEnum.WECHANT_FOUR.getDesc(), AuthCodeTypeEnum.WECHANT_FIVE.getDesc())) {
            return PaymentWayEnum.WECHAT.name().toLowerCase();
        }
        if (JudgeUtils.equals(code, AuthCodeTypeEnum.ALIPAY.getDesc())) {
            return PaymentWayEnum.ALIPAY.name().toLowerCase();
        }
        if (JudgeUtils.equals(code, AuthCodeTypeEnum.CMPAY.getDesc())) {
            return PaymentWayEnum.CMPAY.name().toLowerCase();
        }
        if (JudgeUtils.equals(code, AuthCodeTypeEnum.UNIONPAY.getDesc())) {
            return PaymentWayEnum.UNIONPAY.name().toLowerCase();
        }
        return null;
    }

    /**
     * 获取相对超时时间
     *
     * @param payOrderBO
     * @return
     */
    @Override
    public String getTimeoutExpress(PayOrderBO payOrderBO) {
        if (JudgeUtils.isBlank(payOrderBO.getTimeExpire())) {
            return payOrderBO.getTimeoutExpress();
        }
        SimpleDateFormat format = new SimpleDateFormat(Constants.DATA_FORMAT_WITH_S);
        try {
            Date outTime = format.parse(payOrderBO.getTimeExpire());
            String time = format.format(new Date());
            Date nowTime = format.parse(time);
            if (outTime.before(nowTime)) {
                BusinessException.throwBusinessException(MsgCodeEnum.ORDER_EXPIRE);
            }
            long nm = Constants.MILLISECOND;
            // 获得两个时间的毫秒时间差异
            long diff = outTime.getTime() - nowTime.getTime();
            // 计算差多少分钟
            long min = diff / nm;
            logger.info("相对超时时间：" + min + "m");
            return min + "m";
        } catch (ParseException e) {
            logger.error(e.getMessage());
        }
        return null;
    }

    /**
     * 得到过期时间
     *
     * @param payOrderBO
     * @return String
     */
    @Override
    public String getExpireTime(PayOrderBO payOrderBO) {
        String timeoutExpress = payOrderBO.getTimeoutExpress();
        if (StringUtils.isNotEmpty(payOrderBO.getTimeExpire())
                && JudgeUtils.notEquals(PaymentWayEnum.CMPAY.toString().toLowerCase(), payOrderBO.getPaymentRout())) {
            SimpleDateFormat format = new SimpleDateFormat(Constants.DATA_FORMAT_WITH_S);
            try {
                Date outTime = format.parse(payOrderBO.getTimeExpire());
                Date date = new Date();
                if (outTime.before(date)) {
                    BusinessException.throwBusinessException(MsgCodeEnum.ORDER_EXPIRE);
                } else {
                    logger.info("绝对超时时间" + payOrderBO.getTimeExpire());
                    return payOrderBO.getTimeExpire();
                }
            } catch (ParseException e) {
                logger.error(e.getMessage());
            }
        }
        logger.info("过期时间: {}", timeoutExpress);
        LocalDateTime localDateTime = DateTimeUtils.getCurrentLocalDateTime();
        // 微信付款码支付订单有效期30秒加15秒的buffer
        if (StringUtils.equals(payOrderBO.getScene(), PaymentSceneEnum.BARCODE.name().toLowerCase())
                && StringUtils.equals(payOrderBO.getPaymentRout(), PaymentWayEnum.WECHAT.name().toLowerCase())) {
            return DateTimeUtils.formatLocalDateTime(localDateTime.plusSeconds(45));
        }


        if (JudgeUtils.isEmpty(timeoutExpress)) {
            // 订单默认有效期30分钟
            payOrderBO.setValidityUnit(CommonConstant.VALIDITY_UNIT_MINUTE);
            payOrderBO.setValidityNumber(Long.toString(30));
            return DateTimeUtils.formatLocalDateTime(localDateTime.plusMinutes(30));
        }

        LocalDateTime expireTime;

        String expireType = StringUtils.substring(timeoutExpress, timeoutExpress.length() - ContractConstants.DATASOURCES_NORMAL);
        long expireValue = Long.parseLong(StringUtils.removeEnd(timeoutExpress, expireType));

        ExpireTimeTypeEnum expireTimeType = EXPIRE_TIME_TYPE_ENUM_FUNCTION.apply(StringUtils.substring(timeoutExpress, timeoutExpress.length() - Constants.INT_ONE));
        switch (expireTimeType) {
            case MINUTE:
                expireTime = localDateTime.plusMinutes(expireValue);
                payOrderBO.setValidityUnit(CommonConstant.VALIDITY_UNIT_MINUTE);
                payOrderBO.setValidityNumber(Long.toString(expireValue));
                break;
            case HOUR:
                expireTime = localDateTime.plusHours(expireValue);
                payOrderBO.setValidityUnit(CommonConstant.VALIDITY_UNIT_MINUTE);
                payOrderBO.setValidityNumber(Long.toString(expireValue * 60));
                break;
            case DAY:
                expireTime = localDateTime.plusDays(expireValue);
                payOrderBO.setValidityUnit(CommonConstant.VALIDITY_UNIT_DAY);
                payOrderBO.setValidityNumber(Long.toString(expireValue));
                break;
            case TODAY:
            default:
                expireTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateStr() + LocalTime.MAX.toString());
                payOrderBO.setValidityUnit(CommonConstant.VALIDITY_UNIT_MINUTE);
                payOrderBO.setValidityNumber(CommonConstant.VALIDITY_NUMBER);
                break;
        }

        return DateTimeUtils.formatLocalDateTime(expireTime);
    }

    /**
     * 截取银行支付地址信息
     */
    @Override
    public void analyzeBankUrl(PayOrderBO payOrderBO) {
        String bankUrl = payOrderBO.getPayUrl();
        if (JudgeUtils.isBlank(bankUrl)) {
            return;
        }
        //首先用<hi:$$>分割
        String[] items = bankUrl.split(BANK_URL_INTERCEPTION);
        if (JudgeUtils.isEmpty(items)) {
            BusinessException.throwBusinessException(MsgCodeEnum.BANK_PAYMENT_URL_EMPTY);
        }
        for (String item : items) {
            String[] params = item.split(BANK_URL_INTERCEPTION_INFO);
            if (params.length != Constants.INT_TWO) {
                continue;
            }
            if (CmpayConstants.BANK_PAY_FLAG_EPCC.equalsIgnoreCase(params[Constants.INT_ZERO])) {
                payOrderBO.setBankPayFlag(CmpayConstants.BANK_PAY_FLAG_EPCC);
            } else {
                payOrderBO.setBankPayFlag(CmpayConstants.BANK_PAY_FLAG_UP);
            }
            if (BANK_URL_ADDRESS.equalsIgnoreCase(params[Constants.INT_ZERO])) {
                payOrderBO.setPayUrl(params[Constants.INT_ONE]);
            } else if (BANK_URL_METHOD.equalsIgnoreCase(params[Constants.INT_ZERO])) {
                payOrderBO.setBankMethod(params[Constants.INT_ONE]);
            } else {
                payOrderBO.setEpccGwMsg(params[Constants.INT_ONE]);
            }
        }
    }

}
