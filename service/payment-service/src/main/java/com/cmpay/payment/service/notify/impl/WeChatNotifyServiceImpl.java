package com.cmpay.payment.service.notify.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.notify.WeChatNotifyBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.TradeNotifyService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.notify.IWeChatNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/14 15:01
 */
@Service
@Slf4j
public class WeChatNotifyServiceImpl implements IWeChatNotifyService {
    @Autowired
    private TradeNotifyService tradeNotifyService;
    @Autowired
    private AsynCommonService asynCommonService;
    @Autowired
    private ExtPayOrderService payOrderService;

    @Override
    public String payNotify(WeChatNotifyBO weChatNotifyBO) {
        // 根据resultCode的状态判断时候支付成功
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setTradeOrderNo(weChatNotifyBO.getOutTradeNo());
        tradeNotifyBO.setResult(weChatNotifyBO.getResultCode());
        tradeNotifyBO.setPaymentRout(PaymentWayEnum.WECHAT.name().toLowerCase());
        if (JudgeUtils.equals(weChatNotifyBO.getResultCode(), WeChatCommonResponseEnum.ResultCode.SUCCESS.name())
                && StringUtils.equals(ContractConstants.SUCCESS, weChatNotifyBO.getTradeState())) {
            tradeNotifyBO.setTradeAmount(new BigDecimal(weChatNotifyBO.getTotalFee()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
            tradeNotifyBO.setBnkTradeNo(weChatNotifyBO.getTransactionId());
            tradeNotifyBO.setFinishDate(weChatNotifyBO.getTimeEnd());
            tradeNotifyBO.setAccountDate(weChatNotifyBO.getTimeEnd().substring(0, 8));
            tradeNotifyBO.setMobileNumber(weChatNotifyBO.getOpenid());
        }
        if (JudgeUtils.equals(weChatNotifyBO.getResultCode(), WeChatCommonResponseEnum.ResultCode.FAIL.name())) {
            tradeNotifyBO.setErrCode(weChatNotifyBO.getErrCode());
            tradeNotifyBO.setErrCodeDes(weChatNotifyBO.getErrCodeDes());
            tradeNotifyBO.setFinishDate(DateTimeUtils.getCurrentDateTimeStr());
            tradeNotifyBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
        }
        if (JudgeUtils.isEmpty(weChatNotifyBO.getTimeEnd())) {
            weChatNotifyBO.setTimeEnd(DateTimeUtils.getCurrentDateTimeStr());
        }
        TradeNotifyBO notifyBO = tradeNotifyService.backendNotify(tradeNotifyBO);
        // 通知商户
        TradeOrderDO tradeOrderDO = payOrderService.get(weChatNotifyBO.getOutTradeNo());
        if (JudgeUtils.isNull(tradeOrderDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        if (StringUtils.equalsAny(tradeOrderDO.getStatus(), OrderStatusEnum.TRADE_SUCCESS.name(), OrderStatusEnum.TRADE_FAIL.name())) {
            asynCommonService.asyncNotify(notifyBO);
            log.info("notifyContractPayWeChat modify status success");
            return ContractConstants.SUCCESS;
        }
        return "";
    }

    @Override
    public void unifiedOrderNotify(WeChatNotifyBO weChatNotifyBO) {
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setTradeOrderNo(weChatNotifyBO.getOutTradeNo());
        tradeNotifyBO.setResult(weChatNotifyBO.getResultCode());
        // 注意当前暂不支持微信的营销资源，故取全部金额对比
        tradeNotifyBO.setTradeAmount(new BigDecimal(weChatNotifyBO.getTotalFee()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
        tradeNotifyBO.setFinishDate(weChatNotifyBO.getTimeEnd());
        tradeNotifyBO.setPaymentRout(PaymentWayEnum.WECHAT.name().toLowerCase());
        tradeNotifyBO.setBnkTradeNo(weChatNotifyBO.getTransactionId());
        if (JudgeUtils.isEmpty(weChatNotifyBO.getTimeEnd())) {
            weChatNotifyBO.setTimeEnd(DateTimeUtils.getCurrentDateTimeStr());
        }
        tradeNotifyBO.setAccountDate(weChatNotifyBO.getTimeEnd().substring(0, 8));
        tradeNotifyBO.setMobileNumber(weChatNotifyBO.getOpenid());
        TradeNotifyBO notifyBO = tradeNotifyService.backendNotify(tradeNotifyBO);
        asynCommonService.asyncNotify(notifyBO);
    }
}
