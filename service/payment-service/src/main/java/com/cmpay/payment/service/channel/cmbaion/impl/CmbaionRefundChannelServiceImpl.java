package com.cmpay.payment.service.channel.cmbaion.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.channel.CmbaionPayChannelEnum;
import com.cmpay.payment.constant.cmbaion.CmbaionConstants;
import com.cmpay.payment.dto.cmbaion.AbstractCmbaionDTO;
import com.cmpay.payment.dto.cmbaion.CmbaionRefundReqDTO;
import com.cmpay.payment.dto.cmbaion.CmbaionRefundRspDTO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.channel.cmbaion.CmbaionRefundChannelService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author： PengAnHai
 * @date： 2024-08-13
 * @description：招行一网通退款处理实现类
 * @modifiedBy：
 * @version: 1.0
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmbaionRefundChannelServiceImpl extends CmbaionBaseRequestServiceImpl implements CmbaionRefundChannelService {

    @Override
    public void send(RefundBO refundBO) {
        cmbaionRefund(refundBO);
    }

    @Override
    public void cmbaionRefund(RefundBO refundBO) {
        // 设置退款操作为非异步
        refundBO.setAsync(true);
        // 创建退款请求DTO
        CmbaionRefundReqDTO cmbaionRefundReqDTO = createRefundReqDTO(refundBO);
        // 构建并发送退款请求
        buildCmbaionRequest(cmbaionRefundReqDTO,refundBO, CmbaionPayChannelEnum.CMBAION_REFUND.getName());
    }

    /**
     * 处理来自Cmbaion的退款响应。
     * 如果响应代码不是预期的成功、失败或状态未知代码之一，则设置错误消息并抛出业务异常。
     *
     * @param result 响应DTO，包含响应数据。
     * @param baseHandlerBO 基础处理业务对象，用于进一步处理。
     * @return 处理后的业务对象。
     */
    @Override
    public BaseHandlerBO handlerCmbaionResponse(AbstractCmbaionDTO result, BaseHandlerBO baseHandlerBO) {
        // 强制类型转换为退款响应DTO
        CmbaionRefundRspDTO refundRspDTO = (CmbaionRefundRspDTO) result;
        // 获取响应数据
        CmbaionRefundRspDTO.RsqData rsqData = refundRspDTO.getRsqData();
        // 强制类型转换为退款业务对象
        RefundBO refundBO = (RefundBO) baseHandlerBO;
        // 检查响应代码是否不在预期的成功、失败或状态未知的列表中
        if (!JudgeUtils.equalsAny(rsqData.getRspCode(), CmbaionConstants.SUCCESS, CmbaionConstants.FAIL, CmbaionConstants.NOT_STATUS)) {

            // 设置错误代码
            refundBO.setErrMsgCd(rsqData.getRspCode());
            refundBO.setErrMsgInfo(rsqData.getRspMsg());
            BusinessException.throwBusinessException(rsqData.getRspCode(), rsqData.getRspMsg());
        }
        return refundBO;
    }

    /**
     * 从退款业务对象创建退款请求DTO。
     *
     * @param refundBO 退款业务对象，包含原始支付交易订单等信息。
     * @return 创建的退款请求DTO。
     */
    private CmbaionRefundReqDTO createRefundReqDTO(RefundBO refundBO) {
        // 获取原始支付交易订单
        TradeOrderDO tradeOrderDO = refundBO.getOriginalPayTradeOrder();
        CmbaionRefundReqDTO cmbaionRefundReqDTO = new CmbaionRefundReqDTO();
        CmbaionRefundReqDTO.ReqData reqData = new CmbaionRefundReqDTO.ReqData();
        // 设置当前日期时间字符串
        reqData.setDateTime(DateTimeUtils.getCurrentDateTimeStr());
        // 设置分行号
        reqData.setBranchNo(getBranchNo(tradeOrderDO.getBankMerchantNo()));
        // 设置商户号
        reqData.setMerchantNo(getMerchantNo(tradeOrderDO.getBankMerchantNo()));
        // 设置当前日期字符串
        reqData.setDate(tradeOrderDO.getOrderDate());
        // 设置交易订单号
        reqData.setOrderNo(tradeOrderDO.getTradeOrderNo());
        // 设置退款流水号
        reqData.setRefundSerialNo(refundBO.getOutRequestNo());
        // 设置退款金额
        reqData.setAmount(refundBO.getRefundAmount().toString());
        cmbaionRefundReqDTO.setReqData(reqData);
        setRequestDataDTO(cmbaionRefundReqDTO);
        return cmbaionRefundReqDTO;
    }
}
