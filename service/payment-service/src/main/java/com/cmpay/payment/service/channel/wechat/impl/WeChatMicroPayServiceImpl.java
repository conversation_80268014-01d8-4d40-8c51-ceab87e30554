package com.cmpay.payment.service.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.AlertCapable;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.dto.wechat.WXPayMicropayRequest;
import com.cmpay.payment.dto.wechat.WXPayMicropayResponse;
import com.cmpay.payment.service.channel.wechat.WeChatMicroPayService;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatMicroPayServiceImpl implements WeChatMicroPayService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatMicroPayServiceImpl.class);

    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;

    /**
     * 微信付款码支付
     *
     * @param payOrderBO
     */
    @Override
    public void microPay(PayOrderBO payOrderBO) {
        WXPayMicropayRequest wxPayMicropayRequest = new WXPayMicropayRequest();
        // 微信对body（商品描述）字段格式按使用场景有特殊要求，H5支付的商品字段规则：浏览器打开的移动网页的主页title名-商品概述，样例：腾讯充值中心-QQ会员充值
        wxPayMicropayRequest.setBody(payOrderBO.getSubject());
        wxPayMicropayRequest.setOutTradeNo(payOrderBO.getOutTradeNo());
        // 金额单位：分
        wxPayMicropayRequest.setTotalFee(payOrderBO.getRealAmount().multiply(new BigDecimal(100)).intValue());
        wxPayMicropayRequest.setSpbillCreateIp(payOrderBO.getClientIps());
        Optional.ofNullable(payOrderBO.getLimitPay()).ifPresent(limitPay -> wxPayMicropayRequest.setLimitPay(limitPay));
        Optional.ofNullable(payOrderBO.getTradeDate() + payOrderBO.getTradeTime()).ifPresent(timeStart -> wxPayMicropayRequest.setTimeStart(timeStart));
        Optional.ofNullable(payOrderBO.getExpireTime()).ifPresent(expireTime -> wxPayMicropayRequest.setTimeExpire(expireTime));
        wxPayMicropayRequest.setAuthCode(payOrderBO.getAuthCode());
        wxPayMicropayRequest.setMchId(payOrderBO.getPaymentId());
        wxPayMicropayRequest.setKey(payOrderBO.getContractSecureValue());

        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.MICROPAY.getSource());
        request.setRoute(WXPayChannel.MICROPAY.getRoute());
        request.setBusiType(WXPayChannel.MICROPAY.getBusType());
        request.setTarget(wxPayMicropayRequest);
        logger.info("WeChatMicroPayServiceImpl Source : {} Route : {} BusiType : {}",request.getSource(),request.getRoute(),request.getBusiType());
        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(payOrderBO, (WXPayMicropayResponse) result));

    }

    @Override
    public void send(PayOrderBO payOrderBO) {
        microPay(payOrderBO);
    }

    private void handleResult(PayOrderBO payOrderBO, WXPayMicropayResponse response) {
        payOrderBO.setThirdOrdNo(response.getTransactionId());
        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                payOrderBO.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                payOrderBO.setFinishDateTime(response.getTimeEnd());
                payOrderBO.setThirdOrdDt(StringUtils.substring(response.getTimeEnd(),0, 8));
                payOrderBO.setMobileNumber(response.getOpenid());
            } else {
                logger.info("micro paying... weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getErrCode());
                AlertCapable alertCapable;
                switch (errorCodeEnum) {

                    // 支付确认失败
                    // 注意：如果当前交易返回的支付状态是明确的错误原因造成的支付失败（支付确认失败），请重新下单支付；
                    case PARAM_ERROR:
                    case ORDERPAID:
                    case NOAUTH:
                    case XML_FORMAT_ERROR:
                    case REQUIRE_POST_METHOD:
                    case SIGNERROR:
                    case LACK_PARAMS:
                    case NOT_UTF8:
                    case APPID_NOT_EXIST:
                    case MCHID_NOT_EXIST:
                    case APPID_MCHID_NOT_MATCH:
                    case INVALID_REQUEST:
                    case TRADE_ERROR:
                        payOrderBO.setErrMsgCd(response.getErrCode());
                        payOrderBO.setErrMsgInfo(response.getErrCodeDes());
                        payOrderBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                        alertCapable = MsgCodeEnum.WECHAT_SYSTEM_ERROR;
                        break;
                    case AUTHCODEEXPIRE:
                        payOrderBO.setErrMsgCd(response.getErrCode());
                        payOrderBO.setErrMsgInfo(response.getErrCodeDes());
                        payOrderBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                        alertCapable = MsgCodeEnum.WECHAT_AUTH_CODE_EXPIRE;
                        break;
                    case NOTENOUGH:
                        payOrderBO.setErrMsgCd(response.getErrCode());
                        payOrderBO.setErrMsgInfo(response.getErrCodeDes());
                        payOrderBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                        alertCapable = MsgCodeEnum.WECHAT_NOT_ENOUGH;
                        break;
                    case NOTSUPORTCARD:
                        payOrderBO.setErrMsgCd(response.getErrCode());
                        payOrderBO.setErrMsgInfo(response.getErrCodeDes());
                        payOrderBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                        alertCapable = MsgCodeEnum.WECHAT_NOT_SUPORT_CARD;
                        break;
                    case ORDERCLOSED:
                        payOrderBO.setErrMsgCd(response.getErrCode());
                        payOrderBO.setErrMsgInfo(response.getErrCodeDes());
                        payOrderBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                        alertCapable = MsgCodeEnum.WECHAT_ORDER_CLOSED;
                        break;
                    case ORDERREVERSED:
                        payOrderBO.setErrMsgCd(response.getErrCode());
                        payOrderBO.setErrMsgInfo(response.getErrCodeDes());
                        payOrderBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                        alertCapable = MsgCodeEnum.WECHAT_ORDER_REVERSED;
                        break;
                    case AUTH_CODE_ERROR:
                        payOrderBO.setErrMsgCd(response.getErrCode());
                        payOrderBO.setErrMsgInfo(response.getErrCodeDes());
                        payOrderBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                        alertCapable = MsgCodeEnum.WECHAT_AUTH_CODE_ERROR;
                        break;
                    case AUTH_CODE_INVALID:
                        payOrderBO.setErrMsgCd(response.getErrCode());
                        payOrderBO.setErrMsgInfo(response.getErrCodeDes());
                        payOrderBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                        alertCapable = MsgCodeEnum.WECHAT_AUTH_CODE_INVALID;
                        break;
                    case BUYER_MISMATCH:
                        payOrderBO.setErrMsgCd(response.getErrCode());
                        payOrderBO.setErrMsgInfo(response.getErrCodeDes());
                        payOrderBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                        alertCapable = MsgCodeEnum.WECHAT_BUYER_MISMATCH;
                        break;
                    case OUT_TRADE_NO_USED:
                        payOrderBO.setErrMsgCd(response.getErrCode());
                        payOrderBO.setErrMsgInfo(response.getErrCodeDes());
                        payOrderBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                        alertCapable = MsgCodeEnum.WECHAT_OUT_TRADE_NO_USED;
                        break;

                    // 支付结果未知
                    // 注意：如果当前交易返回的支付状态是不明错误（支付结果未知），请调用查询订单接口确认状态
                    // 如果长时间（建议30秒）都得不到明确状态请调用撤销订单接口。
                    case USERPAYING:
                    case SYSTEMERROR:
                    case BANKERROR:
                    default:
                        payOrderBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
                        return;
                }
                BusinessException.throwBusinessException(alertCapable);
            }
        } else {
            logger.error("micro pay failure, weChat return message is : {}", response.getReturnMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_SYSTEM_ERROR);
        }
    }
}
