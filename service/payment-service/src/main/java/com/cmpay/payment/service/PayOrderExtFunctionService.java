package com.cmpay.payment.service;

import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.bo.config.SubMerchantBO;

import java.math.BigDecimal;

/**
 * @date 2023-11-23 19:21
 * <AUTHOR>
 * @Version 1.0
 */
public interface PayOrderExtFunctionService {
    SubMerchantBO subMerchantCheck(String subMerchant);

    void obtainProvinceOrderNo(PayOrderBO payOrderBO);

    void inputCannotEmptyCheck(PayOrderBO payOrderBO);

    void checkAmount(BigDecimal totalAmount, BigDecimal realAmount, BigDecimal discountableAmount);

    void checkAuthCode(String authCode, String payWay, String scene);

    String getPaymentWay(String code);

    String getTimeoutExpress(PayOrderBO payOrderBO);

    String getExpireTime(PayOrderBO payOrderBO);

    void analyzeBankUrl(PayOrderBO payOrderBO);
}
