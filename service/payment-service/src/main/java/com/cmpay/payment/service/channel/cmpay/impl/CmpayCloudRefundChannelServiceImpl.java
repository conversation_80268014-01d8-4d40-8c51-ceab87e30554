package com.cmpay.payment.service.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.CpsCloudRefundBO;
import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.bo.config.SubMerchantRefreshAmountBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dto.cmpay.CmpayPosRefundPendReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayPosRefundPendRspDTO;
import com.cmpay.payment.entity.CmpayRefundJrnDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.CompleteOrderSplitService;
import com.cmpay.payment.service.SubMerchantIncomeService;
import com.cmpay.payment.service.channel.cmpay.CmpayCloudRefundChannelService;
import com.cmpay.payment.service.channel.cmpay.CmpayCommonService;
import com.cmpay.payment.service.ext.ExtCmpayRefundJrnService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Created on 2018/11/29
 * cmapy 云支付渠道  退款业务类
 *
 * @author: sun_zhh
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayCloudRefundChannelServiceImpl implements CmpayCloudRefundChannelService {
    private static final Logger logger = LoggerFactory.getLogger(CmpayCloudRefundChannelServiceImpl.class);
    @Autowired
    ExtCmpayRefundJrnService extCmpayRefundJrnService;
    @Value("${cmpay.cpsBackendRefundNotifyUrl:}")
    private String cpsBackendRefundNotifyUrl;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Autowired
    private CmpayCommonService cmpayCommonService;
    @Autowired
    private CompleteOrderSplitService splitService;
    @Autowired
    private SubMerchantIncomeService refundConfineService;
    @Autowired
    ExtParamInfoService paramInfoService;
    @Override
    public void send(RefundBO refundBO) {
        cmpayCloudRefund(refundBO);
    }

    @Override
    public void cmpayCloudRefund(RefundBO refundBO) {
        CmpayRefundJrnDO cmpayRefundJrnDO = new CmpayRefundJrnDO();
        cmpayRefundJrnDO.setTradeOrderNo(refundBO.getOutRequestNo());
        List<CmpayRefundJrnDO> find = extCmpayRefundJrnService.find(cmpayRefundJrnDO);
        if (find.size() == 0) {
            TradeOrderDO originalPayTradeOrderDO = refundBO.getOriginalPayTradeOrder();;
            String subMerchant = StringUtils.isEmpty(refundBO.getSubMerchant()) ? originalPayTradeOrderDO.getSubMerchantNo() : refundBO.getSubMerchant();
            //原订单号子商户不为空，进行退款限制
            if(StringUtils.isNotEmpty(subMerchant)){
                // 查询和包退款流水，如果没有这笔退款，说明是第一次发起，则进行限制
                SubMerchantRefreshAmountBO refreshAmountBO = new SubMerchantRefreshAmountBO();
                refreshAmountBO.setSubMerchantNo(subMerchant);
                refreshAmountBO.setAmount(refundBO.getRefundOrder().getOrderAmount());
                refundConfineService.refundConfine(refreshAmountBO);
            }

            try {
                cmpayRefundJrnDO = extCmpayRefundJrnService.insertByNewTranscation(refundBO);
            } catch (Exception e) {
                // 插入失败，报错，则自增回去
                splitService.refundFailIncrement(refundBO.getRefundOrder(), originalPayTradeOrderDO);
                // 报错退出
                BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ERROR);
            }
        } else {
            cmpayRefundJrnDO = find.get(0);
        }
        try {
            CpsCloudRefundBO cmpayCloudRefundBO = cmpayPosPendRefund(refundBO);
            cmpayRefundJrnDO.setTradeStatus(cmpayCloudRefundBO.getJrnStatus());
            cmpayRefundJrnDO.setReturnMsg(cmpayCloudRefundBO.getMessageCode());
            cmpayRefundJrnDO.setReturnMsgInfo(cmpayCloudRefundBO.getMessageInfo());
            refundBO.setAccountDate(cmpayCloudRefundBO.getAccountDate());
            refundBO.setStatus(cmpayCloudRefundBO.getJrnStatus());
            refundBO.setErrMsgCd(cmpayCloudRefundBO.getErrMsgCd());
            refundBO.setErrMsgInfo(cmpayCloudRefundBO.getErrMsgInfo());
        } catch (Exception e) {
            cmpayCommonService.cmapyThrowClassHandle(cmpayRefundJrnDO,e);
            throw e;
        } finally {
            extCmpayRefundJrnService.updateByNewTranscation(cmpayRefundJrnDO);
        }
        if (!MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd().equals(cmpayRefundJrnDO.getReturnMsg())) {
            // 报错退出
            BusinessException.throwBusinessException(cmpayRefundJrnDO.getReturnMsg());
        }
    }

    private CpsCloudRefundBO cmpayPosPendRefund(RefundBO refundEventBO) {
        CpsCloudRefundBO cpsCloudRefundBO = new CpsCloudRefundBO();
        CmpayPosRefundPendReqDTO posRefundReqDTO = new CmpayPosRefundPendReqDTO();
        posRefundReqDTO.setMerchantId(refundEventBO.getOriginalPayTradeOrder().getBankMerchantNo());
        posRefundReqDTO.setRequestId(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.CMPAY_JRN_NO, 18));
        posRefundReqDTO.setOrderId(refundEventBO.getOriginalPayTradeOrder().getOutTradeNo());
        posRefundReqDTO.setRefundRequestId(refundEventBO.getRefundOrder().getOutTradeNo());
        posRefundReqDTO.setSignType(refundEventBO.getSignMethod());
        posRefundReqDTO.setType(CmpayConstants.CPOS_REFUND);
        posRefundReqDTO.setVersion(CmpayConstants.CPOS_VERSION);
        posRefundReqDTO.setAmount(refundEventBO.getRefundAmount().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_DOWN));
        posRefundReqDTO.setReason(refundEventBO.getRefundReason());

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(host)) {
            try {
                posRefundReqDTO.setRefNotifUrl(UrlUtils.replaceDomainOrIp(cpsBackendRefundNotifyUrl,host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                posRefundReqDTO.setRefNotifUrl(cpsBackendRefundNotifyUrl);
            }
        } else {
            posRefundReqDTO.setRefNotifUrl(cpsBackendRefundNotifyUrl);
        }

        posRefundReqDTO.setHmac(refundEventBO.getContractSecureValue());
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(CmpayPayChannelEnum.CMPAY_POS);
        request.setBusiType(CmpayPayChannelEnum.CMPAYPOS_REFUND_PEND.getName());
        request.setSource(CmpayPayChannelEnum.CMPAY_POS);
        request.setTarget(posRefundReqDTO);
        GenericRspDTO<Response> genericRspDTO = null;
        if (StringUtils.equalsAny(refundEventBO.getSourceApp(), AppEnum.integrationbatch.name(), AppEnum.integrationshecdule.name())) {
            genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        } else {
            genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        }
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            cmpayCommonService.cgwReturnInfoCheck(genericRspDTO);
            BusinessException.throwBusinessException(genericRspDTO);
        }
        Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleCposRefund(cpsCloudRefundBO, (CmpayPosRefundPendRspDTO) result));
        return cpsCloudRefundBO;
    }

    private void handleCposRefund(CpsCloudRefundBO cpsCloudRefundBO, CmpayPosRefundPendRspDTO refundPendRspDTO) {
        if (JudgeUtils.isNull(refundPendRspDTO)) {
            cpsCloudRefundBO.setMessageCode(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgCd());
            cpsCloudRefundBO.setMessageInfo(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgInfo());
            return;
        }
        if (StringUtils.equalsIgnoreCase(CloudOrderStatusEnum.SUCCESS.name(), refundPendRspDTO.getStatus())) {
            cpsCloudRefundBO.setJrnStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            cpsCloudRefundBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            cpsCloudRefundBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
        } else if (StringUtils.equalsIgnoreCase(MsgCodeEnum.CMPAY_REFUND_ORDER_EXISTS_REFUND.getMsgCd(), refundPendRspDTO.getReturnCode())) {
            cpsCloudRefundBO.setJrnStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            cpsCloudRefundBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            cpsCloudRefundBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
        } else if (StringUtils.equalsIgnoreCase(MsgCodeEnum.CMPAY_REFUND_ORDER_EXISTS_ACCEPT.getMsgCd(), refundPendRspDTO.getReturnCode())) {
            cpsCloudRefundBO.setJrnStatus(OrderStatusEnum.REFUND_FAIL.name());
            cpsCloudRefundBO.setErrMsgCd(refundPendRspDTO.getReturnCode());
            cpsCloudRefundBO.setErrMsgInfo(refundPendRspDTO.getMessage());
            cpsCloudRefundBO.setMessageCode(refundPendRspDTO.getReturnCode());
            cpsCloudRefundBO.setMessageInfo(refundPendRspDTO.getMessage());
        } else {
            cpsCloudRefundBO.setMessageCode(refundPendRspDTO.getReturnCode());
            cpsCloudRefundBO.setMessageInfo(refundPendRspDTO.getMessage());
        }
    }
}
