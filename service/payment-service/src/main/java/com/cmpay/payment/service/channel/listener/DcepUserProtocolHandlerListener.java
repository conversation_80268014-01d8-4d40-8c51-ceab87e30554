package com.cmpay.payment.service.channel.listener;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.BaseDcepUserProtocolHandlerBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.protocol.DcepUserProtocolHandlerTypeEnum;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.dcep.DcepUserProtocolAbolishService;
import com.cmpay.payment.service.channel.dcep.DcepUserProtocolApplyService;
import com.cmpay.payment.service.channel.dcep.DcepUserProtocolQueryService;
import com.cmpay.payment.service.channel.dcep.DcepUserProtocolSignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/05/13
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class DcepUserProtocolHandlerListener extends PaymentListenerService<BaseDcepUserProtocolHandlerBO> {

    @Autowired
    ApplicationContext applicationContext;

    /**
     * 处理工行事件
     *
     * @param userProtocolHandlerBO
     */
    @EventListener
    public void handleDcepUserProtocol(BaseDcepUserProtocolHandlerBO userProtocolHandlerBO) {
        execute(userProtocolHandlerBO);
    }

    @Override
    protected boolean checkChannelExecutable(BaseDcepUserProtocolHandlerBO userProtocolHandlerBO) {
        return Optional.ofNullable(userProtocolHandlerBO)
                .map(BaseDcepUserProtocolHandlerBO::getHandlerType)
                .filter(signWay -> (JudgeUtils.isNotNull(DcepUserProtocolHandlerTypeEnum.valueOf(signWay))))
                .isPresent();
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(BaseDcepUserProtocolHandlerBO userProtocolHandlerBO) {
        return Optional.ofNullable(userProtocolHandlerBO)
                .map(BaseDcepUserProtocolHandlerBO::getHandlerType)
                .map(String::toUpperCase)
                .map(DcepUserProtocolHandlerTypeEnum::valueOf)
                .map(handlerType -> {
                    switch (handlerType) {
                        case APPLY:
                            return getBean(DcepUserProtocolApplyService.class);
                        case SIGN:
                            return getBean(DcepUserProtocolSignService.class);
                        case CANCEL:
                            return getBean(DcepUserProtocolAbolishService.class);
                        case QUERY:
                            return getBean(DcepUserProtocolQueryService.class);
                        default:
                            BusinessException.throwBusinessException(MsgCodeEnum.INTERFACE_NOT_EXISTS);
                    }
                    return null;
                }).orElse(null);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
