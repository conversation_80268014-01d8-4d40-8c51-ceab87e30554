package com.cmpay.payment.service.channel.aplipay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.dto.alipay.BaseRsp;
import com.cmpay.payment.dto.alipay.TradeFastpayRefundQueryReq;
import com.cmpay.payment.dto.alipay.TradeFastpayRefundQueryRsp;
import com.cmpay.payment.entity.AlipayRefundJrnDO;
import com.cmpay.payment.service.TradeCommonService;
import com.cmpay.payment.service.channel.aplipay.AlipayRefundQueryChannelService;
import com.cmpay.payment.service.ext.ExtAlipayRefundJrnService;
import com.cmpay.payment.util.PaymentUtils;
import feign.RetryableException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created on 2018/12/10
 *
 * @author: wulinfeng
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class AlipayRefundQueryChannelServiceImpl implements AlipayRefundQueryChannelService {

    private static final Logger logger = LoggerFactory.getLogger(AlipayRefundQueryChannelServiceImpl.class);
    @Autowired
    ExtAlipayRefundJrnService extAlipayRefundJrnService;

    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Autowired
    private TradeCommonService tradeCommonService;
    private static final String CODE_SUCCESS = "10000";

    @Override
    public void alipayBaseRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {

        logger.info("=====================Alipay Refund Order Query Start!!=====================");

        AlipayRefundJrnDO queryAlipayRefundJrnDO = new AlipayRefundJrnDO();
        queryAlipayRefundJrnDO.setTradeOrderNo(refundOrderQueryBO.getRefundOrder().getTradeOrderNo());
        //  查询支付宝退款流水
        AlipayRefundJrnDO alipayRefundJrnDO = extAlipayRefundJrnService.load(queryAlipayRefundJrnDO);
        if (JudgeUtils.isNull(alipayRefundJrnDO)) {
            logger.info("Alipay Journal Record Not Exists,tradeOrderNo is {},msgcd is {}", MsgCodeEnum.MERCHANT_JOURNAL_INFO_INSERT_ERROR);
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_JOURNAL_INFO_INSERT_ERROR);
        }
        logger.info("Alipay Journal Record Query Success,tradeOrderNo is {}", alipayRefundJrnDO.getTradeOrderNo());

        // 支付宝退款流水加锁
        extAlipayRefundJrnService.lock(alipayRefundJrnDO);
        logger.info("Alipay Journal Record For Update Success,tradeOrderNo is {}", alipayRefundJrnDO.getTradeOrderNo());

        try {

            // 调用支付宝退款查询接口
            TradeFastpayRefundQueryReq refundQueryReq = new TradeFastpayRefundQueryReq();
            refundQueryReq.setOutTradeNo(refundOrderQueryBO.getOriginalPayTradeOrder().getTradeOrderNo());
            refundQueryReq.setOutRequestNo(refundOrderQueryBO.getRefundOrder().getBankOrderNo());
            GenericRspDTO<Response> response = this.nrtCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_REFUND_QUERY.getName(), refundQueryReq));

            Response body = response.getBody();

            alipayRefundJrnDO.setRemark(refundOrderQueryBO.getStatus());
            Optional.ofNullable(body)
                    .ifPresent(b -> {
                        refundOrderQueryBO.setAlipayRefundQueryRsp(b);
                        alipayRefundJrnDO.setReturnMsg(b.getMsgCode());
                        alipayRefundJrnDO.setReturnMsgInfo(b.getMsgInfo());
                    });

            if (StringUtils.contains(response.getMsgCd(), ContractConstants.SENDGW_SUCCESS)) {
                TradeFastpayRefundQueryRsp tradeFastpayRefundQueryRsp = (TradeFastpayRefundQueryRsp) response.getBody().getResult();
                if (JudgeUtils.equals(tradeFastpayRefundQueryRsp.getCode(), CODE_SUCCESS)) {
                    // 支付宝返回成功走正常退款查询流程
                    alipayRefundJrnDO.setReturnMsg(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
                    alipayRefundJrnDO.setReturnMsgInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
                    if (JudgeUtils.isNotEmpty(tradeFastpayRefundQueryRsp.getRefundAmount())) {
                        // 支付宝退款成功
                        refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
                        alipayRefundJrnDO.setTradeStatus(OrderStatusEnum.REFUND_SUCCESS.name());
                        //取完成时间
                        refundOrderQueryBO.getRefundOrder().setAccountDate(DateTimeUtils.getCurrentDateStr());
                    } else {
                        // 支付订单号成功，退款订单号错误时，响应成功，但无订单信息
                        tradeCommonService.handleRefundOrderNotExist(refundOrderQueryBO,MsgCodeEnum.REFUND_ORDER_NOT_EXISTS);
                        alipayRefundJrnDO.setTradeStatus(refundOrderQueryBO.getStatus());
                    }
                }
            } else {
                // 支付宝失败
                refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
                alipayRefundJrnDO.setTradeStatus(OrderStatusEnum.REFUND_FAIL.name());
                //保存报错信息
                if(JudgeUtils.isNotNull((response.getBody().getResult()))){
                    refundOrderQueryBO.setErrMsgCd(((BaseRsp)response.getBody().getResult()).getSubCode());
                    refundOrderQueryBO.setErrMsgInfo(((BaseRsp)response.getBody().getResult()).getSubMsg());
                }
            }

        } catch (RetryableException e) {
            if(e.getMessage().contains(CommonConstant.READ_TIMED_OUT)){
                alipayRefundJrnDO.setReturnMsg(MsgCodeEnum.REQUEST_RETURN_TIME_OUT.getMsgCd());
                alipayRefundJrnDO.setReturnMsgInfo(MsgCodeEnum.REQUEST_RETURN_TIME_OUT.getMsgInfo());
            } else {
                alipayRefundJrnDO.setReturnMsg(MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd());
                alipayRefundJrnDO.setReturnMsgInfo(MsgCodeEnum.REFUND_SYS_ERROR.getMsgInfo());
            }
            throw e;
        } catch (Exception e) {

            logger.info("Alipay Order Query Exception,tradeOrderNo is {},exception is {}",
                    alipayRefundJrnDO.getTradeOrderNo(), e);
            //系统异常，比如网络异常 记录异常日志
            alipayRefundJrnDO.setReturnMsg(MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd());
            alipayRefundJrnDO.setReturnMsgInfo(MsgCodeEnum.REFUND_SYS_ERROR.getMsgInfo());
            throw e;

        } finally {

            alipayRefundJrnDO.setReturnDate(DateTimeUtils.getCurrentDateStr());
            alipayRefundJrnDO.setReturnTime(DateTimeUtils.getCurrentTimeStr());
            //将异常信息更新到 alipay退款流水记录
            extAlipayRefundJrnService.updateByNewTranscation(alipayRefundJrnDO);
            logger.info("Alipay Journal Record Update Success,tradeOrderNo is {}",
                    alipayRefundJrnDO.getTradeOrderNo());
            if (!MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd().equals(alipayRefundJrnDO.getReturnMsg())) {
                // 报错退出
                BusinessException.throwBusinessException(alipayRefundJrnDO.getReturnMsg());
            }

        }

        logger.info("=====================Alipay Refund Order Query End!!=====================");

    }

    @Override
    public void send(RefundOrderQueryBO refundBO) {
        alipayBaseRefundQuery(refundBO);
    }

}
