package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.dcep.DcepRefundService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/5/7
 */
@Service
public class DcepRefundListenerServiceImpl extends PaymentListenerService<RefundBO> {

    @Autowired
    private ApplicationContext applicationContext;

    @EventListener
    public void handlerDcepRefund(RefundBO refundBO) {
        super.execute(refundBO);
    }

    @Override
    protected boolean checkChannelExecutable(RefundBO refundBO) {
        return Optional.ofNullable(refundBO)
                .map(RefundBO::getOriginalPayTradeOrder)
                .filter(tradeOrder -> StringUtils.equals(tradeOrder.getAimProductCode(), PaymentWayEnum.DCEPPAY.name().toLowerCase()))
                .isPresent();
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(RefundBO refundBO) {

        return Optional.ofNullable(refundBO)
                .map(RefundBO::getOriginalPayTradeOrder)
                .map(TradeOrderDO::getPayWayCode)
                .map(String::toUpperCase)
                .map(PaymentSceneEnum::valueOf)
                .map(paymentScene -> {
                    switch (paymentScene) {
                        case TOKEN:
                        case MMPAY:
                        case JSAPIPAY:
                        case MICROPAY:
                        case SCANPAY:
                        case APP:
                            return getBean(DcepRefundService.class);
                        default:
                            return null;
                    }
                }).orElse(null);

    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
