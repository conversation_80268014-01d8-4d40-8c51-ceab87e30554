package com.cmpay.payment.service.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.bo.RpmOnlineRefundBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.dto.cmpay.CmpayOnlineRefundQueryReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayOnlineRefundQueryRspDTO;
import com.cmpay.payment.entity.CmpayRefundJrnDO;
import com.cmpay.payment.service.TradeCommonService;
import com.cmpay.payment.service.channel.cmpay.CmpayCommonService;
import com.cmpay.payment.service.channel.cmpay.CmpayContractRefundQueryChannelService;
import com.cmpay.payment.service.ext.ExtCmpayRefundJrnService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created on 2018/12/4
 *
 * @author: lixiang
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayContractRefundQueryChannelServiceImpl implements CmpayContractRefundQueryChannelService {

    private static final Logger logger = LoggerFactory.getLogger(CmpayCloudRefundQueryChannelServiceImpl.class);


    @Autowired
    ExtCmpayRefundJrnService extCmpayRefundJrnService;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Autowired
    private CmpayCommonService cmpayCommonService;
    @Autowired
    private TradeCommonService tradeCommonService;

    @Override
    public void send(RefundOrderQueryBO refundOrderQueryBO) {
        cmpayContractRefundQuery(refundOrderQueryBO);
    }

    @Override
    public void cmpayContractRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {
        CmpayRefundJrnDO querycmpayJournalDO = new CmpayRefundJrnDO();
        querycmpayJournalDO.setTradeOrderNo(refundOrderQueryBO.getRefundOrder().getTradeOrderNo());
        //  查询和包退款流水
        CmpayRefundJrnDO cmpayRefundJrnDO = extCmpayRefundJrnService.load(querycmpayJournalDO);
        if (JudgeUtils.isNull(cmpayRefundJrnDO)) {
            // 处理历史数据
            RefundBO refundBO = new RefundBO();
            refundBO.setRefundOrder(refundOrderQueryBO.getRefundOrder());
            refundBO.setOriginalPayTradeOrder(refundOrderQueryBO.getOriginalPayTradeOrder());
            refundBO.setRefundAmount(refundOrderQueryBO.getRefundOrder().getOrderAmount());
            cmpayRefundJrnDO=extCmpayRefundJrnService.insertByNewTranscation(refundBO);
        }
        // 和包退款流水加锁
        extCmpayRefundJrnService.lock(cmpayRefundJrnDO);
        try {
            // 调用和包远程退款查询接口
            RpmOnlineRefundBO cmpayOnlineRefundBO = onlineRefundQuery(refundOrderQueryBO);
            cmpayRefundJrnDO.setReturnMsg(cmpayOnlineRefundBO.getMessageCode());
            cmpayRefundJrnDO.setReturnMsgInfo(cmpayOnlineRefundBO.getMessageInfo());
            // 退款状态
            if (OrderStatusEnum.REFUND_SUCCESS.name().equals(cmpayOnlineRefundBO.getOrderStatus())
                    || OrderStatusEnum.REFUND_FAIL.name().equals(cmpayOnlineRefundBO.getOrderStatus())) {
                refundOrderQueryBO.setStatus(cmpayOnlineRefundBO.getOrderStatus());
                if (OrderStatusEnum.REFUND_FAIL.name().equals(cmpayOnlineRefundBO.getOrderStatus())) {
                    refundOrderQueryBO.setErrMsgCd(cmpayOnlineRefundBO.getErrMsgCd());
                    refundOrderQueryBO.setErrMsgInfo(cmpayOnlineRefundBO.getErrMsgInfo());
                }
                refundOrderQueryBO.getRefundOrder().setAccountDate(cmpayOnlineRefundBO.getAccountDate());
                cmpayRefundJrnDO.setTradeStatus(refundOrderQueryBO.getStatus());
            } else if (StringUtils.equals(MsgCodeEnum.CMPAY_ONLINE_REFUND_NOT_EXISTS.getMsgCd(), cmpayOnlineRefundBO.getMessageCode())) {
                // 和包返回订单不存在，如果请求时间超过30分钟记为退款失败
                tradeCommonService.handleCmpayRefundOrderNotExist(refundOrderQueryBO,cmpayRefundJrnDO,MsgCodeEnum.CMPAY_ONLINE_REFUND_NOT_EXISTS);
            }
        } catch (Exception e) {
            //系统异常，比如网络异常 记录异常日志
            cmpayCommonService.cmapyThrowClassHandle(cmpayRefundJrnDO,e);
            throw e;
        } finally {
            cmpayRefundJrnDO.setReturnDate(DateTimeUtils.getCurrentDateStr());
            cmpayRefundJrnDO.setReturnTime(DateTimeUtils.getCurrentTimeStr());
            //将异常信息更新到 cmpay退款流水记录
            extCmpayRefundJrnService.updateByNewTranscation(cmpayRefundJrnDO);
        }
        logger.info("=====================Cmpay Cloud Refund Order Query End!!=====================");
    }

    private RpmOnlineRefundBO onlineRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {
        RpmOnlineRefundBO refundQueryBO = new RpmOnlineRefundBO();
        CmpayOnlineRefundQueryReqDTO onlineRefundQueryReqDTO = new CmpayOnlineRefundQueryReqDTO();
        onlineRefundQueryReqDTO.setMerchantOrderNo(refundOrderQueryBO.getOriginalPayTradeOrder().getBankOrderNo());
        onlineRefundQueryReqDTO.setMerchantRefundSequence(refundOrderQueryBO.getRefundOrder().getBankOrderNo());
        onlineRefundQueryReqDTO.setMerchantId(refundOrderQueryBO.getRefundOrder().getBankMerchantNo());
        onlineRefundQueryReqDTO.setMerchantOrderDate(refundOrderQueryBO.getOriginalPayTradeOrder().getOrderDate());
        onlineRefundQueryReqDTO.setSignType(refundOrderQueryBO.getSignMethod());
        onlineRefundQueryReqDTO.setHmac(refundOrderQueryBO.getContractSecureValue());
        onlineRefundQueryReqDTO.setVersion(CommonConstant.CMPAY_ONLINE_PAYMENT_VERSION);
        onlineRefundQueryReqDTO.setType(CommonConstant.CMPAY_REFUND_QUERY_TYPE);
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(CmpayPayChannelEnum.CMPAY_ONLINE);
        request.setBusiType(CmpayPayChannelEnum.CMPAY_ONLINE_REFUND_QUERY.getName());
        request.setSource(CmpayPayChannelEnum.CMPAY_ONLINE);
        request.setTarget(onlineRefundQueryReqDTO);
        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleOnlineRefundQuery(refundQueryBO, (CmpayOnlineRefundQueryRspDTO) result));
        return refundQueryBO;
    }

    private void handleOnlineRefundQuery(RpmOnlineRefundBO refundQueryBO, CmpayOnlineRefundQueryRspDTO onlineRefundQueryRspDTO) {
        if (JudgeUtils.isNull(onlineRefundQueryRspDTO)) {
            refundQueryBO.setMessageCode(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgCd());
            refundQueryBO.setMessageInfo(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgInfo());
            return;
        }
        refundQueryBO.setOrderStatus(onlineRefundQueryRspDTO.getRefundStatus());
        if (OrderStatusEnum.REFUND_SUCCESS.name().equals(onlineRefundQueryRspDTO.getRefundStatus())) {
            refundQueryBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            refundQueryBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
        } else if (OrderStatusEnum.REFUND_FAIL.name().equals(onlineRefundQueryRspDTO.getRefundStatus())) {
            refundQueryBO.setErrMsgCd(onlineRefundQueryRspDTO.getMsgCd());
            refundQueryBO.setErrMsgInfo(onlineRefundQueryRspDTO.getMsgInfo());
            refundQueryBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            refundQueryBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
        } else if (OrderStatusEnum.REFUND_WAIT.name().equals(onlineRefundQueryRspDTO.getRefundStatus())
                || OrderStatusEnum.REFUND_ACCEPT.name().equals(onlineRefundQueryRspDTO.getRefundStatus())) {
            refundQueryBO.setOrderStatus(OrderStatusEnum.REFUND_WAIT.name());
            refundQueryBO.setMessageCode(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgCd());
            refundQueryBO.setMessageInfo(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgInfo());
        } else {
            if (MsgCodeEnum.CMPAY_ONLINE_REFUND_NOT_EXISTS.getMsgCd().equals(onlineRefundQueryRspDTO.getMsgCd())) {
                refundQueryBO.setMessageCode(MsgCodeEnum.CMPAY_ONLINE_REFUND_NOT_EXISTS.getMsgCd());
                refundQueryBO.setMessageInfo(MsgCodeEnum.CMPAY_ONLINE_REFUND_NOT_EXISTS.getMsgInfo());
            } else {
                refundQueryBO.setMessageCode(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgCd());
                refundQueryBO.setMessageInfo(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgInfo());
            }
        }
    }
}
