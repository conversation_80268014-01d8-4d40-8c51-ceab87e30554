package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.constant.protocol.DcepOrderStatusEnum;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.DcepRefundOrderQueryReq;
import com.cmpay.payment.dto.dcep.DcepRefundOrderQueryRsp;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.TradeCommonService;
import com.cmpay.payment.service.channel.dcep.DcepRefundQueryService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/5/7
 */
@Service
public class DcepRefundQueryServiceImpl extends AbstractDcepRequestServiceImpl implements DcepRefundQueryService {

    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;
    @Autowired
    private TradeCommonService tradeCommonService;

    private static final String ORDER_TYPE = "refund";

    @Override
    public void handlerDcepRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {
        refundOrderQueryBO.setPayWayCode(refundOrderQueryBO.getRefundOrder().getPayWayCode().toLowerCase());
        refundOrderQueryBO.setBusinessType(DcepPaymentChannelEnum.REFUND_ORDER_QUERY.getName());
        DcepRefundOrderQueryReq refundOrderQueryReq = new DcepRefundOrderQueryReq();
        refundOrderQueryReq.setOrgNo(dcepPaymentProperties.getOrgNo());
        buildRequestDto(refundOrderQueryReq, refundOrderQueryBO, refundOrderQueryBO.getOriginalPayTradeOrder().getBankMerchantNo());
        refundOrderQueryReq.setSubMerchantNo(refundOrderQueryBO.getOrderPaymentAttachDO().getSubMerchantId());
        refundOrderQueryReq.setOriOutTranNo(refundOrderQueryBO.getOriginalPayTradeOrder().getOutTradeNo());
        refundOrderQueryReq.setOutRefundNo(refundOrderQueryBO.getRefundOrder().getOutTradeNo());
        refundOrderQueryReq.setTranDate(refundOrderQueryBO.getRefundOrder().getOrderDate());
        buildDcepRequest(refundOrderQueryReq, refundOrderQueryBO, refundOrderQueryBO.getBusinessType());
    }

    @Override
    public void send(RefundOrderQueryBO refundOrderQueryBO) {
        handlerDcepRefundQuery(refundOrderQueryBO);
    }

    @Override
    public BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        if (!JudgeUtils.equalsAny(abstractDcepRsp.getCode(), DcepConstants.SUCCESS_CODE,DcepConstants.REFUND_NOT_EXIST)) {
            BusinessException.throwBusinessException(MsgCodeEnum.DCEP_PAY_QUERY_FAIL);
        }
        RefundOrderQueryBO refundOrderQueryBO = (RefundOrderQueryBO) baseDcepHandlerBO;
        DcepRefundOrderQueryRsp refundOrderQueryRsp = (DcepRefundOrderQueryRsp) abstractDcepRsp;
        if(JudgeUtils.equals(abstractDcepRsp.getCode(), DcepConstants.REFUND_NOT_EXIST)){
            tradeCommonService.handleRefundOrderNotExist(refundOrderQueryBO,MsgCodeEnum.REFUND_ORDER_NOT_EXISTS);
            // 订单不存在时，refundOrderQueryRsp.getData()为null
            return refundOrderQueryBO;
        }else {
            DcepOrderStatusEnum enumName = DcepOrderStatusEnum.getEnumName(ORDER_TYPE, refundOrderQueryRsp.getData().getRefundStatus());
            refundOrderQueryBO.setStatus(enumName != null ? enumName.name() : DcepOrderStatusEnum.REFUND_WAIT.name());
        }
        refundOrderQueryBO.getRefundOrder().setRefundId(refundOrderQueryRsp.getData().getRefundSeq());
        refundOrderQueryBO.getRefundOrder().setAccountDate(refundOrderQueryRsp.getData().getRefundSuccessDate());
        String finishDateTime = refundOrderQueryRsp.getData().getRefundSuccessDate() + refundOrderQueryRsp.getData().getRefundSuccessTime();
        refundOrderQueryBO.getRefundOrder().setOrderCompleteTime(StringUtils.isBlank(finishDateTime) || finishDateTime.length() != 14 ? DateTimeUtils.getCurrentDateStr() : finishDateTime);
        return refundOrderQueryBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {
        DcepRefundOrderQueryReq refundOrderQueryReq = (DcepRefundOrderQueryReq) abstractDcepReq;
        refundOrderQueryReq.setOrgNo(dcepPaymentProperties.getNewOrgNo());
        baseDcepHandlerBO.setBusinessType(DcepPaymentChannelEnum.DCEP_REFUND_ORDER_QUERY.getName());
    }
}
