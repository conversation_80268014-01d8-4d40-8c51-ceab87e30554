package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.integralpay.IntegralRefundService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created on 2021/08/31
 * 积分支付监听类
 *
 * @author: luo wenzhan
 */
@Service
@Transactional(propagation = Propagation.REQUIRES_NEW)
public class IntegralRefundOrderListener extends PaymentListenerService<RefundBO> {
	@Autowired
    ApplicationContext applicationContext;

	@Override
	protected ApplicationContext getApplicationContext() {
		return applicationContext;
	}

	@EventListener
	@Override
	public void execute(RefundBO refundBO) {
		super.execute(refundBO);
	}
	@Override
	protected boolean checkChannelExecutable(RefundBO refundBO) {
		return Optional.ofNullable(refundBO)
				.map(RefundBO::getOriginalPayTradeOrder)
				.filter(tradeOrder -> StringUtils.equals(PaymentWayEnum.INTEGRALPAY.name().toLowerCase(), tradeOrder.getAimProductCode()))
				.isPresent();
	}

	@Override
	protected PaymentChannelService determinateChannelExecuteBean(RefundBO refundBO) {
		return getBean(IntegralRefundService.class);
	}
}
