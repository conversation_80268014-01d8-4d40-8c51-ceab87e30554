package com.cmpay.payment.service;

import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.entity.CmpayRefundJrnDO;

/**
 * @author： pengAnHai
 * @date： 2023-12-04
 * @description：
 */
public interface TradeCommonService {

    /**
     * 退款订单不存在时的处理
     * @param refundOrderQueryBO
     * @param msgCodeEnum
     */
    void handleRefundOrderNotExist(RefundOrderQueryBO refundOrderQueryBO, MsgCodeEnum msgCodeEnum);

    /**
     * 和包退款订单不存在时的处理
     * @param refundOrderQueryBO
     * @param cmpayRefundJrnDO
     * @param msgCodeEnum
     */
    void handleCmpayRefundOrderNotExist(RefundOrderQueryBO refundOrderQueryBO, CmpayRefundJrnDO cmpayRefundJrnDO, MsgCodeEnum msgCodeEnum);

}
