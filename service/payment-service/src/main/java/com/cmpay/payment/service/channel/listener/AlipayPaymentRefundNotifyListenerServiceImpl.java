package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.RefundNotifyBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.aplipay.AlipayRefundNotifyChannelService;
import com.cmpay.payment.service.channel.PaymentChannelService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created on 2018/12/13
 *
 * @author: wulinfeng
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class AlipayPaymentRefundNotifyListenerServiceImpl extends PaymentListenerService<RefundNotifyBO> {

    @Autowired
    ApplicationContext applicationContext;

    @EventListener
    @Override
    public void execute(RefundNotifyBO eventBO) {
        super.execute(eventBO);
    }

    @Override
    protected boolean checkChannelExecutable(RefundNotifyBO refundNotifyBO) {
        return Optional.ofNullable(refundNotifyBO)
                .map(RefundNotifyBO::getPaymentRoute)
                .map(route->{return StringUtils.equalsIgnoreCase(PaymentWayEnum.ALIPAY.name(), route);})
                .orElse(false);
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(RefundNotifyBO refundNotifyBO) {
        return getBean(AlipayRefundNotifyChannelService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }

}
