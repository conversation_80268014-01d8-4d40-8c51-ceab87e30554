package com.cmpay.payment.service;

import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.bo.RefundOrderBO;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;

import java.util.List;

/**
 * Created on 2018/11/30
 *
 * @author: sun_zhh
 */
public interface RefundServcie {

    /**
     * 退款交易
     *
     * @param refundBO
     * @return RefundBO
     */
    RefundBO refund(RefundBO refundBO);

    /**
     * 新退款交易
     *
     * @param refundBO
     * @return RefundBO
     */
    RefundOrderBO refundOrder(RefundOrderBO refundBO);

    /**
     * 校验是否满足退款条件
     * @param originalPayTradeOrderDO
     * @param refundParam
     */
    void checkRefundReq(TradeOrderDO originalPayTradeOrderDO, RefundBO refundParam);

    /**
     * 退款成功次数限制
     * @param tradeOrderDO
     * @param refundBO
     * @return true：通过校验，超过次数抛出异常
     */
    boolean refundFrequencyLimit(TradeOrderDO tradeOrderDO, RefundBO refundBO);

    /**
     * 检查退款订单的状态
     * 此方法用于验证在给定的退款订单列表中，是否存在待处理或等待退款状态的退款订单
     * 如果存在待处理或等待退款状态的退款订单，则抛出业务异常，提示退款未完成
     *
     * @param refundOrderList 退款订单列表，用于检查是否存在未处理完成的退款订单
     */
    void checkRefundOrderStatus(List<RefundOrderDO> refundOrderList);
}
