package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.RefundOrderCloneBO;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.bo.data.OrderFeeBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.RefundFeeWayEnum;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.CompleteOrderSplitService;
import com.cmpay.payment.service.RefundOrderHandleService;
import com.cmpay.payment.service.TradeNotifyService;
import com.cmpay.payment.service.data.IDataFeeService;
import com.cmpay.payment.service.ext.ExtRefundOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author： PengAnHai
 * @date： 2024-08-22
 * @description：退款订单处理类
 * @modifiedBy：
 * @version: 1.0
 */
@Slf4j
@Service
public class RefundOrderHandleServiceImpl implements RefundOrderHandleService {

    @Autowired
    private ExtRefundOrderService refundOrderService;
    @Autowired
    private CompleteOrderSplitService splitService;
    @Autowired
    private IDataFeeService dataFeeService;
    @Autowired
    private TradeNotifyService tradeNotifyService;

    @Override
    public RefundOrderQueryBO setPayOrderStatus(RefundOrderQueryBO refundQueryBO) {
        // 计算已退款总金额
        sumTotalMoney(refundQueryBO);
        // 获取原支付订单对象
        TradeOrderDO tradeOrderDO = refundQueryBO.getOriginalPayTradeOrder();
        // 获取退款订单对象
        RefundOrderDO refundOrderDO = refundQueryBO.getRefundOrder();
        // 本次退款总金额 = 已退款金额 + 本次退款金额
        BigDecimal totalRefundAmountCurrent = refundQueryBO.getTotalRefundMoney().add(refundOrderDO.getOrderAmount());
        if (totalRefundAmountCurrent.compareTo(tradeOrderDO.getRealAmount()) == 0) {
            // 若退款金额等于支付订单金额则全额退款
            tradeOrderDO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
        } else if (totalRefundAmountCurrent.compareTo(tradeOrderDO.getRealAmount()) < 0) {
            // 若退款金额小于支付订单金额则部分退款
            tradeOrderDO.setStatus(OrderStatusEnum.REFUND_PART.name());
        } else {
            // 若退款金额超出支付订单金额则记录日志并抛出异常
            log.info("Refund Amount Beyond Payment Amount,refundAmount is {},totalAmount is {},totalRefundAmount is {}",
                    refundOrderDO.getOrderAmount(), tradeOrderDO.getOrderAmount(), totalRefundAmountCurrent);
            BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
        }
        // 更新支付订单的退款次数
        tradeOrderDO.setRefundTimes(tradeOrderDO.getRefundTimes() + 1);
        // 更新支付订单的成功退款金额
        tradeOrderDO.setSuccessRefundAmount(tradeOrderDO.getSuccessRefundAmount().add(refundOrderDO.getOrderAmount()));
        // 返回更新后的退款订单查询业务对象
        return refundQueryBO;
    }

    @Override
    public RefundOrderQueryBO setPayOrderStatusRefFail(RefundOrderQueryBO refundQueryBO) {
        // 计算已退款总金额
        sumTotalMoney(refundQueryBO);
        // 获取原支付订单对象
        TradeOrderDO tradeOrderDO = refundQueryBO.getOriginalPayTradeOrder();
        // 判断已退款总金额是否为零
        if (refundQueryBO.getTotalRefundMoney().compareTo(BigDecimal.ZERO) == 0) {
            // 若已退款金额为零，则设置支付订单状态为交易成功
            tradeOrderDO.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
        } else {
            // 若已退款金额不为零，则设置支付订单状态为部分退款
            tradeOrderDO.setStatus(OrderStatusEnum.REFUND_PART.name());
        }
        // 返回更新后的退款订单查询业务对象
        return refundQueryBO;
    }

    @Override
    public RefundOrderQueryBO setOrderFeeAmount(RefundOrderQueryBO refundQueryBO) {
        // 获取原支付订单对象
        TradeOrderDO tradeOrderDO = refundQueryBO.getOriginalPayTradeOrder();
        // 获取退款订单对象
        RefundOrderDO refundOrderDO = refundQueryBO.getRefundOrder();
        // 计算退款手续费
        if (tradeOrderDO.getOrderFeeAmount().compareTo(BigDecimal.ZERO) > 0
                && !JudgeUtils.equals(tradeOrderDO.getRefundFeeWay(), RefundFeeWayEnum.NO_REFUND_FEE_WAY.getDesc())) {
            // 如果是分账订单
            if (splitService.checkSplitOrder(refundOrderDO, tradeOrderDO)) {
                // 计算退款分账服务费
                splitService.splitOrderRefundFee(refundOrderDO, tradeOrderDO);
            } else {
                // 创建订单手续费业务对象
                OrderFeeBO orderFeeBO = new OrderFeeBO();
                orderFeeBO.setOrderAmount(tradeOrderDO.getOrderAmount());
                orderFeeBO.setRefundAmount(tradeOrderDO.getOrderAmount());
                orderFeeBO.setSuccessRefundFeeAmount(refundQueryBO.getTotalRefundFeeAmount());
                orderFeeBO.setSuccessRefundAmount(refundQueryBO.getTotalRefundMoney());
                orderFeeBO.setRefundFeeAmount(BigDecimal.ZERO);
                orderFeeBO.setOrderFeeAmount(tradeOrderDO.getOrderFeeAmount());
                orderFeeBO.setOrderRate(tradeOrderDO.getOrderRate());
                // 调用数据费用服务来计算退款手续费
                orderFeeBO = dataFeeService.refundFeeCalculate(orderFeeBO);
                // 设置退款订单的手续费金额
                refundOrderDO.setOrderFeeAmount(orderFeeBO.getRefundFeeAmount());
            }
        }
        // 返回更新后的退款订单查询业务对象
        return refundQueryBO;
    }


    @Override
    public boolean notifyMerchant(RefundOrderDO refundOrderDO) {
            // 创建退款订单克隆业务对象
            RefundOrderCloneBO refundOrderCloneBO = new RefundOrderCloneBO();
            // 将退款订单数据复制到克隆对象中
            BeanUtils.copyProperties(refundOrderDO, refundOrderCloneBO);
            // 调用贸易通知服务处理退款通知
            tradeNotifyService.handleRefundNotify(refundOrderCloneBO);
            // 通知发送成功，返回true
            return true;
    }

    /**
     * 计算总退款金额
     *
     * @param refundQueryBO 退款查询业务对象
     * @return 更新后的退款查询业务对象，包括总退款金额和总退款手续费金额
     */
    private RefundOrderQueryBO sumTotalMoney(RefundOrderQueryBO refundQueryBO) {
        // 获取原支付交易订单
        TradeOrderDO tradeOrderDO = refundQueryBO.getOriginalPayTradeOrder();
        // 创建退款订单查询对象，设置商户号、原订单号和退款成功状态
        RefundOrderDO refundOrderLsQuery = new RefundOrderDO();
        refundOrderLsQuery.setMerchantNo(tradeOrderDO.getMerchantNo());
        refundOrderLsQuery.setOrgOrderNo(tradeOrderDO.getTradeOrderNo());
        refundOrderLsQuery.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
        // 查询已退款的订单列表
        List<RefundOrderDO> refundOrderLs = refundOrderService.find(refundOrderLsQuery);
        // 初始化总退款金额和总退款手续费金额为零
        BigDecimal totalRefundMoney = BigDecimal.ZERO;
        BigDecimal totalRefundFeeAmount = BigDecimal.ZERO;
        if (!refundOrderLs.isEmpty()) {
            // 计算总退款金额(合计每个退款订单的订单金额)
            totalRefundMoney = refundOrderLs.stream()
                    .map(RefundOrderDO::getOrderAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 计算总退款手续费金额(合计每个退款订单的订单手续费金额)
            totalRefundFeeAmount = refundOrderLs.stream()
                    .map(RefundOrderDO::getOrderFeeAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        // 记录日志，打印已退款总金额、原交易订单号和原订单总金额
        log.info("Already Refund Total Amount is {}, orgTradeNO is {}, totalAmount is {}",
                totalRefundMoney, tradeOrderDO.getTradeOrderNo(), tradeOrderDO.getOrderAmount());
        // 更新退款查询业务对象的总退款金额和总退款手续费金额
        refundQueryBO.setTotalRefundMoney(totalRefundMoney);
        refundQueryBO.setTotalRefundFeeAmount(totalRefundFeeAmount);
        // 返回更新后的退款查询业务对象
        return refundQueryBO;
    }

}
