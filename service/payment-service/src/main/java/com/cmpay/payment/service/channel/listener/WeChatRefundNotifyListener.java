package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.RefundNotifyBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.wechat.WeChatRefundNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatRefundNotifyListener extends PaymentListenerService<RefundNotifyBO> {


    @Autowired
    ApplicationContext applicationContext;

    @EventListener
    public void handleWeChatRefundNotify(RefundNotifyBO refundNotifyBO) {
        execute(refundNotifyBO);
    }

    @Override
    protected boolean checkChannelExecutable(RefundNotifyBO refundNotifyBO) {
        return Optional.ofNullable(refundNotifyBO)
                .map(RefundNotifyBO::getRoute)
                .filter(PaymentWayEnum.WECHAT::equals)
                .isPresent();
    }


    @Override
    protected PaymentChannelService determinateChannelExecuteBean(RefundNotifyBO refundNotifyBO) {
        return getBean(WeChatRefundNotifyService.class);
    }


    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
