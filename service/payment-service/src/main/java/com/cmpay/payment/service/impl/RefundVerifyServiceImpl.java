package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.bo.TradeOrderCloneBO;
import com.cmpay.payment.bo.config.SubMerchantRefreshAmountBO;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.dao.IJkThirdPayParamExtDao;
import com.cmpay.payment.entity.JkThirdPayParamDO;
import com.cmpay.payment.entity.OrderPaymentAttachDO;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.RefundServcie;
import com.cmpay.payment.service.RefundVerifyService;
import com.cmpay.payment.service.SubMerchantIncomeService;
import com.cmpay.payment.service.ext.ExtPayOrderAttachService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.ExtRefundOrderService;
import com.cmpay.payment.utils.CloneableUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import com.cmpay.payment.utils.PaymentUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * @date 2023-11-23 17:00
 * <AUTHOR>
 * @Version 1.0
 */
@Service
public class RefundVerifyServiceImpl implements RefundVerifyService {
    private static final Logger logger = LoggerFactory.getLogger(RefundVerifyServiceImpl.class);

    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private IJkThirdPayParamExtDao jkThirdPayParamExtDao;
    @Autowired
    private ExtRefundOrderService refundOrderService;
    @Autowired
    private ExtPayOrderAttachService payOrderAttachService;
    @Autowired
    private SubMerchantIncomeService redisService;

    @Autowired
    private RefundServcie refundServcie;

    private static final DateTimeFormatter COMMON_YEAR_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final String PRI_FIX = "REF";
    @Override
    public void verifyOrderStatus(RefundBO refundBO) {
        refundBO.setChannelType(ChannelTypeEnum.INTERFACE.name().toLowerCase());
        refundBO.setChannelTypeDesc(ChannelTypeEnum.INTERFACE.getDesc());
        refundBO.setMsgCode(MsgCodeEnum.REFUND_ERROR.getMsgCd());
        //检查退款单号是否存在,原订单是否存在
        verifyRefundOrderExist(refundBO);
        TradeOrderDO originalPayTradeOrderDO = refundBO.getOriginalPayTradeOrder();

        if (StringUtils.equals(originalPayTradeOrderDO.getAimProductCode(), PaymentWayEnum.ICBCPAY.name().toLowerCase())) {
            refundBO.setTradeOrderNo(JhIdGenUtils.generateJhIdWithDate(DcepConstants.PAYMENT_REFUND, PRI_FIX, 15));
        }

        if (originalPayTradeOrderDO.getRealAmount().compareTo(refundBO.getRefundAmount()) != 0
                && JudgeUtils.equals(PaymentWayEnum.CMPAY.name().toLowerCase(), originalPayTradeOrderDO.getPayProductCode())
                && JudgeUtils.equals(PaymentSceneEnum.CONTRACTPAY.name().toLowerCase(), originalPayTradeOrderDO.getPayWayCode())) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_NOT_PART);
        }

        //检测是否超过退款时长
        verifyRefundTimeout(refundBO);

        //保存订单附加表信息
        if (DcepConstants.DCEP.equals(originalPayTradeOrderDO.getDcepFlag()) ||
                PaymentWayEnum.ICBCPAY.name().equalsIgnoreCase(originalPayTradeOrderDO.getPayProductCode())) {
            OrderPaymentAttachDO attachDO = new OrderPaymentAttachDO();
            attachDO.setOutTradeNo(originalPayTradeOrderDO.getOutTradeNo());
            attachDO.setOrderDate(originalPayTradeOrderDO.getRequestDate());
            OrderPaymentAttachDO orderPaymentAttachDO = payOrderAttachService.load(attachDO);
            refundBO.setOrderPaymentAttachDO(orderPaymentAttachDO);
            refundBO.setDcepFlag(originalPayTradeOrderDO.getDcepFlag());
        }

        // 判断原订单状态
        verifyOrigOrderStatus(refundBO);
    }
    @Override
    public void verifyRefundOrderExist(RefundBO refundBO) {
        //检查退款单号是否存在
        RefundOrderDO refundOrderQuery = refundOrderService.get(refundBO.getOutRequestNo());
        if (JudgeUtils.isNotNull(refundOrderQuery)) {
            //如果存在说明该款单号已发起过退款
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_ALREADY_EXIST);
        }
        //只带订单号，查询原支付订单
        TradeOrderDO originalPayTradeOrderDO = payOrderService.get(refundBO.getOutTradeNo());
        if (JudgeUtils.isNull(originalPayTradeOrderDO)) {
            //如果原支付订单不存在，则不能发起退款
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORG_ORDER_NOT_EXISTS);
        }
        //保存订单记录到参数对像里，以便后面使用
        refundBO.setOriginalPayTradeOrder(CloneableUtils.gsonDeepClone(originalPayTradeOrderDO, TradeOrderCloneBO.class));
    }
    @Override
    public void verifyRefundTimeout(RefundBO refundBO) {
        TradeOrderDO originalPayTradeOrderDO = refundBO.getOriginalPayTradeOrder();
        //检测是否超过退款时长
        JkThirdPayParamDO refundOrderParamDO = new JkThirdPayParamDO();

        refundOrderParamDO.setAimProductCode(originalPayTradeOrderDO.getAimProductCode());
        refundOrderParamDO.setPayProductCode(originalPayTradeOrderDO.getPayProductCode());
        refundOrderParamDO.setPayWayCode(originalPayTradeOrderDO.getPayWayCode());
        refundOrderParamDO.setParamMark(ThirdPayWayParamEnum.REFUND_PARAM.name());
        JkThirdPayParamDO refund = jkThirdPayParamExtDao.findRecord(refundOrderParamDO);
        if (JudgeUtils.isNotNull(refund)) {
            logger.info("退款时长检测===");
            String orderDate = originalPayTradeOrderDO.getOrderDate();
            Long refundValue = Long.parseLong(refund.getParamValue());
            String nowDate = refundBO.getTradeDate();
            String afterDate = plusDays(orderDate, refundValue);
            logger.info("======================afterDate is {}=========================", afterDate);
            if (nowDate.compareTo(afterDate) > 0) {
                BusinessException.throwBusinessException(MsgCodeEnum.ORDER_REFUND_PARAM_TIME_OUT);
            }
        }
    }
    @Override
    public void verifyOrigOrderStatus(RefundBO refundBO) {
        TradeOrderDO originalPayTradeOrderDO = refundBO.getOriginalPayTradeOrder();

        //原支付订单状态不为支付成功并且不为部分退款，不允许退款
        if (!OrderStatusEnum.TRADE_SUCCESS.name().equals(originalPayTradeOrderDO.getStatus())
                && !OrderStatusEnum.REFUND_PART.name().equals(originalPayTradeOrderDO.getStatus())
                && !OrderStatusEnum.REFUND_WAIT.name().equals(originalPayTradeOrderDO.getStatus())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_STATUS_CANNOT_REFUND);
        }
    }

    @Override
    public void verifyRefundAmount(RefundBO refundBO) {
        TradeOrderDO originalPayTradeOrderDO = refundBO.getOriginalPayTradeOrder();
        // 判断是否是两位小数
        if(!PaymentUtils.isTwoDecimalPlaces(refundBO.getRefundAmount().toPlainString())){
            BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
        }
        //检查当前已登记退款金额
        RefundOrderDO refundOrder = new RefundOrderDO();
        refundOrder.setOrgOrderNo(refundBO.getOutTradeNo());
        refundOrder.setOrderDate(originalPayTradeOrderDO.getOrderDate());
        List<RefundOrderDO> refundOrderList = refundOrderService.findValidRefudnOrder(refundOrder);
        if (refundOrderList != null) {
            {
                //计算退款已登记总金额
                BigDecimal refundRegistMoney = refundOrderList.stream().map(RefundOrderDO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal refundMoney = refundRegistMoney.add(refundBO.getRefundAmount());
                if (refundMoney.compareTo(originalPayTradeOrderDO.getRealAmount()) > 0) {
                    BusinessException.throwBusinessException(MsgCodeEnum.REFUND_AMOUNT_ERROR);
                }
            }
        }
        // 全额退款，需要解析原支付订单的分账参数，在退款表登记子订单号列表；
        if (refundBO.getRefundAmount().compareTo(originalPayTradeOrderDO.getRealAmount()) == Constants.ZERO_NUMBER) {
            //如退款金额等于订单金科则为全额退款
            refundBO.setRefundFlag(Constants.ZERO);
        } else {
            refundBO.setRefundFlag(Constants.STR_ONE);
            // 检测是否存在退款未完成订单订单
            refundServcie.checkRefundOrderStatus(refundOrderList);
            // 校验退款成功次数限制
            refundServcie.refundFrequencyLimit(originalPayTradeOrderDO, refundBO);
        }
        RefundOrderDO refundOrderLsQuery = new RefundOrderDO();
        refundOrderLsQuery.setOrgOrderNo(refundBO.getOutTradeNo());
        refundOrderLsQuery.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
        List<RefundOrderDO> refundOrderLs = refundOrderService.find(refundOrderLsQuery);
        BigDecimal totalRefundMoney = BigDecimal.ZERO;
        if (refundOrderLs != null) {
            //计算退款总金额
            totalRefundMoney = refundOrderLs.stream().map(RefundOrderDO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        }
        // 本次退款总金额 =已退款金额+本次退款金额
        BigDecimal totalRefundAmountCurrent = totalRefundMoney.add(refundBO.getRefundAmount());
        if (totalRefundAmountCurrent.compareTo(originalPayTradeOrderDO.getRealAmount()) > 0) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_AMOUNT_ERROR);
        }

    }
    @Override
    public void subMerchantRefundConfine(RefundBO refundBO) {
        TradeOrderDO originalPayTradeOrderDO = refundBO.getOriginalPayTradeOrder();
        if (StringUtils.isNotEmpty(originalPayTradeOrderDO.getSubMerchantNo()) || StringUtils.isNotEmpty(refundBO.getSubMerchant())) {
            String subMerchant = StringUtils.isEmpty(refundBO.getSubMerchant()) ? originalPayTradeOrderDO.getSubMerchantNo() : refundBO.getSubMerchant();
            if (StringUtils.isNotEmpty(subMerchant) &&
                    !StringUtils.equals(originalPayTradeOrderDO.getAimProductCode(), PaymentWayEnum.CMPAY.name().toLowerCase())) {
                SubMerchantRefreshAmountBO refreshAmountBO = new SubMerchantRefreshAmountBO();
                refreshAmountBO.setSubMerchantNo(refundBO.getSubMerchant());
                refreshAmountBO.setAmount(refundBO.getRefundAmount());
                redisService.refundConfine(refreshAmountBO);
            }
        }
    }

    /**
     * 某个日期基础上,增加天数
     * @param date
     * @param days
     * @return
     */
    private static String plusDays(String date, long days) {
        LocalDate localDate = LocalDate.parse(date, COMMON_YEAR_FORMAT);
        localDate = localDate.plusDays(days);
        return localDate.format(COMMON_YEAR_FORMAT);
    }
}
