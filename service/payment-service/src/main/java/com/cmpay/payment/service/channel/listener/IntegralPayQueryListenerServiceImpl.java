package com.cmpay.payment.service.channel.listener;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.integralpay.IntegralPayQueryChannelService;
import com.cmpay.payment.service.channel.integralpay.IntegralPayTimeOutQueryChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/6 14:12
 * @description ：
 */
@Service
public class IntegralPayQueryListenerServiceImpl extends PaymentListenerService<PaymentQueryBO> {

    @Autowired
    ApplicationContext applicationContext;

    @EventListener
    @Override
    public void execute(PaymentQueryBO paymentQueryBO) {
        super.execute(paymentQueryBO);
    }

    @Override
    protected boolean checkChannelExecutable(PaymentQueryBO paymentQueryBO) {
        return Optional.ofNullable(paymentQueryBO)
                .map(PaymentQueryBO::getPaymentOrder)
                .map(TradeOrderDO::getAimProductCode)
                .map(code -> {
                    return StringUtils.equalsIgnoreCase(paymentQueryBO.getPaymentOrder().getAimProductCode(), PaymentWayEnum.INTEGRALPAY.name());
                })
                .orElse(false);
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(PaymentQueryBO paymentQueryBO) {
        if(JudgeUtils.isNull(paymentQueryBO.getPaymentOrder().getBankOrderNo()) &&
                MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT.getMsgCd().equals(paymentQueryBO.getPaymentOrder().getErrMsgCd())) {
            return getBean(IntegralPayTimeOutQueryChannelService.class);
        } else if (paymentQueryBO.getPaymentOrder().getBankOrderNo() != null){
            return getBean(IntegralPayQueryChannelService.class);
        } else {
            return null;
        }
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return this.applicationContext;
    }
}
