package com.cmpay.payment.service.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PaymentCloseOrderBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.PaymentStatusEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.constant.wechat.WeChatTradeStatusEnum;
import com.cmpay.payment.dto.wechat.WXPayCloseorderRequest;
import com.cmpay.payment.dto.wechat.WXPayCloseorderResponse;
import com.cmpay.payment.dto.wechat.WXPayOrderqueryRequest;
import com.cmpay.payment.dto.wechat.WXPayOrderqueryResponse;
import com.cmpay.payment.service.channel.wechat.WeChatCloseOrderService;
import com.cmpay.payment.service.ext.ExtCloseOrderService;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatCloseOrderServiceImpl implements WeChatCloseOrderService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatCloseOrderServiceImpl.class);

    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private ExtCloseOrderService closeOrderService;

    /**
     * 微信支付关闭订单
     *
     * @param paymentCloseOrderBO
     */
    @Override
    public PaymentCloseOrderBO weChatPaymentCloseOrder(PaymentCloseOrderBO paymentCloseOrderBO) {
        // 关闭订单前先查询订单状态
        WXPayOrderqueryRequest wxPayOrderqueryRequest = new WXPayOrderqueryRequest();
        wxPayOrderqueryRequest.setOutTradeNo(paymentCloseOrderBO.getOutTradeNo());
        wxPayOrderqueryRequest.setMchId(paymentCloseOrderBO.getBankMerchantNo());
        wxPayOrderqueryRequest.setKey(paymentCloseOrderBO.getContractSecureValue());

        Request orderQuery = new Request();
        orderQuery.setRequestId(UUID.randomUUID().toString()); //请求id
        orderQuery.setSource(WXPayChannel.ORDERQUERY.getSource()); //
        orderQuery.setRoute(WXPayChannel.ORDERQUERY.getRoute());
        orderQuery.setBusiType(WXPayChannel.ORDERQUERY.getBusType());
        orderQuery.setTarget(wxPayOrderqueryRequest);
        logger.info("WeChatCloseOrderServiceImpl Source : {} Route : {} BusiType : {}",orderQuery.getSource(),orderQuery.getRoute(),orderQuery.getBusiType());
        GenericRspDTO<Response> orderQueryResponse = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, orderQuery));

        if (JudgeUtils.isNotSuccess(orderQueryResponse)) {
            BusinessException.throwBusinessException(orderQueryResponse);
        }

        Optional.ofNullable(orderQueryResponse)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> {
                    WXPayOrderqueryResponse response = (WXPayOrderqueryResponse) result;
                    paymentCloseOrderBO.setErrMsgCd( response.getResultCode());//修改第三方报错代码
                    paymentCloseOrderBO.setErrMsgInfo(response.getReturnMsg());//修改第三方错误描述
                    if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
                        if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                            if (Objects.isNull(response.getTradeState())) {
                                //更新状态
                                paymentCloseOrderBO.setStatus(OrderStatusEnum.CLOSE_FAIL.name());
                                closeOrderService.updateByNewTransaction(paymentCloseOrderBO);
                                // 支付状态未确认不允许关闭订单
                                logger.error("query order failure, weChat return message is : {}", response.getReturnMsg());
                                BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_QUERY_ORDER_FAILURE);
                            } else {
                                WeChatTradeStatusEnum status = EnumUtils.getEnum(WeChatTradeStatusEnum.class, response.getTradeState());
                                switch (status) {
                                    case NOTPAY:
                                        // 未支付允许关闭订单
                                        break;
                                    default:
                                        //更新状态
                                        paymentCloseOrderBO.setStatus(OrderStatusEnum.CLOSE_FAIL.name());
                                        closeOrderService.updateByNewTransaction(paymentCloseOrderBO);
                                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_QUERY_ORDER_FAILURE);
                                }
                            }
                        } else {
                            logger.error("query order failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                            WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getErrCode());
                            switch (errorCodeEnum) {
                                default:
                                    //更新状态
                                    paymentCloseOrderBO.setStatus(OrderStatusEnum.CLOSE_FAIL.name());
                                    closeOrderService.updateByNewTransaction(paymentCloseOrderBO);
                                    BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_QUERY_ORDER_FAILURE);
                            }
                        }
                    } else {
                        //更新状态
                        paymentCloseOrderBO.setStatus(OrderStatusEnum.CLOSE_FAIL.name());
                        closeOrderService.updateByNewTransaction(paymentCloseOrderBO);
                        // 支付状态未确认不允许关闭订单
                        logger.error("query order failure, weChat return message is : {}", response.getReturnMsg());
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_QUERY_ORDER_FAILURE);
                    }
                });

        // 关闭订单
        WXPayCloseorderRequest wxPayCloseorderRequest = new WXPayCloseorderRequest();
        wxPayCloseorderRequest.setOutTradeNo(paymentCloseOrderBO.getOutTradeNo());
        wxPayCloseorderRequest.setMchId(paymentCloseOrderBO.getBankMerchantNo());
        wxPayCloseorderRequest.setKey(paymentCloseOrderBO.getContractSecureValue());
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.CLOSEORDER.getSource());
        request.setRoute(WXPayChannel.CLOSEORDER.getRoute());
        request.setBusiType(WXPayChannel.CLOSEORDER.getBusType());
        request.setTarget(wxPayCloseorderRequest);

        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(paymentCloseOrderBO, (WXPayCloseorderResponse) result));
        WXPayCloseorderResponse wxPayCloseorderResponse = (WXPayCloseorderResponse) genericRspDTO.getBody().getResult();

        paymentCloseOrderBO.setErrMsgCd( wxPayCloseorderResponse.getResultCode());//修改第三方报错代码
        paymentCloseOrderBO.setErrMsgInfo(wxPayCloseorderResponse.getReturnMsg());//修改第三方错误描述
        if(JudgeUtils.equals(wxPayCloseorderResponse.getResultCode(),OrderStatusEnum.SUCCESS.name())){
            paymentCloseOrderBO.setStatus(OrderStatusEnum.TRADE_CLOSED.name());
        }else {
            paymentCloseOrderBO.setStatus(OrderStatusEnum.CLOSE_FAIL.name());
        }
        //更新状态
        closeOrderService.updateByNewTransaction(paymentCloseOrderBO);
        return paymentCloseOrderBO;
    }

    @Override
    public void send(PaymentCloseOrderBO paymentCloseOrderBO) {
        weChatPaymentCloseOrder(paymentCloseOrderBO);
    }

    private void handleResult(PaymentCloseOrderBO paymentCloseOrderBO, WXPayCloseorderResponse response) {
        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                paymentCloseOrderBO.setTradeStatus(PaymentStatusEnum.TRADE_CLOSED.name());
            } else {
                logger.error("close order failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getErrCode());
                switch (errorCodeEnum) {
                    case ORDERCLOSED:
                        paymentCloseOrderBO.setTradeStatus(PaymentStatusEnum.TRADE_CLOSED.name());
                        break;
                    default:
                        paymentCloseOrderBO.setTradeStatus(PaymentStatusEnum.WAIT_PAY.name());
                }
            }
        } else {
            logger.error("close order failure, weChat return message is : {}", response.getReturnMsg());
            paymentCloseOrderBO.setTradeStatus(PaymentStatusEnum.WAIT_PAY.name());
        }
    }
}
