package com.cmpay.payment.service.notify;

import com.cmpay.payment.bo.PayNotifyBO;
import com.cmpay.payment.bo.TradeNotifyBO;

/**
 * @author： PengAnHai
 * @date： 2024-08-15
 * @description：招行通知处理service
 * @modifiedBy：
 * @version: 1.0
 */
public interface CmbaionNotifyService {

    /**
     * 页面回调通知处理
     * @param orderNo
     * @return
     */
    PayNotifyBO pageNotifyHandle(String orderNo);

    /**
     * 招行一网通支付成功处理
     * @param tradeNotifyBO
     */
    void NotifyHandle(TradeNotifyBO tradeNotifyBO);
}
