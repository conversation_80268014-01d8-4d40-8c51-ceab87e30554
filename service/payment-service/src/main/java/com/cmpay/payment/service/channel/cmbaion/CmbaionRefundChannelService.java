package com.cmpay.payment.service.channel.cmbaion;

import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.service.channel.PaymentChannelService;

/**
 * @author： PengAnHai
 * @date： 2024-08-13
 * @description：招行一网通退款处理类
 * @modifiedBy：
 * @version: 1.0
 */
public interface CmbaionRefundChannelService extends PaymentChannelService<RefundBO> {

    /**
     * 招行一网通退款处理
     * @param refundBO
     */
    void cmbaionRefund(RefundBO refundBO);

}
