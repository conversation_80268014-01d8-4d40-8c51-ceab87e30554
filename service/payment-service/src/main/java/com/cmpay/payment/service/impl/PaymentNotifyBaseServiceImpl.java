package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.ErrorMsgCode;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.PaymentOrderHandleService;
import com.cmpay.payment.utils.PaymentUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author： PengAnHai
 * @date： 2024-08-20
 * @description：支付成功通知处理公共类
 * @modifiedBy：
 * @version: 1.0
 */
public abstract class PaymentNotifyBaseServiceImpl {

    @Autowired
    private PaymentOrderHandleService paymentOrderHandleService;

    protected abstract boolean orderSuccessHandle(TradeOrderDO tradeOrder);

    /**
     * 对给定的消息进行URL编码，并提供默认错误信息。
     *
     * @param message 需要编码的消息
     * @return 编码后的消息字符串
     */
    protected String setMessage(String message) {
        return PaymentUtils.urlEncode(StringUtils.defaultIfBlank(message, ErrorMsgCode.SYS_ERROR.getMsgInfo()));
    }

    /**
     * 配置订单的费率信息
     *
     * @param tradeOrder
     * @return
     */
    protected TradeOrderDO setPayOrderRate(TradeOrderDO tradeOrder) {
        return paymentOrderHandleService.setPayOrderRate(tradeOrder);
    }

    /**
     * 通知商户
     *
     * @param tradeOrder 交易订单对象
     * @return 通知结果，成功返回true，失败返回false
     */
    protected boolean notifyMerchant(TradeOrderDO tradeOrder) {
        return paymentOrderHandleService.notifyMerchant(tradeOrder);
    }

}
