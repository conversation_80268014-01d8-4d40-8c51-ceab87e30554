package com.cmpay.payment.service.notify.impl;

import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.payment.bo.RefundNotifyBO;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.notify.DcepPaymentNotifyBO;
import com.cmpay.payment.bo.notify.DcepRefundNotifyBO;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.TradeNotifyService;
import com.cmpay.payment.service.notify.IDcepNotifyService;
import com.cmpay.payment.util.LemonAmount;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/14 14:09
 */
@Service
public class DcepNotifyServiceImpl implements IDcepNotifyService {
    @Autowired
    private TradeNotifyService tradeNotifyService;

    @Autowired
    private AsynCommonService asynCommonService;

    @Override
    public void backendDcepPaymentNotifyWithStatus(DcepPaymentNotifyBO dcepPaymentNotifyBO) {
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setTradeOrderNo(dcepPaymentNotifyBO.getOutTranNo());
        tradeNotifyBO.setResult(OrderStatusEnum.TRADE_SUCCESS.name());
        String finishDateTime = dcepPaymentNotifyBO.getPaySuccessDate() + dcepPaymentNotifyBO.getPaySuccessTime();
        tradeNotifyBO.setFinishDate(StringUtils.isBlank(finishDateTime) || finishDateTime.length() != 14 ? DateTimeUtils.getCurrentDateTimeStr() : finishDateTime);
        tradeNotifyBO.setPaymentRout(PaymentWayEnum.DCEPPAY.name().toLowerCase());
        tradeNotifyBO.setAccountDate(tradeNotifyBO.getFinishDate().substring(0, 8));
        tradeNotifyBO.setBnkTradeNo(dcepPaymentNotifyBO.getPaySeq());
        tradeNotifyBO.setTradeAmount(new BigDecimal(new LemonAmount(dcepPaymentNotifyBO.getAmount()).fen2yuan()));
        TradeNotifyBO notifyBO = tradeNotifyService.backendNotify(tradeNotifyBO);
        asynCommonService.asyncNotify(notifyBO);
    }

    @Override
    public void backendDcepRefundNotifyWithStatus(DcepRefundNotifyBO dcepRefundNotifyBO) {
        RefundOrderDO refundOrderDO = new RefundOrderDO();
        refundOrderDO.setOutTradeNo(dcepRefundNotifyBO.getOutRefundNo());
        refundOrderDO.setOrgOrderNo(dcepRefundNotifyBO.getOriOutTranNo());
        refundOrderDO.setAccountDate(dcepRefundNotifyBO.getRefundSuccessDate());
        RefundOrderQueryBO refundOrderQueryBO = new RefundOrderQueryBO();
        refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
        refundOrderQueryBO.setRefundOrder(refundOrderDO);
        RefundNotifyBO refundNotifyBO = new RefundNotifyBO();
        refundNotifyBO.setRefundOrderQueryBO(refundOrderQueryBO);
        tradeNotifyService.refundResultNotify(refundNotifyBO);
    }
}
