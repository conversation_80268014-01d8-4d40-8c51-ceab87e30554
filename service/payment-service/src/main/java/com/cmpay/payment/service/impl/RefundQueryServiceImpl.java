package com.cmpay.payment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.AmountInfoBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.bo.RefundQueryBO;
import com.cmpay.payment.service.RefundQueryService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.ExtRefundOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created on 2018/11/30
 *
 * @author: sun_zhh
 */

@Transactional(propagation = Propagation.SUPPORTS)
@Service
public class RefundQueryServiceImpl implements RefundQueryService {

    @Autowired
    ExtPayOrderService payOrderService;
    @Autowired
    ExtRefundOrderService refundOrderService;
    @Override
    public RefundQueryBO refundQuery(RefundQueryBO refundOrderQueryBO) {
        // 检查传入参数
        if (JudgeUtils.isNull(refundOrderQueryBO.getMerchantId())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isNull(refundOrderQueryBO.getOutRequestNo())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ORDER_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isNull(refundOrderQueryBO.getTradeDate())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_REQUEST_DATE_CANNOT_BE_EMPTY);
        }
        // 查询退款订单信息
        RefundOrderDO refundOrderQuery = new RefundOrderDO();
        refundOrderQuery.setMerchantNo(refundOrderQueryBO.getMerchantId());
        refundOrderQuery.setRequestDate(refundOrderQueryBO.getTradeDate());
        refundOrderQuery.setOutTradeNo(refundOrderQueryBO.getOutRequestNo());
        refundOrderQuery.setOrgOrderNo(refundOrderQueryBO.getOutTradeNo());
        RefundOrderDO refundOrder = refundOrderService.load(refundOrderQuery);
        if (JudgeUtils.isNull(refundOrder)) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_NOT_EXISTS);
        }
        // 查询退款原支付订单总金额
        TradeOrderDO payOrderQuery = new TradeOrderDO();
        payOrderQuery.setMerchantNo(refundOrderQueryBO.getMerchantId());
        payOrderQuery.setOutTradeNo(refundOrderQueryBO.getOutTradeNo());
        TradeOrderDO oriPayOrder = payOrderService.load(payOrderQuery);
        if (JudgeUtils.isNull(oriPayOrder)) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORG_ORDER_NOT_EXISTS);
        }
        // 设置退款信息
        refundOrderQueryBO.setRefundAmount(refundOrder.getOrderAmount());
        refundOrderQueryBO.setRefundStatus(refundOrder.getStatus());
        if (OrderStatusEnum.REFUND_PEND.name().equals(refundOrder.getStatus())){
            refundOrderQueryBO.setRefundStatus(OrderStatusEnum.REFUND_WAIT.name());
        }
        refundOrderQueryBO.setRefundReason(refundOrder.getRefundReason());
        refundOrderQueryBO.setExtra(refundOrder.getRemark());
        if (OrderStatusEnum.REFUND_SUCCESS.name().equals(refundOrder.getStatus())
                || OrderStatusEnum.REFUND_FAIL.name().equals(refundOrder.getStatus())) {
            refundOrderQueryBO.setFinishDateTime(refundOrder.getOrderCompleteTime());
        }
        // 支付订单金额
        refundOrderQueryBO.setTotalAmount(oriPayOrder.getOrderAmount());
        refundOrderQueryBO.setAccountDate(refundOrder.getAccountDate());
        refundOrderQueryBO.setErrMsgCd(refundOrder.getErrMsgCd());
        refundOrderQueryBO.setErrMsgInfo(refundOrder.getErrMsgInfo());
        if (JudgeUtils.isNotBlank(refundOrder.getRefAmountList())) {
            // 记录退款金额明细
            refundOrderQueryBO.setRefAmountList(JSONObject.parseArray(refundOrder.getRefAmountList(), AmountInfoBO.class));
        }
        refundOrderQueryBO.setInstPaidAmount(refundOrder.getInstPaidAmount());
        refundOrderQueryBO.setInstDiscountSettlementAmount(refundOrder.getInstDiscountSettlementAmount());
        refundOrderQueryBO.setInstDiscountUnsettledAmount(refundOrder.getInstDiscountUnsettledAmount());
        return refundOrderQueryBO;
    }

}
