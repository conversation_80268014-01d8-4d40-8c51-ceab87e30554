package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.config.PayServiceBeanBO;
import com.cmpay.payment.bo.data.OrderFeeBO;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.*;
import com.cmpay.payment.service.data.IDataFeeService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.config.IExtRateService;
import com.cmpay.payment.utils.TypeCheckUtils;
import feign.RetryableException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import sun.net.util.IPAddressUtil;

import java.time.LocalDateTime;

/**
 * Created on 2018/11/30
 *
 * @author: sun_zhh
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class PayOrderServiceImpl implements PayOrderService {
    @Value("${cmpayWap.requestUrl:}")
    private String requestUrl;
    @Value("${cmpayWap.MBL_NO:}")
    private String MBL_NO;
    @Value("${cmpayWap.DEV_ID:}")
    private String DEV_ID;
    @Value("${cmpayWap.MERCSIGN:}")
    private String MERCSIGN;
    @Value("${cmpayWap.TAG_PAG:}")
    private String TAG_PAG;
    private static final Logger logger = LoggerFactory.getLogger(PayOrderServiceImpl.class);
    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    private IDataFeeService dataFeeService;
    @Autowired
    private IExtRateService rateService;
    @Autowired
    private RiskControlService riskControlService;
    @Autowired
    private SensorsDataService sensorsDataService;
    @Autowired
    private PayOrderExtFunctionService extFunctionService;

    @Autowired
    private AsynCommonService asynCommonService;


    private static final String FAP_SUCCESSFUL = "FAP00000";

    @Override
    public PayOrderBO payOrder(PayOrderBO payOrderBO) {
        try {
            paymentProcess(payOrderBO);
        } catch (Exception e) {
            String msg;
            if (e instanceof BusinessException) {
                msg = ((BusinessException) e).getMsgCd();
                payOrderBO.setMsgCode(msg);
            } else if ((e instanceof RetryableException) && e.getMessage().contains(CommonConstant.READ_TIMED_OUT)) {
                msg = MsgCodeEnum.REQUEST_RETURN_TIME_OUT.getMsgCd();
                logger.error("Exception:.", e);
            } else {
                msg = MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd();
                logger.error("Exception:.", e);
            }
            BusinessException.throwBusinessException(msg);
        }

        return payOrderBO;
    }


    /**
     * 支付处理
     *
     * @param payOrderBO
     */
    private void paymentProcess(PayOrderBO payOrderBO) {
        LocalDateTime paymentStartTime = DateTimeUtils.getCurrentLocalDateTime();
        // 驾驶舱埋点，接受支付请求
        sensorsDataService.functionRequest(CommonConstant.PAYMENT);
        PayServiceBeanBO rateBO = new PayServiceBeanBO();
//        此try catch捕获聚合系统异常
        try {
            extFunctionService.inputCannotEmptyCheck(payOrderBO);
            if (StringUtils.isNotEmpty(payOrderBO.getSubMerchant())) {
                if (JudgeUtils.isBlankAny(payOrderBO.getSettlementDept(), payOrderBO.getSettlementItem(), payOrderBO.getMerchantChannelType())) {
                    BusinessException.throwBusinessException(MsgCodeEnum.SPLIT_INFO_IS_NULL);
                }
                extFunctionService.subMerchantCheck(payOrderBO.getSubMerchant());
            }
            if (StringUtils.equals(payOrderBO.getScene(), CmpayConstants.APPLEPAY)) {
                payOrderBO.setScene(CmpayConstants.ALE);
            }
            extFunctionService.checkAmount(payOrderBO.getTotalAmount(), payOrderBO.getRealAmount(), payOrderBO.getDiscountableAmount());
            if (TypeCheckUtils.checkPaymentWay(PaymentWayEnum.valueOf(payOrderBO.getPayWay().toUpperCase()))) {
                BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_WAY_ERROR);
            }
            if (TypeCheckUtils.checkPaymentScene(PaymentSceneEnum.valueOf(payOrderBO.getScene().toUpperCase()))) {
                BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_SCENE_ERROR);
            }
            //设置支付场景为企业网银
            if (JudgeUtils.equals(PaymentSceneEnum.NETBANK.name().toLowerCase(), payOrderBO.getScene())
                    || JudgeUtils.equals(PaymentSceneEnum.NETBANKB2B.name().toLowerCase(), payOrderBO.getScene())) {
                if (JudgeUtils.isBlank(payOrderBO.getBankAbbreviation())) {
                    BusinessException.throwBusinessException(MsgCodeEnum.BANK_ABBREVIATION_CANNOT_BE_EMPTY);
                }
                if (payOrderBO.getBankAbbreviation().endsWith("B2B")) {
                    payOrderBO.setScene(PaymentSceneEnum.NETBANKB2B.name().toLowerCase());
                }
                //设置银行简称
                payOrderBO.setHallAreaCode(payOrderBO.getBankAbbreviation());
            }
            //指定渠道花呗，且支付场景wap 和 app，修改支付场景XXXpcredit
            if (JudgeUtils.equals(payOrderBO.getSpecifiedChannel(), CommonConstant.ALIPAY_PCREDIT) &&
                    JudgeUtils.equalsAny(payOrderBO.getScene(), PaymentSceneEnum.WAP.name().toLowerCase(), PaymentSceneEnum.APP.name().toLowerCase())) {
                payOrderBO.setScene(payOrderBO.getScene().concat(CommonConstant.ALIPAY_PCREDIT));
            }
            //查询商户配置费率
            rateBO.setMerchantNo(payOrderBO.getMerchantId());
            rateBO.setPaymentChannl(payOrderBO.getPayWay());
            rateBO.setOrderScene(payOrderBO.getScene());
            rateBO.setOrderAmount(payOrderBO.getRealAmount());
            rateBO.setProvinceCode(JudgeUtils.isBlank(payOrderBO.getProvinceCode()) ?
                    StringUtils.substring(payOrderBO.getOutTradeNo(), 0, 4) : payOrderBO.getProvinceCode());
            rateBO.setOrderDate(DateTimeUtils.getCurrentDateStr());
            rateBO = rateService.findPaymentRout(rateBO);
            if (JudgeUtils.isNull(rateBO)) {
                BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ROUT_CANNOT_VALIDITY);
            }
            payOrderBO.setOrderRate(rateBO.getRate());
            payOrderBO.setServiceCharge(rateBO.getServiceCharge());
            payOrderBO.setPaymentId(rateBO.getBankMerchantNo());
            payOrderBO.setPaymentType(rateBO.getBusinessType());
            payOrderBO.setPaymentRout(rateBO.getPaymentRout());
            payOrderBO.setRefundFeeWay(rateBO.getRefundFeeWay());
            payOrderBO.setContractSecureValue(rateBO.getSecureValue());
            payOrderBO.setSignMethod(rateBO.getSignMethod());
            extFunctionService.checkAuthCode(payOrderBO.getAuthCode(), payOrderBO.getPayWay(), payOrderBO.getScene());
            //计算服务费
            OrderFeeBO orderFeeBO = new OrderFeeBO();
            orderFeeBO.setOrderAmount(payOrderBO.getRealAmount());
            orderFeeBO.setOrderRate(payOrderBO.getOrderRate());
            orderFeeBO.setMinFeeLimit(rateBO.getMinFeeLimit());
            orderFeeBO.setMaxFeeLimit(rateBO.getMaxFeeLimit());
            orderFeeBO = dataFeeService.feeCalculate(orderFeeBO);
            payOrderBO.setOrderFeeAmount(orderFeeBO.getOrderFeeAmount());

            //查询银联配置活动
            //unionActivityQuery(payOrderBO);
            payOrderBO.setBusinessCode(payOrderBO.getDiscountCode());
            payOrderBO.setTradeOrderNo(payOrderBO.getOutTradeNo());
            payOrderBO.setTradeTime(DateTimeUtils.getCurrentTimeStr());
            payOrderBO.setTimeoutExpress(extFunctionService.getTimeoutExpress(payOrderBO));
            payOrderBO.setExpireTime(extFunctionService.getExpireTime(payOrderBO));
            if (JudgeUtils.equals(payOrderBO.getPaymentRout(), PaymentWayEnum.WECHAT.name().toLowerCase())
                    && JudgeUtils.equalsAny(payOrderBO.getScene(), PaymentSceneEnum.SCAN.name().toLowerCase(), PaymentSceneEnum.BARCODE.name().toLowerCase(),
                    PaymentSceneEnum.APP.name().toLowerCase(), PaymentSceneEnum.WAP.name().toLowerCase(), PaymentSceneEnum.JSAPI.name().toLowerCase(),
                    PaymentSceneEnum.APPLET.name().toLowerCase())) {
                if (JudgeUtils.isBlank(payOrderBO.getAppId())) {
                    payOrderBO.setAppId(weChatProperties.getAppid());
                }
            } else {
                payOrderBO.setAppId("");
            }
//        tradeOrderDO.setRefundFeeWay(payOrderBO.getRefundFeeWay());
            extFunctionService.obtainProvinceOrderNo(payOrderBO);

        } catch (Exception e) {
            if (e instanceof BusinessException) {
                sensorsDataService.functionDelay(CommonConstant.PAYMENT, paymentStartTime);
                sensorsDataService.requestSuccess(CommonConstant.PAYMENT);
            }
            throw e;
        }
        //支付处理时延、支付接口调用成功
        sensorsDataService.functionDelay(CommonConstant.PAYMENT, paymentStartTime);
        sensorsDataService.requestSuccess(CommonConstant.PAYMENT);

        payOrderBO.setClientIps(choiceClientIp(payOrderBO.getClientIps(), payOrderBO.getClientIp()));
        //判断IP地址类型
        checkIptype(payOrderBO);
        //进行风控拦截
        riskControlService.riskIntercept(payOrderBO, rateBO);
        payOrderBO.setSplitFlag(Constants.N);
        TradeOrderDO tradeOrder = payOrderService.insertByNewTranscation(payOrderBO);

        long startTime = System.currentTimeMillis();
        try {
            applicationContext.publishEvent(payOrderBO);
            logger.info("RequestExternalInterfaceMonitoringLog{tc='{}',dur='{}',mc='{}'}", payOrderBO.getPaymentRout() + "-" + payOrderBO.getPayWay(), System.currentTimeMillis() - startTime, FAP_SUCCESSFUL);
            if (JudgeUtils.equals(PaymentSceneEnum.NETBANK.name().toLowerCase(), payOrderBO.getScene())
                    || JudgeUtils.equals(PaymentSceneEnum.NETBANKB2B.name().toLowerCase(), payOrderBO.getScene())) {
                extFunctionService.analyzeBankUrl(payOrderBO);
            }
            tradeOrder.setThirdOrdNo(payOrderBO.getThirdOrdNo());
            tradeOrder.setThirdOrdDt(payOrderBO.getThirdOrdDt());
        } catch (Exception e) {
            // 微信付款码支付同步返回支付确认失败
            if (StringUtils.equals(payOrderBO.getPaymentRout(), PaymentWayEnum.WECHAT.name().toLowerCase())
                    && StringUtils.equals(payOrderBO.getScene(), PaymentSceneEnum.BARCODE.name().toLowerCase())
                    && StringUtils.equals(payOrderBO.getStatus(), OrderStatusEnum.TRADE_FAIL.name())) {
                tradeOrder.setStatus(payOrderBO.getStatus());
                tradeOrder.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                tradeOrder.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
            } else {
                tradeOrder.setStatus(null);
                tradeOrder.setOrderCompleteTime(null);
                tradeOrder.setReceiveNotifyTime(null);
            }
            //设置返回码
            if (e instanceof BusinessException) {
                payOrderBO.setMsgCode(((BusinessException) e).getMsgCd());
            } else if ((e instanceof RetryableException) && e.getMessage().contains(CommonConstant.READ_TIMED_OUT)) {
                payOrderBO.setMsgCode(MsgCodeEnum.REQUEST_RETURN_TIME_OUT.getMsgCd());
            } else {
                payOrderBO.setMsgCode(MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd());
            }
            if (JudgeUtils.isNull(payOrderBO.getErrMsgCd())) {
                payOrderBO.setErrMsgCd(payOrderBO.getMsgCode());
            }
            logger.info("RequestExternalInterfaceMonitoringLog{tc='{}',dur='{}',mc='{}'}", payOrderBO.getPaymentRout() + "-" + payOrderBO.getPayWay(), System.currentTimeMillis() - startTime, payOrderBO.getErrMsgCd());
            tradeOrder.setErrMsgCd(payOrderBO.getErrMsgCd());
            tradeOrder.setErrMsgInfo(payOrderBO.getErrMsgInfo());
            payOrderService.updateByNewTranscation(tradeOrder);
            riskControlService.riskAfterPay(tradeOrder);
            throw e;
        }
        payOrderBO.setMsgCode(FAP_SUCCESSFUL);
        if (JudgeUtils.equals(payOrderBO.getPaymentRout(), PaymentWayEnum.CMPAY.name().toLowerCase())) {
            if (JudgeUtils.equals(payOrderBO.getScene(), PaymentSceneEnum.BARCODE.name().toLowerCase())) {
                if (StringUtils.equals(payOrderBO.getStatus(), CommonConstant.BARCODE_SUCCESS)) {
                    if (JudgeUtils.isEmpty(payOrderBO.getFinishDateTime())) {
                        payOrderBO.setFinishDateTime(DateTimeUtils.getCurrentDateTimeStr());
                    }
                    if (JudgeUtils.isEmpty(payOrderBO.getAccountDate())) {
                        payOrderBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
                    }
                    tradeOrder.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                    tradeOrder.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                    tradeOrder.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
                    tradeOrder.setAccountDate(payOrderBO.getAccountDate());
                    payOrderService.updateByNewTranscation(tradeOrder);
                    riskControlService.riskAfterPay(tradeOrder);
                    TradeNotifyBO tradeNotifyBO = assembleNotifyBO(payOrderBO);
                    asynCommonService.asyncNotify(tradeNotifyBO);
                } else {
                    BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_ORDER_PAY_FAIL);
                }
            }
        } else if (JudgeUtils.equals(payOrderBO.getPaymentRout(), PaymentWayEnum.ALIPAY.name().toLowerCase())) {
            tradeOrder.setStatus(OrderStatusEnum.WAIT_PAY.name());

            if (StringUtils.isNotBlank(payOrderBO.getStatus())) {
                tradeOrder.setStatus(payOrderBO.getStatus());
                if (StringUtils.equals(OrderStatusEnum.TRADE_SUCCESS.name(), payOrderBO.getStatus())) {
                    tradeOrder.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
                    tradeOrder.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                    if (JudgeUtils.isEmpty(payOrderBO.getFinishDateTime())) {
                        payOrderBO.setFinishDateTime(DateTimeUtils.getCurrentDateTimeStr());
                    }
                    if (JudgeUtils.isEmpty(payOrderBO.getAccountDate())) {
                        payOrderBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
                    }
                    tradeOrder.setAccountDate(payOrderBO.getAccountDate());
                    tradeOrder.setMobileNumber(payOrderBO.getMobileNumber());
                    tradeOrder.setErrMsgCd(payOrderBO.getErrMsgCd());
                    tradeOrder.setErrMsgInfo(payOrderBO.getErrMsgInfo());
                    if (StringUtils.equals(OrderStatusEnum.TRADE_SUCCESS.name(), payOrderBO.getStatus()) || StringUtils.isNotBlank(tradeOrder.getErrMsgCd())) {
                        payOrderService.updateByNewTranscation(tradeOrder);
                    }
                    //异步通知
                    TradeNotifyBO tradeNotifyBO = assembleNotifyBO(payOrderBO);
                    asynCommonService.asyncNotify(tradeNotifyBO);
                }
            }
            if (JudgeUtils.equalsAny(payOrderBO.getStatus(), OrderStatusEnum.TRADE_SUCCESS.name(), OrderStatusEnum.TRADE_FAIL.name(), OrderStatusEnum.TRADE_CLOSED.name())) {
                riskControlService.riskAfterPay(tradeOrder);
            }
        } else if (StringUtils.equals(payOrderBO.getPaymentRout(), PaymentWayEnum.WECHAT.name().toLowerCase())
                && StringUtils.equals(payOrderBO.getScene(), PaymentSceneEnum.BARCODE.name().toLowerCase())
                && StringUtils.equals(payOrderBO.getStatus(), OrderStatusEnum.TRADE_SUCCESS.name())) {
            // 微信付款码支付同步返回支付成功
            if (JudgeUtils.isEmpty(payOrderBO.getAccountDate())) {
                payOrderBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
            }
            tradeOrder.setStatus(payOrderBO.getStatus());
            tradeOrder.setOrderCompleteTime(payOrderBO.getFinishDateTime());
            tradeOrder.setReceiveNotifyTime(payOrderBO.getFinishDateTime());
            tradeOrder.setAccountDate(payOrderBO.getAccountDate());
            tradeOrder.setMobileNumber(payOrderBO.getMobileNumber());
            payOrderService.updateByNewTranscation(tradeOrder);
            riskControlService.riskAfterPay(tradeOrder);
            TradeNotifyBO tradeNotifyBO = assembleNotifyBO(payOrderBO);
            asynCommonService.asyncNotify(tradeNotifyBO);
        }
        asynCommonService.asynConnectThirdNo(tradeOrder);
        payOrderBO.setMsgCode(FAP_SUCCESSFUL);
    }

    /**
     * 判断ip类型
     *
     * @param payOrderBO
     * @return
     */
    private static void checkIptype(PayOrderBO payOrderBO) {
        //判断IP地址类型
        if (IPAddressUtil.isIPv4LiteralAddress(payOrderBO.getClientIps())) {
            payOrderBO.setIpType("IPV4");
        } else if (IPAddressUtil.isIPv6LiteralAddress(payOrderBO.getClientIps())) {
            payOrderBO.setIpType("IPV6");
        } else {
            //ip不为ipv4，也不为ipv6，则取本地框架ip，并判断本地框架ip类型
            payOrderBO.setClientIps(payOrderBO.getClientIp());
            if (IPAddressUtil.isIPv4LiteralAddress(payOrderBO.getClientIp())) {
                payOrderBO.setIpType("IPV4");
            } else if (IPAddressUtil.isIPv6LiteralAddress(payOrderBO.getClientIp())) {
                payOrderBO.setIpType("IPV6");
            }
        }
    }

    /**
     * 选择客户端ip
     *
     * @param clientIps
     * @param clientIp
     * @return
     */
    private static String choiceClientIp(String clientIps, String clientIp) {
        if (JudgeUtils.isBlank(clientIps)) {
            return clientIp;
        } else {
            if (clientIps.contains(CommonConstant.COMMA) || clientIps.contains(CommonConstant.CN_COMMA)) {
                return clientIps.split(CommonConstant.REGEX_COMMA)[0];
            } else {
                return clientIps;
            }
        }
    }

    private TradeNotifyBO assembleNotifyBO(PayOrderBO payOrderBO) {
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setMerchantNo(payOrderBO.getMerchantId());
        tradeNotifyBO.setTradeOrderNo(payOrderBO.getTradeOrderNo());
        tradeNotifyBO.setOutOrderNo(payOrderBO.getOutTradeNo());
        tradeNotifyBO.setTradeDate(payOrderBO.getTradeDate());
        tradeNotifyBO.setTradeAmount(payOrderBO.getTotalAmount());
        tradeNotifyBO.setDiscountableAmount(payOrderBO.getDiscountableAmount());
        tradeNotifyBO.setNotifyType(TradeTypeEnum.PAYMENT.name().toLowerCase());
        tradeNotifyBO.setNotifyUrl(payOrderBO.getNotifyUrl());
        tradeNotifyBO.setExtra(payOrderBO.getExtra());
        tradeNotifyBO.setSecretIndex(payOrderBO.getSecretIndex());
        tradeNotifyBO.setFinishDate(payOrderBO.getFinishDateTime());
        return tradeNotifyBO;
    }

}
