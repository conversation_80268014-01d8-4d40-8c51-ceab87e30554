package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.PaymentCloseOrderBO;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.config.ContractDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.wechat.WeChatCloseOrderService;
import com.cmpay.payment.service.channel.wechat.WeChatReverseOrderService;
import com.cmpay.payment.service.ext.config.IExtContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatCloseOrderListener extends PaymentListenerService<PaymentCloseOrderBO> {
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    private IExtContractService contractService;

    /**
     * 微信H5支付关闭订单
     *
     * @param paymentCloseOrderBO
     */
    @EventListener
    public void handleWeChatCloseOrder(PaymentCloseOrderBO paymentCloseOrderBO) {
        execute(paymentCloseOrderBO);
    }

    @Override
    protected boolean checkChannelExecutable(PaymentCloseOrderBO paymentCloseOrderBO) {
        return Optional.ofNullable(paymentCloseOrderBO)
                .map(PaymentCloseOrderBO::getAimProductCode)
                .filter(aimProductCode -> StringUtils.equals(PaymentWayEnum.WECHAT.name().toLowerCase(), aimProductCode))
                .isPresent();
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(PaymentCloseOrderBO paymentCloseOrderBO) {
        ContractDO contract = contractService.getContract(paymentCloseOrderBO.getBankMerchantNo());
        paymentCloseOrderBO.setContractSecureValue(contract.getSecretKey());
        return Optional.ofNullable(paymentCloseOrderBO)
                .map(PaymentCloseOrderBO::getScene)
                .map(String::toUpperCase)
                .map(PaymentSceneEnum::valueOf)
                .map(scene -> {
                    PaymentChannelService paymentChannelService = null;
                    switch (scene) {
                        case WAP:
                        case JSAPI:
                        case APP:
                        case SCAN:
                            paymentChannelService = getBean(WeChatCloseOrderService.class);
                            break;
                        case BARCODE:
                            paymentChannelService = getBean(WeChatReverseOrderService.class);
                            break;
                        default:
                            break;
                    }
                    return paymentChannelService;
                })
                .orElse(null);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
