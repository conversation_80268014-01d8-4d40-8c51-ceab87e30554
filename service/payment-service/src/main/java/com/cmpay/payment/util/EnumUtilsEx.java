package com.cmpay.payment.util;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.EnumUtils;

import java.lang.reflect.Array;
import java.util.EnumSet;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
public class EnumUtilsEx extends EnumUtils {


    /**
     * Static Map Indexed by Field Utility
     *
     * @param clazz
     * @param mapper
     * @param <T>
     * @param <E>
     * @return
     */
    public static <T, E extends Enum<E>> Function<T, E> lookupMap(Class<E> clazz, Function<E, T> mapper) {
        @SuppressWarnings("unchecked")
        E[] emptyArray = (E[]) Array.newInstance(clazz, 0);
        return lookupMap(EnumSet.allOf(clazz).toArray(emptyArray), mapper);
    }

    /**
     * Static Map Indexed by Field Utility
     *
     * @param values
     * @param mapper
     * @param <T>
     * @param <E>
     * @return
     */
    public static <T, E extends Enum<E>> Function<T, E> lookupMap(E[] values, Function<E, T> mapper) {
        Map<T, E> index = Maps.newHashMapWithExpectedSize(values.length);
        for (E value : values) {
            index.put(mapper.apply(value), value);
        }
        return (T key) -> index.get(key);
    }
}
