package com.cmpay.payment.service.channel.integralpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.RandomUtils;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.channel.IntegralPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.IntegralOrderStatusEnum;
import com.cmpay.payment.constant.IntegralpayConstants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.dto.integralpay.IntegralOrderQueryReq;
import com.cmpay.payment.dto.integralpay.IntegralOrderQueryRsp;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.TradeCommonService;
import com.cmpay.payment.service.channel.integralpay.IntegralPayRefundQueryService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/10 15:12
 * @description ：
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class IntegralPayRefundQueryServiceImpl implements IntegralPayRefundQueryService {
    private static final Logger logger = LoggerFactory.getLogger(IntegralPayRefundQueryServiceImpl.class);

    private static final String CODE_SUCCESS = "0000";
    private static final String PAGE = "1";
    private static final String ROWS = "10";

    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Autowired
    private ExtPayOrderService paymentOrderService;
    @Value("${integralpay.channelCode:}")
    private String channelCode;
    @Autowired
    private TradeCommonService tradeCommonService;

    @Override
    public void IntegralPayRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {
        if (checkQueryParams(refundOrderQueryBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.OUT_TRADE_NO_CANNOT_BE_EMPTY);
        }
        IntegralOrderQueryReq integralOrderQueryReq = new IntegralOrderQueryReq();
        TradeOrderDO tradeOrderDO = paymentOrderService.get(refundOrderQueryBO.getRefundOrder().getOrgOrderNo());
        integralOrderQueryReq.setMobile(tradeOrderDO.getMobileNumber());
        integralOrderQueryReq.setOrderId(tradeOrderDO.getBankOrderNo());
        integralOrderQueryReq.setPage(PAGE);
        integralOrderQueryReq.setRows(ROWS);
        String jrn_no = RandomUtils.randomStringFixLength(16);
        integralOrderQueryReq.setTraceId(jrn_no);
        integralOrderQueryReq.setSpanId(jrn_no);
        integralOrderQueryReq.setServiceCode(IntegralpayConstants.S0004);
        integralOrderQueryReq.setChannelCode(channelCode);
        integralOrderQueryReq.setReqTime(DateFormatUtils.format(new Date(), "yyyyMMddHHmmss"));
        integralOrderQueryReq.setAccessChannel(IntegralpayConstants.API_APP);
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(IntegralPayChannelEnum.INTEGRAL_PAY);
        request.setRoute(IntegralPayChannelEnum.INTEGRAL_PAY);
        request.setBusiType(IntegralPayChannelEnum.INTEGRAL_ORDER_QUERY.getName());
        request.setTarget(integralOrderQueryReq);
        GenericRspDTO<Response> response = this.nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        if (JudgeUtils.isSuccess(response.getMsgCd())) {
            IntegralOrderQueryRsp integralOrderQueryRsp = (IntegralOrderQueryRsp) response.getBody().getResult();
            //登记积分商城订单号
            if(JudgeUtils.isNotNull(integralOrderQueryRsp)){
                if (StringUtils.equals(CODE_SUCCESS, integralOrderQueryRsp.getRespCode())) {
                    if(JudgeUtils.isNotNull(integralOrderQueryRsp.getResult())){
                        if(integralOrderQueryRsp.getResult().size()==1){
                            String status = integralOrderQueryRsp.getResult().get(0).getSubOrderStatus();
                            IntegralOrderStatusEnum statusEnum = IntegralOrderStatusEnum.getMsgCodeEnum(status);
                            switch (statusEnum) {
                                // 积分订单状态为TO_BE_SHIPPED，SHIPPED都可以进行退款，查询到这2个状态时，都可能是请求未到积分
                                case TO_BE_SHIPPED:
                                    tradeCommonService.handleRefundOrderNotExist(refundOrderQueryBO,MsgCodeEnum.INTEGRAL_ORDER_REFUND_UNSUCCESSFUL);
                                    break;
                                case SHIPPED:
                                    tradeCommonService.handleRefundOrderNotExist(refundOrderQueryBO,MsgCodeEnum.INTEGRAL_ORDER_FIND_SHIPPED);
                                    break;
                                case CLOSED:
                                    refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
                                    refundOrderQueryBO.setErrMsgCd(MsgCodeEnum.INTEGRAL_ORDER_FIND_CLOSED.getMsgCd());
                                    refundOrderQueryBO.setErrMsgInfo(MsgCodeEnum.INTEGRAL_ORDER_FIND_CLOSED.getMsgInfo());
                                    break;
                                case RESCINDED:
                                    refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
                                    break;
                                default:
                                    break;
                            }
                        } else if(integralOrderQueryRsp.getResult().size()>1) {
                            logger.error("=======查询到多笔订单, integralOrderQueryRsp：{}",integralOrderQueryRsp.toString());
                            BusinessException.throwBusinessException(MsgCodeEnum.INTEGRAL_ORDER_FIND_FALSE);
                        }
                    } else {
                        logger.error("=======查询失败,订单不存在, integralOrderQueryRsp：{}",integralOrderQueryRsp.toString());
                        BusinessException.throwBusinessException(MsgCodeEnum.INTEGRAL_ORDER_FIND_FALSE);
                    }
                } else {
                    logger.error("=======退款订单查询失败, RespCode：{}",integralOrderQueryRsp.getRespCode());
                    BusinessException.throwBusinessException(MsgCodeEnum.INTEGRAL_ORDER_FIND_FALSE);
                }
            } else {
                logger.error("=======退款订单查询失败, integralOrderQueryRsp is null");
                BusinessException.throwBusinessException(MsgCodeEnum.INTEGRAL_ORDER_FIND_FALSE);
            }
        }
    }

    @Override
    public void send(RefundOrderQueryBO refundOrderQueryBO) {
        IntegralPayRefundQuery(refundOrderQueryBO);
    }

    private boolean checkQueryParams(RefundOrderQueryBO refundOrderQueryBO) {
        return Optional.ofNullable(refundOrderQueryBO)
                .map(RefundOrderQueryBO::getRefundOrder)
                .map(RefundOrderDO::getOrgOrderNo)
                .map(StringUtils::isEmpty)
                .orElse(false);
    }
}
