package com.cmpay.payment.service.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.channel.SensorsEventTypeEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.dto.SensorsData.SensorsDataEventReq;
import com.cmpay.payment.service.SensorsDataService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.PaymentUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * @date 2023-03-24 15:03
 * 联机只管把数据给到cgw-out，cgw-out接收到数据会立马返回；然后cgw-out神策会自己启一个线程（bean注入的方式）把数据发送到远程
 * cgw-out发送数据到神策成功与否不影响原接口调用；如果发送失败可以重发（但没做这个逻辑，不需要重发）
 * <AUTHOR>
 * @Version 1.0
 */
@Service
public class SensorsDataServiceImpl implements SensorsDataService {
    private static final Logger logger = LoggerFactory.getLogger(SensorsDataServiceImpl.class);

    @Autowired
    private ExtParamInfoService paramInfoService;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    /**
     * 神策数据开关
     */
    public static final String SENSORS_DATA_EVENT_TRACK = "SENSORS_DATA_EVENT_TRACK";

    public static final String PARAM_QUERY = "Y";
    /**
     * 时延处理
     */
    @Override
    public void functionDelay(String serviceName, LocalDateTime startTime) {
        try {
            if (!checkParam()) {
                logger.info("驾驶舱建设埋点参数已关闭");
                return;
            }
            Duration duration = Duration.between(startTime, DateTimeUtils.getCurrentLocalDateTime());
            //相差毫秒数
            int millis = (int) duration.toMillis();

            SensorsDataEventReq eventReq = new SensorsDataEventReq();
            eventReq.setInterfaceName(serviceName);
            eventReq.setTimeDelay(millis);
            GenericRspDTO<Response> response = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(SensorsEventTypeEnum.SENSORS, SensorsEventTypeEnum.DELAY.getName(), eventReq));

            if(StringUtils.contains(response.getMsgCd(),"00000")){
                logger.info(serviceName + "时延埋点发送成功: {}ms", millis);
            }
        } catch (Exception e) {
            logger.info(serviceName +"时延埋点发送失败");
        }
    }

    @Override
    public void functionRequest(String serviceName) {
        try {
            if (!checkParam()) {
                logger.info("驾驶舱建设埋点参数已关闭");
                return;
            }

            SensorsDataEventReq eventReq = new SensorsDataEventReq();
            eventReq.setInterfaceName(serviceName);
            GenericRspDTO<Response> response = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(SensorsEventTypeEnum.SENSORS, SensorsEventTypeEnum.FUNCTION_REQUEST.getName(), eventReq));

            if(StringUtils.contains(response.getMsgCd(),"00000")){
                logger.info(serviceName + "接受请求埋点发送成功");
            }
        } catch (Exception e) {
            logger.info(serviceName + "接受请求埋点发送失败");
        }
    }

    @Override
    public void requestSuccess(String serviceName) {
        try {
            if (!checkParam()) {
                logger.info("驾驶舱建设埋点参数已关闭");
                return;
            }

            SensorsDataEventReq eventReq = new SensorsDataEventReq();
            eventReq.setInterfaceName(serviceName);
            GenericRspDTO<Response> response = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(SensorsEventTypeEnum.SENSORS, SensorsEventTypeEnum.REQUEST_SUCCESS.getName(), eventReq));

            if(StringUtils.contains(response.getMsgCd(),"00000")){
                logger.info(serviceName + "请求成功埋点发送成功");
            }
        } catch (Exception e) {
            logger.info(serviceName + "请求成功埋点发送失败");
        }
    }

    /**
     * 通用：判断参数，是否执行数据发送
     *
     * @return
     */
    public boolean checkParam() {
        String paramValue = paramInfoService.getParameter(SENSORS_DATA_EVENT_TRACK);
        //Y开启，N关闭
        if (JudgeUtils.equals(paramValue, PARAM_QUERY)) {
            return true;
        }
        return false;
    }
}
