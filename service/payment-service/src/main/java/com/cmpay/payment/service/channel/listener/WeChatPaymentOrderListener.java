package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.wechat.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatPaymentOrderListener extends PaymentListenerService<PayOrderBO> {

    @Autowired
    ApplicationContext applicationContext;

    /**
     * 处理微信支付订单事件（统一下单或付款码支付）
     *
     * @param payOrderBO
     */
    @EventListener
    public void handleWeChatPaymentOrder(PayOrderBO payOrderBO) {
        execute(payOrderBO);
    }

    @Override
    protected boolean checkChannelExecutable(PayOrderBO payOrderBO) {
        return Optional.ofNullable(payOrderBO)
                .map(PayOrderBO::getPaymentRout)
                .filter(paymentRoute -> StringUtils.equals(paymentRoute, PaymentWayEnum.WECHAT.name().toLowerCase()))
                .isPresent();
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(PayOrderBO payOrderBO) {
        return Optional.ofNullable(payOrderBO)
                .map(PayOrderBO::getScene)
                .map(String::toUpperCase)
                .map(PaymentSceneEnum::valueOf)
                .map(scene -> {
                    PaymentChannelService paymentChannelService = null;
                    switch (scene) {
                        case BARCODE:
                            paymentChannelService = getBean(WeChatMicroPayService.class);
                            break;
                        case JSAPI:
                        case APPLET:
                            paymentChannelService = getBean(WeChatUnifiedOrderJSAPIService.class);
                            break;
                        case SCAN:
                            paymentChannelService = getBean(WeChatUnifiedOrderScanService.class);
                            break;
                        case APP:
                            paymentChannelService = getBean(WeChatUnifiedOrderAppService.class);
                            break;
                        case WAP:
                            paymentChannelService = getBean(WeChatUnifiedOrderH5Service.class);
                            break;
                        default:
                            break;
                    }
                    return paymentChannelService;
                })
                .orElse(null);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
