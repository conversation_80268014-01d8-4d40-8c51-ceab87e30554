package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.ProtocolPaymentBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.constant.protocol.DcepOrderStatusEnum;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.DcepMicroPayOrderReq;
import com.cmpay.payment.dto.dcep.DcepMicroPayOrderRsp;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.DcepPaymentMicroPayService;
import com.cmpay.payment.util.LemonAmount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/7/13
 */
@Service
public class DcepPaymentMicroPayServiceImpl extends AbstractDcepRequestServiceImpl implements DcepPaymentMicroPayService {

    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;
    private static final String TRADE_TYPE = "payment";

    @Override
    public void dcepPaymentProtocol(ProtocolPaymentBO protocolPaymentBO) {
        DcepMicroPayOrderReq microPayOrderReq = new DcepMicroPayOrderReq();
        buildRequestDto(microPayOrderReq, protocolPaymentBO, protocolPaymentBO.getChannelNo());
        microPayOrderReq.setOrgNo(dcepPaymentProperties.getNewOrgNo());
        microPayOrderReq.setSubMerchantNo(protocolPaymentBO.getSubMerchantId());
        microPayOrderReq.setQrCode(protocolPaymentBO.getQrCode());
        microPayOrderReq.setOutTranNo(protocolPaymentBO.getOutTradeNo());
        microPayOrderReq.setGoodsInfo(protocolPaymentBO.getProductDesc());
        microPayOrderReq.setBusiType(protocolPaymentBO.getBusinessType());
        microPayOrderReq.setBusiCode(protocolPaymentBO.getBusinessCode());
        microPayOrderReq.setExpireMinutes(Integer.parseInt(protocolPaymentBO.getExpireMinutes()));
        microPayOrderReq.setOutTranDate(protocolPaymentBO.getOrderDate());
        microPayOrderReq.setOutTranTime(protocolPaymentBO.getOrderTime());
        microPayOrderReq.setCurrType(DcepConstants.CURRENT_TYPE);
        microPayOrderReq.setAmount(Long.parseLong(new LemonAmount(protocolPaymentBO.getRealAmount()).yuan2fen()));
        microPayOrderReq.setNotifyUrl(dcepPaymentProperties.getNotifyUrl());
        buildDcepRequest(microPayOrderReq, protocolPaymentBO, DcepPaymentChannelEnum.PAYMENT_MICRO_PAY.getName());
    }

    @Override
    public void send(ProtocolPaymentBO protocolPaymentBO) {
        dcepPaymentProtocol(protocolPaymentBO);
    }

    @Override
    protected BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        DcepMicroPayOrderRsp microPayOrderRsp = (DcepMicroPayOrderRsp) abstractDcepRsp;
        ProtocolPaymentBO protocolPaymentBO = (ProtocolPaymentBO) baseDcepHandlerBO;
        if (StringUtils.equals(microPayOrderRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            DcepMicroPayOrderRsp.Data data = microPayOrderRsp.getData();
            DcepOrderStatusEnum orderStatusEnum = DcepOrderStatusEnum.getEnumName(TRADE_TYPE, data.getPayStatus());
            protocolPaymentBO.setTradeStatus(orderStatusEnum != null ? orderStatusEnum.name() : OrderStatusEnum.WAIT_PAY.name());
            String finishDateTime = data.getPaySuccessDate() + data.getPaySuccessTime();
            protocolPaymentBO.setFinishDateTime((StringUtils.isBlank(finishDateTime) || finishDateTime.length() != 14 ? DateTimeUtils.getCurrentDateTimeStr() : finishDateTime));
            protocolPaymentBO.setAccountDate(data.getPaySuccessDate());
            Optional.ofNullable(data.getErrCode()).ifPresent(errCode -> protocolPaymentBO.setErrCode(data.getErrCode()));
            Optional.ofNullable(data.getErrMsg()).ifPresent(errMsg -> protocolPaymentBO.setErrCodeDes(data.getErrMsg()));
        } else {
            protocolPaymentBO.setErrCode(microPayOrderRsp.getCode());
            protocolPaymentBO.setErrCodeDes(microPayOrderRsp.getMsg());
            protocolPaymentBO.setTradeStatus(OrderStatusEnum.TRADE_FAIL.name());
        }
        protocolPaymentBO.setErrMsgCd(protocolPaymentBO.getErrCode());
        protocolPaymentBO.setErrMsgInfo(protocolPaymentBO.getErrCodeDes());
        return protocolPaymentBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }
}
