package com.cmpay.payment.service.channel.aplipay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.AlipayConstants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.dto.alipay.TradeCreateReq;
import com.cmpay.payment.dto.alipay.TradeCreateRsp;
import com.cmpay.payment.properties.AlipayProperties;
import com.cmpay.payment.service.channel.aplipay.AlipayAppletPaymentChannelService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.PaymentUtils;
import com.cmpay.payment.util.UrlUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 2022/3/7
 */
@Service
public class AlipayAppletPaymentChannelServiceImpl implements AlipayAppletPaymentChannelService {

    private static final Logger logger = LoggerFactory.getLogger(AlipayAppletPaymentChannelServiceImpl.class);

    @Autowired
    AlipayProperties alipayProperties;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Override
    public void alipayAppletPayment(PayOrderBO payOrderBO) {
        TradeCreateReq tradeCreateReq = new TradeCreateReq();
        tradeCreateReq.setOutTradeNo(payOrderBO.getTradeOrderNo());
        tradeCreateReq.setSubject(payOrderBO.getSubject());
        tradeCreateReq.setBuyerId(payOrderBO.getBuyerId());
        tradeCreateReq.setTotalAmount(payOrderBO.getRealAmount().toString());

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                tradeCreateReq.setNotifyUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getNotifyUrl(),host));
                tradeCreateReq.setReturnUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getReturnUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                tradeCreateReq.setNotifyUrl(alipayProperties.getNotifyUrl());
                tradeCreateReq.setReturnUrl(alipayProperties.getReturnUrl());
            }
        } else {
            tradeCreateReq.setNotifyUrl(alipayProperties.getNotifyUrl());
            tradeCreateReq.setReturnUrl(alipayProperties.getReturnUrl());
        }

        try {
            GenericRspDTO<Response> response = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_CREATE.getName(), tradeCreateReq));
            if (StringUtils.contains(response.getMsgCd(), AlipayConstants.GATEWAY_SUCCESS)) {
                TradeCreateRsp tradeCreateRsp = (TradeCreateRsp) response.getBody().getResult();
                if (!StringUtils.equalsIgnoreCase(tradeCreateRsp.getMsg(), AlipayConstants.ALIPAY_SUCCESS)) {
                    payOrderBO.setErrMsgCd(tradeCreateRsp.getSubCode());
                    payOrderBO.setErrMsgInfo(tradeCreateRsp.getSubMsg());
                    BusinessException.throwBusinessException(MsgCodeEnum.ALIPAY_ORDER_PAY_FAIL);
                }
                payOrderBO.setPaymentOrderNo(tradeCreateRsp.getTradeNo());
            } else {
                BusinessException.throwBusinessException(MsgCodeEnum.ALIPAY_ORDER_PAY_FAIL);
            }
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.error("outTradeNo:{}", payOrderBO.getTradeOrderNo());
            } else {
                logger.error("sys error:{}, outTradeNo:{}", e, payOrderBO.getTradeOrderNo());
            }
            throw e;
        }
    }

    @Override
    public void send(PayOrderBO payOrderBO) {
        alipayAppletPayment(payOrderBO);
    }
}
