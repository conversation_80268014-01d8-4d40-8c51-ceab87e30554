package com.cmpay.payment.service.channel.listener;

import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.IntegralOrderQueryBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.TradeFinishExtDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.integralpay.IntegralPayFinishOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/28 17:02 IntegralFinishOrderQueryListener
 * @description ：
 */
@Service
public class IntegralFinishOrderQueryListener extends PaymentListenerService<IntegralOrderQueryBO> {

    @Autowired
    ApplicationContext applicationContext;

    @EventListener
    @Override
    public void execute(IntegralOrderQueryBO IntegralQueryBO) {
        super.execute(IntegralQueryBO);
    }

    @Override
    protected boolean checkChannelExecutable(IntegralOrderQueryBO IntegralQueryBO) {
        return Optional.ofNullable(IntegralQueryBO)
                .map(IntegralOrderQueryBO::getFinishOrder)
                .map(TradeFinishExtDO::getAimProductCode)
                .map(code -> {
                    return StringUtils.equalsIgnoreCase(IntegralQueryBO.getFinishOrder().getAimProductCode(), PaymentWayEnum.INTEGRALPAY.name());
                })
                .orElse(false);
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(IntegralOrderQueryBO paymentQueryBO) {
         return getBean(IntegralPayFinishOrderService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return this.applicationContext;
    }
}
