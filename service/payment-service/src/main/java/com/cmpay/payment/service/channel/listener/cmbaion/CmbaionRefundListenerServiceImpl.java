package com.cmpay.payment.service.channel.listener.cmbaion;

import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.cmbaion.CmbaionRefundChannelService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @author： PengAnHai
 * @date： 2024-08-13
 * @description：行一网通退款处理监听类
 * @modifiedBy：
 * @version: 1.0
 */
@Service
public class CmbaionRefundListenerServiceImpl extends PaymentListenerService<RefundBO> {

    @Autowired
    ApplicationContext applicationContext;

    @EventListener
    @Override
    public void execute(RefundBO eventBO) {
        super.execute(eventBO);
    }

    @Override
    protected boolean checkChannelExecutable(RefundBO refundBO) {
        return Optional.ofNullable(refundBO)
                .map(RefundBO::getOriginalPayTradeOrder)
                .map(order -> StringUtils.equalsIgnoreCase(PaymentWayEnum.CMBAION.name(), order.getAimProductCode()))
                .orElse(false);
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(RefundBO refundBO) {
        return getBean(CmbaionRefundChannelService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }

}
