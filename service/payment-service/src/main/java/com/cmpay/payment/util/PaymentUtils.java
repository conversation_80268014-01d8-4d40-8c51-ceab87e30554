package com.cmpay.payment.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cmpay.channel.common.utils.JudgeUtils;
import com.cmpay.channel.data.Request;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.payment.bo.AmountInfoBO;
import com.cmpay.payment.bo.ParamInfoBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.channel.PaymentOgwOutEnum;
import com.cmpay.payment.channel.dto.TradeNotifyReq;
import com.cmpay.payment.constant.CardTypeEnum;
import com.cmpay.payment.constant.CmpayAmountTypeEnum;
import com.cmpay.payment.constant.cmbaion.CmbaionConstants;
import com.cmpay.payment.dto.cmpay.CmpayOnlinePaymentQueryRspDTO;
import com.cmpay.payment.dto.cmpay.CmpayOnlineRefundQueryRspDTO;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created on 2018/11/20
 *
 * @author: sun_zhh
 */
public class PaymentUtils {
    public static GenericDTO<Request> buildRequest(String name, TradeNotifyReq taget, Object otherParams) {

        if (otherParams != null && taget != null) {
            taget.setOtherParams(JSONObject.toJSONString(otherParams));
        }
        return buildRequest(name, taget);
    }

    public static GenericDTO<Request> buildRequest(String name, Object taget) {

        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setRoute(PaymentOgwOutEnum.PAYMENT_PAY);
        request.setBusiType(name);
        request.setSource(PaymentOgwOutEnum.JK_PAY);
        request.setTarget(taget);
        return GenericDTO.newChildInstanceWithBody(GenericDTO.class, request);
    }

    public static GenericDTO<Request> buildRequest(String route, String name, Object taget) {

        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setRoute(route);
        request.setBusiType(name);
        request.setSource(PaymentOgwOutEnum.JK_PAY);
        request.setTarget(taget);
        return GenericDTO.newChildInstanceWithBody(GenericDTO.class, request);
    }

    public static GenericDTO<Request> buildRequest(String route, String name, String source, Object taget) {
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setRoute(route);
        request.setBusiType(name);
        request.setSource(source);
        request.setTarget(taget);
        return GenericDTO.newChildInstanceWithBody(GenericDTO.class, request);
    }
    /**
     *时间格式化
     * @param date
     * @return
     */
    public static  String getStringDate(String date) {
        //传入数据为2017-01-01 17：46：25 格式
        date=date.substring(0,4) +date.substring(5,7)+date.substring(8,10)
        +date.substring(11,13) +date.substring(14,16)+date.substring(17,19);
        return  date;
    }
    /**
     *时间格式化
     * @param date
     * @return
     */
    public static  String getDate(String date) {
        //传入数据为2017-01-01 17：46：25 格式
        date=date.substring(0,4) +date.substring(5,7)+date.substring(8,10);
        return  date;
    }

    /**
     * 时间格式化
     */
    public static String getDateString(String date){
        //传入数据为20200719格式
        date =date.substring(0,4) + "-" + date.substring(4,6)+ "-" +date.substring(6,8);
        return date;
    }
    /**
     *时间格式化
     * @param date
     * @return
     */
    public static  String getStrDate(Date date) {
        return  new SimpleDateFormat("yyyyMMddHHmmss").format(date);
    }

    /**
     * 判断是否是json格式字符串
     * @param str
     * @return
     */
    public static boolean isJSON2(String str) {
        boolean result = false;
        try {
            Object obj= JSON.parse(str);
            result = true;
        } catch (Exception e) {
            result=false;
        }
        return result;
    }
    /**
     * 判断BigDecimal类型，是否只有两位小数
     * @param amount
     * @return 两位小数返回true
     */
    public static boolean isTwoDecimalPlaces(BigDecimal amount) {
        return amount.scale() <= 2;
    }

    /**
     * BigDecimal元转分后转long
     * 注意：BigDecimal转long会直接舍弃小数点后的数字
     * @param decimal
     * @return
     */
    public static long bigdecimal2long(BigDecimal decimal){
        BigDecimal fen = decimal.multiply(new BigDecimal(100));
        return fen.longValue();
    }

    /**
     * 根据给定的卡类型字符串，返回相应的卡类型描述
     *
     * @param cardType 输入的卡类型字符串
     * @return 对应的卡类型描述
     */
    public static String setCmbaionCrdAcTyp(String cardType) {
        // 如果卡类型属于借记卡（包括招商银行和其他银行的借记卡）
        if (JudgeUtils.equalsAny(cardType, CmbaionConstants.CARD_TYPE_CMB_DEBIT, CmbaionConstants.CAR_TYPE_OTHER_DEBIT)) {
            // 返回借记卡类型描述
            return CardTypeEnum.DEBIT_CARD_TYPE.getDesc();
            // 如果卡类型属于信用卡（包括招商银行和其他银行的信用卡）
        } else if (JudgeUtils.equalsAny(cardType, CmbaionConstants.CARD_TYPE_CMB_CEBIT, CmbaionConstants.CAR_TYPE_OTHER_CEBIT)) {
            // 返回信用卡类型描述
            return CardTypeEnum.CEBIT_CARD_TYPE.getDesc();
            // 如果卡类型不匹配上述两种情况，返回默认卡类型描述
        } else {
            // 返回默认卡类型描述
            return CardTypeEnum.DEFUND_CARD_TYPE.getDesc();
        }
    }

    /**
     * 设置并返回一个包含特定金额类型信息的列表。
     *
     * @param amountInfoBOList 原始的金额信息列表
     * @param amountListParam 参数信息列表，其中包含需要匹配的金额类型
     * @return 包含特定金额类型信息的新列表，对于原始列表中不存在的金额类型，创建新的 {@link AmountInfoBO} 并设置金额为零
     */
    public static List<AmountInfoBO> setAmountInfoBOList(List<AmountInfoBO> amountInfoBOList, List<ParamInfoBO> amountListParam) {
        // 如果任一输入列表为空，直接返回 null
        if (JudgeUtils.isEmpty(amountInfoBOList) || JudgeUtils.isEmpty(amountListParam)) {
            return null;
        }
        // 使用 HashMap 存储金额信息，以便通过金额类型快速查找
        Map<String, AmountInfoBO> amountInfoBOMap = new HashMap<>();
        for (AmountInfoBO amountInfoBO : amountInfoBOList) {
            amountInfoBOMap.put(amountInfoBO.getAmountType(), amountInfoBO);
        }
        // 创建一个新列表用于存储最终的结果
        List<AmountInfoBO> amountInfoBOS = new ArrayList<>();
        // 遍历参数信息列表
        for (ParamInfoBO paramInfoBO : amountListParam) {
            String amountType = paramInfoBO.getParamValue();
            // 尝试从 HashMap 中获取与参数值匹配的金额信息
            AmountInfoBO amountInfoBO = amountInfoBOMap.get(amountType);
            // 如果找到匹配的金额信息，将其添加到结果列表
            if (JudgeUtils.isNotNull(amountInfoBO)) {
                amountInfoBOS.add(amountInfoBO);
            } else {
                // 如果没有找到匹配的金额信息，创建一个新的 AmountInfoBO 实例
                amountInfoBO = new AmountInfoBO();
                amountInfoBO.setAmountType(amountType);
                amountInfoBO.setAmount(BigDecimal.ZERO);
                amountInfoBOS.add(amountInfoBO);
            }
        }
        // 返回包含特定金额类型信息的新列表
        return amountInfoBOS;
    }

    public static List<AmountInfoBO> convertToAmountInfoList(TradeNotifyBO tradeNotify) {
        List<AmountInfoBO> result = new ArrayList<>();

        // 处理各个字段的映射
        addAmountIfValid(result, CmpayAmountTypeEnum.OWNER_COUPON_AMOUNT, tradeNotify.getOwnerCouponAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.MONEY_OFF_AMT, tradeNotify.getMoneyOffAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.OWNER_BONS_AMOUNT, tradeNotify.getOwnerBonsAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.RED_USE_AMOUNT, tradeNotify.getRedUseAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.INTEGRAL_AMOUNT, tradeNotify.getIntegralAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.SALE_AMOUNT, tradeNotify.getSaleAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.SALE_COUPON_AMOUNT, tradeNotify.getSaleCouponAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.SALE_BONUS_AMOUNT, tradeNotify.getSaleBonusAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.BALANCE_AMOUNT, tradeNotify.getBalanceAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.SUPPLY_AMOUNT, tradeNotify.getSupplyAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.CNY_AMT, tradeNotify.getCashAmount());

        return result;
    }

    public static List<AmountInfoBO> convertToAmountInfoList(CmpayOnlinePaymentQueryRspDTO paymentQueryRspDTO) {
        List<AmountInfoBO> result = new ArrayList<>();

        // 处理各个字段的映射
        addAmountIfValid(result, CmpayAmountTypeEnum.OWNER_COUPON_AMOUNT, paymentQueryRspDTO.getOwnerCouponAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.MONEY_OFF_AMT, paymentQueryRspDTO.getMoneyOffAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.OWNER_BONS_AMOUNT, paymentQueryRspDTO.getOwnerBonsAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.RED_USE_AMOUNT, paymentQueryRspDTO.getRedUseAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.INTEGRAL_AMOUNT, paymentQueryRspDTO.getIntegralAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.SALE_AMOUNT, paymentQueryRspDTO.getSaleAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.SALE_COUPON_AMOUNT, paymentQueryRspDTO.getSaleCouponAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.SALE_BONUS_AMOUNT, paymentQueryRspDTO.getSaleBonusAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.BALANCE_AMOUNT, paymentQueryRspDTO.getBalanceAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.SUPPLY_AMOUNT, paymentQueryRspDTO.getSupplyAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.CNY_AMT, paymentQueryRspDTO.getCashAmount());

        return result;
    }

    public static List<AmountInfoBO> convertToAmountInfoList(CmpayOnlineRefundQueryRspDTO onlineRefundQueryRspDTO) {
        List<AmountInfoBO> result = new ArrayList<>();

        // 处理各个字段的映射
        addAmountIfValid(result, CmpayAmountTypeEnum.OWNER_COUPON_AMOUNT, onlineRefundQueryRspDTO.getOwnerCouponAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.MONEY_OFF_AMT, onlineRefundQueryRspDTO.getMoneyOffAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.OWNER_BONS_AMOUNT, onlineRefundQueryRspDTO.getOwnerBonsAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.RED_USE_AMOUNT, onlineRefundQueryRspDTO.getRedUseAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.INTEGRAL_AMOUNT, onlineRefundQueryRspDTO.getIntegralAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.SALE_AMOUNT, onlineRefundQueryRspDTO.getSaleAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.SALE_COUPON_AMOUNT, onlineRefundQueryRspDTO.getSaleCouponAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.SALE_BONUS_AMOUNT, onlineRefundQueryRspDTO.getSaleBonusAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.BALANCE_AMOUNT, onlineRefundQueryRspDTO.getBalanceAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.SUPPLY_AMOUNT, onlineRefundQueryRspDTO.getSupplyAmount());
        addAmountIfValid(result, CmpayAmountTypeEnum.CNY_AMT, onlineRefundQueryRspDTO.getCashAmount());

        return result;
    }

    private static void addAmountIfValid(List<AmountInfoBO> list, CmpayAmountTypeEnum type, BigDecimal amount) {
        BigDecimal validAmount = nullSafeGet(amount);
        AmountInfoBO info = new AmountInfoBO();
        info.setAmountType(type.name());
        info.setAmount(validAmount);
        list.add(info);
    }

    private static BigDecimal nullSafeGet(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }

}
