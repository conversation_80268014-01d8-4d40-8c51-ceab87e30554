package com.cmpay.payment.service.channel.dcep;

import com.cmpay.payment.bo.protocol.ProtocolSignBO;
import com.cmpay.payment.service.channel.PaymentChannelService;

/**
 * Created on 2020/5/12
 *
 * @author: huang_yh1
 */
public interface DcepUserProtocolSignService extends PaymentChannelService<ProtocolSignBO> {
    /**
     * 协议签约
     *
     * @param protocolSignBO
     */
    void protocolSign(ProtocolSignBO protocolSignBO);
}
