package com.cmpay.payment.service.channel.aplipay.impl;

import com.cmpay.channel.common.utils.StringUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.AlipayBizContentBO;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.properties.AlipayProperties;
import com.cmpay.payment.service.channel.aplipay.AlipayAppPaymentChannelService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.AlipaySDKOrderInfoUtils;
import com.cmpay.payment.utils.AlipaySignUtils;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.net.URISyntaxException;
import java.util.Map;

/**
 * Created on 2018/12/13
 *
 * @author: wulinfeng
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class AlipayAppPaymentChannelServiceImpl extends AlipayBasePaymentChannelServiceImpl implements AlipayAppPaymentChannelService {
    private static final Logger logger = LoggerFactory.getLogger(AlipayAppPaymentChannelServiceImpl.class);
    @Autowired
    private AlipayProperties alipayProperties;
    @Autowired
    ExtParamInfoService paramInfoService;

    private static final String FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public boolean getStatus(PayOrderBO payOrderBO) {
        AlipayBizContentBO alipayBizContentBO = new AlipayBizContentBO();
        alipayBizContentBO.setOut_trade_no(payOrderBO.getTradeOrderNo());
        alipayBizContentBO.setSubject(payOrderBO.getSubject());
        alipayBizContentBO.setTotal_amount(payOrderBO.getRealAmount());
        String dateTime = DateTimeUtils.formatLocalDateTime(DateTimeUtils.parseLocalDateTime(payOrderBO.getExpireTime()), FORMAT);
        alipayBizContentBO.setTime_expire(dateTime);
        if (StringUtils.isNotBlank(payOrderBO.getTimeoutExpress())) {
            alipayBizContentBO.setTimeout_express(payOrderBO.getTimeoutExpress());
        }
        //app花呗支付
        if (JudgeUtils.equals(payOrderBO.getScene(), PaymentSceneEnum.APPPCREDIT.name().toLowerCase())) {
            alipayBizContentBO.setSpecified_channel(CommonConstant.ALIPAY_PCREDIT);
        }
//      营销参数不为空，设置值
        if(StringUtils.isNotBlank(payOrderBO.getPromoParams())){
//            alipayBizContentBO.setPromoParams(JSONObject.parseObject(payOrderBO.getPromoParams()).toJSONString());
            alipayBizContentBO.setPromo_params(payOrderBO.getPromoParams());
        }
        //alipayBizContentBO.setNotifyUrl(alipayProperties.getNotifyUrl());

        String notifyUrl = alipayProperties.getNotifyUrl();
        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                notifyUrl = UrlUtils.replaceDomainOrIp(alipayProperties.getNotifyUrl(),host);
            } catch (URISyntaxException e) {
                logger.info("通知地址域名获取异常");
                notifyUrl = alipayProperties.getNotifyUrl();
            }
        }

        Map<String, String> params = AlipaySDKOrderInfoUtils.buildOrderParamMap(alipayProperties.getAppid(), new Gson().toJson(alipayBizContentBO), AlipaySignUtils.AlipaySignTypeEnum.RSA2.name(), alipayProperties.getVersion(), notifyUrl);
        String orderParams = AlipaySDKOrderInfoUtils.buildOrderParam(params);

        String sign = AlipaySDKOrderInfoUtils.getSign(params, alipayProperties.getRsa2Private(), true);

        final String orderInfo = orderParams + "&" + sign;

        payOrderBO.setPayUrl(orderInfo);
        return true;
    }

    @Override
    public void alipayAppPayment(PayOrderBO payOrderBO) {
        super.alipayBasePayment(payOrderBO);
    }

    @Override
    public void send(PayOrderBO payOrderBO) {
        alipayAppPayment(payOrderBO);
    }

}
