package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.ProtocolPaymentBO;
import com.cmpay.payment.bo.utils.TripleDes;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.dao.config.ITrdContractDao;
import com.cmpay.payment.dto.dcep.*;
import com.cmpay.payment.entity.config.TrdContractDO;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.properties.ProtocolProperties;
import com.cmpay.payment.service.channel.dcep.DcepPaymentAppPayService;
import com.cmpay.payment.util.AES256Util;
import com.cmpay.payment.util.LemonAmount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
public class DcepPaymentAppPayServiceImpl extends AbstractDcepRequestServiceImpl implements DcepPaymentAppPayService {

    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;
    @Autowired
    private ProtocolProperties protocolProperties;
    @Autowired
    private ITrdContractDao trdContractDao;

    @Override
    public void send(ProtocolPaymentBO protocolPaymentBO) {
        dcepAppPayment(protocolPaymentBO);
    }

    @Override
    public void dcepAppPayment(ProtocolPaymentBO protocolPaymentBO) {
        DcepCashierPayReq cashierPayReq = new DcepCashierPayReq();
        protocolPaymentBO.setPayWayCode(PaymentSceneEnum.APP.name().toLowerCase());
        buildRequestDto(cashierPayReq, protocolPaymentBO, protocolPaymentBO.getChannelNo());
        cashierPayReq.setSubMerchantNo(protocolPaymentBO.getSubMerchantId());
        cashierPayReq.setOutTranNo(protocolPaymentBO.getOutTradeNo());
        if(StringUtils.isNotBlank(protocolPaymentBO.getMobileNo())){
            protocolPaymentBO.setMobileNo(TripleDes.decrypt(protocolProperties.getTripleDes(), protocolPaymentBO.getMobileNo()));
            TrdContractDO trdContractDO = trdContractDao.getContract(protocolPaymentBO.getChannelNo());
            if (JudgeUtils.isNull(trdContractDO)) {
                BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ROUT_CANNOT_VALIDITY);
            }
            cashierPayReq.setMobile(AES256Util.encode(trdContractDO.getSecretKey(), protocolPaymentBO.getMobileNo()));
        }
        cashierPayReq.setAmount(Long.parseLong(new LemonAmount(protocolPaymentBO.getRealAmount()).yuan2fen()));
        cashierPayReq.setOutTranDate(protocolPaymentBO.getOrderDate());
        cashierPayReq.setOutTranTime(protocolPaymentBO.getOrderTime());
        cashierPayReq.setBusiScene(protocolPaymentBO.getBusiScene());
        cashierPayReq.setExpireMinutes(Integer.parseInt(protocolPaymentBO.getExpireMinutes()));
        cashierPayReq.setNotifyUrl(dcepPaymentProperties.getNotifyUrl());
        cashierPayReq.setGoodsDesc(protocolPaymentBO.getProductDesc());
        cashierPayReq.setPayScene(protocolPaymentBO.getScene());
        cashierPayReq.setDeviceIp(protocolPaymentBO.getTerminalIp());
        if (StringUtils.isNotBlank(protocolPaymentBO.getPageNotifyUrl())){
            cashierPayReq.setPageNotifyUrl(protocolPaymentBO.getPageNotifyUrl());
        }
        cashierPayReq.setOsType(protocolPaymentBO.getOsType());
        buildDcepRequest(cashierPayReq, protocolPaymentBO, DcepPaymentChannelEnum.DCEP_CASHIER_PAY.getName());
    }

    @Override
    protected BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        DcepCashierPayRsp dcepCashierPayRsp = (DcepCashierPayRsp) abstractDcepRsp;
        ProtocolPaymentBO protocolPaymentBO = (ProtocolPaymentBO) baseDcepHandlerBO;
        if (StringUtils.equals(dcepCashierPayRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            DcepCashierPayRsp.Data data = dcepCashierPayRsp.getData();
            if (!StringUtils.isBlank(data.getPayUrl())) {
                protocolPaymentBO.setPayUrl(data.getPayUrl());
                protocolPaymentBO.setPaymentOrderNo(data.getPaySeq());
            }
            if (StringUtils.isNotBlank(data.getErrCode())) {
                protocolPaymentBO.setErrCode(data.getErrCode());
                protocolPaymentBO.setErrCodeDes(data.getErrMsg());
                BusinessException.throwBusinessException(MsgCodeEnum.DCEP_REUQEST_FAIL.getMsgCd());
            }
        } else {
            protocolPaymentBO.setErrCode(dcepCashierPayRsp.getCode());
            protocolPaymentBO.setErrCodeDes(dcepCashierPayRsp.getMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.DCEP_REUQEST_FAIL.getMsgCd());
        }
        return protocolPaymentBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }
}
