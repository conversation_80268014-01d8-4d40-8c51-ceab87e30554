package com.cmpay.payment.service.channel.integralpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.IntegralOrderQueryBO;
import com.cmpay.payment.channel.IntegralPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.IntegralOrderStatusEnum;
import com.cmpay.payment.constant.IntegralpayConstants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.dto.integralpay.IntegralOrderQueryReq;
import com.cmpay.payment.dto.integralpay.IntegralOrderQueryRsp;
import com.cmpay.payment.entity.TradeFinishExtDO;
import com.cmpay.payment.service.channel.integralpay.IntegralPayFinishOrderService;
import com.cmpay.lemon.common.utils.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/29 15:26
 * @description ：
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class IntegralPayFinishOrderServiceImpl implements IntegralPayFinishOrderService {
    private static final Logger logger = LoggerFactory.getLogger(IntegralPayFinishOrderServiceImpl.class);

    private static final String CODE_SUCCESS = "0000";
    private static final String PAGE = "1";
    private static final String ROWS = "10";

    @Autowired
    IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Value("${integralpay.channelCode:}")
    private String channelCode;

    @Override
    public void IntegralPayFinishOrder(IntegralOrderQueryBO integralOrderQueryBO) {
        if (checkQueryParams(integralOrderQueryBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.OUT_TRADE_NO_CANNOT_BE_EMPTY);
        }
        IntegralOrderQueryReq integralOrderQueryReq = new IntegralOrderQueryReq();
        integralOrderQueryReq.setMobile(integralOrderQueryBO.getFinishOrder().getMobileNumber());
        integralOrderQueryReq.setOrderId(integralOrderQueryBO.getBnkTraceNo());
        integralOrderQueryReq.setPage(PAGE);
        integralOrderQueryReq.setRows(ROWS);
        String jrn_no = RandomUtils.randomStringFixLength(16);
        integralOrderQueryReq.setTraceId(jrn_no);
        integralOrderQueryReq.setSpanId(jrn_no);
        integralOrderQueryReq.setServiceCode(IntegralpayConstants.S0004);
        integralOrderQueryReq.setChannelCode(channelCode);
        integralOrderQueryReq.setReqTime(DateFormatUtils.format(new Date(), "yyyyMMddHHmmss"));
        integralOrderQueryReq.setAccessChannel(IntegralpayConstants.API_APP);
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(IntegralPayChannelEnum.INTEGRAL_PAY);
        request.setRoute(IntegralPayChannelEnum.INTEGRAL_PAY);
        request.setBusiType(IntegralPayChannelEnum.INTEGRAL_ORDER_QUERY.getName());
        request.setTarget(integralOrderQueryReq);
        GenericRspDTO<Response> response = this.nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        if (JudgeUtils.isSuccess(response.getMsgCd())) {
            IntegralOrderQueryRsp integralOrderQueryRsp = (IntegralOrderQueryRsp) response.getBody().getResult();
            //登记积分商城订单号
            if(JudgeUtils.isNotNull(integralOrderQueryRsp)
                &&StringUtils.equals(CODE_SUCCESS, integralOrderQueryRsp.getRespCode())
                && JudgeUtils.isNotNull(integralOrderQueryRsp.getResult())
                &&integralOrderQueryRsp.getResult().size()==1){
                String status = integralOrderQueryRsp.getResult().get(0).getSubOrderStatus();
                IntegralOrderStatusEnum statusEnum = IntegralOrderStatusEnum.getMsgCodeEnum(status);
                switch (statusEnum) {
                    case TO_BE_SHIPPED:
                        break;
                    case SHIPPED:
                        if(JudgeUtils.equals(OrderStatusEnum.SEND_WAIT.name(),integralOrderQueryBO.getFinishOrder().getStatus())){
                            integralOrderQueryBO.getFinishOrder().setStatus(OrderStatusEnum.SEND_FINISH.name());
                        }
                        break;
                    case CLOSED:
                        if(JudgeUtils.equals(OrderStatusEnum.SEND_WAIT.name(),integralOrderQueryBO.getFinishOrder().getStatus())){
                            integralOrderQueryBO.getFinishOrder().setStatus(OrderStatusEnum.SEND_FINISH.name());
                        } else if (JudgeUtils.equals(OrderStatusEnum.SHIP_WAIT.name(),integralOrderQueryBO.getFinishOrder().getStatus())){
                            integralOrderQueryBO.getFinishOrder().setStatus(OrderStatusEnum.SHIP_FINISH.name());
                        }
                        break;
                    case RESCINDED:
                        if(JudgeUtils.equals(OrderStatusEnum.SEND_WAIT.name(),integralOrderQueryBO.getFinishOrder().getStatus())){
                            integralOrderQueryBO.getFinishOrder().setStatus(OrderStatusEnum.SEND_FAIL.name());
                        } else if (JudgeUtils.equals(OrderStatusEnum.SHIP_WAIT.name(),integralOrderQueryBO.getFinishOrder().getStatus())){
                            integralOrderQueryBO.getFinishOrder().setStatus(OrderStatusEnum.SHIP_FAIL.name());
                        }
                        integralOrderQueryBO.getFinishOrder().setErrMsgCd(MsgCodeEnum.INTEGRAL_ORDER_FIND_RESCINDED.getMsgCd());
                        integralOrderQueryBO.getFinishOrder().setErrMsgInfo(MsgCodeEnum.INTEGRAL_ORDER_FIND_RESCINDED.getMsgInfo());
                        break;
                    default:
                }
            } else {
                logger.error("=======integral order find false,integralOrderQueryRsp:",integralOrderQueryRsp.toString());
                BusinessException.throwBusinessException(MsgCodeEnum.INTEGRAL_ORDER_FIND_FALSE);
            }
        }
    }

    @Override
    public void send(IntegralOrderQueryBO integralOrderQueryBO) {
        IntegralPayFinishOrder(integralOrderQueryBO);
    }

    private boolean checkQueryParams(IntegralOrderQueryBO integralOrderQueryBO) {
        return Optional.ofNullable(integralOrderQueryBO)
                .map(IntegralOrderQueryBO::getFinishOrder)
                .map(TradeFinishExtDO::getOutTradeNo)
                .map(StringUtils::isEmpty)
                .orElse(false);
    }
}
