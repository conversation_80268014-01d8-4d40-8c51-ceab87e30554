package com.cmpay.payment.service.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.AlertCapable;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.Constants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.constant.wechat.WeChatTradeTypeEnum;
import com.cmpay.payment.dto.wechat.WXPayUnifiedorderRequest;
import com.cmpay.payment.dto.wechat.WXPayUnifiedorderResponse;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.channel.wechat.WeChatUnifiedOrderScanService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatUnifiedOrderScanServiceImpl implements WeChatUnifiedOrderScanService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatUnifiedOrderH5ServiceImpl.class);

    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    ExtParamInfoService paramInfoService;

    /**
     * 微信扫码支付统一下单
     *
     * @param payOrderBO
     */
    @Override
    public void unifiedOrderScan(PayOrderBO payOrderBO) {


        if (StringUtils.isBlank(payOrderBO.getProductCode())) {
            BusinessException.throwBusinessException(MsgCodeEnum.REQUEST_PARAM_CANNOT_BE_EMPTY);
        }

        WXPayUnifiedorderRequest wxPayUnifiedorderRequest = new WXPayUnifiedorderRequest();
        // 微信对body（商品描述）字段格式按使用场景有特殊要求，H5支付的商品字段规则：浏览器打开的移动网页的主页title名-商品概述，样例：腾讯充值中心-QQ会员充值
        wxPayUnifiedorderRequest.setBody(payOrderBO.getSubject());
        wxPayUnifiedorderRequest.setOutTradeNo(payOrderBO.getOutTradeNo());
        // 金额单位：分
        wxPayUnifiedorderRequest.setTotalFee(payOrderBO.getRealAmount().multiply(new BigDecimal(100)).intValue());
        wxPayUnifiedorderRequest.setSpbillCreateIp(payOrderBO.getClientIps());
        Optional.ofNullable(payOrderBO.getTradeDate() + payOrderBO.getTradeTime()).ifPresent(timeStart -> wxPayUnifiedorderRequest.setTimeStart(timeStart));
        Optional.ofNullable(payOrderBO.getExpireTime()).ifPresent(expireTime -> wxPayUnifiedorderRequest.setTimeExpire(expireTime));

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                wxPayUnifiedorderRequest.setNotifyUrl(UrlUtils.replaceDomainOrIp(weChatProperties.getNotifyUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                wxPayUnifiedorderRequest.setNotifyUrl(weChatProperties.getNotifyUrl());
            }
        } else {
            wxPayUnifiedorderRequest.setNotifyUrl(weChatProperties.getNotifyUrl());
        }

        wxPayUnifiedorderRequest.setTradeType(WeChatTradeTypeEnum.NATIVE.name());
        wxPayUnifiedorderRequest.setProductId(payOrderBO.getProductCode());
        wxPayUnifiedorderRequest.setMchId(payOrderBO.getPaymentId());
        wxPayUnifiedorderRequest.setKey(payOrderBO.getContractSecureValue());
        Optional.ofNullable(payOrderBO.getLimitPay()).ifPresent(limitPay -> wxPayUnifiedorderRequest.setLimitPay(limitPay));

        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.UNIFIEDORDER.getSource());
        request.setRoute(WXPayChannel.UNIFIEDORDER.getRoute());
        request.setBusiType(WXPayChannel.UNIFIEDORDER.getBusType());
        request.setTarget(wxPayUnifiedorderRequest);
        LocalDateTime sendTimeFirst = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        logger.info("WeChatUnifiedOrderScanServiceImpl Source : {} Route : {} BusiType : {}",request.getSource(),request.getRoute(),request.getBusiType());

        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        LocalDateTime responseTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        int count= 1;
        while (count <= Constants.WECHAT_ORDER_RESEND_NUMBER
                && JudgeUtils.equals(genericRspDTO.getMsgCd(), Constants.WECHAT_ORDER_MSGCD)
                && sendTimeFirst.plusSeconds(1).compareTo(responseTime) >= 0){
            logger.info("wechatOrderFail"+count +"Scan");
            count++;
            genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            responseTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
            if (JudgeUtils.isSuccess(genericRspDTO)) {
                logger.info("wechatunifiedorderSuccessScan");
                break;
            }
        }
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(payOrderBO, (WXPayUnifiedorderResponse) result));
    }

    @Override
    public void send(PayOrderBO payOrderBO) {
        unifiedOrderScan(payOrderBO);
    }

    private void handleResult(PayOrderBO payOrderBO, WXPayUnifiedorderResponse response) {
        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                payOrderBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
                payOrderBO.setPrepayId(response.getPrepayId());
                payOrderBO.setPayUrl(response.getCodeUrl());
            } else {
                logger.error("unified order failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getErrCode());
                payOrderBO.setErrMsgCd(response.getErrCode());
                payOrderBO.setErrMsgInfo(response.getErrCodeDes());
                AlertCapable alertCapable;
                switch (errorCodeEnum) {
                    case NOAUTH:
                        alertCapable = MsgCodeEnum.WECHAT_NO_AUTH;
                        break;
                    case NOTENOUGH:
                        alertCapable = MsgCodeEnum.WECHAT_NOT_ENOUGH;
                        break;
                    case ORDERPAID:
                        alertCapable = MsgCodeEnum.WECHAT_ORDER_PAID;
                        break;
                    case ORDERCLOSED:
                        alertCapable = MsgCodeEnum.WECHAT_ORDER_CLOSED;
                        break;
                    case OUT_TRADE_NO_USED:
                        alertCapable = MsgCodeEnum.WECHAT_OUT_TRADE_NO_USED;
                        break;
                    default:
                        alertCapable = MsgCodeEnum.WECHAT_UNIFIED_ORDER_FAILURE;
                        break;
                }
                BusinessException.throwBusinessException(alertCapable);
            }
        } else {
            logger.error("unified order failure, weChat return message is : {}", response.getReturnMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_UNIFIED_ORDER_FAILURE);
        }
    }
}
