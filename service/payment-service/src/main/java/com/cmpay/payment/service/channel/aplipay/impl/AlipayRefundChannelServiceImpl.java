package com.cmpay.payment.service.channel.aplipay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.dto.alipay.TradeRefundReq;
import com.cmpay.payment.dto.alipay.TradeRefundRsp;
import com.cmpay.payment.entity.AlipayRefundJrnDO;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.properties.AlipayProperties;
import com.cmpay.payment.service.channel.aplipay.AlipayRefundChannelService;
import com.cmpay.payment.service.ext.ExtAlipayRefundJrnService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.PaymentUtils;
import com.cmpay.payment.util.UrlUtils;
import feign.RetryableException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created on 2018/12/13
 *
 * @author: wulinfeng
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class AlipayRefundChannelServiceImpl implements AlipayRefundChannelService {
    Logger logger = LoggerFactory.getLogger(AlipayRefundChannelServiceImpl.class);
    @Autowired
    AlipayProperties alipayProperties;
    @Autowired
    ExtAlipayRefundJrnService extAlipayRefundJrnService;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    private static final String CODE_SUCCESS = "10000";
    private static final String FUND_NOT_CHANGE = "N";
    @Override
    public void alipayBaseRefund(RefundBO refundBO) {

        refundBO.setAsync(true);

        // 新增alipay退款流水记录
        AlipayRefundJrnDO alipayRefundJrnDO = extAlipayRefundJrnService.insertByNewTranscation(refundBO);
        try {
            // 调用alipay退款接口
            TradeOrderDO orderPay = refundBO.getOriginalPayTradeOrder();
            //取金科订单主键
            TradeRefundReq tradeRefundReq = new TradeRefundReq();
            tradeRefundReq.setOutTradeNo(orderPay.getTradeOrderNo());

            String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
            if (StringUtils.isNotBlank(host)) {
                try {
                    tradeRefundReq.setNotifyUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getNotifyUrl(),host));
                    tradeRefundReq.setReturnUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getReturnUrl(),host));
                } catch (Exception e) {
                    logger.info("通知地址域名获取异常");
                    tradeRefundReq.setNotifyUrl(alipayProperties.getRefundUrl());
                    tradeRefundReq.setReturnUrl(alipayProperties.getReturnUrl());
                }
            } else {
                tradeRefundReq.setNotifyUrl(alipayProperties.getRefundUrl());
                tradeRefundReq.setReturnUrl(alipayProperties.getReturnUrl());
            }

            tradeRefundReq.setRefundAmount(refundBO.getRefundAmount().toString());
            tradeRefundReq.setRefundReason(refundBO.getRefundReason());
            RefundOrderDO refundOrderDO = refundBO.getRefundOrder();
            //如果是部分退款
            tradeRefundReq.setOutRequestNo(refundOrderDO.getBankOrderNo());
            GenericRspDTO<Response> response = paymentCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_REFUND.getName(), tradeRefundReq));
            if (StringUtils.contains(response.getMsgCd(), "00000")) {
                TradeRefundRsp tradeRefundRsp = (TradeRefundRsp) response.getBody().getResult();
                if (JudgeUtils.equals(tradeRefundRsp.getCode(), CODE_SUCCESS) && JudgeUtils.isNotEmpty(tradeRefundRsp.getRefundFee())) {
                    if(JudgeUtils.equals(tradeRefundRsp.getFundChange(), FUND_NOT_CHANGE)){
                        alipayRefundJrnDO.setReturnMsg(MsgCodeEnum.ALIPAY_BUSINESS_FAIL.getMsgCd());
                        alipayRefundJrnDO.setReturnMsgInfo(response.getMsgInfo());
                        BusinessException.throwBusinessException(alipayRefundJrnDO.getReturnMsg());
                    } else {
                        alipayRefundJrnDO.setReturnMsg(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
                    }
                } else {
                    alipayRefundJrnDO.setReturnMsg(MsgCodeEnum.ALIPAY_BUSINESS_FAIL.getMsgCd());
                    alipayRefundJrnDO.setReturnMsgInfo(response.getMsgInfo());
                    //保存报错信息
                    refundBO.setErrMsgCd(tradeRefundRsp.getSubCode());
                    refundBO.setErrMsgInfo(tradeRefundRsp.getSubMsg());
                    BusinessException.throwBusinessException(alipayRefundJrnDO.getReturnMsg());
                }

            } else {

                alipayRefundJrnDO.setReturnMsg(MsgCodeEnum.ALIPAY_SYS_FAIL.getMsgCd());
                alipayRefundJrnDO.setReturnMsgInfo(response.getMsgInfo());
                BusinessException.throwBusinessException(alipayRefundJrnDO.getReturnMsg());
            }

        } catch (BusinessException e) {
            alipayRefundJrnDO.setReturnMsg(((BusinessException) e).getMsgCd());
            alipayRefundJrnDO.setReturnMsgInfo(e.getMessage());
            throw e;
        } catch (RetryableException e){
            if(JudgeUtils.isNotNull(e.getMessage())&&e.getMessage().contains(CommonConstant.READ_TIMED_OUT)){
                alipayRefundJrnDO.setReturnMsg(MsgCodeEnum.REQUEST_RETURN_TIME_OUT.getMsgCd());
                alipayRefundJrnDO.setReturnMsgInfo(e.getMessage());
            } else {
                alipayRefundJrnDO.setReturnMsg(MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd());
                alipayRefundJrnDO.setReturnMsgInfo(e.getMessage());
            }
            throw e;
        } catch (Exception e){
            alipayRefundJrnDO.setReturnMsg(MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd());
            alipayRefundJrnDO.setReturnMsgInfo(e.getMessage());
            throw e;
        } finally {
            //将异常信息更新到alipay退款流水记录
            extAlipayRefundJrnService.updateByNewTranscation(alipayRefundJrnDO);
            if (!MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd().equals(alipayRefundJrnDO.getReturnMsg())) {
                // 报错退出
                BusinessException.throwBusinessException(alipayRefundJrnDO.getReturnMsg());
            }

        }


    }

    @Override
    public void send(RefundBO refundBO) {
        alipayBaseRefund(refundBO);
    }

}
