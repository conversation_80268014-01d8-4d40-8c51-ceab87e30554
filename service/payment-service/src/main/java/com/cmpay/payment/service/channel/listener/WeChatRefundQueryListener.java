package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.config.ContractDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.wechat.WeChatRefundQueryService;
import com.cmpay.payment.service.ext.config.IExtContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
public class WeChatRefundQueryListener extends PaymentListenerService<RefundOrderQueryBO> {


    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    private IExtContractService contractService;

    /**
     * 微信H5支付查询退款
     *
     * @param refundOrderQueryBO
     */
    @EventListener
    public void handleWeChatRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {
        execute(refundOrderQueryBO);
    }


    @Override
    protected boolean checkChannelExecutable(RefundOrderQueryBO refundOrderQueryBO) {
        return Optional.ofNullable(refundOrderQueryBO)
                .map(RefundOrderQueryBO::getRefundOrder)
                .filter(refundOrderDO -> StringUtils.equals(PaymentWayEnum.WECHAT.name().toLowerCase(), refundOrderDO.getAimProductCode()))
                .isPresent();
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(RefundOrderQueryBO refundOrderQueryBO) {
        ContractDO contract = contractService.getContract(refundOrderQueryBO.getOriginalPayTradeOrder().getBankMerchantNo());
        refundOrderQueryBO.setContractSecureValue(contract.getSecretKey());
        return getBean(WeChatRefundQueryService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
