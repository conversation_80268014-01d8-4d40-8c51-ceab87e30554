package com.cmpay.payment.service.impl;

import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.entity.CmpayRefundJrnDO;
import com.cmpay.payment.service.TradeCommonService;
import com.cmpay.payment.util.DateUtils;
import org.springframework.stereotype.Service;

/**
 * @author： pengAnHai
 * @date： 2023-12-04
 * @description：交易公共服务类
 */
@Service
public class TradeCommonServiceImpl implements TradeCommonService {

    /**
     * 退款订单不存在时的处理
     * @param refundOrderQueryBO
     * @param msgCodeEnum
     */
    @Override
    public void handleRefundOrderNotExist(RefundOrderQueryBO refundOrderQueryBO, MsgCodeEnum msgCodeEnum) {
        String requestDateTime = refundOrderQueryBO.getRefundOrder().getOrderDate() + refundOrderQueryBO.getRefundOrder().getOrderTime();
        if (DateUtils.compareTimeLessThanThirty(requestDateTime)) {
            refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
        } else {
            refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
            refundOrderQueryBO.setErrMsgCd(msgCodeEnum.getMsgCd());
            refundOrderQueryBO.setErrMsgInfo(msgCodeEnum.getMsgInfo());
        }
    }

    /**
     * 和包退款订单不存在时的处理
     * @param refundOrderQueryBO
     * @param cmpayRefundJrnDO
     * @param msgCodeEnum
     */
    @Override
    public void handleCmpayRefundOrderNotExist(RefundOrderQueryBO refundOrderQueryBO, CmpayRefundJrnDO cmpayRefundJrnDO, MsgCodeEnum msgCodeEnum) {
        String requestDateTime = cmpayRefundJrnDO.getTradeDate() + cmpayRefundJrnDO.getTradeTime();
        if (DateUtils.compareTimeLessThanThirty(requestDateTime)) {
            refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
            cmpayRefundJrnDO.setTradeStatus(OrderStatusEnum.REFUND_WAIT.name());
        } else {
            cmpayRefundJrnDO.setRemark(msgCodeEnum.getMsgCd());
            cmpayRefundJrnDO.setReturnMsg(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            cmpayRefundJrnDO.setReturnMsgInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
            refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
            refundOrderQueryBO.setErrMsgCd(msgCodeEnum.getMsgCd());
            refundOrderQueryBO.setErrMsgInfo(msgCodeEnum.getMsgInfo());
            cmpayRefundJrnDO.setTradeStatus(OrderStatusEnum.REFUND_FAIL.name());
        }
    }

}
