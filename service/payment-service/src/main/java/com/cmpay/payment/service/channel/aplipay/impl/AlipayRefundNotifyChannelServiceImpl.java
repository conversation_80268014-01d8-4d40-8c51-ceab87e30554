package com.cmpay.payment.service.channel.aplipay.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.RefundNotifyBO;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.dto.alipay.TradeFastpayRefundQueryReq;
import com.cmpay.payment.entity.AlipayRefundJrnDO;
import com.cmpay.payment.service.channel.aplipay.AlipayRefundNotifyChannelService;
import com.cmpay.payment.service.ext.ExtAlipayRefundJrnService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created on 2018/12/25
 *
 * @author: wulinfeng
 */
@Service
public class AlipayRefundNotifyChannelServiceImpl implements AlipayRefundNotifyChannelService {

    private static Logger logger = LoggerFactory.getLogger(AlipayRefundNotifyChannelServiceImpl.class);

    @Autowired
    ExtAlipayRefundJrnService extAlipayRefundJrnService;



    @Override
    public void alipayRefundNotify(RefundNotifyBO refundNotifyBO) {

        RefundOrderQueryBO refundOrderQueryBO = refundNotifyBO.getRefundOrderQueryBO();

        AlipayRefundJrnDO queryAlipayRefundJrnDO = new AlipayRefundJrnDO();
        queryAlipayRefundJrnDO.setTradeOrderNo(refundOrderQueryBO.getRefundOrder().getTradeOrderNo());
        //  查询支付宝退款流水
        AlipayRefundJrnDO alipayRefundJrnDO = extAlipayRefundJrnService.load(queryAlipayRefundJrnDO);
        if (JudgeUtils.isNull(alipayRefundJrnDO)) {
            logger.info("Alipay Journal Record Not Exists,tradeOrderNo is {},msgcd is {}", MsgCodeEnum.MERCHANT_JOURNAL_INFO_INSERT_ERROR);
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_JOURNAL_INFO_INSERT_ERROR);
        }
        logger.info("Alipay Journal Record Query Success,tradeOrderNo is {}", alipayRefundJrnDO.getTradeOrderNo());

        // 支付宝退款流水加锁
        extAlipayRefundJrnService.lock(alipayRefundJrnDO);
        logger.info("Alipay Journal Record For Update Success,tradeOrderNo is {}", alipayRefundJrnDO.getTradeOrderNo());

        try {

            // 调用支付宝退款查询接口
            TradeFastpayRefundQueryReq refundQueryReq = new TradeFastpayRefundQueryReq();
            refundQueryReq.setOutTradeNo(refundOrderQueryBO.getOriginalPayTradeOrder().getTradeOrderNo());
            refundQueryReq.setOutRequestNo(refundOrderQueryBO.getRefundOrder().getBankOrderNo());

            alipayRefundJrnDO.setReturnMsg(refundNotifyBO.getMsgCode());
            alipayRefundJrnDO.setReturnMsgInfo(refundNotifyBO.getMsgInfo());

            if(refundNotifyBO.isSuccess()) {
                // 支付宝退款成功
                refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
                alipayRefundJrnDO.setTradeStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            }
            else {
                // 支付宝失败
                refundOrderQueryBO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
                alipayRefundJrnDO.setTradeStatus(OrderStatusEnum.REFUND_FAIL.name());
            }

        } catch (Exception e) {

            logger.info("Alipay Order Query Exception,tradeOrderNo is {},exception is {}",
                    alipayRefundJrnDO.getTradeOrderNo(), e);
            //系统异常，比如网络异常 记录异常日志
            alipayRefundJrnDO.setReturnMsg(MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd());
            alipayRefundJrnDO.setReturnMsgInfo(MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd());
            throw e;

        } finally {

            alipayRefundJrnDO.setReturnDate(DateTimeUtils.getCurrentDateStr());
            alipayRefundJrnDO.setReturnTime(DateTimeUtils.getCurrentTimeStr());
            //将异常信息更新到 alipay退款流水记录
            extAlipayRefundJrnService.updateByNewTranscation(alipayRefundJrnDO);
            logger.info("Alipay Journal Record Update Success,tradeOrderNo is {}",
                    alipayRefundJrnDO.getTradeOrderNo());
            if (!OrderStatusEnum.REFUND_SUCCESS.name().equals(alipayRefundJrnDO.getTradeStatus())) {
                // 报错退出
                BusinessException.throwBusinessException(alipayRefundJrnDO.getReturnMsg());
            }

        }


    }

    @Override
    public void send(RefundNotifyBO refundNotifyBO) {
        alipayRefundNotify(refundNotifyBO);
    }

}
