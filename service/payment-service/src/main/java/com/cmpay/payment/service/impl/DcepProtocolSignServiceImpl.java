package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.DcepIdBO;
import com.cmpay.payment.bo.ProtocolPaymentBO;
import com.cmpay.payment.bo.ProtocolSignInTokenBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.protocol.ProtocolAbolishBO;
import com.cmpay.payment.bo.protocol.ProtocolApplyBO;
import com.cmpay.payment.bo.protocol.ProtocolQueryBO;
import com.cmpay.payment.bo.protocol.ProtocolSignBO;
import com.cmpay.payment.bo.utils.TripleDes;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.constant.TradeTypeEnum;
import com.cmpay.payment.constant.protocol.DcepUserProtocolHandlerTypeEnum;
import com.cmpay.payment.constant.protocol.ProtocolSignFlagEnum;
import com.cmpay.payment.constant.protocol.ProtocolStatusEnum;
import com.cmpay.payment.entity.OrderPaymentAttachDO;
import com.cmpay.payment.entity.protocol.ProtocolDO;
import com.cmpay.payment.entity.protocol.ProtocolDeleteDO;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.properties.ProtocolProperties;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.IDcepUserProtocolSignService;
import com.cmpay.payment.service.ext.IDcepIdService;
import com.cmpay.payment.service.ext.protocol.IExtProtocolAbolishService;
import com.cmpay.payment.service.ext.protocol.IExtProtocolService;
import com.cmpay.payment.utils.RsaUtils;
import com.cmpay.payment.utils.TypeCheckUtils;
import com.cmpay.sso.internal.client.SsoInternalIgwClient;
import com.cmpay.sso.internal.dto.InternalTokenCheckReqDTO;
import com.cmpay.sso.internal.dto.InternalTokenCheckRspDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * Created on 2020/5/12
 *
 * @author: huang_yh1
 */
@Service
public class DcepProtocolSignServiceImpl extends DcepBaseHandler implements IDcepUserProtocolSignService {
    private static final Logger logger = LoggerFactory.getLogger(DcepProtocolSignServiceImpl.class);
    @Autowired
    private IExtProtocolService protocolService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ProtocolProperties protocolProperties;
    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;
    @Autowired
    private IExtProtocolAbolishService protocolAbolishService;
    @Autowired
    private IDcepIdService dcepIdService;
    @Autowired
    private AsynCommonService asynCommonService;
    @Resource
    private SsoInternalIgwClient ssoInternalClient;
    @Value("${icbcpay.targetChannelMark}")
    private String targetChannelMark;

    @Override
    public void protocolApply(ProtocolApplyBO protocolApplyBO) {
        try {
            checkApplyParam(protocolApplyBO);
            checkContractCode(protocolApplyBO);
            checkUserProtocolStatus(protocolApplyBO);
            handlerProtocolApply(protocolApplyBO);
        } catch (Exception e) {
            exceptionHandler(e, "protocolApply" + protocolApplyBO.getErrCodeDes());
        }
    }

    private void checkApplyParam(ProtocolApplyBO protocolApplyBO) {
        if (TypeCheckUtils.checkPaymentWay(PaymentWayEnum.valueOf(protocolApplyBO.getSignWay().toUpperCase()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.SIGN_WAY_ERROR);
        }
        if (TypeCheckUtils.checkPaymentScene(PaymentSceneEnum.valueOf(protocolApplyBO.getSignScene().toUpperCase()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.SIGN_SCENE_ERROR);
        }
        try {
            String idNo = RsaUtils.decryptByPrivateKey(protocolApplyBO.getIdNo());
            protocolApplyBO.setIdNo(idNo);
            String userName = RsaUtils.decryptByPrivateKey(protocolApplyBO.getUserName());
            protocolApplyBO.setUserName(userName);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgCodeEnum.ID_NO_ERROR);
            logger.error("decryptError:" + e.getMessage());
        }
    }


    private void checkContractCode(ProtocolApplyBO protocolApplyBO) {
        if (JudgeUtils.isNotNull(protocolService.findByContractCode(protocolApplyBO))) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_ALREADY_EXISTS);
        }
    }

    private void checkUserProtocolStatus(ProtocolApplyBO protocolApplyBO) {
        ProtocolPaymentBO protocolPaymentBO = new ProtocolPaymentBO();
        protocolPaymentBO.setMobileNo(TripleDes.encrypt(protocolProperties.getTripleDes(), protocolApplyBO.getMobileNo()));
        if (JudgeUtils.isNotNull(protocolService.findByContractByMblNo(protocolPaymentBO))) {
            BusinessException.throwBusinessException(MsgCodeEnum.MBL_NO_HAS_SIGN);
        }
    }

    private void handlerProtocolApply(ProtocolApplyBO protocolApplyBO) {
        ProtocolPaymentBO protocolPaymentBO = new ProtocolPaymentBO();
        protocolPaymentBO.setMerchantId(protocolApplyBO.getMerchantId());
        protocolPaymentBO.setSubMerchantId(protocolApplyBO.getSubMerchantId());
        checkMerchantAttach(protocolPaymentBO);
        String mobileNo = protocolApplyBO.getMobileNo();
        String idNo = protocolApplyBO.getIdNo();
        String userName = protocolApplyBO.getUserName();
        protocolApplyBO.setMobileNo(TripleDes.encrypt(protocolProperties.getTripleDes(), protocolApplyBO.getMobileNo()));
        protocolApplyBO.setIdNo(TripleDes.encrypt(protocolProperties.getTripleDes(), protocolApplyBO.getIdNo()));
        protocolApplyBO.setUserName(TripleDes.encrypt(protocolProperties.getTripleDes(), protocolApplyBO.getUserName()));
        protocolApplyBO.setBankMerchantNo(dcepPaymentProperties.getChannelNo());
        ProtocolDO protocolDO = protocolService.insertContractInfo(protocolApplyBO);
        protocolApplyBO.setMobileNo(mobileNo);
        protocolApplyBO.setIdNo(idNo);
        protocolApplyBO.setUserName(userName);
        protocolApplyBO.setHandlerType(DcepUserProtocolHandlerTypeEnum.APPLY.name());
        applicationContext.publishEvent(protocolApplyBO);
        protocolDO.setSerialNo(protocolApplyBO.getSerialNo());
        protocolDO.setAgreementId(protocolApplyBO.getAgreementId());
        protocolDO.setSignStatus(protocolApplyBO.getSignStatus());
        protocolDO.setApplyReturnCode(protocolApplyBO.getApplyReturnCode());
        protocolDO.setApplyReturnMsg(protocolApplyBO.getApplyReturnMsg());
        protocolService.updateContractInfo(protocolDO);
    }

    @Override
    public void protocolSign(ProtocolSignBO protocolSignBO) {
        try {
            ProtocolDO protocolDO = getUserProtocol(protocolSignBO);
            protocolSignBO.setAgreementId(protocolDO.getAgreementId());
            protocolSignBO.setSerialNo(protocolDO.getSerialNo());
            protocolSignBO.setHandlerType(DcepUserProtocolHandlerTypeEnum.SIGN.name());
            applicationContext.publishEvent(protocolSignBO);
            handlerProtocolSign(protocolDO, protocolSignBO);
            protocolSignBO.setAgreementStatus(protocolSignBO.getAgreementStatus());
            protocolSignBO.setSignedTime(protocolDO.getSignedTime());
            protocolSignBO.setSignWay(protocolDO.getSignWay());
            protocolSignBO.setSerialNo(protocolDO.getSerialNo());
            protocolSignBO.setOrderId(protocolDO.getOrderId());
            protocolSignBO.setErrCodeDes(protocolSignBO.getSignReturnMsg());
        } catch (Exception e) {
            exceptionHandler(e, "protocolSign" + protocolSignBO.getErrCodeDes());
        }
    }

    private ProtocolDO getUserProtocol(ProtocolSignBO protocolSignBO) {
        ProtocolApplyBO protocolApplyBO = new ProtocolApplyBO();
        protocolApplyBO.setAgreementReqNo(protocolSignBO.getAgreementReqNo());
        protocolApplyBO.setAgreementReqDate(protocolSignBO.getAgreementReqDate());
        protocolApplyBO.setSignStatus(ProtocolStatusEnum.CONTRACT_APPLY.getDesc());
        ProtocolDO protocolDO = protocolService.findByContractCode(protocolApplyBO);
        if (JudgeUtils.isNull(protocolDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_INFO_NOT_EXISTS);
        }
        return protocolDO;
    }

    private void handlerProtocolSign(ProtocolDO protocolDO, ProtocolSignBO protocolSignBO) {
        if (ProtocolStatusEnum.CONTRACT_SUCCESS.getDesc().equals(protocolSignBO.getAgreementStatus())) {
            protocolDO.setSignedTime(DateTimeUtils.getCurrentDateTimeStr());
        }
        protocolDO.setSignStatus(protocolSignBO.getAgreementStatus());
        protocolDO.setAgreementReqDate(protocolSignBO.getAgreementReqDate());
        protocolDO.setAgreementReqNo(protocolSignBO.getAgreementReqNo());
        protocolDO.setAgreementId(protocolSignBO.getAgreementId());
        protocolDO.setSignReturnCode(protocolSignBO.getSignReturnCode());
        protocolDO.setSignReturnMsg(protocolSignBO.getSignReturnMsg());
        protocolService.updateContractInfo(protocolDO);
        if (ProtocolStatusEnum.CONTRACT_SUCCESS.getDesc().equals(protocolSignBO.getAgreementStatus())) {
            handlerSignNotify(protocolDO);
        }
    }

    private void handlerSignNotify(ProtocolDO protocolDO) {
        if (!StringUtils.isAnyBlank(protocolDO.getMerchantNo(), protocolDO.getNotifyUrl())) {
            TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
            tradeNotifyBO.setMerchantNo(protocolDO.getMerchantNo());
            tradeNotifyBO.setTradeOrderNo(protocolDO.getAgreementReqNo());
            tradeNotifyBO.setOutOrderNo(protocolDO.getAgreementReqNo());
            tradeNotifyBO.setTradeDate(protocolDO.getAgreementReqDate());
            tradeNotifyBO.setNotifyType(TradeTypeEnum.SIGN.name().toLowerCase());
            tradeNotifyBO.setNotifyUrl(protocolDO.getNotifyUrl());
            tradeNotifyBO.setExtra(protocolDO.getExtra());
            tradeNotifyBO.setFinishDate(protocolDO.getSignedTime());
            asynCommonService.asyncNotify(tradeNotifyBO);
        }
    }

    @Override
    public void protocolAbolish(ProtocolAbolishBO protocolAbolishBO) {
        try {
            ProtocolDO protocolDO = checkUserProtocol(protocolAbolishBO);
            ProtocolDeleteDO protocolDeleteDO = protocolAbolishService.insertContractDeleteInfo(protocolDO, protocolAbolishBO);
            protocolAbolishBO.setHandlerType(DcepUserProtocolHandlerTypeEnum.CANCEL.name());
            applicationContext.publishEvent(protocolAbolishBO);
            protocolAbolishDeal(protocolDeleteDO, protocolAbolishBO, protocolDO);
            notifyMerchantAbolish(protocolDeleteDO, protocolDO);
            protocolAbolishBO.setErrCodeDes(protocolAbolishBO.getReturnMsg());
        } catch (Exception e) {
            exceptionHandler(e, "protocolAbolish" + protocolAbolishBO.getErrCodeDes());
        }
    }

    private ProtocolDO checkUserProtocol(ProtocolAbolishBO protocolAbolishBO) {
        ProtocolApplyBO protocolApplyBO = new ProtocolApplyBO();
        protocolApplyBO.setAgreementReqNo(protocolAbolishBO.getAgreementReqNo());
        protocolApplyBO.setAgreementReqDate(protocolAbolishBO.getAgreementReqDate());
        protocolApplyBO.setAgreementId(protocolAbolishBO.getAgreementId());
        protocolApplyBO.setSignStatus(ProtocolStatusEnum.CONTRACT_SUCCESS.name());
        ProtocolDO protocolDO = protocolService.findByContractCode(protocolApplyBO);
        if (JudgeUtils.isNull(protocolDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_NOT_EXIST);
        }
        return protocolDO;
    }

    private void protocolAbolishDeal(ProtocolDeleteDO protocolDeleteDO, ProtocolAbolishBO protocolAbolishBO, ProtocolDO protocolDO) {
        protocolDeleteDO.setReturnCode(protocolAbolishBO.getReturnCode());
        protocolDeleteDO.setReturnMsg(protocolAbolishBO.getReturnMsg());
        protocolDeleteDO.setSignStatus(protocolAbolishBO.getSignStatus());
        if (ProtocolStatusEnum.CONTRACT_TERMINATED.getDesc().equals(protocolAbolishBO.getSignStatus())) {
            protocolDeleteDO.setTerminatedTime(DateTimeUtils.getCurrentDateTimeStr());
        }
        protocolAbolishService.updateContractDeleteInfo(protocolDeleteDO);
        if (ProtocolStatusEnum.CONTRACT_TERMINATED.getDesc().equals(protocolAbolishBO.getSignStatus())) {
            protocolDO.setSignStatus(protocolAbolishBO.getSignStatus());
            protocolDO.setTerminatedTime(protocolDeleteDO.getTerminatedTime());
            protocolService.updateContractInfo(protocolDO);
        }
    }

    private void notifyMerchantAbolish(ProtocolDeleteDO protocolDeleteDO, ProtocolDO protocolDO) {
        if (!StringUtils.isAnyBlank(protocolDeleteDO.getMerchantNo(), protocolDeleteDO.getNotifyUrl())) {
            TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
            tradeNotifyBO.setMerchantNo(protocolDO.getMerchantNo());
            tradeNotifyBO.setTradeOrderNo(protocolDeleteDO.getAgreementReqNo());
            tradeNotifyBO.setOutOrderNo(protocolDO.getAgreementReqNo());
            tradeNotifyBO.setOutRequestNo(protocolDeleteDO.getAgreementReqNo());
            tradeNotifyBO.setNotifyType(TradeTypeEnum.ABOLISH.name().toLowerCase());
            tradeNotifyBO.setTradeDate(protocolDeleteDO.getAgreementReqDate());
            tradeNotifyBO.setNotifyUrl(protocolDeleteDO.getNotifyUrl());
            tradeNotifyBO.setExtra(protocolDeleteDO.getExtra());
            tradeNotifyBO.setFinishDate(protocolDeleteDO.getTerminatedTime());
            asynCommonService.asyncNotify(tradeNotifyBO);
        }
    }

    @Override
    public void protocolQuery(ProtocolQueryBO protocolQueryBO) {
        ProtocolDO protocolDO = checkUserProtocol(protocolQueryBO);
        protocolQueryBO.setAgreementStatus(protocolDO.getSignStatus());
        protocolQueryBO.setAgreementId(protocolDO.getAgreementId());
        if (JudgeUtils.equalsAny(protocolQueryBO.getAgreementStatus(), ProtocolStatusEnum.CONTRACT_SUCCESS.name(),
                ProtocolStatusEnum.CONTRACT_TERMINATED.name(), ProtocolStatusEnum.CONTRACT_TERMINATED_WAIT.name())) {
            protocolQueryBO.setSignedTime(protocolDO.getSignedTime());
            protocolQueryBO.setExtra(protocolDO.getExtra());
        }
        if (JudgeUtils.equals(protocolQueryBO.getAgreementStatus(), ProtocolStatusEnum.CONTRACT_TERMINATED.name())) {
            ProtocolDeleteDO protocolDeleteDO = checkUserProtocolDelete(protocolQueryBO);
            protocolQueryBO.setTerminatedTime(protocolDO.getTerminatedTime());
            protocolQueryBO.setExtra(protocolDeleteDO.getExtra());
        }
    }

    private ProtocolDO checkUserProtocol(ProtocolQueryBO protocolQueryBO) {
        ProtocolApplyBO protocolApplyBO = new ProtocolApplyBO();
        protocolApplyBO.setAgreementReqNo(protocolQueryBO.getAgreementReqNo());
        protocolApplyBO.setAgreementReqDate(protocolQueryBO.getAgreementReqDate());
        protocolApplyBO.setAgreementId(protocolQueryBO.getAgreementId());
        ProtocolDO protocolDO = protocolService.findProtocol(protocolApplyBO);
        if (JudgeUtils.isNull(protocolDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_NOT_EXIST);
        }
        return protocolDO;
    }

    private ProtocolDeleteDO checkUserProtocolDelete(ProtocolQueryBO protocolQueryBO) {
        ProtocolAbolishBO protocolAbolishBO = new ProtocolAbolishBO();
        protocolAbolishBO.setAgreementReqDate(protocolQueryBO.getAgreementReqDate());
        protocolAbolishBO.setAgreementReqNo(protocolQueryBO.getAgreementReqNo());
        ProtocolDeleteDO protocolDeleteDO = protocolAbolishService.findByContractCode(protocolAbolishBO);
        if (JudgeUtils.isNull(protocolDeleteDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_NOT_EXIST);
        }
        return protocolDeleteDO;
    }

    @Override
    public void protocolWapQuery(ProtocolQueryBO protocolQueryBO) {
        try {
            ProtocolPaymentBO protocolPaymentBO = new ProtocolPaymentBO();
            protocolPaymentBO.setOutTradeNo(protocolQueryBO.getOutTradeNo());
            protocolPaymentBO.setTradeDate(protocolQueryBO.getTradeDate());
            OrderPaymentAttachDO paymentAttachDO = checkOrderAttach(protocolPaymentBO);
            protocolQueryBO.setHandlerType(DcepUserProtocolHandlerTypeEnum.QUERY.name());
            protocolQueryBO.setMobileNo(TripleDes.decrypt(protocolProperties.getTripleDes(), paymentAttachDO.getMobileNo()));
            protocolQueryBO.setSubMerchantId(paymentAttachDO.getSubMerchantId());
            applicationContext.publishEvent(protocolQueryBO);
        } catch (Exception e) {
            exceptionHandler(e,"protocolWapQuery" + protocolQueryBO.getErrMsgInfo());
        }
    }

    @Override
    public void getMobileNo(ProtocolSignInTokenBO protocolSignInTokenBO) {
        InternalTokenCheckReqDTO internalTokenCheckReqDTO = new InternalTokenCheckReqDTO();
        buildRequest(protocolSignInTokenBO, internalTokenCheckReqDTO);
        InternalTokenCheckRspDTO internalTokenCheckRspDTO = ssoInternalClient.checkInternalToken(internalTokenCheckReqDTO);
        if (JudgeUtils.isNull(internalTokenCheckRspDTO) || JudgeUtils.isNotSuccess(internalTokenCheckRspDTO.getMsgCd())) {
            BusinessException.throwBusinessException(internalTokenCheckRspDTO.getMsgCd());
        }
        protocolSignInTokenBO.setMobileNo(internalTokenCheckRspDTO.getMobileNo());
        protocolSignInTokenBO.setExtraString(internalTokenCheckRspDTO.getExtraString());
        protocolSignInTokenBO.setJoinChannelMark(internalTokenCheckRspDTO.getJoinChannelMark());
    }

    private void buildRequest(ProtocolSignInTokenBO protocolSignInTokenBO, InternalTokenCheckReqDTO internalTokenCheckReqDTO) {
        internalTokenCheckReqDTO.setRequestId(UUID.randomUUID().toString());
        internalTokenCheckReqDTO.setInternalToken(protocolSignInTokenBO.getForeignToken());
        internalTokenCheckReqDTO.setTargetChannelMark(targetChannelMark);
    }

    @Override
    public ProtocolQueryBO protocolInfoQuery(ProtocolQueryBO protocolQueryBO) {
        ProtocolPaymentBO protocolPaymentBO = new ProtocolPaymentBO();
        protocolPaymentBO.setMobileNo(TripleDes.encrypt(protocolProperties.getTripleDes(), protocolQueryBO.getMobileNo()));
        ProtocolDO protocolDO = protocolService.findByContractByMblNo(protocolPaymentBO);
        DcepIdBO dcepIdBO = new DcepIdBO();
        dcepIdBO.setMobileNo(protocolQueryBO.getMobileNo());
        dcepIdService.addDcepId(dcepIdBO);
        protocolQueryBO.setDcepId(dcepIdBO.getDcepId());
        if (JudgeUtils.isNotNull(protocolDO)) {
            protocolQueryBO.setSignFlag(ProtocolSignFlagEnum.SIGN.getCode());
            BeanUtils.copyProperties(protocolQueryBO, protocolDO);
        }
        protocolQueryBO.setSignFlag(ProtocolSignFlagEnum.UNSIGN.getCode());
        return protocolQueryBO;
    }
}
