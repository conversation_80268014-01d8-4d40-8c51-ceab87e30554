package com.cmpay.payment.service.channel.integralpay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.channel.IntegralPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.IntegralPaymentStatusEnum;
import com.cmpay.payment.constant.IntegralpayConstants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.dto.integralpay.IntegralCancelOrderReq;
import com.cmpay.payment.dto.integralpay.IntegralCancelOrderRsp;
import com.cmpay.payment.service.channel.integralpay.IntegralRefundService;
import com.cmpay.payment.util.PaymentUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR> wenzhan
 * @Date 2021-9-1 0001 21:22
 */
@Service
public class IntegralRefundServiceImpl implements IntegralRefundService {
    private static final Logger logger = LoggerFactory.getLogger(IntegralRefundServiceImpl.class);
    @Autowired
    IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Override
    public void send(RefundBO refundBO) {
        refundBO.setAsync(true);
        IntegralCancelOrderReq integralCancelOrderReq = new IntegralCancelOrderReq();
        integralCancelOrderReq.setVersion(IntegralpayConstants.VERSION);
        integralCancelOrderReq.setIdentityId(refundBO.getIntegralMercNo());
        integralCancelOrderReq.setSource(IntegralpayConstants.SOURCE);
        IntegralCancelOrderReq.ReqBody reqBody = new IntegralCancelOrderReq.ReqBody();
        reqBody.setOrderId(refundBO.getRefundOrder().getBankOrderNo());
        reqBody.setMemo(refundBO.getRefundReason());
        reqBody.setUserPhone(refundBO.getOriginalPayTradeOrder().getMobileNumber());
        integralCancelOrderReq.setData(reqBody);
        logger.info("integralCancelOrderReq : {}",integralCancelOrderReq.toString());

        GenericRspDTO<Response> genericRspDTO = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(IntegralPayChannelEnum.INTEGRAL_PAY, IntegralPayChannelEnum.INTEGRAL_CANCEL_ORDER.getName(), IntegralPayChannelEnum.INTEGRAL_PAY, integralCancelOrderReq));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(refundBO, (IntegralCancelOrderRsp) result));
    }

    public void handleResult(RefundBO refundBO, IntegralCancelOrderRsp integralCancelOrderRsp){
        logger.info("integralCancelOrderRsp : {}",integralCancelOrderRsp.toString());
        if(JudgeUtils.equals(integralCancelOrderRsp.getCode(), IntegralpayConstants.VIRTUAL_SUCCESS_CODE)){
            refundBO.setStatus(IntegralPaymentStatusEnum.REFUND_WAIT.name());
        }else{
            refundBO.setStatus(IntegralPaymentStatusEnum.REFUND_FAIL.name());
            refundBO.setErrMsgInfo(integralCancelOrderRsp.getMsg());
            refundBO.setErrMsgCd(integralCancelOrderRsp.getCode());
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ERROR);
        }
    }
}
