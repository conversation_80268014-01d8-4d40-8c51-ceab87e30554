package com.cmpay.payment.service.channel.cmpay.impl;


import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.constant.CgwReturnCodeEnum;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.entity.CmpayRefundJrnDO;
import com.cmpay.payment.service.channel.cmpay.CmpayCommonService;
import feign.RetryableException;
import org.springframework.stereotype.Service;

@Service
public class CmpayCommonServiceImpl implements CmpayCommonService {

    /**
     * 网关返回异常处理
     * @param genericRspDTO
     */
    public void cgwReturnInfoCheck(GenericRspDTO<Response> genericRspDTO){
        if(StringUtils.equals(CgwReturnCodeEnum.RECEIVE_MSG_IS_NULL.getMsgCode(),genericRspDTO.getMsgCd())){
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_RETURN_MSG_IS_NULL);
        } else if(StringUtils.equals(CgwReturnCodeEnum.UNKNOWN_EXCEPTION.getMsgCode(),genericRspDTO.getMsgCd())&&
                JudgeUtils.isNotNull(genericRspDTO.getMsgInfo())&&genericRspDTO.getMsgInfo().contains(CommonConstant.READ_TIMED_OUT)){
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_RETURN_TIME_OUT);
        }
    }
    //和包捕获异常处理
    public void cmapyThrowClassHandle(CmpayRefundJrnDO cmpayRefundJrnDO, Exception e){
        if(e instanceof BusinessException){
            cmpayRefundJrnDO.setReturnMsg(((BusinessException) e).getMsgCd());
            cmpayRefundJrnDO.setReturnMsgInfo(((BusinessException) e).getMsgInfo());
        } else if ((e instanceof RetryableException)
                &&JudgeUtils.isNotNull(e.getMessage())&&e.getMessage().contains(CommonConstant.READ_TIMED_OUT)) {
            cmpayRefundJrnDO.setReturnMsg(MsgCodeEnum.CMPAY_RETURN_TIME_OUT.getMsgCd());
            cmpayRefundJrnDO.setReturnMsgInfo(MsgCodeEnum.CMPAY_RETURN_TIME_OUT.getMsgInfo());
        } else {
            //系统异常，比如网络异常 记录异常日志
            cmpayRefundJrnDO.setReturnMsg(MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd());
            cmpayRefundJrnDO.setReturnMsgInfo(MsgCodeEnum.REFUND_SYS_ERROR.getMsgInfo());
        }
    }
}
