package com.cmpay.payment.service.channel.listener.cmbaion;

import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.cmbaion.CmbaionRefundQueryChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @author： PengAnHai
 * @date： 2024-08-21
 * @description：招行一网通退款查询处理监听类
 * @modifiedBy：
 * @version: 1.0
 */
@Service
public class CmbaionRefundQueryListenerServiceImpl extends PaymentListenerService<RefundOrderQueryBO> {
    @Autowired
    ApplicationContext applicationContext;

    @EventListener
    @Override
    public void execute(RefundOrderQueryBO refundQueryBO) {
        super.execute(refundQueryBO);
    }

    @Override
    protected boolean checkChannelExecutable(RefundOrderQueryBO refundQueryBO) {
        return Optional.ofNullable(refundQueryBO)
                .map(RefundOrderQueryBO::getRefundOrder)
                .map(RefundOrderDO::getAimProductCode)
                .map(route -> StringUtils.equalsIgnoreCase(PaymentWayEnum.CMBAION.name(), route))
                .orElse(false);
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(RefundOrderQueryBO refundQueryBO) {
        return getBean(CmbaionRefundQueryChannelService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return this.applicationContext;
    }

}
