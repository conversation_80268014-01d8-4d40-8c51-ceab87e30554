package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.IntegralFinishQueryBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.dao.ITradeFinishExtDao;
import com.cmpay.payment.entity.TradeFinishDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.IntegralFinishQueryService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> wenzhan
 * @Date 2021-10-9 0009 9:41
 */
@Service
public class IntegralFinishQueryServiceImpl implements IntegralFinishQueryService {
    @Autowired
    private ITradeFinishExtDao tradeFinishDao;
    @Autowired
    ExtPayOrderService payOrderService;
    @Override
    public IntegralFinishQueryBO finishQuery(IntegralFinishQueryBO integralFinishQueryBO) {
        // 检查传入参数
        if (JudgeUtils.isNull(integralFinishQueryBO.getMerchantId())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NO_CANNOT_BE_EMPTY);
        }
        // 查询完结订单信息
        TradeFinishDO tradeFinishDO = new TradeFinishDO();
        tradeFinishDO.setMerchantNo(integralFinishQueryBO.getMerchantId());
        tradeFinishDO.setRequestDate(integralFinishQueryBO.getTradeDate());
        tradeFinishDO.setOutTradeNo(integralFinishQueryBO.getOutRequestNo());
        tradeFinishDO.setOrgOrderNo(integralFinishQueryBO.getOutTradeNo());
        List<TradeFinishDO> tradeOrderList = tradeFinishDao.find(tradeFinishDO);
        if (JudgeUtils.isNull(tradeOrderList)||tradeOrderList.size()<1) {
            BusinessException.throwBusinessException(MsgCodeEnum.FINISH_ORDER_NOT_EXISTS);
        }
        // 查询完结原支付订单总金额
        TradeOrderDO payOrderQuery = new TradeOrderDO();
        payOrderQuery.setMerchantNo(integralFinishQueryBO.getMerchantId());
        payOrderQuery.setOutTradeNo(integralFinishQueryBO.getOutTradeNo());
        TradeOrderDO oriPayOrder = payOrderService.load(payOrderQuery);
        if (JudgeUtils.isNull(oriPayOrder)) {
            BusinessException.throwBusinessException(MsgCodeEnum.FINISH_ORG_ORDER_NOT_EXISTS);
        }
        tradeFinishDO = tradeOrderList.get(0);
        integralFinishQueryBO.setStatus(tradeFinishDO.getStatus());
        return integralFinishQueryBO;
    }
}
