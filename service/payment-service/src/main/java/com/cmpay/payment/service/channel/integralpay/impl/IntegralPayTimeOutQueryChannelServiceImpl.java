package com.cmpay.payment.service.channel.integralpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.RandomUtils;
import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.channel.IntegralPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.AppEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.dto.integralpay.IntegralExchangeReq;
import com.cmpay.payment.dto.integralpay.IntegralExchangeRsp;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.channel.integralpay.IntegralPayTimeOutQueryChannelService;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/24 10:55
 * @description ：
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class IntegralPayTimeOutQueryChannelServiceImpl implements IntegralPayTimeOutQueryChannelService {
    private static final Logger logger = LoggerFactory.getLogger(IntegralPayTimeOutQueryChannelServiceImpl.class);

    private static final String CODE_SUCCESS = "0000";

    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;

    @Override
    public void integralTimeOutPaymentQuery(PaymentQueryBO paymentQueryBO) {
        if (checkQueryParams(paymentQueryBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.OUT_TRADE_NO_CANNOT_BE_EMPTY);
        }
        IntegralExchangeReq integralExchangeReq = new IntegralExchangeReq();
        integralExchangeReq.setMobile(paymentQueryBO.getPaymentOrder().getMobileNumber());
        IntegralExchangeReq.GoodInfo goodInfo = new IntegralExchangeReq.GoodInfo();
        List<IntegralExchangeReq.GoodInfo> goodInfoList = new ArrayList<>();
        goodInfo.setGoodId(paymentQueryBO.getPaymentOrder().getGoodsId());
        goodInfo.setGoodCount(String.valueOf(paymentQueryBO.getPaymentOrder().getQuantity()));
        goodInfo.setGoodPoints(String.valueOf(paymentQueryBO.getPaymentOrder().getOrderAmount().multiply(new BigDecimal(110))));
        goodInfoList.add(goodInfo);
        integralExchangeReq.setGoodInfo(goodInfoList);
        integralExchangeReq.setCustomerIp(paymentQueryBO.getPaymentOrder().getOrderIp());
        integralExchangeReq.setOrderPoints(String.valueOf(paymentQueryBO.getPaymentOrder().getOrderAmount()));
        String jrn_no = RandomUtils.randomStringFixLength(16);
        integralExchangeReq.setTraceId(paymentQueryBO.getPaymentOrder().getOutTradeNo());
        integralExchangeReq.setSpanId(jrn_no);
        integralExchangeReq.setServiceCode("S0002");
        integralExchangeReq.setChannelCode("200000000000147");
        integralExchangeReq.setReqTime(DateFormatUtils.format(new Date(), "yyyyMMddHHmmss"));
        integralExchangeReq.setAccessChannel("1006");
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(IntegralPayChannelEnum.INTEGRAL_PAY);
        request.setRoute(IntegralPayChannelEnum.INTEGRAL_PAY);
        request.setBusiType(IntegralPayChannelEnum.INTEGRAL_EXCHANGE.getName());
        request.setTarget(integralExchangeReq);
        GenericRspDTO<Response> response = null;
        if (StringUtils.equalsAny(paymentQueryBO.getSourceApp(), AppEnum.integrationbatch.name(), AppEnum.integrationshecdule.name())) {
            response =this.nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        } else {
            response =this.paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        }
        if (JudgeUtils.isSuccess(response.getMsgCd())) {
            IntegralExchangeRsp integralExchangeRsp = (IntegralExchangeRsp) response.getBody().getResult();
            //登记积分商城订单号
            if(integralExchangeRsp==null){
                BusinessException.throwBusinessException(MsgCodeEnum.OUT_TRADE_NO_CANNOT_BE_EMPTY);
            } else {
                if (StringUtils.equals(CODE_SUCCESS, integralExchangeRsp.getRespCode())) {
                    if(integralExchangeRsp.getResult()!=null){
                        String orderId = integralExchangeRsp.getResult().getOrderId();
                        if(JudgeUtils.isNotNull(orderId)){
                            paymentQueryBO.getPaymentOrder().setBankOrderNo(orderId);
                        }
                    } else {
                        BusinessException.throwBusinessException(MsgCodeEnum.OUT_TRADE_NO_CANNOT_BE_EMPTY);
                    }
                }
            }
        }
    }

    @Override
    public void send(PaymentQueryBO paymentQueryBO) {
        integralTimeOutPaymentQuery(paymentQueryBO);
    }

    private boolean checkQueryParams(PaymentQueryBO paymentQueryBO) {
        return Optional.ofNullable(paymentQueryBO)
                .map(PaymentQueryBO::getPaymentOrder)
                .map(TradeOrderDO::getOutTradeNo)
                .map(StringUtils::isEmpty)
                .orElse(false);
    }
}
