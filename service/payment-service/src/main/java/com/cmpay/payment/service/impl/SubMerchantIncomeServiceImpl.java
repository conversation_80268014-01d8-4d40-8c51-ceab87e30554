package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.config.SubMerchantBO;
import com.cmpay.payment.bo.config.SubMerchantRefreshAmountBO;
import com.cmpay.payment.constant.Constants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.dao.IJkCountSubMerRefundAmountExtDao;
import com.cmpay.payment.dao.IJkCountSubMerTradeAmountExtDao;
import com.cmpay.payment.entity.JkCountSubMerRefundAmountDO;
import com.cmpay.payment.entity.JkCountSubMerTradeAmountDO;
import com.cmpay.payment.entity.JkCountSubMerTradeAmountExtDO;
import com.cmpay.payment.service.SubMerchantIncomeService;
import com.cmpay.payment.service.ext.config.IExtSubMerchantService;
import com.cmpay.payment.util.PaymentUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * @date 2023-11-23 11:59
 * <AUTHOR>
 * @Version 1.0
 */
@Transactional(propagation = Propagation.REQUIRED)
@Service
public class SubMerchantIncomeServiceImpl implements SubMerchantIncomeService {
    private static final Logger logger = LoggerFactory.getLogger(SubMerchantIncomeServiceImpl.class);

    @Autowired
    private IJkCountSubMerRefundAmountExtDao jkCountSubMerRefundAmountExtDao;
    @Autowired
    private IJkCountSubMerTradeAmountExtDao jkCountSubMerTradeAmountExtDao;
    @Autowired
    private IExtSubMerchantService subMerchantService;

    /**
     * 如果退款总金额加上该笔退款金额大于支付总金额，不允许退款，否则就同步该子商户当日最新退款总金额到数据库
     *
     * @param refreshAmountBO
     */
    @Override
    public void refundConfine(SubMerchantRefreshAmountBO refreshAmountBO) {
        if (JudgeUtils.isEmpty(refreshAmountBO.getSubMerchantNo()) || JudgeUtils.isNull(refreshAmountBO.getAmount()) || (refreshAmountBO.getAmount().compareTo(BigDecimal.ZERO) == Constants.ZERO_NUMBER)) {
            BusinessException.throwBusinessException(MsgCodeEnum.PARAM_ISNULL);
        }
        // 查询并锁住当天当前子商户的退款总金额数据
        String currentDate = DateTimeUtils.getCurrentDateStr();
        refreshAmountBO.setCountDate(currentDate);
        JkCountSubMerRefundAmountDO subMerRefundAmountDo = getAndLockRefundAmountDo(refreshAmountBO);
        // 计算本次退款后的退款总金额
        BigDecimal refundTotalAmountBigDecimal = refreshAmountBO.getAmount().add(subMerRefundAmountDo.getCountAmount());
        long refundTotalAmount = PaymentUtils.bigdecimal2long(refundTotalAmountBigDecimal);
        SubMerchantBO subMerchantBO = subMerchantService.querySubMerchant(refreshAmountBO.getSubMerchantNo());
        // 判断子商户号是否进行退款金额检查
        if (JudgeUtils.notEquals(subMerchantBO.getCheckAmountFlag(),Constants.NOT_CHECK_AMOUNT_FLAG)) {
            // 获取当天该子商户的支付总金额
            JkCountSubMerTradeAmountDO jkCountSubMerTradeAmountDO = new JkCountSubMerTradeAmountExtDO();
            jkCountSubMerTradeAmountDO.setCountDate(currentDate);
            jkCountSubMerTradeAmountDO.setSubMerchantNo(refreshAmountBO.getSubMerchantNo());
            JkCountSubMerTradeAmountExtDO subMerTradeAmountExtDo = jkCountSubMerTradeAmountExtDao.countPayAmount(jkCountSubMerTradeAmountDO);
            long tradeTotalAmount = PaymentUtils.bigdecimal2long(subMerTradeAmountExtDo.getCountSubMerAmount());
            logger.info("退款发起，子商户:{}, 当前退款总金额:{}, 当前支付总金额:{}", refreshAmountBO.getSubMerchantNo(), refundTotalAmount, tradeTotalAmount);
            // 如果当次退款后该子商户的退款总金额是大于支付总金额不允许退款
            if (tradeTotalAmount < refundTotalAmount) {
                BusinessException.throwBusinessException(MsgCodeEnum.SUB_MERCHANT_REFUND_FAIL);
            }
        }
        subMerRefundAmountDo.setCountAmount(refundTotalAmountBigDecimal);
        // 如果本次退款为该子商户当天第一笔，新增一条当天退款总金额数据,不为第一天就修改退款总金额
        if (refreshAmountBO.isNotExistFlag() && (refundTotalAmountBigDecimal.compareTo(refreshAmountBO.getAmount()) == 0)) {
            jkCountSubMerRefundAmountExtDao.insert(subMerRefundAmountDo);
        } else {
            jkCountSubMerRefundAmountExtDao.update(subMerRefundAmountDo);
        }

    }

    @Override
    public boolean refundFailSubtractAmount(SubMerchantRefreshAmountBO refreshAmountBo) {
        JkCountSubMerRefundAmountDO subMerRefundAmountDo = new JkCountSubMerRefundAmountDO();
        subMerRefundAmountDo.setCountAmount(refreshAmountBo.getAmount());
        subMerRefundAmountDo.setSubMerchantNo(refreshAmountBo.getSubMerchantNo());
        subMerRefundAmountDo.setCountDate(refreshAmountBo.getCountDate());
        subMerRefundAmountDo.setTmSmp(DateTimeUtils.getCurrentDateTimeStr());
        int updateTotal = jkCountSubMerRefundAmountExtDao.subtractRefundAmount(subMerRefundAmountDo);
        return updateTotal > 0;
    }

    /**
     *  获取当日该子商户的退款金额信息并锁住该行
     * @param refreshAmountBo
     * @return
     */
    private JkCountSubMerRefundAmountDO getAndLockRefundAmountDo(SubMerchantRefreshAmountBO refreshAmountBo) {
        JkCountSubMerRefundAmountDO subMerRefundAmountDo = new JkCountSubMerRefundAmountDO();
        subMerRefundAmountDo.setCountDate(refreshAmountBo.getCountDate());
        subMerRefundAmountDo.setSubMerchantNo(refreshAmountBo.getSubMerchantNo());
        JkCountSubMerRefundAmountDO refundAmountDo = jkCountSubMerRefundAmountExtDao.getAndLockRefundAmountDo(subMerRefundAmountDo);
        if (JudgeUtils.isNull(refundAmountDo)) {
            subMerRefundAmountDo.setCountAmount(BigDecimal.ZERO);
            refreshAmountBo.setNotExistFlag(true);
        } else {
            subMerRefundAmountDo.setCountAmount(refundAmountDo.getCountAmount());
        }
        return subMerRefundAmountDo;
    }

}
