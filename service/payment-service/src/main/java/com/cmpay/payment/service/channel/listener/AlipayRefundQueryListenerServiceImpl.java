package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.aplipay.AlipayRefundQueryChannelService;
import com.cmpay.payment.service.channel.PaymentChannelService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Created on 2018/12/13
 *
 * @author: wulinfeng
 */
@Service
public class AlipayRefundQueryListenerServiceImpl extends PaymentListenerService<RefundOrderQueryBO> {

    @Autowired
    ApplicationContext applicationContext;

    @EventListener
    @Override
    public void execute(RefundOrderQueryBO eventBO) {
        super.execute(eventBO);
    }

    @Override
    protected boolean checkChannelExecutable(RefundOrderQueryBO refundQueryBO) {
        return Optional.ofNullable(refundQueryBO)
                .map(RefundOrderQueryBO::getRefundOrder)
                .map(order -> {
                    return StringUtils.equalsIgnoreCase(PaymentWayEnum.ALIPAY.name(), order.getAimProductCode());
                })
                .orElse(false);
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(RefundOrderQueryBO refundQueryBO) {
        return getBean(AlipayRefundQueryChannelService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }

}
