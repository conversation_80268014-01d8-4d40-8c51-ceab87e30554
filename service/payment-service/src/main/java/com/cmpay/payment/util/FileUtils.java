package com.cmpay.payment.util;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.constant.Constants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.utils.Digests;
import org.apache.commons.io.input.ReversedLinesFileReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.Charset;
import java.text.DecimalFormat;


/**
 * @date 2023-08-21 14:25
 * <AUTHOR>
 * @Version 1.0
 */
public final class FileUtils {
    private static final Logger logger = LoggerFactory.getLogger(FileUtils.class);

    /**
     * 保留两位小数
     */
    public static final String pattern = "#.00";
    /**
     * 获取文件总行数
     * @param filePath
     * @return
     * @throws IOException
     */
    public static long getFileCount(String filePath) throws IOException {
        LineNumberReader reader = new LineNumberReader(new FileReader(filePath));
        reader.skip(Long.MAX_VALUE);
        int lineCount = reader.getLineNumber();
        return lineCount;
    }


    /**
     * 转换文件大小,将文件长度转为 KB、MB、GB
     * @param filesize
     * @return
     */
    public static String formetFileSize(long filesize) {
        DecimalFormat df = new DecimalFormat(pattern);
        String fileSizestr = "";
        if (filesize < Constants.LONG_B) {
            fileSizestr = df.format((double) filesize) + "B";
        } else if (filesize < Constants.LONG_KB) {
            fileSizestr = df.format((double) filesize / Constants.LONG_B) + "KB";
        } else if (filesize < Constants.LONG_MB) {
            fileSizestr = df.format((double) filesize / Constants.LONG_KB) + "MB";
        } else {
            fileSizestr = df.format((double) filesize / Constants.LONG_MB) + "GB";
        }
        return fileSizestr;
    }

    /**
     * 重命名、移动目录
     * 若目录相同则重命名，若目录不同则移动原文件至新目录
     * 注：若目标地址有相同文件名，此方法不会覆盖，返回false
     * @param oriFile 带绝对路径的原文件
     * @param newFileName 带绝对路径的新文件名
     * @return
     */
    public static boolean renameFile(File oriFile, String newFileName){
        if(!oriFile.exists()){
            BusinessException.throwBusinessException(MsgCodeEnum.CHECK_FILE_NOT_EXISTS);
        }
        File newFile = new File(newFileName);
        boolean renameFlag = oriFile.renameTo(newFile);
        return renameFlag;
    }
    /**
     * 重命名、移动目录
     * 若目录相同则重命名，若目录不同则移动原文件至新目录
     * 注：若目标地址有相同文件名，此方法不会覆盖，返回false
     * @param oriFileName 带绝对路径的原文件名
     * @param newFileName 带绝对路径的新文件名
     * @return
     */
    public static boolean renameFile(String oriFileName, String newFileName){
        File oriFile = new File(oriFileName);
        if(!oriFile.exists()){
            BusinessException.throwBusinessException(MsgCodeEnum.CHECK_FILE_NOT_EXISTS);
        }
        File newFile = new File(newFileName);
        boolean renameFlag = oriFile.renameTo(newFile);
        return renameFlag;
    }
    /**
     * 删除文件
     * @param fileName
     * @return
     */
    public static void deleteIfExist(String fileName){
        if(StringUtils.isBlank(fileName)){
            throw new RuntimeException();
        }
        File file = new File(fileName);
        if(file.exists()){
            file.delete();
        }
    }

    /**
     * 对比 两个文件的MD5值 相同返回true
     * @param file1
     * @param file2
     * @return 32位MD5字符串
     */
    public static boolean MD5FileValid(File file1, File file2) {
        if(!file1.exists() || !file2.exists()){
            BusinessException.throwBusinessException(MsgCodeEnum.LOCAL_FILE_NOT_EXISTS);
        }
        String file1MD5 = generateFileMD5(file1);
        String file2MD5 = generateFileMD5(file2);
        if(file1MD5.equals(file2MD5)){
            return true;
        }else {
            return false;
        }
    }

    /**
     * 生成文件的MD5值
     * @param file
     * @return 32位MD5字符串
     */
    public static String generateFileMD5(File file) {
        try {
            FileInputStream is = new FileInputStream(file);
            byte[] md5Byte = Digests.md5(is);
            StringBuilder sb = new StringBuilder();
            for (byte b : md5Byte) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (FileNotFoundException e) {
            BusinessException.throwBusinessException(MsgCodeEnum.LOCAL_FILE_NOT_EXISTS);
        } catch (IOException e) {
            BusinessException.throwBusinessException(MsgCodeEnum.IO_EXCEPTION);
        }
        return null;
    }

    /**
     * 读取文件最后一行数据
     * @param file
     * @return
     */
    public static String readLastLineV2(File file) {
        String lastLine = "";
        try (ReversedLinesFileReader reversedLinesReader = new ReversedLinesFileReader(file, Charset.defaultCharset())) {
            lastLine = reversedLinesReader.readLine();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return lastLine;
    }

    /**
     * 删除指定目录下，文件名中带有matchedStr的所有文件
     * @param path 指定目录（绝对路径）
     * @param matchedStr 文件名匹配字符串
     * @return
     */
    public static boolean delMatchedFile(String path, String matchedStr) {
        try {
            // 指定目录，这里使用当前目录
            File directory = new File(path);
//            带过滤器，精准匹配，避免查询到所有文件
            File[] files = directory.listFiles((dir, name) -> name.contains(matchedStr));
            if (files != null) {
                for (File file : files) {
                    // 删除文件
                    file.delete();
                }
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }
}
