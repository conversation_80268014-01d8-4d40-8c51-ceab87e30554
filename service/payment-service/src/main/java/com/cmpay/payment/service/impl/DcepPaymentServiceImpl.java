package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.*;
import com.cmpay.payment.bo.config.MerchantBO;
import com.cmpay.payment.bo.config.PayServiceBeanBO;
import com.cmpay.payment.bo.protocol.ProtocolQueryBO;
import com.cmpay.payment.bo.utils.TripleDes;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.constant.protocol.DcepUserProtocolHandlerTypeEnum;
import com.cmpay.payment.entity.OrderPaymentAttachDO;
import com.cmpay.payment.entity.TradeNotifyRecordDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.entity.config.MerchantDO;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.properties.ProtocolProperties;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.IDcepPaymentService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.service.ext.ExtPayOrderAttachService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.IDcepIdService;
import com.cmpay.payment.service.ext.config.IExtMerchantService;
import com.cmpay.payment.service.ext.config.IExtRateService;
import com.cmpay.payment.utils.TypeCheckUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/7/5
 */
@Service
public class DcepPaymentServiceImpl extends DcepBaseHandler implements IDcepPaymentService {

    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private ExtPayOrderAttachService payOrderAttachService;
    @Autowired
    private ProtocolProperties protocolProperties;
    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;
    @Autowired
    private ExtParamInfoService paramInfoService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IExtMerchantService merchantService;
    @Autowired
    private IDcepIdService dcepIdService;
    @Autowired
    private IExtRateService rateService;
    @Autowired
    private AsynCommonService asynCommonService;

    @Override
    public void protocolPrePayment(ProtocolPaymentBO protocolPaymentBO) {
        checkAmount(protocolPaymentBO);
        MerchantDO merchantDO = checkMerchant(protocolPaymentBO);
        checkMerchantAttach(protocolPaymentBO);
        insertTradeOrder(protocolPaymentBO);
        insertAttachOrder(protocolPaymentBO, merchantDO);
        //生成缓存TOKEN信息
        protocolPaymentBO.setIdNo(TripleDes.decrypt(protocolProperties.getTripleDes(), protocolPaymentBO.getIdNo()));
        protocolPaymentBO.setMobileNo(TripleDes.decrypt(protocolProperties.getTripleDes(), protocolPaymentBO.getMobileNo()));
        DcepIdBO dcepIdBO = new DcepIdBO();
        dcepIdBO.setMobileNo(protocolPaymentBO.getMobileNo());
        dcepIdService.addDcepId(dcepIdBO);
        protocolPaymentBO.setDcepId(dcepIdBO.getDcepId());
    }

    private MerchantDO checkMerchant(ProtocolPaymentBO protocolPaymentBO) {
        MerchantBO checkMerchant = new MerchantBO();
        checkMerchant.setMerchantNumber(protocolPaymentBO.getMerchantId());
        MerchantDO merchantDO = merchantService.queryMerchant(checkMerchant);
        if (merchantDO == null) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NOT_EXISTS);
        }
        return merchantDO;
    }

    private String getExpireTime(String timeUnit) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        return DateTimeUtils.formatLocalDateTime(localDateTime.plusMinutes(Integer.parseInt(timeUnit)));
    }

    private TradeOrderDO insertTradeOrder(ProtocolPaymentBO protocolPaymentBO) {
        //查询商户配置费率
        PayServiceBeanBO rateBO = new PayServiceBeanBO();
        rateBO.setMerchantNo(protocolPaymentBO.getMerchantId());
        rateBO.setPaymentChannl(protocolPaymentBO.getPayWay());
        rateBO.setOrderScene(protocolPaymentBO.getScene());
        rateBO.setOrderAmount(protocolPaymentBO.getRealAmount());
        rateBO.setOrderDate(DateTimeUtils.getCurrentDateStr());
        rateBO.setProvinceCode(JudgeUtils.isBlank(protocolPaymentBO.getProvinceCode()) ?
                StringUtils.substring(protocolPaymentBO.getOutTradeNo(), 0, 4) : protocolPaymentBO.getProvinceCode());
        rateBO = rateService.findPaymentRout(rateBO);
        if (JudgeUtils.isNull(rateBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ROUT_CANNOT_VALIDITY);
        }
        PayOrderBO payOrderBO = new PayOrderBO();
        BeanUtils.copyProperties(payOrderBO, protocolPaymentBO);
        payOrderBO.setOrderRate(rateBO.getRate());
        payOrderBO.setServiceCharge(rateBO.getServiceCharge());
        payOrderBO.setPaymentType(rateBO.getBusinessType());
        payOrderBO.setRefundFeeWay(rateBO.getRefundFeeWay());
        payOrderBO.setPaymentId(rateBO.getBankMerchantNo());
        payOrderBO.setAuthCode(protocolPaymentBO.getQrCode());
        payOrderBO.setMobileNumber(protocolPaymentBO.getMobileNo());
        payOrderBO.setSubject(protocolPaymentBO.getProductDesc());
        payOrderBO.setExpireTime(getExpireTime(protocolPaymentBO.getExpireMinutes()));
        payOrderBO.setPaymentRout(PaymentWayEnum.DCEPPAY.name().toLowerCase());
        payOrderBO.setBusinessCode(protocolPaymentBO.getBusinessCode());
        payOrderBO.setRealAmount(protocolPaymentBO.getRealAmount());
        payOrderBO.setDiscountableAmount(protocolPaymentBO.getDiscountableAmount());
        payOrderBO.setDcepFlag(DcepConstants.DCEP);
        payOrderBO.setSplitFlag(Constants.N);
        return payOrderService.insert(payOrderBO);
    }

    private OrderPaymentAttachDO insertAttachOrder(ProtocolPaymentBO protocolPaymentBO, MerchantDO merchantDO) {

        if (JudgeUtils.equalsIgnoreCase(PaymentWayEnum.ICBCPAY.name(), protocolPaymentBO.getPayWay())
                && JudgeUtils.equalsIgnoreCase(PaymentSceneEnum.JSAPIPAY.name(), protocolPaymentBO.getScene())
                && DcepConstants.TYPE_MOBILE.equals(protocolPaymentBO.getAuthType())) {
            protocolPaymentBO.setMobileNo(TripleDes.encrypt(protocolProperties.getTripleDes(), protocolPaymentBO.getAuthCode()));
        }
        protocolPaymentBO.setAgreementId(protocolPaymentBO.getAgreementId());
        protocolPaymentBO.setExpireTime(getExpireTime(protocolPaymentBO.getExpireMinutes()));
        protocolPaymentBO.setTradeStatus(OrderStatusEnum.WAIT_PAY.name());
        protocolPaymentBO.setMerchantName(merchantDO.getMerchantName());
        protocolPaymentBO.setRealAmount(protocolPaymentBO.getRealAmount());
        // 数币子商户
        protocolPaymentBO.setSubMerchantId(protocolPaymentBO.getSubMerchantId());
        return payOrderAttachService.insert(protocolPaymentBO);
    }

    @Override
    public void protocolPrePaymentQuery(ProtocolPaymentBO protocolPaymentBO) {
        try {
            TradeOrderDO tradeOrderDO = checkOrderInfo(protocolPaymentBO);
            OrderPaymentAttachDO orderPaymentAttach = checkOrderAttach(protocolPaymentBO);
            ProtocolQueryBO protocolQueryBO = getUserSignInfo(orderPaymentAttach);
            protocolPaymentBO.setMerchantName(orderPaymentAttach.getMerchantName());
            protocolPaymentBO.setIdNo(TripleDes.decrypt(protocolProperties.getTripleDes(), orderPaymentAttach.getIdNo()));
            protocolPaymentBO.setMobileNo(TripleDes.decrypt(protocolProperties.getTripleDes(), orderPaymentAttach.getMobileNo()));
            protocolPaymentBO.setTotalAmount(tradeOrderDO.getOrderAmount());
            protocolPaymentBO.setProductName(tradeOrderDO.getGoodsName());
            protocolPaymentBO.setProductDesc(tradeOrderDO.getGoodsDesc());
            protocolPaymentBO.setSignFlag(protocolQueryBO.getSignFlag());
            protocolPaymentBO.setErrCodeDes(protocolQueryBO.getErrMsgInfo());
            protocolPaymentBO.setChannelNo(dcepPaymentProperties.getNewChannelNo());
            protocolPaymentBO.setSubMerchantId(orderPaymentAttach.getSubMerchantId());
            if (StringUtils.isBlank(orderPaymentAttach.getAgreementId())) {
                protocolPaymentBO.setAgreementId(protocolQueryBO.getAgreementId());
                updateOrderAttachMsg(orderPaymentAttach, protocolPaymentBO);
            }
        } catch (Exception e) {
            exceptionHandler(e, "protocolPrePaymentQuery:" + protocolPaymentBO.getErrCodeDes());
        }
    }

    private ProtocolQueryBO getUserSignInfo(OrderPaymentAttachDO orderPaymentAttach) {
        ProtocolQueryBO protocolQueryBO = new ProtocolQueryBO();
        protocolQueryBO.setHandlerType(DcepUserProtocolHandlerTypeEnum.QUERY.name());
        protocolQueryBO.setMobileNo(TripleDes.decrypt(protocolProperties.getTripleDes(), orderPaymentAttach.getMobileNo()));
        protocolQueryBO.setSubMerchantId(orderPaymentAttach.getSubMerchantId());
        protocolQueryBO.setOutTradeNo(orderPaymentAttach.getOutTradeNo());
        protocolQueryBO.setTradeDate(orderPaymentAttach.getOrderDate());
        applicationContext.publishEvent(protocolQueryBO);
        return protocolQueryBO;
    }

    private TradeOrderDO checkOrderInfo(ProtocolPaymentBO protocolPaymentBO) {
        TradeOrderDO tradeOrderDO = new TradeOrderDO();
        tradeOrderDO.setOutTradeNo(protocolPaymentBO.getOutTradeNo());
        tradeOrderDO.setRequestDate(protocolPaymentBO.getTradeDate());
        TradeOrderDO paymentOrder = payOrderService.load(tradeOrderDO);
        if (JudgeUtils.isNull(paymentOrder)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        if (!JudgeUtils.equals(paymentOrder.getStatus(), OrderStatusEnum.WAIT_PAY.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_STATUS_CANNOT_PAYMENT);
        }
        if (DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr()).isAfter(DateTimeUtils.parseLocalDateTime(paymentOrder.getExpireTime()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_EXPIRE);
        }
        return paymentOrder;
    }

    @Override
    public void protocolPayment(ProtocolPaymentBO protocolPaymentBO) {
        try {
            OrderPaymentAttachDO paymentAttach = getOrderPaymentAttachDO(protocolPaymentBO);
            TradeOrderDO tradeOrder = checkOrderInfo(protocolPaymentBO);
            //数字货币交话费额度检查
            checkPhoneLimit(paymentAttach.getMobileNo(), tradeOrder);
            ProtocolQueryBO protocolQueryBO = getUserSignInfo(paymentAttach);
            protocolPaymentBO.setAgreementId(protocolQueryBO.getAgreementId());
            paymentAttach.setAgreementId(protocolPaymentBO.getAgreementId());
            if (StringUtils.isBlank(protocolPaymentBO.getAgreementId())) {
                BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_STATUS_UNDEFINED);
            }
            buildRequest(protocolPaymentBO, tradeOrder, paymentAttach);
            applicationContext.publishEvent(protocolPaymentBO);
            updateOrderAttachMsg(paymentAttach, protocolPaymentBO);
            setAccountDateAndFinishTime(protocolPaymentBO);
            updateTradeOrder(tradeOrder, protocolPaymentBO);
            if (JudgeUtils.equals(protocolPaymentBO.getTradeStatus(), OrderStatusEnum.TRADE_SUCCESS.name())) {
                doSuccess(tradeOrder);
            }
        } catch (Exception e) {
            exceptionHandler(e, "protocolPayment:" + protocolPaymentBO.getErrCodeDes());
        }
    }

    private OrderPaymentAttachDO getOrderPaymentAttachDO(ProtocolPaymentBO protocolPaymentBO) {
        OrderPaymentAttachDO paymentAttachDO = new OrderPaymentAttachDO();
        paymentAttachDO.setOrderDate(protocolPaymentBO.getTradeDate());
        paymentAttachDO.setOutTradeNo(protocolPaymentBO.getOutTradeNo());
        OrderPaymentAttachDO paymentAttach = payOrderAttachService.load(paymentAttachDO);
        if (JudgeUtils.isNull(paymentAttach)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        return paymentAttach;
    }

    private void checkPhoneLimit(String mobileNo, TradeOrderDO tradeOrder) {
        if (JudgeUtils.notEquals(protocolProperties.getPhoneMerchantNo(), tradeOrder.getMerchantNo())) {
            return;
        }
        DcepUserPayLimitBO dcepUserPayLimitBO = new DcepUserPayLimitBO();
        dcepUserPayLimitBO.setMobileNumber(mobileNo);
        dcepUserPayLimitBO.setOrderDate(tradeOrder.getOrderDate());
        dcepUserPayLimitBO.setMerchantNo(protocolProperties.getPhoneMerchantNo());
        //查询用户支付次数
        dcepUserPayLimitBO = payOrderService.queryUserOrderInfo(dcepUserPayLimitBO);
        ParamInfoBO paramNumberBO = new ParamInfoBO();
        paramNumberBO.setParamName(DcepConstants.PHONE_LIMIT_NUMBER_PARAM);
        paramNumberBO = paramInfoService.getNotifyIntervalParameter(paramNumberBO);
        if (JudgeUtils.isNotBlank(paramNumberBO.getParamValue())) {
            if (Integer.parseInt(dcepUserPayLimitBO.getOrderSuccessNumber()) >=
                    Integer.parseInt((paramNumberBO.getParamValue()))) {
                BusinessException.throwBusinessException(MsgCodeEnum.MORE_PHONE_PAY_LIMIT_NUMBER);
            }
        }
        ParamInfoBO paramAmountBO = new ParamInfoBO();
        paramAmountBO.setParamName(DcepConstants.PHONE_LIMIT_AMOUNT_PARAM);
        paramAmountBO = paramInfoService.getNotifyIntervalParameter(paramAmountBO);
        if (JudgeUtils.isNotBlank(paramAmountBO.getParamValue())) {
            BigDecimal payAmount = dcepUserPayLimitBO.getOrderSuccessAmount().add(tradeOrder.getRealAmount());
            if (payAmount.compareTo(new BigDecimal(paramAmountBO.getParamValue())) > 0) {
                BusinessException.throwBusinessException(MsgCodeEnum.MORE_PHONE_PAY_LIMIT_AMOUNT);
            }
        }
    }

    public void buildRequest(ProtocolPaymentBO protocolPaymentBO, TradeOrderDO tradeOrder, OrderPaymentAttachDO paymentAttach) {
        protocolPaymentBO.setChannelNo(tradeOrder.getBankMerchantNo());
        protocolPaymentBO.setAgreementId(paymentAttach.getAgreementId());
        protocolPaymentBO.setSubMerchantId(paymentAttach.getSubMerchantId());
        protocolPaymentBO.setBusinessType(paymentAttach.getUpBusinessType());
        protocolPaymentBO.setBusinessCode(paymentAttach.getUpBusinessCode());
        protocolPaymentBO.setProductDesc(tradeOrder.getGoodsDesc());
        protocolPaymentBO.setExpireMinutes(paymentAttach.getExpireMinutes());
        protocolPaymentBO.setTradeDate(paymentAttach.getOrderDate());
        protocolPaymentBO.setTradeTime(paymentAttach.getOrderTime());
        protocolPaymentBO.setTotalAmount(tradeOrder.getOrderAmount());
        protocolPaymentBO.setRealAmount(tradeOrder.getRealAmount());
        protocolPaymentBO.setPaymentScene(tradeOrder.getPayWayCode());
        protocolPaymentBO.setOrderDate(tradeOrder.getOrderDate());
        protocolPaymentBO.setOrderTime(tradeOrder.getOrderTime());
    }

    private void setAccountDateAndFinishTime(ProtocolPaymentBO protocolPaymentBO) {
        if (!StringUtils.equals(protocolPaymentBO.getTradeStatus(), OrderStatusEnum.WAIT_PAY.name())) {
            if (StringUtils.isEmpty(protocolPaymentBO.getFinishDateTime()) || protocolPaymentBO.getFinishDateTime().length() != 14) {
                protocolPaymentBO.setFinishDateTime(DateTimeUtils.getCurrentDateTimeStr());
            }
            if (StringUtils.isEmpty(protocolPaymentBO.getAccountDate()) || protocolPaymentBO.getAccountDate().length() != 8) {
                protocolPaymentBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
            }
            if (StringUtils.isEmpty(protocolPaymentBO.getReceiveNotifyTime()) || protocolPaymentBO.getReceiveNotifyTime().length() != 14) {
                protocolPaymentBO.setReceiveNotifyTime(DateTimeUtils.getCurrentDateStr());
            }
        }
    }

    private void updateOrderAttachMsg(OrderPaymentAttachDO paymentAttach, ProtocolPaymentBO protocolPaymentBO) {
        paymentAttach.setAgreementId(protocolPaymentBO.getAgreementId());
        paymentAttach.setErrCode(protocolPaymentBO.getErrCode());
        paymentAttach.setErrCodeDesc(protocolPaymentBO.getErrCodeDes());
        payOrderAttachService.update(paymentAttach);
    }

    private void updateTradeOrder(TradeOrderDO tradeOrderDaoDO, ProtocolPaymentBO protocolPaymentBO) {
        tradeOrderDaoDO.setStatus(protocolPaymentBO.getTradeStatus());
        tradeOrderDaoDO.setAccountDate(protocolPaymentBO.getAccountDate());
        tradeOrderDaoDO.setReceiveNotifyTime(protocolPaymentBO.getFinishDateTime());
        tradeOrderDaoDO.setOrderCompleteTime(protocolPaymentBO.getFinishDateTime());
        tradeOrderDaoDO.setThirdOrdNo(protocolPaymentBO.getPaymentOrderNo());
        tradeOrderDaoDO.setErrMsgCd(protocolPaymentBO.getErrCode());
        tradeOrderDaoDO.setErrMsgInfo(protocolPaymentBO.getErrCodeDes());
        payOrderService.update(tradeOrderDaoDO);
        asynCommonService.asynConnectThirdNo(tradeOrderDaoDO);
    }

    private void doSuccess(TradeOrderDO tradeOrderDaoDO) {
        if (!StringUtils.isAnyBlank(tradeOrderDaoDO.getNotifyUrl(), tradeOrderDaoDO.getMerchantNo())) {
            TradeNotifyBO tradeNotifyBO = registerNotify(tradeOrderDaoDO);
            asynCommonService.asyncNotify(tradeNotifyBO);
        }
    }
    protected TradeNotifyBO registerNotify(TradeOrderDO tradeOrderDO) {
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setOutOrderNo(tradeOrderDO.getOutTradeNo());
        tradeNotifyBO.setTradeDate(tradeOrderDO.getRequestDate());
        tradeNotifyBO.setMerchantNo(tradeOrderDO.getMerchantNo());
        tradeNotifyBO.setTradeOrderNo(tradeOrderDO.getTradeOrderNo());
        tradeNotifyBO.setTradeAmount(tradeOrderDO.getOrderAmount());
        tradeNotifyBO.setSecretIndex(tradeOrderDO.getSecretIndex());
        tradeNotifyBO.setFinishDate(tradeOrderDO.getOrderCompleteTime());
        tradeNotifyBO.setDiscountableAmount(tradeOrderDO.getDiscountableAmount());
        tradeNotifyBO.setNotifyType(TradeTypeEnum.PAYMENT.name().toLowerCase());
        tradeNotifyBO.setNotifyUrl(tradeOrderDO.getNotifyUrl());
        tradeNotifyBO.setExtra(tradeOrderDO.getRemark());
        return tradeNotifyBO;
    }


    @Override
    public void protocolMmpayPayment(ProtocolPaymentBO protocolPaymentBO) {
        try {
            checkAmount(protocolPaymentBO);
            MerchantDO merchantDO = checkMerchant(protocolPaymentBO);
            checkMerchantAttach(protocolPaymentBO);
            TradeOrderDO tradeOrderDO = insertTradeOrder(protocolPaymentBO);
            OrderPaymentAttachDO paymentAttachDO = insertAttachOrder(protocolPaymentBO, merchantDO);
            ProtocolQueryBO protocolQueryBO = getUserSignInfo(paymentAttachDO);
            protocolPaymentBO.setAgreementId(protocolQueryBO.getAgreementId());
            paymentAttachDO.setAgreementId(protocolPaymentBO.getAgreementId());
            if (StringUtils.isBlank(protocolPaymentBO.getAgreementId())) {
                BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_STATUS_UNDEFINED);
            }
            protocolPaymentBO.setMobileNo(TripleDes.decrypt(protocolProperties.getTripleDes(), protocolPaymentBO.getMobileNo()));
            buildRequest(protocolPaymentBO, tradeOrderDO, paymentAttachDO);
            protocolPaymentBO.setSourceApp(AppEnum.integrationpaymnet.name());
            applicationContext.publishEvent(protocolPaymentBO);
            updateOrderAttachMsg(paymentAttachDO, protocolPaymentBO);
            setAccountDateAndFinishTime(protocolPaymentBO);
            updateTradeOrder(tradeOrderDO, protocolPaymentBO);
            if (JudgeUtils.equals(protocolPaymentBO.getTradeStatus(), OrderStatusEnum.TRADE_SUCCESS.name())) {
                doSuccess(tradeOrderDO);
            }
        } catch (Exception e) {
            exceptionHandler(e, "protocolMmpayPayment:" + protocolPaymentBO.getErrCodeDes());
        }
    }

    @Override
    public void unifiedPay(ProtocolPaymentBO protocolPaymentBO) {
        try {
            checkParam(protocolPaymentBO);
            MerchantDO merchantDO = checkMerchant(protocolPaymentBO);
            checkMerchantAttach(protocolPaymentBO);
            if (StringUtils.isNotBlank(protocolPaymentBO.getMobileNo())) {
                protocolPaymentBO.setMobileNo(TripleDes.encrypt(protocolProperties.getTripleDes(), protocolPaymentBO.getMobileNo()));
            }
            TradeOrderDO tradeOrderDO = insertTradeOrder(protocolPaymentBO);
            OrderPaymentAttachDO paymentAttachDO = insertAttachOrder(protocolPaymentBO, merchantDO);
            buildRequest(protocolPaymentBO, tradeOrderDO, paymentAttachDO);
            protocolPaymentBO.setSourceApp(AppEnum.integrationpaymnet.name());
            applicationContext.publishEvent(protocolPaymentBO);
            updateOrderAttachMsg(paymentAttachDO, protocolPaymentBO);
            setAccountDateAndFinishTime(protocolPaymentBO);
            updateTradeOrder(tradeOrderDO, protocolPaymentBO);
            if (JudgeUtils.equals(protocolPaymentBO.getTradeStatus(), OrderStatusEnum.TRADE_SUCCESS.name())) {
                doSuccess(tradeOrderDO);
            }
        } catch (Exception e) {
            exceptionHandler(e, "microPay:" + protocolPaymentBO.getErrCodeDes());
        }
    }

    private void checkParam(ProtocolPaymentBO protocolPaymentBO) {
        if (TypeCheckUtils.checkPaymentWay(PaymentWayEnum.valueOf(protocolPaymentBO.getPayWay().toUpperCase()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_WAY_ERROR);
        }
        if (TypeCheckUtils.checkPaymentScene(PaymentSceneEnum.valueOf(protocolPaymentBO.getScene().toUpperCase()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_SCENE_ERROR);
        }
        if (StringUtils.equals(protocolPaymentBO.getScene().toUpperCase(), PaymentSceneEnum.HDWALLET.name().toUpperCase())) {
            checkHdWalletParam(protocolPaymentBO);
        }
        checkAmount(protocolPaymentBO);
        if (StringUtils.equals(protocolPaymentBO.getScene().toUpperCase(), PaymentSceneEnum.JSAPIPAY.name().toUpperCase())
                && StringUtils.isAnyEmpty(protocolPaymentBO.getAuthCode(), protocolPaymentBO.getAuthType())) {
            BusinessException.throwBusinessException(MsgCodeEnum.JSAPI_AUTH_CODE_TYPE);
        }
        if (StringUtils.equals(protocolPaymentBO.getScene().toUpperCase(), PaymentSceneEnum.APP.name().toUpperCase())
                && StringUtils.isBlank(protocolPaymentBO.getBusiScene())) {
            BusinessException.throwBusinessException(MsgCodeEnum.BUSISCENE_CODE_CANNOT_BE_EMPTY);
        }
        if (StringUtils.equals(protocolPaymentBO.getScene().toUpperCase(), PaymentSceneEnum.APP.name().toUpperCase())
                && StringUtils.isBlank(protocolPaymentBO.getOsType())) {
            BusinessException.throwBusinessException(MsgCodeEnum.OSTYPE_CODE_CANNOT_BE_EMPTY);
        }
        if (StringUtils.equals(protocolPaymentBO.getScene().toUpperCase(), PaymentSceneEnum.APP.name().toUpperCase())
                && StringUtils.isBlank(protocolPaymentBO.getTerminalIp())) {
            BusinessException.throwBusinessException(MsgCodeEnum.TERMINALIP_CODE_CANNOT_BE_EMPTY);
        }
        if (StringUtils.equals(protocolPaymentBO.getScene().toUpperCase(), PaymentSceneEnum.APP.name().toUpperCase())
                && StringUtils.isBlank(protocolPaymentBO.getMobileNo())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MOBILE_NO_CANNOT_BE_EMPTY);
        }
        if (!StringUtils.equals(protocolPaymentBO.getScene().toUpperCase(), PaymentSceneEnum.APP.name().toUpperCase())
                && StringUtils.isBlank(protocolPaymentBO.getBusinessType())) {
            BusinessException.throwBusinessException(MsgCodeEnum.UP_BUS_TYP_CANNOT_BE_EMPTY);
        }
        if (!StringUtils.equals(protocolPaymentBO.getScene().toUpperCase(), PaymentSceneEnum.APP.name().toUpperCase())
                && StringUtils.isBlank(protocolPaymentBO.getBusinessCode())) {
            BusinessException.throwBusinessException(MsgCodeEnum.UP_BUS_CODE_CANNOT_BE_EMPTY);
        }
        // 非分账模式：子商户号不为空，则结算单位、结算项、渠道类型都不能为空，要用这些数据统计出结算单
        if (JudgeUtils.isNotBlank(protocolPaymentBO.getSubMerchant())
                && JudgeUtils.isBlankAny(protocolPaymentBO.getSettlementDept(), protocolPaymentBO.getSettlementItem(), protocolPaymentBO.getMerchantChannelType())) {
            BusinessException.throwBusinessException(MsgCodeEnum.SPLIT_INFO_IS_NULL);
        }
    }

    private void checkAmount(ProtocolPaymentBO protocolPaymentBO) {
        if (JudgeUtils.isNull(protocolPaymentBO.getTotalAmount())) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_AMOUNT_CANNOT_BE_EMPTY);
        }
        if (protocolPaymentBO.getTotalAmount().compareTo(BigDecimal.ZERO) == -1) {
            BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
        }
        if (JudgeUtils.isNull(protocolPaymentBO.getDiscountableAmount())) {
            protocolPaymentBO.setDiscountableAmount(BigDecimal.ZERO);
            if (JudgeUtils.isNull(protocolPaymentBO.getRealAmount())) {
                protocolPaymentBO.setRealAmount(protocolPaymentBO.getTotalAmount());
            } else {
                if (protocolPaymentBO.getTotalAmount().compareTo(protocolPaymentBO.getRealAmount()) != 0) {
                    BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
                }
            }
        } else {
            if (JudgeUtils.isNull(protocolPaymentBO.getRealAmount())) {
                BusinessException.throwBusinessException(MsgCodeEnum.REAL_AMOUNT_CANNOT_BE_EMPTY);
            }
            if (protocolPaymentBO.getDiscountableAmount().compareTo(BigDecimal.ZERO) == -1
                    || protocolPaymentBO.getRealAmount().compareTo(BigDecimal.ZERO) == -1) {
                BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
            }
            if (protocolPaymentBO.getDiscountableAmount().add(protocolPaymentBO.getRealAmount()).compareTo(protocolPaymentBO.getTotalAmount()) != 0) {
                BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
            }
        }
    }

    private void checkHdWalletParam(ProtocolPaymentBO protocolPaymentBO) {
        if (StringUtils.isBlank(protocolPaymentBO.getHdWalletToken())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAY_TOKEN_NOT_NULL);
        }
        if (StringUtils.isBlank(protocolPaymentBO.getProductDesc())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PRODUCT_DESC_CANNOT_BE_EMPTY);
        }
        if (StringUtils.isBlank(protocolPaymentBO.getBusiScene())) {
            BusinessException.throwBusinessException(MsgCodeEnum.BUSISCENE_CODE_CANNOT_BE_EMPTY);
        }
        if (StringUtils.isBlank(protocolPaymentBO.getBusinessCode())) {
            BusinessException.throwBusinessException(MsgCodeEnum.UP_BUS_CODE_CANNOT_BE_EMPTY);
        }
        if (StringUtils.isBlank(protocolPaymentBO.getBusinessType())) {
            BusinessException.throwBusinessException(MsgCodeEnum.UP_BUS_TYP_CANNOT_BE_EMPTY);
        }
        if (StringUtils.isBlank(protocolPaymentBO.getTransPlace())) {
            BusinessException.throwBusinessException(MsgCodeEnum.TRAN_PLACE_CANNOT_BE_EMPTY);
        }
        if (StringUtils.isBlank(protocolPaymentBO.getTerminalCode())) {
            BusinessException.throwBusinessException(MsgCodeEnum.TERMINAL_CODE_CANNOT_BE_EMPTY);
        }
    }
}
