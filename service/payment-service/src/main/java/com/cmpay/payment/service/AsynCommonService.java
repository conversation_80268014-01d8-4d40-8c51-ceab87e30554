package com.cmpay.payment.service;

import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.entity.TradeOrderDO;

/**
 * @author： pengAnHai
 * @date： 2023-12-04
 * @description：
 */
public interface AsynCommonService {

    /**
     * 异步新增第三方订单号和订单号的对应关系
     *
     * @param tradeOrderDO
     */
    void asynConnectThirdNo(TradeOrderDO tradeOrderDO);

    /**
     * 调用异步通知
     *
     * @param tradeNotifyBO
     */
    void asyncNotify(TradeNotifyBO tradeNotifyBO);


    void asyncContractNotify(ContractWithholdBO contractWithholdBO);


}
