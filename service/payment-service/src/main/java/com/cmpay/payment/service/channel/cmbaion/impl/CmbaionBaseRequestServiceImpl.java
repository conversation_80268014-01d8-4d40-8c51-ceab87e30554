package com.cmpay.payment.service.channel.cmbaion.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.channel.CmbaionPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.AppEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.cmbaion.CmbaionConstants;
import com.cmpay.payment.dto.cmbaion.AbstractCmbaionDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;
import java.util.UUID;

/**
 * @author： PengAnHai
 * @date： 2024-08-14
 * @description：招行一网通公共构建请求类
 * @modifiedBy：
 * @version: 1.0
 */
public abstract class CmbaionBaseRequestServiceImpl extends CmbaionBasePayChannelServiceImpl{

    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;

    /**
     * 构建并发送招行一网通请求。
     *
     * @param abstractCmbaionDTO 通用DTO，包含请求的具体数据。
     * @param baseHandlerBO 基础处理业务对象，用于处理响应。
     * @param requestBusiType 请求的业务类型。
     */
    protected void buildCmbaionRequest(AbstractCmbaionDTO abstractCmbaionDTO, BaseHandlerBO baseHandlerBO, String requestBusiType) {
        // 创建请求对象并设置基本属性
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(CmbaionPayChannelEnum.CMBAION);
        request.setRoute(CmbaionPayChannelEnum.CMBAION);
        request.setBusiType(requestBusiType);
        request.setTarget(abstractCmbaionDTO);
        // 发送请求并获取响应
        GenericRspDTO<Response> genericRspDTO;
        // 判断是哪个应用触发的请求
        if (StringUtils.equalsAny(baseHandlerBO.getSourceApp(), AppEnum.integrationbatch.name(), AppEnum.integrationshecdule.name())) {
            // 分布式和批量应用触发的请求，使用 nrtCgwOutClient 发起请求，并将请求的返回结果赋值给 genericRspDTO
            genericRspDTO = nrtCgwOutClient.request(createGenericDTOWithBody(request));
        } else {
            // 联机，则使用 paymentCgwOutClient 发起请求，并将请求的返回结果赋值给 genericRspDTO
            genericRspDTO = paymentCgwOutClient.request(createGenericDTOWithBody(request));
        }
        // 检查返回结果 genericRspDTO 是否不成功
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            // 如果返回结果不成功，抛出业务异常
            BusinessException.throwBusinessException(genericRspDTO);
        }
        // 处理响应，如果响应为空则抛出业务异常
        Optional.ofNullable(genericRspDTO)
                .map(GenericDTO::getBody)
                .map(Response::getResult)
                .map(result -> handlerCmbaionResponse((AbstractCmbaionDTO) result, baseHandlerBO))
                .orElseThrow(() -> new BusinessException(MsgCodeEnum.REQUEST_TIME_OUT));
    }

    /**
     * 抽象方法，由子类实现，用于处理响应结果。
     *
     * @param result 响应结果，具体类型为AbstractDcepRsp。
     * @param baseHandlerBO 基础处理业务对象。
     * @return 处理后的业务对象。
     */
    protected abstract BaseHandlerBO handlerCmbaionResponse(AbstractCmbaionDTO result, BaseHandlerBO baseHandlerBO);

    /**
     * 创建带有请求体的GenericDTO对象。
     *
     * @param request 请求对象。
     * @return 创建的GenericDTO对象。
     */
    private GenericDTO<Request> createGenericDTOWithBody(Request request) {
        return GenericDTO.newChildInstanceWithBody(GenericDTO.class, request);
    }

    /**
     * 配置版本为 ‘2.0’的请求参数
     *
     * @param cmbaionDTO
     * @return
     */
    protected AbstractCmbaionDTO setRequestDataDTO(AbstractCmbaionDTO cmbaionDTO) {
        // 设置版本号，设置字符集,设置签名,设置签名类型
        cmbaionDTO.setVersion(CmbaionConstants.VERSION_TWO);
        cmbaionDTO.setCharset(CmbaionConstants.CHARSET);
        cmbaionDTO.setSignType(CmbaionConstants.SHA256);
        return cmbaionDTO;
    }

    /**
     * 配置版本为 ‘1.0’的请求参数
     *
     * @param cmbaionDTO
     * @return
     */
    protected AbstractCmbaionDTO setRequestVersionDataDTO(AbstractCmbaionDTO cmbaionDTO) {
        // 设置版本号，设置字符集,设置签名,设置签名类型
        cmbaionDTO.setVersion(CmbaionConstants.VERSION);
        cmbaionDTO.setCharset(CmbaionConstants.CHARSET);
        cmbaionDTO.setSignType(CmbaionConstants.SHA256);
        return cmbaionDTO;
    }


}
