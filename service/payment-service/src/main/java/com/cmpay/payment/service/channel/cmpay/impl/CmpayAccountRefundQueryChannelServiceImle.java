package com.cmpay.payment.service.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.AmountInfoBO;
import com.cmpay.payment.bo.CmpayAccountRefundBO;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.dto.cmpay.CmpayAccountRefundQueryReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayAccountRefundQueryRspDTO;
import com.cmpay.payment.entity.CmpayRefundJrnDO;
import com.cmpay.payment.service.TradeCommonService;
import com.cmpay.payment.service.channel.cmpay.CmpayAccountRefundQueryChannelService;
import com.cmpay.payment.service.channel.cmpay.CmpayCommonService;
import com.cmpay.payment.service.ext.ExtCmpayRefundJrnService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.PaymentUtils;
import com.cmpay.payment.utils.BeanConvertUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Created on 2024/04/27
 *
 * @author: lb
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CmpayAccountRefundQueryChannelServiceImle implements CmpayAccountRefundQueryChannelService {

    private static final Logger logger = LoggerFactory.getLogger(CmpayAccountRefundQueryChannelServiceImle.class);

    @Value("${cmpay.channelId:}")
    private String channelId;
    @Autowired
    ExtCmpayRefundJrnService extCmpayRefundJrnService;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Autowired
    private CmpayCommonService cmpayCommonService;
    @Autowired
    private TradeCommonService tradeCommonService;
    @Autowired
    private ExtParamInfoService paramInfoService;

    @Override
    public void send(RefundOrderQueryBO refundOrderQueryBO) {
        cmpayAccountRefundQuery(refundOrderQueryBO);
    }

    @Override
    public void cmpayAccountRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {

        CmpayRefundJrnDO querycmpayJournalDO = new CmpayRefundJrnDO();
        querycmpayJournalDO.setTradeOrderNo(refundOrderQueryBO.getRefundOrder().getTradeOrderNo());
        //  查询和包退款流水
        CmpayRefundJrnDO cmpayRefundJrnDO = extCmpayRefundJrnService.load(querycmpayJournalDO);
        if (JudgeUtils.isNull(cmpayRefundJrnDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_JOURNAL_INFO_GET_ERROR);
        }
        // 和包退款流水加锁
        extCmpayRefundJrnService.lock(cmpayRefundJrnDO);
        try {
            // 调用和包账户退款查询接口
            CmpayAccountRefundBO cmpayAccountRefundBO = accountRefundQuery(refundOrderQueryBO);
            logger.info("Cmpay Account Order Query Success,tradeOrderNo is {}", cmpayRefundJrnDO.getTradeOrderNo());
            cmpayRefundJrnDO.setRemark(cmpayAccountRefundBO.getOrderStatus());
            logger.info("Cmpay Account Order Result,tradeOrderNo is {},orderStatus is {}",
                    cmpayRefundJrnDO.getTradeOrderNo(), cmpayAccountRefundBO.getOrderStatus());
            cmpayRefundJrnDO.setReturnMsg(cmpayAccountRefundBO.getMessageCode());
            logger.info("Cmpay Account Order Result,tradeOrderNo is {},messageCode is {}",
                    cmpayRefundJrnDO.getTradeOrderNo(), cmpayAccountRefundBO.getMessageCode());
            cmpayRefundJrnDO.setReturnMsgInfo(cmpayAccountRefundBO.getMessageInfo());

            // 退款状态
            if (OrderStatusEnum.REFUND_SUCCESS.name().equals(cmpayAccountRefundBO.getOrderStatus())
                    || OrderStatusEnum.REFUND_FAIL.name().equals(cmpayAccountRefundBO.getOrderStatus())) {
                refundOrderQueryBO.setStatus(cmpayAccountRefundBO.getOrderStatus());
                if (OrderStatusEnum.REFUND_FAIL.name().equals(cmpayAccountRefundBO.getOrderStatus())) {
                    refundOrderQueryBO.setErrMsgCd(cmpayAccountRefundBO.getErrMsgCd());
                    refundOrderQueryBO.setErrMsgInfo(cmpayAccountRefundBO.getErrMsgInfo());
                }
                refundOrderQueryBO.getRefundOrder().setAccountDate(cmpayAccountRefundBO.getAccountDate());
                refundOrderQueryBO.setRefAmountList(cmpayAccountRefundBO.getRefAmountList());
            } else {
                // 若返回错误码为BCS20001，则判断是否超过30分钟，若超过则当作失败处理
                if (MsgCodeEnum.CMPAY_ACCOUNT_REFUND_NOT_EXISTS.getMsgCd().equals(cmpayAccountRefundBO.getMessageCode())) {
                    tradeCommonService.handleCmpayRefundOrderNotExist(refundOrderQueryBO,cmpayRefundJrnDO,MsgCodeEnum.CMPAY_ACCOUNT_REFUND_NOT_EXISTS);
                }
            }
        } catch (Exception e) {
            logger.info("Cmpay Account Order Query Exception,tradeOrderNo is {},exception is {}",
                    cmpayRefundJrnDO.getTradeOrderNo(), e);
            //系统异常，比如网络异常 记录异常日志
            cmpayCommonService.cmapyThrowClassHandle(cmpayRefundJrnDO,e);
            throw e;
        } finally {
            cmpayRefundJrnDO.setReturnDate(DateTimeUtils.getCurrentDateStr());
            cmpayRefundJrnDO.setReturnTime(DateTimeUtils.getCurrentTimeStr());
            //将异常信息更新到 cmpay退款流水记录
            extCmpayRefundJrnService.updateByNewTranscation(cmpayRefundJrnDO);
            logger.info("Cmpay Account Journal Record Update Success,tradeOrderNo is {}",
                    cmpayRefundJrnDO.getTradeOrderNo());
            if (!MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd().equals(cmpayRefundJrnDO.getReturnMsg())) {
                // 报错退出
                BusinessException.throwBusinessException(cmpayRefundJrnDO.getReturnMsg());
            }
        }

        logger.info("=====================Cmpay Cloud Refund Order Query End!!=====================");
    }

    private CmpayAccountRefundBO accountRefundQuery(RefundOrderQueryBO refundOrderQueryBO) {
        CmpayAccountRefundBO cmpayAccountRefundBO = new CmpayAccountRefundBO();
        CmpayAccountRefundQueryReqDTO cmpayAccountRefundQueryReqDTO = new CmpayAccountRefundQueryReqDTO();
        cmpayAccountRefundQueryReqDTO.setVersion(CmpayConstants.VERSION);
        cmpayAccountRefundQueryReqDTO.setType(CmpayConstants.TYPE);
        cmpayAccountRefundQueryReqDTO.setRequestNo(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.CMPAY_JRN_NO, 18));
        cmpayAccountRefundQueryReqDTO.setChannelNo(channelId);
        cmpayAccountRefundQueryReqDTO.setMerchantOrderNo(refundOrderQueryBO.getOriginalPayTradeOrder().getBankOrderNo());
        cmpayAccountRefundQueryReqDTO.setMerchantOrderDate(refundOrderQueryBO.getOriginalPayTradeOrder().getOrderDate());
        cmpayAccountRefundQueryReqDTO.setMerchantId(refundOrderQueryBO.getRefundOrder().getBankMerchantNo());
        cmpayAccountRefundQueryReqDTO.setMerchantRefundNo(refundOrderQueryBO.getRefundOrder().getBankOrderNo());

        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(CmpayPayChannelEnum.CMPAY_ACCOUNT);
        request.setBusiType(CmpayPayChannelEnum.CMPAY_ACCOUNT_REFUND_QUERY.getName());
        request.setSource(CmpayPayChannelEnum.CMPAY_ACCOUNT);
        request.setTarget(cmpayAccountRefundQueryReqDTO);
        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            cmpayCommonService.cgwReturnInfoCheck(genericRspDTO);
            BusinessException.throwBusinessException(genericRspDTO);
        }
        Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleOnlineRefundQuery(cmpayAccountRefundBO, (CmpayAccountRefundQueryRspDTO) result));
        return cmpayAccountRefundBO;
    }

    private void handleOnlineRefundQuery(CmpayAccountRefundBO cmpayAccountRefundBO, CmpayAccountRefundQueryRspDTO cmpayAccountRefundQueryRspDTO) {
        if (JudgeUtils.isNull(cmpayAccountRefundQueryRspDTO)) {
            cmpayAccountRefundBO.setMessageCode(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgCd());
            cmpayAccountRefundBO.setMessageInfo(MsgCodeEnum.CMPAY_REFUND_REQUEST_ERROR.getMsgInfo());
            return;
        }
        if (JudgeUtils.equals(cmpayAccountRefundQueryRspDTO.getRefundStatus(),CmpayAccountOrderStatusEnum.S.name())) {
            cmpayAccountRefundBO.setOrderStatus(OrderStatusEnum.REFUND_SUCCESS.name());
            cmpayAccountRefundBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            cmpayAccountRefundBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
            // 记录退款金额明细集合
            List<AmountInfoBO> amountInfoBOS = BeanConvertUtils.convertList(cmpayAccountRefundQueryRspDTO.getPayAmountList(), AmountInfoBO.class);
            cmpayAccountRefundBO.setRefAmountList(PaymentUtils.setAmountInfoBOList(amountInfoBOS, paramInfoService.getAmountListParam()));
        } else if (JudgeUtils.equals(cmpayAccountRefundQueryRspDTO.getRefundStatus(),CmpayAccountOrderStatusEnum.U.name())) {
            cmpayAccountRefundBO.setOrderStatus(OrderStatusEnum.REFUND_WAIT.name());
            cmpayAccountRefundBO.setMessageCode(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgCd());
            cmpayAccountRefundBO.setMessageInfo(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgInfo());
        } else if (JudgeUtils.equals(cmpayAccountRefundQueryRspDTO.getRefundStatus(),CmpayAccountOrderStatusEnum.F.name())) {
            cmpayAccountRefundBO.setOrderStatus(OrderStatusEnum.REFUND_FAIL.name());
            cmpayAccountRefundBO.setErrMsgCd(cmpayAccountRefundQueryRspDTO.getMsgCd());
            cmpayAccountRefundBO.setErrMsgInfo(cmpayAccountRefundQueryRspDTO.getMsgInfo());
            cmpayAccountRefundBO.setMessageCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            cmpayAccountRefundBO.setMessageInfo(MsgCodeEnum.FAP_SUCCESSFUL.getMsgInfo());
        } else {
            if (MsgCodeEnum.CMPAY_ACCOUNT_REFUND_NOT_EXISTS.getMsgCd().equals(cmpayAccountRefundQueryRspDTO.getMsgCd())) {
                cmpayAccountRefundBO.setMessageCode(MsgCodeEnum.CMPAY_ACCOUNT_REFUND_NOT_EXISTS.getMsgCd());
                cmpayAccountRefundBO.setMessageInfo(MsgCodeEnum.CMPAY_ACCOUNT_REFUND_NOT_EXISTS.getMsgInfo());
            } else {
                cmpayAccountRefundBO.setMessageCode(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgCd());
                cmpayAccountRefundBO.setMessageInfo(MsgCodeEnum.REFUND_STATUS_UNDEFINED.getMsgInfo());
            }
        }
    }
}
