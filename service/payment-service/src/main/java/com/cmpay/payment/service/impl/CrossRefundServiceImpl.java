package com.cmpay.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.bo.RefundOrderCloneBO;
import com.cmpay.payment.bo.config.SubOrderInfoExtBO;
import com.cmpay.payment.constant.Constants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.*;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.ExtRefundOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


/**
 * @date 2023-11-23 17:00
 * <AUTHOR>
 * @Version 1.0
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class CrossRefundServiceImpl implements CrossRefundService {
    private static final Logger logger = LoggerFactory.getLogger(CrossRefundServiceImpl.class);
    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private RefundVerifyService refundVerifyService;
    @Autowired
    private ExtRefundOrderService refundOrderService;
    @Autowired
    private TradeNotifyService tradeNotifyService;
    @Autowired
    private PayOrderExtFunctionService extFunctionService;
    @Autowired
    SubOrderInfosResolveService resolveService;


    @Override
//    不带商户号加分布式锁
    @DistributedLocked(lockName = "'OrderRefund:'+#refundBO.getOutTradeNo()", leaseTime = 60, waitTime = 1, ignoreUnableToAcquiredLockException = false)
    public RefundBO crossRefund(RefundBO refundBO) {
        //检查子商户号是否合法
        extFunctionService.subMerchantCheck(refundBO.getSubMerchant());
        // 原订单状态校验
        refundVerifyService.verifyOrderStatus(refundBO);
        // 此跨渠道退款接口 的 特殊校验
        if(!verifyOrderType(refundBO)){
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_TYPE_IS_NOT_SUPPORTED);
        }
        // 金额判断
        refundVerifyService.verifyRefundAmount(refundBO);
        // 登记退款表：分账参数更新进订单表
        if(Constants.INT_ONE != registerCrossRefund(refundBO)){
            BusinessException.throwBusinessException(MsgCodeEnum.TRADE_ORDER_UPDATE_ERROR);
        }
        // 发往支付机构
         return sendRefundRequest(refundBO);
    }

    private RefundBO sendRefundRequest(RefundBO refundBO) {
        TradeOrderDO originalPayTradeOrderDO = refundBO.getOriginalPayTradeOrder();
        RefundOrderDO refundOrderDO = refundBO.getRefundOrder();

        try {
            //和包退款先登记再发往
            if (JudgeUtils.equals(originalPayTradeOrderDO.getAimProductCode(), PaymentWayEnum.CMPAY.name().toLowerCase())) {
                // 退款订单状态修改为退款待处理
                refundOrderDO.setStatus(OrderStatusEnum.REFUND_PEND.name());
                // 更新退款订单
                refundOrderService.update(refundOrderDO);
                refundBO.setMsgCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
                return refundBO;
            }
            applicationContext.publishEvent(refundBO);
            // 原支付订单设置状态为REFUND_WAIT
            originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
            // 更新支付订单
            payOrderService.update(originalPayTradeOrderDO);
            refundBO.setMsgCode(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());

        } catch (Exception e) {
            if (e instanceof BusinessException) {
                //记录业务抛出的错误码 及错误信息
                refundBO.setMsgCode(((BusinessException) e).getMsgCd());
                // 退款订单状态修改为退款失败
                refundOrderDO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
                //修改退款时间
                refundOrderDO.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                refundOrderDO.setReceiveNotifyTime(DateTimeUtils.getCurrentDateTimeStr());
                //更新报错信息
                refundOrderDO.setErrMsgCd(refundBO.getErrMsgCd());
                refundOrderDO.setErrMsgInfo(refundBO.getErrMsgInfo());
                // 更新退款订单
                refundOrderService.update(refundOrderDO);
                // 调用退款通知处理
                RefundOrderCloneBO refundOrderCloneBO = new RefundOrderCloneBO();
                BeanUtils.copyProperties(refundOrderDO, refundOrderCloneBO);
                tradeNotifyService.handleRefundNotify(refundOrderCloneBO);

            } else {
                //如果是未知的系统错误，比如网络请求异常  原支付订单状态修改为退款中
                originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
            }
        } finally {
            // 更新返回值
            refundBO.setTotalAmount(originalPayTradeOrderDO.getOrderAmount());
        }
        return refundBO;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int registerCrossRefund(RefundBO refundBO) {
        // 包含子商户号的订单，刷新子商户退款金额
        refundVerifyService.subMerchantRefundConfine(refundBO);
        TradeOrderDO originalOrderDO = refundBO.getOriginalPayTradeOrder();
        // 1、登记退款表
        refundBO.setSplitFlag(Constants.N);
        //创建退款实例
        RefundOrderDO refundOrderDO = refundOrderService.insert(refundBO, originalOrderDO);
        //保存退款订单记录到参数对像里，以便后面使用
        refundBO.setRefundOrder(refundOrderDO);
        // 2、解析原支付订单参数，组装
        List<SubOrderInfoExtBO> subOrderInfoExtBOList = new ArrayList<>();
        if(StringUtils.isNotEmpty(originalOrderDO.getSubOrderInfos())){
            subOrderInfoExtBOList = resolveService.resolveExt(originalOrderDO.getSubOrderInfos());
        }
        SubOrderInfoExtBO subOrderInfoBO = new SubOrderInfoExtBO();
        subOrderInfoBO.setSubMerchant(refundBO.getSubMerchant());
        subOrderInfoBO.setSubOrderNo(refundBO.getOutRequestNo());
        subOrderInfoBO.setSettlementDept(refundBO.getSettlementDept());
        subOrderInfoBO.setSettlementItem(refundBO.getSettlementItem());
        subOrderInfoBO.setMerchantChannelType(refundBO.getMerchantChannelType());
        subOrderInfoBO.setSubOrderAmount(refundBO.getRefundAmount().toPlainString());
        subOrderInfoExtBOList.add(subOrderInfoBO);
        String subOrderInfos = JSON.toJSONString(subOrderInfoExtBOList);
        // 3、update将退款子订单更新到订单表，待到这笔退款成功，就解析进结算表
        originalOrderDO.setSubOrderInfos(subOrderInfos);
        return payOrderService.updateSubOrderInfos(originalOrderDO);
    }

    private boolean verifyOrderType(RefundBO refundBO) {
        TradeOrderDO originalPayTradeOrderDO = refundBO.getOriginalPayTradeOrder();
        // 此退款接口，只支持非分账订单，且没有子商户的，原统一支付订单
        if ((StringUtils.isNotEmpty(originalPayTradeOrderDO.getSplitFlag()) && StringUtils.equals(originalPayTradeOrderDO.getSplitFlag(), Constants.Y))
                || StringUtils.isNotEmpty(originalPayTradeOrderDO.getSubMerchantNo())) {
            return false;
        }
        return true;
    }

}
