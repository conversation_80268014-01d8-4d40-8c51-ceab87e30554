package com.cmpay.payment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.AmountInfoBO;
import com.cmpay.payment.bo.PayNotifyBO;
import com.cmpay.payment.bo.PayOrderQueryBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.PayOrderQueryService;
import com.cmpay.payment.service.SensorsDataService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Created on 2018/11/30
 *
 * @author: sun_zhh
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class PayOrderQueryImpl implements PayOrderQueryService {
    @Autowired
    private ExtPayOrderService paymentOrderService;
    @Autowired
    private SensorsDataService sensorsDataService;

    @Override
    public PayOrderQueryBO payOrderQuery(PayOrderQueryBO payOrderQueryBO) {
        if (JudgeUtils.isNull(payOrderQueryBO.getMerchantId())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isNull(payOrderQueryBO.getOutTradeNo())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ORDER_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isNull(payOrderQueryBO.getTradeDate())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_REQUEST_DATE_CANNOT_BE_EMPTY);
        }
        TradeOrderDO tradeOrderDO = new TradeOrderDO();
        tradeOrderDO.setMerchantNo(payOrderQueryBO.getMerchantId());
        tradeOrderDO.setRequestDate(payOrderQueryBO.getTradeDate());
        tradeOrderDO.setOutTradeNo(payOrderQueryBO.getOutTradeNo());
        TradeOrderDO paymentOrder = paymentOrderService.load(tradeOrderDO);
        if (JudgeUtils.isNull(paymentOrder)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }

        payOrderQueryBO.setTotalAmount(paymentOrder.getOrderAmount());
        payOrderQueryBO.setTradeStatus(paymentOrder.getStatus());
        if (JudgeUtils.equals(paymentOrder.getStatus(), OrderStatusEnum.WAIT_PAY.name())) {
            //判断订单超过有效期返回订单关闭
            LocalDateTime currentDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
            // 和包网银支付不判断过期时间
            boolean isCmpayNetBank = JudgeUtils.equalsAny(paymentOrder.getPayWayCode(), PaymentSceneEnum.NETBANK.name().toLowerCase(), PaymentSceneEnum.NETBANKB2B.name().toLowerCase())
                    && JudgeUtils.equals(PaymentWayEnum.CMPAY.toString().toLowerCase(), paymentOrder.getAimProductCode());
            // 过期
            if (JudgeUtils.isNotNull(paymentOrder.getExpireTime())) {
                boolean timeCompare = currentDateTime.isAfter(DateTimeUtils.parseLocalDateTime(paymentOrder.getExpireTime()));
                if (!isCmpayNetBank && timeCompare) {
                    payOrderQueryBO.setTradeStatus(OrderStatusEnum.TRADE_CLOSED.name());
                }
            }
        }
        if (JudgeUtils.equals(PaymentSceneEnum.NETBANKB2B.name().toLowerCase(), paymentOrder.getPayWayCode())) {
            payOrderQueryBO.setScene(PaymentSceneEnum.NETBANK.name().toLowerCase());
        } else if (JudgeUtils.equalsAny(paymentOrder.getPayWayCode().toUpperCase(), PaymentSceneEnum.APPPCREDIT.name(),
                PaymentSceneEnum.WAPPCREDIT.name())) {
            payOrderQueryBO.setScene(paymentOrder.getPayWayCode().substring(0, 3));
        } else {
            payOrderQueryBO.setScene(paymentOrder.getPayWayCode());
        }
        payOrderQueryBO.setRealAmount(paymentOrder.getRealAmount());
        payOrderQueryBO.setDiscountableAmount(paymentOrder.getDiscountableAmount());
        payOrderQueryBO.setFinishDateTime(paymentOrder.getOrderCompleteTime());
        payOrderQueryBO.setPayWay(paymentOrder.getPayProductCode());
        payOrderQueryBO.setPaymentRout(paymentOrder.getAimProductCode());
        payOrderQueryBO.setExtra(paymentOrder.getRemark());
        payOrderQueryBO.setProductName(paymentOrder.getGoodsName());
        payOrderQueryBO.setReturnUrl(paymentOrder.getReturnUrl());
        payOrderQueryBO.setAccountDate(paymentOrder.getAccountDate());
        if (JudgeUtils.isNotNull(paymentOrder.getDiscountAmount())) {
            if (paymentOrder.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
                payOrderQueryBO.setDiscountAmount(paymentOrder.getDiscountAmount());
            }
        }
        if (JudgeUtils.equalsAny(paymentOrder.getAimProductCode(),
                PaymentWayEnum.ALIPAY.name().toLowerCase(),
                PaymentWayEnum.WECHAT.name().toLowerCase(),
                PaymentWayEnum.INTEGRALPAY.name().toLowerCase())) {
            payOrderQueryBO.setPaymentOrderNo(paymentOrder.getThirdOrdNo());
        }
        //第三方支付机构错误码
        payOrderQueryBO.setErrMsgCd(paymentOrder.getErrMsgCd());
        payOrderQueryBO.setErrMsgInfo(paymentOrder.getErrMsgInfo());
        if (JudgeUtils.isNotBlank(paymentOrder.getPayAmountList())) {
            // 记录支付金额明细
            payOrderQueryBO.setPayAmountList(JSONObject.parseArray(paymentOrder.getPayAmountList(), AmountInfoBO.class));
        }
        payOrderQueryBO.setInstPaidAmount(paymentOrder.getInstPaidAmount());
        payOrderQueryBO.setInstDiscountUnsettledAmount(paymentOrder.getInstDiscountUnsettledAmount());
        payOrderQueryBO.setInstDiscountSettlementAmount(paymentOrder.getInstDiscountSettlementAmount());
        payOrderQueryBO.setActivityId(paymentOrder.getActivityId());
        payOrderQueryBO.setActivityName(paymentOrder.getActivityName());
        return payOrderQueryBO;
    }

    @Override
    public PayNotifyBO pageNotifyQuery(PayOrderQueryBO payOrderQueryBO) {
        if (JudgeUtils.isNull(payOrderQueryBO.getTradeOrderNo())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ORDER_NO_CANNOT_BE_EMPTY);
        }
        TradeOrderDO paymentOrder = paymentOrderService.getOrderByNo(payOrderQueryBO.getTradeOrderNo());
        if (JudgeUtils.isNull(paymentOrder)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }

        PayNotifyBO payNotifyBO = new PayNotifyBO();
        payNotifyBO.setOutTradeNo(paymentOrder.getOutTradeNo());
        payNotifyBO.setReturnUrl(paymentOrder.getReturnUrl());
        payNotifyBO.setTradeDate(paymentOrder.getOrderDate());
        payNotifyBO.setRealAmount(paymentOrder.getRealAmount());
        payNotifyBO.setTotalAmount(paymentOrder.getOrderAmount());
        payNotifyBO.setDiscountableAmount(paymentOrder.getDiscountableAmount());
        payNotifyBO.setExtra(paymentOrder.getRemark());
        payNotifyBO.setProductName(paymentOrder.getSubject());
        payNotifyBO.setTradeStatus(OrderStatusEnum.TRADE_SUCCESS.name());
        payNotifyBO.setMerchantId(paymentOrder.getMerchantNo());
        payNotifyBO.setTradeOrderStatus(paymentOrder.getStatus());
        payNotifyBO.setPayProductCode(paymentOrder.getPayProductCode());
        payNotifyBO.setGoodsName(paymentOrder.getGoodsName());

        return payNotifyBO;
    }
}
