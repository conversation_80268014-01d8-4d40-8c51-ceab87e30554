package com.cmpay.payment.service.notify;

import com.cmpay.payment.bo.notify.AccountNotifyBO;
import com.cmpay.payment.bo.notify.CmpayNotifyBO;

/**
 * <AUTHOR>
 * @date 2025/5/14 13:08
 */
public interface ICmpayNotifyService {
    void backendNotifyAccount(AccountNotifyBO accountNotifyBO);

    void backendNotify(CmpayNotifyBO cmpayNotifyBO);

    void backendNotifyPos(CmpayNotifyBO cmpayNotifyBO);

    void backendNotifyRefund(CmpayNotifyBO cmpayNotifyBO);

    void backendNotifyPosRefund(CmpayNotifyBO cmpayNotifyBO);
}
