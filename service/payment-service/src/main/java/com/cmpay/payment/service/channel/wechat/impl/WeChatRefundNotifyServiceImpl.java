package com.cmpay.payment.service.channel.wechat.impl;

import com.cmpay.bagw.wxpay.dto.WXPayRefundResultNotify;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.RefundNotifyBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.data.OrderFeeBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.RefundFeeWayEnum;
import com.cmpay.payment.constant.TradeTypeEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.constant.wechat.WeChatRefundStatusEnum;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.CompleteOrderSplitService;
import com.cmpay.payment.service.channel.wechat.WeChatRefundNotifyService;
import com.cmpay.payment.service.data.IDataFeeService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.ExtRefundOrderService;
import com.cmpay.payment.util.PaymentUtils;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatRefundNotifyServiceImpl implements WeChatRefundNotifyService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatRefundNotifyServiceImpl.class);

    @Autowired
    private ExtRefundOrderService refundOrderService;
    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private IDataFeeService feeService;
    @Autowired
    private AsynCommonService asynCommonService;
    @Autowired
    private CompleteOrderSplitService splitService;

    /**
     * 微信H5支付退款通知
     *
     * @param refundNotifyBO
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void weChatRefundNotify(RefundNotifyBO refundNotifyBO) {
        WXPayRefundResultNotify refundResult = (WXPayRefundResultNotify) refundNotifyBO.getRefundResult();

        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), refundResult.getReturnCode())) {

            WXPayRefundResultNotify.ReqInfo reqInfo = refundResult.getReqInfo();

            // 当收到通知进行处理时，首先检查对应业务数据的状态，判断该通知是否已经处理过，如果没有处理过再进行处理，如果处理过直接返回结果成功。
            RefundOrderDO refundOrderDO = refundOrderService.get(reqInfo.getOutRefundNo());

            if (StringUtils.endsWithAny(refundOrderDO.getStatus(), OrderStatusEnum.REFUND_SUCCESS.name())) {
                return;
            }
            if (JudgeUtils.isEmpty(reqInfo.getSuccessTime())) {
                refundOrderDO.setAccountDate(DateTimeUtils.getCurrentDateStr());
            } else {
                refundOrderDO.setAccountDate(PaymentUtils.getDate(reqInfo.getSuccessTime()));
            }
            TradeOrderDO originTradeOrderDO = payOrderService.get(refundOrderDO.getOrgOrderNo());

            // 原支付订单状态检查
            if (!StringUtils.equals(OrderStatusEnum.REFUND_WAIT.name(), originTradeOrderDO.getStatus())) {
                logger.error("origin payment order refund status error, orgOrderNo: {}, status: {}", originTradeOrderDO.getTradeOrderNo(), originTradeOrderDO.getStatus());
                BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_STATUS_CANNOT_REFUND);
            }

            //  在对业务数据进行状态检查和处理之前，要采用数据锁进行并发控制，以避免函数重入造成的数据混乱。
            refundOrderService.lock(refundOrderDO);
            payOrderService.lock(originTradeOrderDO);

            WeChatRefundStatusEnum status = EnumUtils.getEnum(WeChatRefundStatusEnum.class, reqInfo.getRefundStatus());
            switch (status) {
                case SUCCESS:
                    refundOrderDO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
                    refundOrderDO.setOrderCompleteTime(DateTimeUtils.formatLocalDateTime(DateTimeUtils.parseLocalDateTime(reqInfo.getSuccessTime()
                            , "yyyy-MM-dd HH:mm:ss"), "yyyyMMddHHmmss"));
                    refundOrderDO.setReceiveNotifyTime(refundOrderDO.getOrderCompleteTime());
                    refundOrderDO.setRefundId(reqInfo.getRefundId());
                    break;
                case REFUNDCLOSE:
                case CHANGE:
                default:
                    refundOrderDO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
                    break;
            }

            if (StringUtils.equals(refundOrderDO.getStatus(), OrderStatusEnum.REFUND_SUCCESS.name())) {
                RefundOrderDO queryRefundOrderDO = new RefundOrderDO();
                queryRefundOrderDO.setMerchantNo(refundOrderDO.getMerchantNo());
                queryRefundOrderDO.setOrgOrderNo(refundOrderDO.getOrgOrderNo());
                queryRefundOrderDO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
                List<RefundOrderDO> refundOrderDOList = refundOrderService.find(queryRefundOrderDO);

                BigDecimal alreadyRefundTotalAmount = BigDecimal.ZERO;
                BigDecimal alreadyRefundTotalFeeAmount = BigDecimal.ZERO;

                if (refundOrderDOList.size() > 0) {
                    //计算退款总金额
                    alreadyRefundTotalAmount = refundOrderDOList.stream().map(RefundOrderDO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    alreadyRefundTotalFeeAmount = refundOrderDOList.stream().map(RefundOrderDO::getOrderFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                // 本次退款总金额 =已退款金额+本次退款金额
                BigDecimal currentRefundTotalAmount = alreadyRefundTotalAmount.add(refundOrderDO.getOrderAmount());

                if (currentRefundTotalAmount.compareTo(originTradeOrderDO.getRealAmount()) == 0) {
                    originTradeOrderDO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
                    originTradeOrderDO.setRefundTimes(originTradeOrderDO.getRefundTimes() + 1);
                    originTradeOrderDO.setSuccessRefundAmount(originTradeOrderDO.getSuccessRefundAmount().add(refundOrderDO.getOrderAmount()));
                } else if (currentRefundTotalAmount.compareTo(originTradeOrderDO.getRealAmount()) < 0) {
                    originTradeOrderDO.setStatus(OrderStatusEnum.REFUND_PART.name());
                    originTradeOrderDO.setRefundTimes(originTradeOrderDO.getRefundTimes() + 1);
                    originTradeOrderDO.setSuccessRefundAmount(originTradeOrderDO.getSuccessRefundAmount().add(refundOrderDO.getOrderAmount()));
                } else {
                    logger.error("refund amount error, current refund amount: {}, already refund amount: {}, origin order amount: {}",
                            refundOrderDO.getOrderAmount(), currentRefundTotalAmount, originTradeOrderDO.getOrderAmount());
                    BusinessException.throwBusinessException(MsgCodeEnum.REFUND_AMOUNT_ERROR);
                }
                //计算退款手续费
                BigDecimal refundFeeAmount = BigDecimal.ZERO;
                if (originTradeOrderDO.getOrderFeeAmount().compareTo(BigDecimal.ZERO) > 0
                        && !JudgeUtils.equals(originTradeOrderDO.getRefundFeeWay(), RefundFeeWayEnum.NO_REFUND_FEE_WAY.getDesc())) {
                    // 如果是分账订单
                    if (splitService.checkSplitOrder(refundOrderDO, originTradeOrderDO)) {
                        //  计算退款分账服务费
                        splitService.splitOrderRefundFee(refundOrderDO, originTradeOrderDO);
                    } else {
                        OrderFeeBO orderFeeBO = new OrderFeeBO();
                        orderFeeBO.setOrderAmount(originTradeOrderDO.getOrderAmount());
                        orderFeeBO.setRefundAmount(refundOrderDO.getOrderAmount());
                        orderFeeBO.setSuccessRefundFeeAmount(alreadyRefundTotalFeeAmount);
                        orderFeeBO.setSuccessRefundAmount(alreadyRefundTotalAmount);
                        orderFeeBO.setRefundFeeAmount(BigDecimal.ZERO);
                        orderFeeBO.setOrderFeeAmount(originTradeOrderDO.getOrderFeeAmount());
                        orderFeeBO.setOrderRate(originTradeOrderDO.getOrderRate());
                        orderFeeBO = feeService.refundFeeCalculate(orderFeeBO);
                        refundFeeAmount = orderFeeBO.getRefundFeeAmount();
                        refundOrderDO.setOrderFeeAmount(orderFeeBO.getRefundFeeAmount());
                    }
                }
                // 更新退款订单
                refundOrderService.update(refundOrderDO);
                // 更新支付订单
                payOrderService.update(originTradeOrderDO);
                // 登记通知
                TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
                tradeNotifyBO.setMerchantNo(refundOrderDO.getMerchantNo());
                tradeNotifyBO.setTradeOrderNo(refundOrderDO.getTradeOrderNo());
                tradeNotifyBO.setOutOrderNo(refundOrderDO.getOrgOrderNo());
                tradeNotifyBO.setOutRequestNo(refundOrderDO.getOutTradeNo());
                tradeNotifyBO.setTradeDate(refundOrderDO.getOrderDate());
                tradeNotifyBO.setFinishDate(refundOrderDO.getOrderCompleteTime());
                tradeNotifyBO.setTradeAmount(refundOrderDO.getOrderAmount());
                tradeNotifyBO.setNotifyDate(DateTimeUtils.getCurrentDateStr());
                tradeNotifyBO.setNotifyTime(DateTimeUtils.getCurrentTimeStr());
                tradeNotifyBO.setNotifyType(TradeTypeEnum.REFUND.name().toLowerCase());
                tradeNotifyBO.setNotifyUrl(refundOrderDO.getNotifyUrl());
                tradeNotifyBO.setExtra(refundOrderDO.getRemark());
                tradeNotifyBO.setSecretIndex(refundOrderDO.getSecretIndex());
                asynCommonService.asyncNotify(tradeNotifyBO);

            }
        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_NOTIFY_FAILURE);
        }

    }

    @Override
    public void send(RefundNotifyBO refundNotifyBO) {
        weChatRefundNotify(refundNotifyBO);
    }

}
