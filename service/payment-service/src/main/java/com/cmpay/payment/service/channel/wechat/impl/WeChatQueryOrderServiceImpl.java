package com.cmpay.payment.service.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.AppEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.constant.wechat.WeChatTradeStatusEnum;
import com.cmpay.payment.dto.wechat.WXPayOrderqueryRequest;
import com.cmpay.payment.dto.wechat.WXPayOrderqueryResponse;
import com.cmpay.payment.service.channel.wechat.WeChatQueryOrderService;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatQueryOrderServiceImpl implements WeChatQueryOrderService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatQueryOrderServiceImpl.class);

    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;

    /**
     * 微信H5支付订单查询
     *
     * @param paymentQueryBO
     */
    @Override
    public void weChatPaymentOrderQuery(PaymentQueryBO paymentQueryBO) {
        WXPayOrderqueryRequest wxPayOrderqueryRequest = new WXPayOrderqueryRequest();
        wxPayOrderqueryRequest.setOutTradeNo(paymentQueryBO.getPaymentOrder().getOutTradeNo());
        wxPayOrderqueryRequest.setAppid(paymentQueryBO.getPaymentOrder().getWechatAppid());
        wxPayOrderqueryRequest.setMchId(paymentQueryBO.getPaymentOrder().getBankMerchantNo());
        wxPayOrderqueryRequest.setKey(paymentQueryBO.getContractSecureValue());
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.ORDERQUERY.getSource());
        request.setRoute(WXPayChannel.ORDERQUERY.getRoute());
        request.setBusiType(WXPayChannel.ORDERQUERY.getBusType());
        request.setTarget(wxPayOrderqueryRequest);
        GenericRspDTO<Response> genericRspDTO;
        if (StringUtils.equalsAny(paymentQueryBO.getSourceApp(), AppEnum.integrationbatch.name(), AppEnum.integrationshecdule.name())) {
            genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        } else {
            genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        }
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(paymentQueryBO, (WXPayOrderqueryResponse) result));
    }

    @Override
    public void send(PaymentQueryBO paymentQueryBO) {
        weChatPaymentOrderQuery(paymentQueryBO);
    }

    private void handleResult(PaymentQueryBO paymentQueryBO, WXPayOrderqueryResponse response) {
        paymentQueryBO.getPaymentOrder().setThirdOrdNo(response.getTransactionId());
        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                if (Objects.isNull(response.getTradeState())) {
                    paymentQueryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
                } else {
                    // 记录流水 卡种根据下单记录流水判断
                    WeChatTradeStatusEnum status = EnumUtils.getEnum(WeChatTradeStatusEnum.class, response.getTradeState());
                    switch (status) {
                        case SUCCESS:
                            paymentQueryBO.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                            paymentQueryBO.setFinishDateTime(response.getTimeEnd());
                            paymentQueryBO.getPaymentOrder().setAccountDate(response.getTimeEnd().substring(0, 8));
                            paymentQueryBO.getPaymentOrder().setThirdOrdDt(response.getTimeEnd().substring(0, 8));
                            paymentQueryBO.getPaymentOrder().setMobileNumber(response.getOpenid());
                            break;
                        case REFUND:
                            paymentQueryBO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
                            paymentQueryBO.setErrMsgInfo(response.getTradeStateDesc());
                            break;
                        case CLOSED:
                        case REVOKED:
                            paymentQueryBO.setStatus(OrderStatusEnum.TRADE_CLOSED.name());
                            paymentQueryBO.setErrMsgInfo(response.getTradeStateDesc());
                            break;
                        case PAYERROR:
                            paymentQueryBO.setErrMsgCd(response.getErrCode());
                            paymentQueryBO.setErrMsgInfo(response.getTradeStateDesc());
                            paymentQueryBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                            break;
                        case USERPAYING:
                        case NOTPAY:
                        case ACCEPT:
                        default:
                            paymentQueryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
                            break;
                    }
                }
            } else {
                logger.error("query order failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                paymentQueryBO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
                WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getErrCode());
                switch (errorCodeEnum) {
                    case ORDERNOTEXIST:
                        paymentQueryBO.setErrMsgCd(response.getErrCode());
                        paymentQueryBO.setErrMsgInfo(response.getErrCodeDes());
                        break;
                    default:
                        BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_QUERY_ORDER_FAILURE);
                }
            }
        } else {
            logger.error("query order failure, weChat return message is : {}", response.getReturnMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.WECHAT_QUERY_ORDER_FAILURE);
        }
    }
}
