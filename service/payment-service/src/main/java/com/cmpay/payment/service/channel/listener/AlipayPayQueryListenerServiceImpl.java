package com.cmpay.payment.service.channel.listener;

import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.aplipay.impl.AlipayPayQueryChannelServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Created on 2018/12/10
 *
 * @author: wulinfeng
 */
@Service
public class AlipayPayQueryListenerServiceImpl extends PaymentListenerService<PaymentQueryBO> {

    @Autowired
    ApplicationContext applicationContext;

    @EventListener
    @Override
    public void execute(PaymentQueryBO paymentQueryBO) {
        super.execute(paymentQueryBO);
    }

    @Override
    protected boolean checkChannelExecutable(PaymentQueryBO paymentQueryBO) {
        return Optional.ofNullable(paymentQueryBO)
                .map(PaymentQueryBO::getPaymentOrder)
                .map(TradeOrderDO::getAimProductCode)
                .map(code -> {
                    return StringUtils.equalsIgnoreCase(paymentQueryBO.getPaymentOrder().getAimProductCode(), PaymentWayEnum.ALIPAY.name());
                })
                .orElse(false);
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(PaymentQueryBO paymentQueryBO) {
        return getBean(AlipayPayQueryChannelServiceImpl.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return this.applicationContext;
    }

}
