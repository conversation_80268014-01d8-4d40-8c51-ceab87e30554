package com.cmpay.payment.service.channel.integralpay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.IntegralPayOrderBO;
import com.cmpay.payment.channel.IntegralPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.IntegralPaymentStatusEnum;
import com.cmpay.payment.constant.IntegralpayConstants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.dto.integralpay.IntegralExchangeReq;
import com.cmpay.payment.dto.integralpay.IntegralExchangeRsp;
import com.cmpay.payment.service.channel.integralpay.IntegralPayService;
import com.cmpay.payment.util.PaymentUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> wenzhan
 * @Date 2021-8-31 0031 14:42
 */
@Service
public class IntegralPayServiceImpl implements IntegralPayService {
    private static final Logger logger = LoggerFactory.getLogger(IntegralPayServiceImpl.class);

    @Autowired
    IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Value("${integralpay.channelCode:}")
    private String channelCode;
    @Override
    public void send(IntegralPayOrderBO integralPayOrderBO) {
        integralPay(integralPayOrderBO);
    }

    @Override
    public void integralPay(IntegralPayOrderBO integralPayOrderBO){
        IntegralExchangeReq integralExchangeReq = new IntegralExchangeReq();
        integralExchangeReq.setTraceId(integralPayOrderBO.getOutTradeNo());
        integralExchangeReq.setSpanId(integralPayOrderBO.getOutTradeNo());
        integralExchangeReq.setServiceCode(IntegralpayConstants.S0002);
        //能力开放平台分配
        integralExchangeReq.setChannelCode(channelCode);
        integralExchangeReq.setReqTime(String.valueOf(System.currentTimeMillis()));
        if(JudgeUtils.equalsIgnoreCase(PaymentSceneEnum.WAP.name(),integralPayOrderBO.getScene())
                || JudgeUtils.equalsIgnoreCase(PaymentSceneEnum.APP.name(),integralPayOrderBO.getScene())){
            integralExchangeReq.setAccessChannel(IntegralpayConstants.API_APP);
        }else if(JudgeUtils.equalsIgnoreCase(PaymentSceneEnum.WEB.name(),integralPayOrderBO.getScene())){
            integralExchangeReq.setAccessChannel(IntegralpayConstants.API_WEB);
        }else {
            integralExchangeReq.setAccessChannel(IntegralpayConstants.API_OTHER);
        }
        integralExchangeReq.setMobile(integralPayOrderBO.getMobile());
        IntegralExchangeReq.GoodInfo goodInfo = new IntegralExchangeReq.GoodInfo();
        goodInfo.setGoodId(integralPayOrderBO.getProductCode());
        goodInfo.setGoodCount(integralPayOrderBO.getProductCount());
        goodInfo.setGoodPoints(integralPayOrderBO.getOrderPoints().toString());
        List<IntegralExchangeReq.GoodInfo> goodInfoList = new ArrayList<>();
        goodInfoList.add(goodInfo);
        integralExchangeReq.setGoodInfo(goodInfoList);
        integralExchangeReq.setCustomerIp(integralPayOrderBO.getCustomerIp());
        integralExchangeReq.setOrderPoints(integralPayOrderBO.getOrderPoints().toString());
        integralExchangeReq.setSmsCode(integralPayOrderBO.getAuthCode());
        integralExchangeReq.setReserved(integralPayOrderBO.getOutTradeNo());
        logger.info("integralExchangeReq : {}",integralExchangeReq);
        GenericRspDTO<Response> genericRspDTO = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(IntegralPayChannelEnum.INTEGRAL_PAY, IntegralPayChannelEnum.INTEGRAL_EXCHANGE.getName(), IntegralPayChannelEnum.INTEGRAL_PAY, integralExchangeReq));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(integralPayOrderBO, (IntegralExchangeRsp) result));
    }

    public void handleResult(IntegralPayOrderBO integralPayOrderBO, IntegralExchangeRsp integralExchangeRsp){
        logger.info("integralExchangeRsp : {}",integralExchangeRsp.toString());
        if(JudgeUtils.equals(integralExchangeRsp.getRespCode(), IntegralpayConstants.SUCCESS_CODE)&& JudgeUtils.isNotNull(integralExchangeRsp.getResult())){
            integralPayOrderBO.setBankOrderNo(integralExchangeRsp.getResult().getOrderId());
        }else{
            integralPayOrderBO.setStatus(IntegralPaymentStatusEnum.TRADE_FAIL.name());
            integralPayOrderBO.setErrMsgCd(integralExchangeRsp.getRespCode());
            integralPayOrderBO.setErrMsgInfo(integralExchangeRsp.getRespDesc());
            BusinessException.throwBusinessException(MsgCodeEnum.POINTS_EXCHANGE_FAIL);
        }
    }
}
