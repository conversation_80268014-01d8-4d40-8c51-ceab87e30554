package com.cmpay.payment.service.channel.aplipay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.dto.alipay.TradePrecreateReq;
import com.cmpay.payment.dto.alipay.TradePrecreateRsp;
import com.cmpay.payment.entity.AlipayPaymentJrnDO;
import com.cmpay.payment.properties.AlipayProperties;
import com.cmpay.payment.service.channel.aplipay.AlipayScanPaymentChannelService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.PaymentUtils;
import com.cmpay.payment.util.UrlUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;

/**
 * Created on 2018/12/13
 *
 * @author: wulinfeng
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS)
public class AlipayScanPaymentChannelServiceImpl extends AlipayBasePaymentChannelServiceImpl implements AlipayScanPaymentChannelService {

    Logger logger = LoggerFactory.getLogger(AlipayScanPaymentChannelServiceImpl.class);
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;

    @Autowired
    AlipayProperties alipayProperties;
    @Autowired
    ExtParamInfoService paramInfoService;
    @Override
    public void alipayScanPayment(PayOrderBO payOrderBO) {
        super.alipayBasePayment(payOrderBO);
    }

    @Override
    public boolean getStatus(PayOrderBO payOrderBO) {
        TradePrecreateReq tradePrecreateReq = new TradePrecreateReq();
        tradePrecreateReq.setOutTradeNo(payOrderBO.getTradeOrderNo());
        tradePrecreateReq.setSubject(payOrderBO.getSubject());
        tradePrecreateReq.setTotalAmount(payOrderBO.getRealAmount().toString());

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                tradePrecreateReq.setNotifyUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getNotifyUrl(),host));
                tradePrecreateReq.setReturnUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getReturnUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                tradePrecreateReq.setNotifyUrl(alipayProperties.getNotifyUrl());
                tradePrecreateReq.setReturnUrl(alipayProperties.getReturnUrl());
            }
        } else {
            tradePrecreateReq.setNotifyUrl(alipayProperties.getNotifyUrl());
            tradePrecreateReq.setReturnUrl(alipayProperties.getReturnUrl());
        }

        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            Date expireTime=simpleDateFormat.parse(payOrderBO.getExpireTime());
            tradePrecreateReq.setTimeExpire(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(expireTime));
        } catch (ParseException e) {
            logger.info(e.getMessage());
        }

        if(StringUtils.isNotBlank(payOrderBO.getTimeoutExpress())){
            tradePrecreateReq.setTimeoutExpress(payOrderBO.getTimeoutExpress());
        }
        try{
            GenericRspDTO<Response> response = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_PRECREATE.getName(), tradePrecreateReq));
            Response body=response.getBody();
            Optional.ofNullable(body)
                    .ifPresent(payOrderBO::setAlipayPayRsp);

            if(StringUtils.contains(response.getMsgCd(),"00000")){

                Response rsp = payOrderBO.getAlipayPayRsp();

                AlipayPaymentJrnDO alipayPaymentJrnDO = new AlipayPaymentJrnDO();

                Optional.ofNullable(rsp)
                        .map(Response::getMsgCode)
                        .ifPresent(alipayPaymentJrnDO::setReturnMsg);

                Optional.ofNullable(rsp)
                        .map(Response::getMsgInfo)
                        .ifPresent(alipayPaymentJrnDO::setReturnMsgInfo);

                alipayPaymentJrnDO.setTradeOrderNo(payOrderBO.getTradeOrderNo());

                Optional.ofNullable(rsp)
                        .map(Response::getResult)
                        .ifPresent(r->{payOrderBO.setPayUrl(((TradePrecreateRsp) r).getQrCode());});

                return true;
            }
        }catch (Exception e){
            return false;
        }
        return false;
    }

    @Override
    public void send(PayOrderBO payOrderBO) {
        alipayScanPayment(payOrderBO);
    }

}
