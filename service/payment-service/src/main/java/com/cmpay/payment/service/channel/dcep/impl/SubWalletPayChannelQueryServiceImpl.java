package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.SubWalletPayChannelQueryBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.SubWalletPayChannelQueryReq;
import com.cmpay.payment.dto.dcep.SubWalletPayChannelQueryRsp;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.SubWalletPayChannelQueryService;
import com.cmpay.payment.util.AES256Util;
import com.cmpay.payment.utils.BeanConvertUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/5/5
 */
@Service
public class SubWalletPayChannelQueryServiceImpl extends AbstractDcepRequestServiceImpl implements SubWalletPayChannelQueryService {

    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;

    @Override
    public void handlerPayChannelQuery(SubWalletPayChannelQueryBO payChannelQueryBO) {
        payChannelQueryBO.setPayWayCode(PaymentSceneEnum.TOKEN.name().toLowerCase());
        SubWalletPayChannelQueryReq subWalletPayChannelQueryReq = new SubWalletPayChannelQueryReq();
        buildRequestDto(subWalletPayChannelQueryReq, payChannelQueryBO, payChannelQueryBO.getChannelNo());
        // 从预下单存的订单attach表里拿，子商户号
//        subWalletPayChannelQueryReq.setSubMerchantNo("********");
        subWalletPayChannelQueryReq.setSubMerchantNo(payChannelQueryBO.getSubMerchantId());
        subWalletPayChannelQueryReq.setMobile(AES256Util.encode(subWalletPayChannelQueryReq.getSmxKey(), payChannelQueryBO.getMobileNo()));
        buildDcepRequest(subWalletPayChannelQueryReq, payChannelQueryBO, DcepPaymentChannelEnum.PAY_CHANNEL_QUERY.getName());
    }

    @Override
    public SubWalletPayChannelQueryBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        SubWalletPayChannelQueryBO payChannelQueryBO = (SubWalletPayChannelQueryBO) baseDcepHandlerBO;
        if (!StringUtils.equals(abstractDcepRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            payChannelQueryBO.setErrorMsg(abstractDcepRsp.getMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.DCEP_REUQEST_FAIL, new String[]{"PayChannelQuery faild"});
        }
        SubWalletPayChannelQueryRsp channelQueryData = (SubWalletPayChannelQueryRsp) abstractDcepRsp;
        payChannelQueryBO.setPaymentList(BeanConvertUtils.convertList(channelQueryData.getData().getPaymentList(), SubWalletPayChannelQueryBO.PaymentChannel.class));
        return payChannelQueryBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }

    @Override
    public void send(SubWalletPayChannelQueryBO payChannelQueryBO) {
        handlerPayChannelQuery(payChannelQueryBO);
    }
}
