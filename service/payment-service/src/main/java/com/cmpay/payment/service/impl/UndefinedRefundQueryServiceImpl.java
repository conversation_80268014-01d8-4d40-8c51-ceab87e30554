package com.cmpay.payment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.bo.RefundOrderCloneBO;
import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.bo.TradeOrderCloneBO;
import com.cmpay.payment.bo.config.SubOrderInfoExtBO;
import com.cmpay.payment.bo.data.OrderFeeBO;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.entity.OrderPaymentAttachDO;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.CompleteOrderSplitService;
import com.cmpay.payment.service.SubOrderInfosResolveService;
import com.cmpay.payment.service.TradeNotifyService;
import com.cmpay.payment.service.UndefinedRefundQueryService;
import com.cmpay.payment.service.data.IDataFeeService;
import com.cmpay.payment.service.ext.ExtPayOrderAttachService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.ExtRefundOrderService;
import com.cmpay.payment.util.DateUtils;
import com.cmpay.payment.utils.CloneableUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Created on 2018/11/14
 *
 * @author: chen_lan
 */
@Service
public class UndefinedRefundQueryServiceImpl implements UndefinedRefundQueryService {

    private static final Logger logger = LoggerFactory.getLogger(UndefinedRefundQueryServiceImpl.class);

    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private ExtRefundOrderService refundOrderService;
    @Autowired
    private IDataFeeService dataFeeService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ExtPayOrderAttachService payOrderAttachService;
    @Autowired
    private TradeNotifyService tradeNotifyService;

    @Autowired
    private CompleteOrderSplitService splitService;
    @Autowired
    private SubOrderInfosResolveService resolveService;

    /**
     * 查询交易订单
     *
     * @param orderQueryParam
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void undefinedRefundOrderQuery(RefundOrderCloneBO orderQueryParam) {

        logger.info("=====================Undefined Status Order of Refund Do Query Start!!=====================");

        // 保存数据用
        RefundOrderDO refundOrderDO = new RefundOrderDO();
        BeanUtils.copyProperties(orderQueryParam, refundOrderDO);
        // 退款订单查询加锁
        RefundOrderDO refundOrderParam = new RefundOrderDO();
        refundOrderParam.setTradeOrderNo(orderQueryParam.getTradeOrderNo());
        refundOrderService.lock(refundOrderParam);
        logger.info("Refund Order For Update Success,tradeOrderNo is {}", orderQueryParam.getTradeOrderNo());

        // 查询原支付订单信息
        TradeOrderDO originalPayTradeOrderDO = payOrderService.get(orderQueryParam.getOrgOrderNo());
        if (JudgeUtils.isNull(originalPayTradeOrderDO)) {
            logger.info("Org Payment Order of Refund Order Not Exists,tradeOrderNo is {},orgOrderNo is {},msgcd is {}",
                    orderQueryParam.getTradeOrderNo(), orderQueryParam.getOrgOrderNo(), MsgCodeEnum.REFUND_ORG_ORDER_NOT_EXISTS.getMsgCd());
            return;
        }
        // 原支付订单加锁
        payOrderService.lock(originalPayTradeOrderDO);
        RefundOrderQueryBO refundOrderQueryParam = new RefundOrderQueryBO();
        //保存订单记录到参数对像里，以便后面使用
        refundOrderQueryParam.setRefundOrder(CloneableUtils.gsonDeepClone(refundOrderDO, RefundOrderCloneBO.class));
        refundOrderQueryParam.setOriginalPayTradeOrder(CloneableUtils.gsonDeepClone(originalPayTradeOrderDO, TradeOrderCloneBO.class));
        if (DcepConstants.DCEP.equals(originalPayTradeOrderDO.getDcepFlag())
                || PaymentWayEnum.ICBCPAY.name().equalsIgnoreCase(originalPayTradeOrderDO.getPayProductCode())) {
            OrderPaymentAttachDO attachDO = new OrderPaymentAttachDO();
            attachDO.setOrderDate(originalPayTradeOrderDO.getRequestDate());
            attachDO.setOutTradeNo(originalPayTradeOrderDO.getOutTradeNo());
            OrderPaymentAttachDO orderPaymentAttachDO = payOrderAttachService.load(attachDO);
            refundOrderQueryParam.setOrderPaymentAttachDO(orderPaymentAttachDO);
        }
        try {
            // 退款查询请求发送
            refundOrderQueryParam.setSourceApp(AppEnum.integrationshecdule.name());
            applicationContext.publishEvent(refundOrderQueryParam);

            // 退款处理中直接退出
            if (StringUtils.equals(refundOrderQueryParam.getStatus(), OrderStatusEnum.REFUND_WAIT.name())) {
                return;
            }
            if (JudgeUtils.equals(refundOrderQueryParam.getOriginalPayTradeOrder().getAimProductCode(), PaymentWayEnum.CMBAION.name().toLowerCase())) {
                return;
            }

            // 修改退款订单状态
            refundOrderDO.setStatus(refundOrderQueryParam.getStatus());
            refundOrderDO.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
            refundOrderDO.setRefundId(refundOrderQueryParam.getRefundOrder().getRefundId());
            if (JudgeUtils.isNotEmpty(refundOrderQueryParam.getRefAmountList())) {
                // 记录退款金额明细集合
                refundOrderDO.setRefAmountList(JSONObject.toJSONString(refundOrderQueryParam.getRefAmountList()));
            }
            // 仅在退款成功记录账期
            if (JudgeUtils.equals(refundOrderQueryParam.getStatus(), OrderStatusEnum.REFUND_SUCCESS.name())) {
                refundOrderDO.setAccountDate(DateUtils.verifiAndSetAccountDate(refundOrderQueryParam.getRefundOrder().getAccountDate()));
            }
            logger.info("Refund Order Query Result Defined,Refund Status is {}", refundOrderDO.getStatus());
            refundOrderDO.setErrMsgCd(refundOrderQueryParam.getErrMsgCd());
            refundOrderDO.setErrMsgInfo(refundOrderQueryParam.getErrMsgInfo());
            logger.info("Refund Query throw BusinessException,errMsgCd is {},errMsgInfo is {}", refundOrderQueryParam.getErrMsgCd(), refundOrderQueryParam.getErrMsgInfo());
        } catch (BusinessException e) {
            logger.info("Refund Query throw BusinessException,tradeOrderNo is {},msgcd is {}",
                    orderQueryParam.getTradeOrderNo(), e.getMsgCd());
            return;
        }

        // 退款前原支付订单订单状态
        String payOrderStatus = OrderStatusEnum.TRADE_SUCCESS.name();
        orderQueryParam.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());

        // 查询已退款金额
        RefundOrderDO refundOrderLsQuery = new RefundOrderDO();
        refundOrderLsQuery.setOrgOrderNo(orderQueryParam.getOrgOrderNo());
        refundOrderLsQuery.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
        List<RefundOrderDO> refundOrderLs = refundOrderService.find(refundOrderLsQuery);
        BigDecimal totalRefundMoney = BigDecimal.ZERO;
        BigDecimal totalRefundFeeAmount = BigDecimal.ZERO;
        if (refundOrderLs.size() != 0) {
            //计算退款总金额
            totalRefundMoney = refundOrderLs.stream().map(RefundOrderDO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            totalRefundFeeAmount = refundOrderLs.stream().map(RefundOrderDO::getOrderFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            payOrderStatus = OrderStatusEnum.REFUND_PART.name();
        }
        logger.info("Already Refund Total Amount is {},orgTradeNO is {},totalAmount is {}",
                totalRefundMoney, orderQueryParam.getOrgOrderNo(), orderQueryParam.getOrderAmount());

        // 本次退款总金额 =已退款金额+本次退款金额
        BigDecimal totalRefundAmountCurrent = totalRefundMoney.add(orderQueryParam.getOrderAmount());
        if (totalRefundAmountCurrent.compareTo(originalPayTradeOrderDO.getRealAmount()) == 0) {
            if (OrderStatusEnum.REFUND_SUCCESS.name().equals(refundOrderDO.getStatus())) {
                // 若退款金额等于支付订单金额则全额退款
                originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
                originalPayTradeOrderDO.setRefundTimes(originalPayTradeOrderDO.getRefundTimes() + 1);
                originalPayTradeOrderDO.setSuccessRefundAmount(originalPayTradeOrderDO.getSuccessRefundAmount().
                        add(orderQueryParam.getOrderAmount()));
            } else if (OrderStatusEnum.REFUND_FAIL.name().equals(refundOrderDO.getStatus())) {
                // 若退款失败则恢复原支付订单状态
                originalPayTradeOrderDO.setStatus(payOrderStatus);
            }
        } else if (totalRefundAmountCurrent.compareTo(originalPayTradeOrderDO.getRealAmount()) < 0) {
            if (OrderStatusEnum.REFUND_SUCCESS.name().equals(refundOrderDO.getStatus())) {
                // 若退款金额小雨支付订单金额则部分退款
                originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_PART.name());
                originalPayTradeOrderDO.setRefundTimes(originalPayTradeOrderDO.getRefundTimes() + 1);
                originalPayTradeOrderDO.setSuccessRefundAmount(originalPayTradeOrderDO.getSuccessRefundAmount().
                        add(orderQueryParam.getOrderAmount()));
            } else if (OrderStatusEnum.REFUND_FAIL.name().equals(refundOrderDO.getStatus())) {
                // 若退款失败则恢复原支付订单状态
                originalPayTradeOrderDO.setStatus(payOrderStatus);
            }
        } else {
            logger.info("Refund Amount Beyond Payment Amount,refundAmount is {},totalAmount is {},totalRefundAmount is {}",
                    orderQueryParam.getOrderAmount(), originalPayTradeOrderDO.getOrderAmount(), totalRefundAmountCurrent);
            return;
        }
        if (originalPayTradeOrderDO.getOrderFeeAmount().compareTo(BigDecimal.ZERO) == 0
                && JudgeUtils.equals(originalPayTradeOrderDO.getAimProductCode(), PaymentWayEnum.ALIPAY.name().toLowerCase())) {
            BigDecimal refundFeeAmount = reefundAlipayCalculate(originalPayTradeOrderDO, orderQueryParam, refundOrderLs, totalRefundMoney);
            orderQueryParam.setOrderFeeAmount(refundFeeAmount);
            refundOrderDO.setOrderFeeAmount(refundFeeAmount);
        }
        //计算退款手续费
        if (originalPayTradeOrderDO.getOrderFeeAmount().compareTo(BigDecimal.ZERO) > 0
                && !JudgeUtils.equals(originalPayTradeOrderDO.getRefundFeeWay(), RefundFeeWayEnum.NO_REFUND_FEE_WAY.getDesc())) {
            // 如果是分账订单
            if (splitService.checkSplitOrder(refundOrderDO, originalPayTradeOrderDO)) {
                //  计算退款分账服务费
                splitService.splitOrderRefundFee(refundOrderDO, originalPayTradeOrderDO);
            } else {
                OrderFeeBO orderFeeBO = new OrderFeeBO();
                orderFeeBO.setOrderAmount(originalPayTradeOrderDO.getOrderAmount());
                orderFeeBO.setRefundAmount(orderQueryParam.getOrderAmount());
                orderFeeBO.setSuccessRefundFeeAmount(totalRefundFeeAmount);
                orderFeeBO.setSuccessRefundAmount(totalRefundMoney);
                orderFeeBO.setRefundFeeAmount(BigDecimal.ZERO);
                orderFeeBO.setOrderFeeAmount(originalPayTradeOrderDO.getOrderFeeAmount());
                orderFeeBO.setOrderRate(originalPayTradeOrderDO.getOrderRate());
                orderFeeBO = dataFeeService.refundFeeCalculate(orderFeeBO);
                refundOrderDO.setOrderFeeAmount(orderFeeBO.getRefundFeeAmount());
            }
            orderQueryParam.setOrderFeeAmount(refundOrderDO.getOrderFeeAmount());
        }
        logger.info("Payment Order Of Refund Order Result Defined,Payment Order Status is {}",
                originalPayTradeOrderDO.getStatus());
        // 更新退款订单
        int i = refundOrderService.updateRefundStatus(refundOrderDO);
        if (i != 1) {
            return;
        } else if (i == 1 && StringUtils.equals(OrderStatusEnum.REFUND_FAIL.name(), refundOrderDO.getStatus())) {
            // 明确退款失败，且退款请求日期是今天，那么自增回去
            splitService.refundFailIncrement(refundOrderDO, originalPayTradeOrderDO);
        }
        logger.info("Refund Order Update Success,tradeOrderNo is {},orgTradeNO is {}",
                refundOrderDO.getTradeOrderNo(), refundOrderDO.getOrgOrderNo());
        // 更新支付订单
        payOrderService.update(originalPayTradeOrderDO);
        logger.info("Payment Order Update Success,tradeOrderNo is {},outTradeNo is {}",
                originalPayTradeOrderDO.getTradeOrderNo(), originalPayTradeOrderDO.getOutTradeNo());
        orderQueryParam.setStatus(refundOrderDO.getStatus());
        tradeNotifyService.handleRefundNotify(orderQueryParam);
        logger.info("Undefined Status Order of Refund Do Query End!!");
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void asynRefundOrder(RefundOrderCloneBO refundOrderCloneBO) {
        // 保存数据用
        RefundOrderDO refundOrderDO = new RefundOrderDO();
        BeanUtils.copyProperties(refundOrderCloneBO, refundOrderDO);
        // 退款订单查询加锁
        RefundOrderDO refundOrderParam = new RefundOrderDO();
        refundOrderParam.setTradeOrderNo(refundOrderCloneBO.getTradeOrderNo());
        refundOrderService.lock(refundOrderParam);
        // 查询原支付订单信息
        TradeOrderDO originalPayTradeOrderDO = payOrderService.get(refundOrderCloneBO.getOrgOrderNo());
        if (JudgeUtils.isNull(originalPayTradeOrderDO)) {
            logger.info("Org Payment Order of Refund Order Not Exists,tradeOrderNo is {},orgOrderNo is {},msgcd is {}",
                    refundOrderCloneBO.getTradeOrderNo(), refundOrderCloneBO.getOrgOrderNo(), MsgCodeEnum.REFUND_ORG_ORDER_NOT_EXISTS.getMsgCd());
            return;
        }
        OrderPaymentAttachDO paymentAttachDO = new OrderPaymentAttachDO();
        if (DcepConstants.DCEP.equals(originalPayTradeOrderDO.getDcepFlag())
                || PaymentWayEnum.ICBCPAY.name().equalsIgnoreCase(originalPayTradeOrderDO.getPayProductCode())) {
            OrderPaymentAttachDO paymentAttachQuery = new OrderPaymentAttachDO();
            paymentAttachQuery.setOutTradeNo(refundOrderCloneBO.getOrgOrderNo());
            paymentAttachDO = payOrderAttachService.load(paymentAttachQuery);
            if (JudgeUtils.isNull(paymentAttachDO)) {
                logger.info("Org Payment attach Order of Refund Order Not Exists,tradeOrderNo is {},orgOrderNo is {},msgcd is {}",
                        refundOrderCloneBO.getTradeOrderNo(), refundOrderCloneBO.getOrgOrderNo(), MsgCodeEnum.REFUND_ORG_ORDER_NOT_EXISTS.getMsgCd());
                return;
            }
        }
        //查询到退款订单表中存在退款中的退款不继续发往支付机构
        RefundOrderDO refundOrderLsQuery = new RefundOrderDO();
        refundOrderLsQuery.setOrgOrderNo(refundOrderCloneBO.getOrgOrderNo());
        refundOrderLsQuery.setStatus(OrderStatusEnum.REFUND_WAIT.name());
        List<RefundOrderDO> refundOrderLs = refundOrderService.find(refundOrderLsQuery);
        if (refundOrderLs.size() > 0) {
            logger.info("Org Payment Order of AsynRefund Order Exists ,tradeOrderNo is {},orgOrderNo is {},msgcd is {}",
                    refundOrderCloneBO.getTradeOrderNo(), refundOrderCloneBO.getOrgOrderNo(), MsgCodeEnum.ORDER_EXIST_UNFINISHED_REDUND.getMsgCd());
            return;
        }
        RefundBO refundBO = new RefundBO();
        refundBO.setMerchantId(refundOrderCloneBO.getMerchantNo());
        refundBO.setOutRequestNo(refundOrderCloneBO.getOutTradeNo());
        refundBO.setTradeDate(refundOrderCloneBO.getRequestDate());
        refundBO.setOutTradeNo(refundOrderCloneBO.getOrgOrderNo());
        refundBO.setChannelType(ChannelTypeEnum.INTERFACE.name().toLowerCase());
        refundBO.setChannelTypeDesc(ChannelTypeEnum.INTERFACE.getDesc());
        refundBO.setOriginalPayTradeOrder(CloneableUtils.gsonDeepClone(originalPayTradeOrderDO, TradeOrderCloneBO.class));
        refundBO.setRefundOrder(refundOrderDO);
        refundBO.setRefundAmount(refundOrderCloneBO.getOrderAmount());
        if (DcepConstants.DCEP.equals(originalPayTradeOrderDO.getDcepFlag())
                || PaymentWayEnum.ICBCPAY.name().equalsIgnoreCase(originalPayTradeOrderDO.getPayProductCode())) {
            refundBO.setOrderPaymentAttachDO(paymentAttachDO);
        }

        refundBO.setSubMerchant(checkCrossRefund(refundBO));
        try {
            refundBO.setSourceApp(AppEnum.integrationshecdule.name());
            applicationContext.publishEvent(refundBO);
            if (JudgeUtils.equals(refundBO.getStatus(), OrderStatusEnum.REFUND_SUCCESS.name())) {
                // 原支付订单设置状态为REFUND_WAIT
                originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
                payOrderService.update(originalPayTradeOrderDO);
                // 退款订单状态修改为退款中
                refundOrderDO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
                refundOrderService.update(refundOrderDO);
            }
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                // 退款订单状态不修改 后续继续退款
                if (OrderStatusEnum.REFUND_FAIL.name().equals(refundBO.getStatus())) {
                    // 退款订单状态修改为失败
                    refundOrderDO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
                    refundOrderDO.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
                    refundOrderDO.setErrMsgCd(refundBO.getErrMsgCd());
                    refundOrderDO.setErrMsgInfo(refundBO.getErrMsgInfo());
                    if (refundOrderService.update(refundOrderDO) == 1) {
                        // 明确退款失败，且退款请求日期是今天，那么自增回去
                        splitService.refundFailIncrement(refundOrderDO, originalPayTradeOrderDO);
                    }
                    // 下发失败通知
                    refundOrderCloneBO.setStatus(refundOrderDO.getStatus());
                    tradeNotifyService.handleRefundNotify(refundOrderCloneBO);
                }
            } else {
                //如果是未知的系统错误，比如网络请求异常  原支付订单状态不修改
                originalPayTradeOrderDO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
            }
            logger.info(" Order Refund Result Fail,Refund Order is {}", refundBO.getOutRequestNo());
        }
    }

    /**
     * 跨渠道退款，获取子商户
     *
     * @param refundBO
     */
    private String checkCrossRefund(RefundBO refundBO) {
        TradeOrderDO originalPayTradeOrderDO = refundBO.getOriginalPayTradeOrder();
        // 判断原支付订单，分账标识不为Y，但是有分账参数,说明是跨渠道退款
        if (!StringUtils.equals(originalPayTradeOrderDO.getSplitFlag(), Constants.Y) && StringUtils.isNotEmpty(originalPayTradeOrderDO.getSubOrderInfos())) {
            //解析分账参数，尝试获取这笔退款的子商户号
            List<SubOrderInfoExtBO> subOrderInfoExtBOS = resolveService.resolveExt(originalPayTradeOrderDO.getSubOrderInfos());
            for (SubOrderInfoExtBO subOrderInfoExtBO : subOrderInfoExtBOS) {
                if (StringUtils.equals(subOrderInfoExtBO.getSubOrderNo(), refundBO.getOutRequestNo())) {
                    return subOrderInfoExtBO.getSubMerchant();
                }
            }
        }
        return refundBO.getSubMerchant();
    }

    /**
     * 计算历史服务费信息
     */
    private BigDecimal reefundAlipayCalculate(TradeOrderDO originalPayTradeOrderDO, RefundOrderCloneBO orderQueryParam,
                                              List<RefundOrderDO> refundOrderLs, BigDecimal totalRefundMoney) {
        LocalDate date = DateTimeUtils.parseLocalDate(originalPayTradeOrderDO.getOrderDate());
        LocalDate localDate = DateTimeUtils.parseLocalDate(Constants.ALIPAY_REFUND_DATE);
        BigDecimal orderRefundFee = BigDecimal.ZERO;
        if (date.isAfter(localDate)) {
            return orderRefundFee;
        }
        //计算订单总服务费信息
        OrderFeeBO orderPaymentFeeBO = new OrderFeeBO();
        orderPaymentFeeBO.setOrderAmount(originalPayTradeOrderDO.getOrderAmount());
        orderPaymentFeeBO.setOrderRate(Constants.ALIPAY_RATE);
        orderPaymentFeeBO = dataFeeService.feeCalculate(orderPaymentFeeBO);
        BigDecimal orderPaymentFee = orderPaymentFeeBO.getOrderFeeAmount();
        //全额退款
        if (originalPayTradeOrderDO.getOrderAmount().compareTo(orderQueryParam.getOrderAmount()) == 0) {
            return orderPaymentFee;
        }
        BigDecimal totalRefundFeeAmount = BigDecimal.ZERO;
        BigDecimal totalRefundAmount = BigDecimal.ZERO;
        if (totalRefundMoney.compareTo(BigDecimal.ZERO) > 0) {
            for (RefundOrderDO refundFeeDO : refundOrderLs) {
                BigDecimal calculateFee;
                OrderFeeBO calculateFeeBO = new OrderFeeBO();
                calculateFeeBO.setOrderAmount(originalPayTradeOrderDO.getOrderAmount());
                calculateFeeBO.setRefundAmount(refundFeeDO.getOrderAmount());
                calculateFeeBO.setRefundFeeAmount(BigDecimal.ZERO);
                calculateFeeBO.setOrderFeeAmount(orderPaymentFeeBO.getOrderFeeAmount());
                calculateFeeBO.setOrderRate(Constants.ALIPAY_RATE);
                calculateFeeBO.setSuccessRefundFeeAmount(totalRefundFeeAmount);
                calculateFeeBO.setSuccessRefundAmount(totalRefundAmount);
                calculateFeeBO = dataFeeService.refundFeeCalculate(calculateFeeBO);
                calculateFee = calculateFeeBO.getRefundFeeAmount();
                totalRefundFeeAmount = totalRefundFeeAmount.add(calculateFee);
                totalRefundAmount = totalRefundAmount.add(refundFeeDO.getOrderAmount());
            }
        }
        OrderFeeBO refundFeeBO = new OrderFeeBO();
        refundFeeBO.setOrderAmount(originalPayTradeOrderDO.getOrderAmount());
        refundFeeBO.setRefundAmount(orderQueryParam.getOrderAmount());
        refundFeeBO.setRefundFeeAmount(BigDecimal.ZERO);
        refundFeeBO.setOrderFeeAmount(orderPaymentFeeBO.getOrderFeeAmount());
        refundFeeBO.setOrderRate(Constants.ALIPAY_RATE);
        refundFeeBO.setSuccessRefundFeeAmount(totalRefundFeeAmount);
        refundFeeBO.setSuccessRefundAmount(totalRefundMoney);
        refundFeeBO = dataFeeService.refundFeeCalculate(refundFeeBO);
        orderRefundFee = refundFeeBO.getRefundFeeAmount();
        return orderRefundFee;
    }

}
