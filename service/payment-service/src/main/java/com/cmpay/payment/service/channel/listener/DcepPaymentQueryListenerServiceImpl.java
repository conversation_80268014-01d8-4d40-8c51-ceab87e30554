package com.cmpay.payment.service.channel.listener;

import com.cmpay.payment.bo.PaymentQueryBO;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.dcep.DcepPaymentQueryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/5/7
 */
@Service
public class DcepPaymentQueryListenerServiceImpl extends PaymentListenerService<PaymentQueryBO> {

    @Autowired
    private ApplicationContext applicationContext;

    @EventListener
    public void handlerDcepPaymentQuery(PaymentQueryBO paymentQueryBO) {
        super.execute(paymentQueryBO);
    }

    @Override
    protected boolean checkChannelExecutable(PaymentQueryBO paymentQueryBO) {
        return Optional.ofNullable(paymentQueryBO)
                .map(PaymentQueryBO::getPaymentOrder)
                .filter(tradeOrder -> StringUtils.equals(PaymentWayEnum.DCEPPAY.name().toLowerCase(), tradeOrder.getAimProductCode()))
                .isPresent();
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(PaymentQueryBO paymentQueryBO) {
        return Optional.ofNullable(paymentQueryBO)
                .map(PaymentQueryBO::getPaymentOrder)
                .map(TradeOrderDO::getPayWayCode)
                .map(String::toUpperCase)
                .map(PaymentSceneEnum::valueOf)
                .map(paymentScene -> {
                    switch (paymentScene) {
                        case TOKEN:
                        case MMPAY:
                        case JSAPIPAY:
                        case MICROPAY:
                        case SCANPAY:
                        case APP:
                            return getBean(DcepPaymentQueryService.class);
                        default:
                            return null;
                    }
                }).orElse(null);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
