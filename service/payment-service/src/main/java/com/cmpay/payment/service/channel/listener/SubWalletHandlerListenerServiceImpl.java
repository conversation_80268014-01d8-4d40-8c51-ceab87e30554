package com.cmpay.payment.service.channel.listener;

import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.BaseSubWalletHandlerBO;
import com.cmpay.payment.constant.protocol.SubWalletHandlerEnum;
import com.cmpay.payment.service.channel.PaymentChannelService;
import com.cmpay.payment.service.channel.PaymentListenerService;
import com.cmpay.payment.service.channel.dcep.SubWalletAddNewCardService;
import com.cmpay.payment.service.channel.dcep.SubWalletPayChannelQueryService;
import com.cmpay.payment.service.channel.dcep.SubWalletStatusQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/5/5
 */
@Service
public class SubWalletHandlerListenerServiceImpl extends PaymentListenerService<BaseSubWalletHandlerBO> {

    @Autowired
    private ApplicationContext applicationContext;

    @EventListener
    public void handlerSubWalletHandle(BaseSubWalletHandlerBO subWalletHandlerBO) {
        super.execute(subWalletHandlerBO);
    }

    @Override
    protected boolean checkChannelExecutable(BaseSubWalletHandlerBO subWalletHandlerBO) {
        return StringUtils.isNotBlank(subWalletHandlerBO.getHandlerName());
    }

    @Override
    protected PaymentChannelService determinateChannelExecuteBean(BaseSubWalletHandlerBO subWalletHandlerBO) {

        return Optional.ofNullable(subWalletHandlerBO)
                .map(BaseSubWalletHandlerBO::getHandlerName)
                .map(String::toUpperCase)
                .map(SubWalletHandlerEnum::valueOf)
                .map(handlerName -> {
                    switch (handlerName) {
                        case SUB_WALLET_STATUS_QUERY:
                            return getBean(SubWalletStatusQueryService.class);
                        case SUB_WALLET_PAY_CHANNEL_QUERY:
                            return getBean(SubWalletPayChannelQueryService.class);
                        case SUB_WALLET_GET_SCHEME:
                            return getBean(SubWalletAddNewCardService.class);
                        default:
                            return null;
                    }
                }).orElse(null);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
