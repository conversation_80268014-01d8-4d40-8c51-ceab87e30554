package com.cmpay.payment.util;

import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.constant.Constants;
import com.cmpay.payment.constant.common.DateTimePatternConstants;

import java.text.ParseException;
import java.time.LocalDateTime;

/**
 * @author： pengAnHai
 * @date： 2023-12-15
 * @description：日期时间工具类
 */
public class DateUtils {

    private static final int CODE = 30;

    public static final Long ONE_MINUTES = 1L;



    /**
     * 比较传入的请求时间与当前时间的差小于30分钟
     * @param requestDateTime
     * @return
     */
    public static boolean compareTimeLessThanThirty(String requestDateTime) {
        LocalDateTime startDateTime = DateTimeUtils.parseLocalDateTime(requestDateTime);
        LocalDateTime localDateTime = DateTimeUtils.getCurrentLocalDateTime();
        // 比较请求时间与当前时间的分钟差
        Long durationMinutes = DateTimeUtils.durationMinutes(startDateTime, localDateTime);
        return durationMinutes.compareTo(new Long(CODE)) < 0;
    }

    /**
     * 比较传入的请求时间与当前时间的差小于5分钟
     * @param requestDateTime
     * @return
     */
    public static boolean compareTimeLessThanFive(String requestDateTime) {
        LocalDateTime startDateTime = DateTimeUtils.parseLocalDateTime(requestDateTime);
        LocalDateTime localDateTime = DateTimeUtils.getCurrentLocalDateTime();
        // 比较请求时间与当前时间的分钟差
        Long durationMinutes = DateTimeUtils.durationMinutes(startDateTime, localDateTime);
        return durationMinutes.compareTo(new Long(5)) < 0;
    }

    /**
     * 当前时间-1 拼接传入的秒数（yyyyMMddHHmm+ss）
     * @param second
     * @return
     */
    public static String getCurrentTimePutSecond(String second) {
        String currentTimeStr = DateTimeUtils.formatLocalDateTime(
                DateTimeUtils.getCurrentLocalDateTime().minusMinutes(ONE_MINUTES), DateTimePatternConstants.DEFAULT_TIME_FORMATTER);
        return currentTimeStr + second;
    }

    /**
     * 根据传入的账期，设置账期的账期
     * @param accountDate
     * @return
     */
    public static String verifiAndSetAccountDate (String accountDate) {
        if (JudgeUtils.isBlank(accountDate) || !isValidDate(accountDate, DateTimePatternConstants.YEAR_MONTH_DAY)) {
            accountDate = DateTimeUtils.getCurrentDateStr();
        }
        return accountDate;
    }

    /**
     * 校验传入的日期和日期格式是否符合
     * @param date
     * @param parsePatterns
     * @return
     */
    public static boolean isValidDate(String date, String parsePatterns) {
        try {
            org.apache.commons.lang3.time.DateUtils.parseDateStrictly(date, parsePatterns);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 检查当前时间是否在传入的时间范围内
     * @param timeRange HHmmss-HHmmss
     * @return
     */
    public static boolean checkTimeExceedRange(String timeRange) {
        // 分割起始时间和终止时间
        String[] timeRanges = timeRange.split(Constants.STUB_BARS);
        // 获取当前时间，格式为HHmmss，时分秒
        String currentTimeStr = DateTimeUtils.getCurrentTimeStr();
        // 比较当前时间是否在传入的直接范围内
        return currentTimeStr.compareTo(timeRanges[0]) >= 0 && currentTimeStr.compareTo(timeRanges[1]) <= 0;
    }

    /**
     * 将传入的时间范围格式化
     * @param timeRange HHmmss-HHmmss
     * @return HH:mm:ss-HH:mm:ss
     */
    public static String timeRangeFormatter(String timeRange) {
        // 分割起始时间和终止时间
        String[] timeRanges = timeRange.split(Constants.STUB_BARS);
        return timeFormatter(timeRanges[0]) + Constants.STUB_BARS + timeFormatter(timeRanges[1]);
    }

    /**
     * 将传入的时间格式化
     * @param time HHmmss
     * @return HH:mm:ss
     */
    public static String timeFormatter(String time) {
        // 使用StringBuilder重新格式化字符串
        StringBuilder sb = new StringBuilder(time);
        sb.insert(2, Constants.COLON);
        sb.insert(5, Constants.COLON);
        return sb.toString();
    }
    /**
     * 比较给定的时间是否已超时
     *
     * @param timeOut 一个表示时间的字符串
     * @param minute 用于判断超时的分钟数
     * @return 如果给定时间比当前时间早（即已超时），则返回 true；否则返回 false
     */
    public static boolean compareTimeIsTimeOut(String timeOut, long minute) {
        // 获取当前本地时间
        LocalDateTime localDateTime = DateTimeUtils.getCurrentLocalDateTime();

        // 将给定的 timeOut 转换为 LocalDateTime 对象，并加上指定的分钟数
        LocalDateTime timeOutDateTime = DateTimeUtils.parseLocalDateTime(timeOut).plusMinutes(minute);

        // 计算当前时间与给定时间的分钟差
        Long durationMinutes = DateTimeUtils.durationMinutes(localDateTime, timeOutDateTime);

        // 如果分钟差为负数，表示已超时，返回 true；否则返回 false
        return durationMinutes < 0;
    }

    /**
     * 获取当前日期的结束时间字符串
     *
     * @return 当前日期结束时间的字符串表示（格式化为"yyyy-MM-dd 23:59:59"）
     */
    public static String getCurrentDateEndStr() {
        // 获取当前日期字符串，并拼接上到 Constants.DATE_END（通常为 " 23:59:59"）
        return DateTimeUtils.getCurrentDateStr()+ Constants.DATE_END;
    }

}
