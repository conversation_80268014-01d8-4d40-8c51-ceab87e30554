package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.DcepRefundOrderReq;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.DcepRefundService;
import com.cmpay.payment.util.LemonAmount;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/5/7
 */
@Service
public class DcepRefundServiceImpl extends AbstractDcepRequestServiceImpl implements DcepRefundService {

    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;

    @Override
    public void handlerDcepRefund(RefundBO refundBO) {
        refundBO.setAsync(true);
        refundBO.setPayWayCode(refundBO.getRefundOrder().getPayWayCode().toLowerCase());
        refundBO.setBusinessType(DcepPaymentChannelEnum.REFUND_ORDER.getName());
        DcepRefundOrderReq refundOrderReq = new DcepRefundOrderReq();
        refundOrderReq.setOrgNo(dcepPaymentProperties.getOrgNo());
        buildRequestDto(refundOrderReq, refundBO, refundBO.getOriginalPayTradeOrder().getBankMerchantNo());
        refundOrderReq.setSubMerchantNo(refundBO.getOrderPaymentAttachDO().getSubMerchantId());
        refundOrderReq.setOriOutTranNo(refundBO.getOriginalPayTradeOrder().getOutTradeNo());
        refundOrderReq.setOutRefundNo(refundBO.getRefundOrder().getOutTradeNo());
        refundOrderReq.setRefundAmount(Long.parseLong(new LemonAmount(refundBO.getRefundAmount()).yuan2fen()));
        refundOrderReq.setRefundDesc(refundBO.getRefundReason());
        refundOrderReq.setNotifyUrl(dcepPaymentProperties.getRefundNotifyUrl());
        refundOrderReq.setOutTranDate(refundBO.getRefundOrder().getOrderDate());
        refundOrderReq.setOutTranTime(refundBO.getRefundOrder().getOrderTime());
        buildDcepRequest(refundOrderReq, refundBO, refundBO.getBusinessType());
    }

    @Override
    public void send(RefundBO refundBO) {
        handlerDcepRefund(refundBO);
    }

    @Override
    public BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        if (!StringUtils.equals(abstractDcepRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            BusinessException.throwBusinessException(MsgCodeEnum.DCEP_PAY_QUERY_FAIL, new String[]{abstractDcepRsp.getMsg()});
        }
        return baseDcepHandlerBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {
        DcepRefundOrderReq refundOrderReq = (DcepRefundOrderReq) abstractDcepReq;
        refundOrderReq.setOrgNo(dcepPaymentProperties.getNewOrgNo());
        baseDcepHandlerBO.setBusinessType(DcepPaymentChannelEnum.DCEP_REFUND_ORDER.getName());
    }
}
