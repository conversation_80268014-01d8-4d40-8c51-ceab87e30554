package com.cmpay.payment.service.notify.impl;

import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.IntegralPayNotifyBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.notify.IntegralPayNotifyRequestBO;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.TradeNotifyService;
import com.cmpay.payment.service.notify.IIntegralPayNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/14 13:59
 */
@Service
public class IntegralPayNotifyServiceImpl implements IIntegralPayNotifyService {
    @Autowired
    private TradeNotifyService tradeNotifyService;

    @Autowired
    private AsynCommonService asynCommonService;

    @Override
    public void integralPayNotify(IntegralPayNotifyRequestBO integralPayNotifyBO) {
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setBnkTradeNo(integralPayNotifyBO.getOrderId());
        tradeNotifyBO.setBankOrderNo(integralPayNotifyBO.getChannelOrderId());
        tradeNotifyBO.setResult(OrderStatusEnum.SUCCESS.name());
        tradeNotifyBO.setPaymentRout(PaymentWayEnum.INTEGRALPAY.name().toLowerCase());
        tradeNotifyBO.setFinishDate(DateTimeUtils.getCurrentDateTimeStr());
        if (JudgeUtils.isNotNull(integralPayNotifyBO.getReserved())) {
            tradeNotifyBO.setTradeOrderNo(integralPayNotifyBO.getReserved());
        }
        TradeNotifyBO notifyBO = tradeNotifyService.backendNotify(tradeNotifyBO);
        asynCommonService.asyncNotify(notifyBO);
    }
}
