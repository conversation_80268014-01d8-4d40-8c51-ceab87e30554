package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.IntegralPayOrderBO;
import com.cmpay.payment.bo.config.PayServiceBeanBO;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.IntegralPaymentStatusEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentStatusEnum;
import com.cmpay.payment.dao.ITradeOrderExtDao;
import com.cmpay.payment.dao.config.IRateDao;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.IntegralPayOrderService;
import com.cmpay.payment.service.PayOrderExtFunctionService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.config.IExtRateService;
import feign.RetryableException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR> wenzhan
 * @Date 2021-8-31 0031 11:37
 */
@Service
public class IntegralPayOrderServiceImpl implements IntegralPayOrderService {
    private static final Logger logger = LoggerFactory.getLogger(IntegralPayOrderServiceImpl.class);

    @Autowired
    ExtPayOrderService payOrderService;
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    IRateDao rateDao;
    @Autowired
    ITradeOrderExtDao payTradeOrderDao;
    @Autowired
    IExtRateService rateService;
    @Autowired
    private PayOrderExtFunctionService extFunctionService;
    @Override
    public IntegralPayOrderBO pay(IntegralPayOrderBO integralPayOrderBO) {
        //订单输入参数检查
        inputCheck(integralPayOrderBO);
        //订单重复检查
        TradeOrderDO tradeOrderDO = payTradeOrderDao.get(integralPayOrderBO.getOutTradeNo());
        if(JudgeUtils.isNotNull(tradeOrderDO)){
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_ALREADY_EXISTS);
        }
        return payProcess(integralPayOrderBO);
    }

    /**
     * 处理新的积分支付请求
     * @param integralPayOrderBO
     * @return
     */
    private IntegralPayOrderBO payProcess(IntegralPayOrderBO integralPayOrderBO) {
        if(JudgeUtils.isEmpty(integralPayOrderBO.getMobile())){
            BusinessException.throwBusinessException(MsgCodeEnum.MOBILE_NO_CANNOT_BE_EMPTY);
        }
        //订单超时时间检查
        expressTimeCheck(integralPayOrderBO);
        //路由检查
        PayServiceBeanBO rateBO = new PayServiceBeanBO();
        rateBO.setMerchantNo(integralPayOrderBO.getMerchantId());
        rateBO.setPaymentChannl(integralPayOrderBO.getPayWay());
        rateBO.setOrderScene(integralPayOrderBO.getScene());
        rateBO.setOrderAmount(integralPayOrderBO.getAmount());
        rateBO.setOrderDate(DateTimeUtils.getCurrentDateStr());
        rateBO.setProvinceCode(integralPayOrderBO.getProvinceCode());
        rateBO = rateService.findPaymentRout(rateBO);
        if (JudgeUtils.isNull(rateBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ROUT_CANNOT_VALIDITY);
        }
        TradeOrderDO tradeOrderDO = payOrderService.insertByNewTranscation(integralPayOrderBO);
        try {
            applicationContext.publishEvent(integralPayOrderBO);
            tradeOrderDO.setErrMsgCd(integralPayOrderBO.getErrMsgCd());
            tradeOrderDO.setErrMsgInfo(integralPayOrderBO.getErrMsgInfo());
            tradeOrderDO.setBankOrderNo(integralPayOrderBO.getBankOrderNo());
            payOrderService.updateByNewTranscation(tradeOrderDO);
            integralPayOrderBO.setMsgCd(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
        } catch (Exception e){
            if(e instanceof BusinessException){
                if(JudgeUtils.equals(integralPayOrderBO.getStatus(), IntegralPaymentStatusEnum.TRADE_FAIL.name())){
                    tradeOrderDO.setErrMsgCd(integralPayOrderBO.getErrMsgCd());
                    tradeOrderDO.setErrMsgInfo(integralPayOrderBO.getErrMsgInfo());
                    payOrderService.updateByNewTranscation(tradeOrderDO);
                    integralPayOrderBO.setMsgCd(MsgCodeEnum.POINTS_EXCHANGE_FAIL.getMsgCd());
                }else if(JudgeUtils.equals(MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT.getMsgCd(),((BusinessException) e).getMsgCd())){
                    tradeOrderDO.setErrMsgCd(MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT.getMsgCd());
                    tradeOrderDO.setErrMsgInfo(MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT.getMsgInfo());
                    payOrderService.updateByNewTranscation(tradeOrderDO);
                    integralPayOrderBO.setMsgCd(MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT.getMsgCd());
                }else{
                    BusinessException.throwBusinessException(MsgCodeEnum.POINTS_EXCHANGE_FAIL);
                }
            } else if ((e instanceof RetryableException)&&e.getMessage().contains(CommonConstant.READ_TIMED_OUT)) {
                BusinessException.throwBusinessException(MsgCodeEnum.REQUEST_RETURN_TIME_OUT);
            }else{
                BusinessException.throwBusinessException(MsgCodeEnum.POINTS_EXCHANGE_FAIL);
            }
        }
        return integralPayOrderBO;
    }

    @Override
    public IntegralPayOrderBO repay(IntegralPayOrderBO integralPayOrderBO) {
        //订单输入参数检查
        inputCheck(integralPayOrderBO);
        //是否为新的一笔积分单
        TradeOrderDO tradeOrderDO = payTradeOrderDao.get(integralPayOrderBO.getOutTradeNo());
        if(JudgeUtils.isNull(tradeOrderDO)){
            return payProcess(integralPayOrderBO);
        } else {
            return repayProcess(integralPayOrderBO, tradeOrderDO);
        }
    }


    public IntegralPayOrderBO repayProcess(IntegralPayOrderBO integralPayOrderBO, TradeOrderDO tradeOrderDO) {
        //订单检查
        if (!orderCheck(tradeOrderDO,integralPayOrderBO)) {
            integralPayOrderBO.setMsgCd(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
            return integralPayOrderBO;
        }
        try {
            applicationContext.publishEvent(integralPayOrderBO);
            tradeOrderDO.setErrMsgCd(integralPayOrderBO.getErrMsgCd());
            tradeOrderDO.setErrMsgInfo(integralPayOrderBO.getErrMsgInfo());
            tradeOrderDO.setBankOrderNo(integralPayOrderBO.getBankOrderNo());
            payOrderService.updateByNewTranscation(tradeOrderDO);
            integralPayOrderBO.setMsgCd(MsgCodeEnum.FAP_SUCCESSFUL.getMsgCd());
        } catch (BusinessException e){
            if(JudgeUtils.equals(integralPayOrderBO.getStatus(), IntegralPaymentStatusEnum.TRADE_FAIL.name())){
                tradeOrderDO.setErrMsgCd(integralPayOrderBO.getErrMsgCd());
                tradeOrderDO.setErrMsgInfo(integralPayOrderBO.getErrMsgInfo());
                tradeOrderDO.setStatus(PaymentStatusEnum.TRADE_FAIL.getDesc());
                payOrderService.updateByNewTranscation(tradeOrderDO);
                integralPayOrderBO.setMsgCd(MsgCodeEnum.POINTS_EXCHANGE_FAIL.getMsgCd());
            }else if(JudgeUtils.equals(MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT.getMsgCd(), e.getMsgCd())){
                tradeOrderDO.setErrMsgCd(MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT.getMsgCd());
                tradeOrderDO.setErrMsgInfo(MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT.getMsgInfo());
                payOrderService.updateByNewTranscation(tradeOrderDO);
                integralPayOrderBO.setMsgCd(MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT.getMsgCd());
            }else{
                BusinessException.throwBusinessException(MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT);
            }
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgCodeEnum.POINTS_EXCHANGE_TIMEOUT);
        }
        return integralPayOrderBO;
    }

    public void expressTimeCheck(IntegralPayOrderBO integralPayOrderBO){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String currentDateTime = DateTimeUtils.getCurrentDateTimeStr();
        Date curDateTime = null;
        Date timeoutExpress = null;
        try {
            curDateTime = sdf.parse(currentDateTime);
            timeoutExpress = sdf.parse(integralPayOrderBO.getTimeoutExpress());
        }catch (Exception e){
            logger.error("expressTimeCheck exception : {}",e);
        }
        if(curDateTime.compareTo(timeoutExpress)>0){
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_OVERDUE);
        }
    }

    public void inputCheck(IntegralPayOrderBO integralPayOrderBO){
        if(JudgeUtils.isEmpty(integralPayOrderBO.getMerchantId())){
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NO_CANNOT_BE_EMPTY);
        }
        if(StringUtils.isNotEmpty(integralPayOrderBO.getSubMerchant())){
            if(JudgeUtils.isBlankAny(integralPayOrderBO.getSettlementDept(), integralPayOrderBO.getSettlementItem(), integralPayOrderBO.getMerchantChannelType())){
                BusinessException.throwBusinessException(MsgCodeEnum.SPLIT_INFO_IS_NULL);
            }
            extFunctionService.subMerchantCheck(integralPayOrderBO.getSubMerchant());
        }
    }

    public Boolean orderCheck(TradeOrderDO tradeOrderDO,IntegralPayOrderBO integralPayOrderBO) {
        if(JudgeUtils.isNull(tradeOrderDO)){
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_NO_NOT_EXISTS);
        }
        //手机号检查
        if (JudgeUtils.isNotNull(tradeOrderDO.getMobileNumber())) {
            integralPayOrderBO.setMobile(tradeOrderDO.getMobileNumber());
        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.MOBILE_NO_CANNOT_BE_EMPTY);
        }
        //订单状态检查
        if (!JudgeUtils.equalsIgnoreCase(tradeOrderDO.getStatus(),IntegralPaymentStatusEnum.WAIT_PAY.getDesc())) {
            logger.info("订单状态不允许重发:{}",tradeOrderDO.getStatus());
            integralPayOrderBO.setStatus(tradeOrderDO.getStatus());
            if (JudgeUtils.equalsIgnoreCase(tradeOrderDO.getStatus(),IntegralPaymentStatusEnum.TRADE_SUCCESS.getDesc())) {
                integralPayOrderBO.setPayWay(tradeOrderDO.getPayWayCode());
                integralPayOrderBO.setAccountDate(tradeOrderDO.getAccountDate());
                integralPayOrderBO.setFinishDateTime(tradeOrderDO.getOrderCompleteTime());
                integralPayOrderBO.setTradeOrderNo(tradeOrderDO.getTradeOrderNo());
                integralPayOrderBO.setBankOrderNo(tradeOrderDO.getBankOrderNo());
            }
            return false;
        }
        //第三方订单号检查
        if (JudgeUtils.isNotNull(tradeOrderDO.getBankOrderNo())) {
            logger.info("当前订单不允许重发:{}",tradeOrderDO.getOutTradeNo());
            return false;
        }
        return true;
    }
}
