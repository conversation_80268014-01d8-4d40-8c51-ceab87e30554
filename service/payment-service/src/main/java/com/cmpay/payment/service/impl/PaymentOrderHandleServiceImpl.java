package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.config.PayServiceBeanBO;
import com.cmpay.payment.bo.data.OrderFeeBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.TradeTypeEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.CompleteOrderSplitService;
import com.cmpay.payment.service.PaymentOrderHandleService;
import com.cmpay.payment.service.data.IDataFeeService;
import com.cmpay.payment.service.ext.config.IExtRateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author： PengAnHai
 * @date： 2024-08-21
 * @description：支付订单处理类
 * @modifiedBy：
 * @version: 1.0
 */
@Service
public class PaymentOrderHandleServiceImpl implements PaymentOrderHandleService {

    @Autowired
    private IExtRateService rateService;
    @Autowired
    private IDataFeeService dataFeeService;
    @Autowired
    private CompleteOrderSplitService splitService;
    @Autowired
    private AsynCommonService asynCommonService;

    /**
     * 配置订单的费率信息
     *
     * @param tradeOrder
     * @return
     */
    @Override
    public TradeOrderDO setPayOrderRate(TradeOrderDO tradeOrder) {
        // 创建并初始化费率服务请求对象
        PayServiceBeanBO rateBO = new PayServiceBeanBO();
        rateBO.setMerchantNo(tradeOrder.getMerchantNo());
        rateBO.setPaymentChannl(tradeOrder.getPayProductCode());
        rateBO.setOrderScene(tradeOrder.getPayWayCode());
        rateBO.setOrderAmount(tradeOrder.getRealAmount());
        rateBO.setProvinceCode(tradeOrder.getProvinceCode());
        rateBO.setOrderDate(DateTimeUtils.getCurrentDateStr());
        // 查找支付路由信息
        rateBO = rateService.findPaymentRout(rateBO);
        // 如果支付路由信息为空，抛出业务异常
        if (JudgeUtils.isNull(rateBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ROUT_CANNOT_VALIDITY);
        }
        // 设置交易订单的费率和业务类型
        tradeOrder.setOrderRate(rateBO.getRate());
        tradeOrder.setBusinessType(rateBO.getBusinessType());
        // 计算服务费
        OrderFeeBO orderFeeBO = new OrderFeeBO();
        orderFeeBO.setOrderAmount(tradeOrder.getRealAmount());
        orderFeeBO.setOrderRate(tradeOrder.getOrderRate());
        orderFeeBO.setMinFeeLimit(rateBO.getMinFeeLimit());
        orderFeeBO.setMaxFeeLimit(rateBO.getMaxFeeLimit());
        // 调用费用计算服务
        orderFeeBO = dataFeeService.feeCalculate(orderFeeBO);
        orderFeeBO.setMaxFeeLimit(rateBO.getMaxFeeLimit());
        orderFeeBO.setMinFeeLimit(rateBO.getMinFeeLimit());
        // 将计算后的服务费设置到交易订单中
        tradeOrder.setOrderFeeAmount(orderFeeBO.getOrderFeeAmount());
        // 分账订单计算服务费（子订单向上取整后求和）
        splitService.splitOrderFeeCalculate(tradeOrder);
        // 返回更新后的交易订单对象
        return tradeOrder;
    }

    @Override
    public boolean notifyMerchant(TradeOrderDO tradeOrder) {
        // 创建并初始化交易通知对象
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setMerchantNo(tradeOrder.getMerchantNo());
        tradeNotifyBO.setTradeOrderNo(tradeOrder.getTradeOrderNo());
        tradeNotifyBO.setOutOrderNo(tradeOrder.getOutTradeNo());
        tradeNotifyBO.setTradeDate(tradeOrder.getRequestDate());
        tradeNotifyBO.setTradeAmount(tradeOrder.getOrderAmount());
        tradeNotifyBO.setDiscountableAmount(tradeOrder.getDiscountableAmount());
        tradeNotifyBO.setNotifyType(TradeTypeEnum.PAYMENT.name().toLowerCase());
        tradeNotifyBO.setNotifyUrl(tradeOrder.getNotifyUrl());
        tradeNotifyBO.setExtra(tradeOrder.getRemark());
        tradeNotifyBO.setSecretIndex(tradeOrder.getSecretIndex());
        tradeNotifyBO.setFinishDate(DateTimeUtils.getCurrentDateTimeStr());
        // 发送支付成功通知服务
        asynCommonService.asyncNotify(tradeNotifyBO);
        // 返回通知结果
        return true;
    }


}
