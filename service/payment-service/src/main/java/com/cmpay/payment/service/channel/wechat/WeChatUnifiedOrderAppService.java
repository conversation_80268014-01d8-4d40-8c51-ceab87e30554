package com.cmpay.payment.service.channel.wechat;

import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.service.channel.PaymentChannelService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
public interface WeChatUnifiedOrderAppService extends PaymentChannelService<PayOrderBO> {
    /**
     * 微信APP支付统一下单
     *
     * @param payOrderBO
     */
    void unifiedOrderAPP(PayOrderBO payOrderBO);
}
