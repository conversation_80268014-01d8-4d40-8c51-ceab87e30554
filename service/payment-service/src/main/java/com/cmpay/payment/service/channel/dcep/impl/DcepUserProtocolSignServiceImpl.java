package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.protocol.ProtocolSignBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.constant.protocol.DcepUserProtocolStatusEnum;
import com.cmpay.payment.constant.protocol.ProtocolStatusEnum;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.DcepUserProtocolConfirmReq;
import com.cmpay.payment.dto.dcep.DcepUserProtocolConfirmRsp;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.DcepUserProtocolSignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created on 2020/5/12
 *
 * @author: huang_yh1
 */
@Service
public class DcepUserProtocolSignServiceImpl extends AbstractDcepRequestServiceImpl implements DcepUserProtocolSignService {
    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;

    private static final String SIGN_TYPE = "sign";

    @Override
    public void protocolSign(ProtocolSignBO protocolSignBO) {

        DcepUserProtocolConfirmReq userProtocolConfirmReq = new DcepUserProtocolConfirmReq();
        userProtocolConfirmReq.setChannelNo(dcepPaymentProperties.getNewChannelNo());
        userProtocolConfirmReq.setSubMerchantNo(protocolSignBO.getSubMerchantId());
        userProtocolConfirmReq.setSignType(DcepConstants.SIGN_TYPE);
        userProtocolConfirmReq.setVersion(DcepConstants.VERSION);
        userProtocolConfirmReq.setSmxKey(dcepPaymentProperties.getNewAesKey());
        userProtocolConfirmReq.setOrgNo(dcepPaymentProperties.getNewOrgNo());
        userProtocolConfirmReq.setAgreementSeq(protocolSignBO.getAgreementId());
        userProtocolConfirmReq.setAuthCode(protocolSignBO.getVerifyCode());
        userProtocolConfirmReq.setSmsSerial(protocolSignBO.getSerialNo());
        buildDcepRequest(userProtocolConfirmReq, protocolSignBO, DcepPaymentChannelEnum.USER_PROTOCOL_SIGN_CONFIRM.getName());
    }

    @Override
    public void send(ProtocolSignBO protocolSignBO) {
        protocolSign(protocolSignBO);
    }

    @Override
    protected BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        DcepUserProtocolConfirmRsp userProtocolConfirmRsp = (DcepUserProtocolConfirmRsp) abstractDcepRsp;
        ProtocolSignBO protocolSignBO = (ProtocolSignBO) baseDcepHandlerBO;
        protocolSignBO.setSignReturnCode(abstractDcepRsp.getCode());
        protocolSignBO.setSignReturnMsg(abstractDcepRsp.getMsg());
        if (JudgeUtils.equals(DcepConstants.SUCCESS_CODE, abstractDcepRsp.getCode())) {
            DcepUserProtocolConfirmRsp.Data data = userProtocolConfirmRsp.getData();
            DcepUserProtocolStatusEnum enums = DcepUserProtocolStatusEnum.getMsgCodeEnum(data.getStatus(), SIGN_TYPE);
            protocolSignBO.setAgreementStatus(enums == null ? ProtocolStatusEnum.CONTRACT_FAIL.getDesc() : enums.name());
        } else {
            protocolSignBO.setAgreementStatus(ProtocolStatusEnum.CONTRACT_FAIL.getDesc());
        }
        return protocolSignBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }
}
