package com.cmpay.payment.service.channel.aplipay.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.dto.alipay.BaseRsp;
import com.cmpay.payment.entity.AlipayPaymentJrnDO;
import com.cmpay.payment.service.channel.aplipay.AlipayPayChannelService;
import com.cmpay.payment.service.ext.ExtPayOrderService;

import java.util.Optional;

/**
 * Created on 2018/12/13
 *
 * @author: wulinfeng
 */
public abstract class AlipayBasePaymentChannelServiceImpl implements AlipayPayChannelService {

    protected ExtPayOrderService extPayOrderService;


    @Override
    public void alipayBasePayment(PayOrderBO payOrderBO) {
        //查询是否存在一条
        AlipayPaymentJrnDO entity=new AlipayPaymentJrnDO();
        entity.setTradeOrderNo(payOrderBO.getTradeOrderNo());

        if(!getStatus(payOrderBO)) {

            Optional.ofNullable(payOrderBO.getAlipayPayRsp())
                    .ifPresent(rsp->{
                        AlipayPaymentJrnDO alipayPaymentJrnDO = new AlipayPaymentJrnDO();
                        alipayPaymentJrnDO.setReturnMsg(rsp.getMsgCode());
                        alipayPaymentJrnDO.setReturnMsgInfo(rsp.getMsgInfo());
                        if(JudgeUtils.isNotNull(rsp.getResult())){
                            payOrderBO.setErrMsgCd(((BaseRsp)rsp.getResult()).getSubCode());
                            payOrderBO.setErrMsgInfo(((BaseRsp)rsp.getResult()).getSubMsg());
                        }
                        alipayPaymentJrnDO.setTradeOrderNo(payOrderBO.getTradeOrderNo());
                        if(JudgeUtils.isNotBlank(payOrderBO.getErrMsgCd())){
                            BusinessException.throwBusinessException(MsgCodeEnum.ALIPAY_ORDER_PAY_FAIL.getMsgCd());
                        }
                    });

        }

    }





    public abstract boolean getStatus(PayOrderBO payOrderBO);

    @Override
    public abstract void send(PayOrderBO payOrderBO);

}
