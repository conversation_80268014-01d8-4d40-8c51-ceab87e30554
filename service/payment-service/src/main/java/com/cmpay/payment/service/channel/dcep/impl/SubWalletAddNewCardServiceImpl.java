package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.SubWalletAddNewCardBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.dto.dcep.*;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.SubWalletAddNewCardService;
import com.cmpay.payment.util.AES256Util;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @date 2022-11-30 10:12
 * <AUTHOR>
 * @Version 1.0
 */
@Service
public class SubWalletAddNewCardServiceImpl  extends AbstractDcepRequestServiceImpl implements SubWalletAddNewCardService {
    private static final Logger logger = LoggerFactory.getLogger(SubWalletAddNewCardServiceImpl.class);
    public static final String HEBAOEXT = "&hebaoext=";
    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;

    @Override
    public void send(SubWalletAddNewCardBO walletAddNewCardBO) {
        handlerAddNewCard(walletAddNewCardBO);
    }

    @Override
    public void handlerAddNewCard(SubWalletAddNewCardBO walletAddNewCardBO) {
        walletAddNewCardBO.setPayWayCode(PaymentSceneEnum.TOKEN.name().toLowerCase());
        SubWalletGetSchemeReq subWalletGetSchemeReq = new SubWalletGetSchemeReq();
        buildRequestDto(subWalletGetSchemeReq, walletAddNewCardBO, walletAddNewCardBO.getChannelNo());
        subWalletGetSchemeReq.setOrgNo(dcepPaymentProperties.getOrgNo());
//        subWalletGetSchemeReq.setSubMerchantNo("********");
        subWalletGetSchemeReq.setSubMerchantNo(walletAddNewCardBO.getSubMerchantId());
        subWalletGetSchemeReq.setMobile(AES256Util.encode(subWalletGetSchemeReq.getSmxKey(), walletAddNewCardBO.getMobileNo()));
        subWalletGetSchemeReq.setOsType(walletAddNewCardBO.getOsType());

        String backUrl = dcepPaymentProperties.getWakeCmpayUrl() + HEBAOEXT + walletAddNewCardBO.getWakeParams();
        subWalletGetSchemeReq.setBackUrl(backUrl);
        logger.info("SubWalletGetSchemeReq : "+ subWalletGetSchemeReq.toString());
        buildDcepRequest(subWalletGetSchemeReq, walletAddNewCardBO, DcepPaymentChannelEnum.SUB_WALLET_GET_SCHEME.getName());
    }

    @Override
    protected BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        SubWalletAddNewCardBO walletAddNewCardBO = (SubWalletAddNewCardBO) baseDcepHandlerBO;
        if (!StringUtils.equals(abstractDcepRsp.getCode(), DcepConstants.SUCCESS_CODE)) {
            walletAddNewCardBO.setErrorMsg(abstractDcepRsp.getMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.DCEP_REUQEST_FAIL, new String[]{"SubWalletAddNewCard token pay faild"});
        }
        SubWalletGetSchemeRsp getSchemeRsp = (SubWalletGetSchemeRsp) abstractDcepRsp;
        walletAddNewCardBO.setSchemeUrl(getSchemeRsp.getData().getSchemeUrl());
        return walletAddNewCardBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }
}
