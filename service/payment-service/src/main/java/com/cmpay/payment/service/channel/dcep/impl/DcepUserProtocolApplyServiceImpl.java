package com.cmpay.payment.service.channel.dcep.impl;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.BaseHandlerBO;
import com.cmpay.payment.bo.protocol.ProtocolApplyBO;
import com.cmpay.payment.channel.DcepPaymentChannelEnum;
import com.cmpay.payment.constant.protocol.DcepConstants;
import com.cmpay.payment.constant.protocol.ProtocolStatusEnum;
import com.cmpay.payment.dto.dcep.AbstractDcepReq;
import com.cmpay.payment.dto.dcep.AbstractDcepRsp;
import com.cmpay.payment.dto.dcep.DcepUserProtocolApplyReq;
import com.cmpay.payment.dto.dcep.DcepUserProtocolApplyRsp;
import com.cmpay.payment.properties.DcepPaymentProperties;
import com.cmpay.payment.service.channel.dcep.DcepUserProtocolApplyService;
import com.cmpay.payment.util.AES256Util;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created on 2020/5/12
 *
 * @author: huang_yh1
 */
@Service
public class DcepUserProtocolApplyServiceImpl extends AbstractDcepRequestServiceImpl implements DcepUserProtocolApplyService {
    @Autowired
    private DcepPaymentProperties dcepPaymentProperties;

    @Override
    public void protocolApply(ProtocolApplyBO protocolApplyBO) {
        DcepUserProtocolApplyReq userProtocolApplyReq = new DcepUserProtocolApplyReq();
        userProtocolApplyReq.setChannelNo(dcepPaymentProperties.getNewChannelNo());
        userProtocolApplyReq.setSignType(DcepConstants.SIGN_TYPE);
        userProtocolApplyReq.setVersion(DcepConstants.VERSION);
        userProtocolApplyReq.setSign(dcepPaymentProperties.getNewAesKey());
        userProtocolApplyReq.setOrgNo(dcepPaymentProperties.getNewOrgNo());
        userProtocolApplyReq.setSubMerchantNo(protocolApplyBO.getSubMerchantId());
        userProtocolApplyReq.setMobile(AES256Util.encode(dcepPaymentProperties.getNewAesKey(), protocolApplyBO.getMobileNo()));
        userProtocolApplyReq.setCertId(AES256Util.encode(dcepPaymentProperties.getNewAesKey(), protocolApplyBO.getIdNo()));
        userProtocolApplyReq.setCertType(protocolApplyBO.getIdType());
        userProtocolApplyReq.setUserName(AES256Util.encode(dcepPaymentProperties.getNewAesKey(), protocolApplyBO.getUserName()));
        userProtocolApplyReq.setSmxKey(dcepPaymentProperties.getNewAesKey());
        buildDcepRequest(userProtocolApplyReq, protocolApplyBO, DcepPaymentChannelEnum.USER_PROTOCOL_SIGN_APPLY.getName());
    }

    @Override
    public void send(ProtocolApplyBO protocolApplyBO) {
        protocolApply(protocolApplyBO);
    }

    @Override
    protected BaseHandlerBO handlerDcepResponse(AbstractDcepRsp abstractDcepRsp, BaseHandlerBO baseDcepHandlerBO) {
        DcepUserProtocolApplyRsp userProtocolApplyRsp = (DcepUserProtocolApplyRsp) abstractDcepRsp;
        ProtocolApplyBO protocolApplyBO = (ProtocolApplyBO) baseDcepHandlerBO;
        protocolApplyBO.setApplyReturnCode(userProtocolApplyRsp.getCode());
        protocolApplyBO.setApplyReturnMsg(userProtocolApplyRsp.getMsg());
        protocolApplyBO.setErrCodeDes(userProtocolApplyRsp.getMsg());
        if (JudgeUtils.equals(DcepConstants.SUCCESS_CODE, userProtocolApplyRsp.getCode())) {
            DcepUserProtocolApplyRsp.Data data = userProtocolApplyRsp.getData();
            protocolApplyBO.setSerialNo(data.getSmsSerial());
            protocolApplyBO.setAgreementId(data.getAgreementSeq());
            protocolApplyBO.setSignStatus(ProtocolStatusEnum.CONTRACT_APPLY.getDesc());
        } else {
            protocolApplyBO.setSignStatus(ProtocolStatusEnum.CONTRACT_FAIL.getDesc());
        }
        return protocolApplyBO;
    }

    @Override
    protected void specificBuildRequestReq(AbstractDcepReq abstractDcepReq, BaseHandlerBO baseDcepHandlerBO) {

    }
}
