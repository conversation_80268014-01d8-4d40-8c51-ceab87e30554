package com.cmpay.payment.service.contract;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.payment.constant.MsgCodeEnum;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public abstract class ContractPaymentListenerService<EVENT> {
    /**
     * 检查该渠道是否能执行 返回true表示要该渠道执行
     *
     * @param event
     * @return
     */
    protected abstract boolean checkChannelExecutable(EVENT event);

    /**
     * 返回该渠道的处理接口
     *
     * @param event
     * @return
     */
    protected abstract <BEANTYPE extends ContractPaymentChannelService> BEANTYPE determinateChannelExecuteBean(EVENT event);

    /**
     * 返回spring  ApplicationContext
     *
     * @return
     */
    protected abstract ApplicationContext getApplicationContext();

    public <BEANTYPE extends ContractPaymentChannelService> void execute(EVENT eventBO) {
        if (!checkChannelExecutable(eventBO)) {
            return;
        }
        BEANTYPE bean = determinateChannelExecuteBean(eventBO);
        if (bean == null) {
            BusinessException.throwBusinessException(MsgCodeEnum.INTERFACE_NOT_EXISTS);
        }
        //触发对应接口发起退款
        bean.send(eventBO);
        afterSend(eventBO);
    }

    protected <BEANTYPE extends ContractPaymentChannelService> BEANTYPE getBean(Class<BEANTYPE> beanType) {
        return getApplicationContext().getBean(beanType);
    }

    public void afterSend(EVENT eventBO) {

    }
}
