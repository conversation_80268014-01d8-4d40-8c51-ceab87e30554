package com.cmpay.payment.service.contract.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.withhold.ContractQueryBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.CmpayConstants;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.dto.cmpay.CmpayOnlineContractQueryReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayOnlineContractQueryRspDTO;
import com.cmpay.payment.entity.config.ContractDO;
import com.cmpay.payment.service.contract.channel.cmpay.CmpayContractQueryService;
import com.cmpay.payment.service.ext.config.IExtContractService;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

/**
 * @date 2022-08-09 10:38
 * <AUTHOR>
 * @Version 1.0
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmpayContractQueryServiceImpl implements CmpayContractQueryService {
    private static final Logger logger = LoggerFactory.getLogger(CmpayContractQueryServiceImpl.class);

    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;
    @Autowired
    private IExtContractService contractService;

    @Override
    public void send(ContractQueryBO contractQueryBO) {
        cmpayContractQuery(contractQueryBO);
    }

    /**
     * 和包免密签约关系查询
     * 签约查询、解约查询共用
     * @param contractQueryBO
     */
    @Override
    public void cmpayContractQuery(ContractQueryBO contractQueryBO) {

        ContractDO contract = contractService.getContract(contractQueryBO.getBankMerchantNo());
        CmpayOnlineContractQueryReqDTO cmpayContractQueryReqDTO = new CmpayOnlineContractQueryReqDTO();
        cmpayContractQueryReqDTO.setCharacterSet(CmpayConstants.CHARACTER_SET2);
        cmpayContractQueryReqDTO.setMerchantId(contractQueryBO.getBankMerchantNo());
        //签约查询是判断改手机号在此商户下有没有签约
        cmpayContractQueryReqDTO.setRequestId(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.CMPAY_JRN_NO, 18));
        cmpayContractQueryReqDTO.setType(CmpayPayChannelEnum.CMPAY_ONLINE_CONTRACT_QUERY_TYPE.getName());
        cmpayContractQueryReqDTO.setVersion(CmpayConstants.CMPAY_CONTRACT_QUERY_VERSION);
        cmpayContractQueryReqDTO.setHmac(contract.getSecretKey());
        cmpayContractQueryReqDTO.setSignType(contract.getSignMethod());
        cmpayContractQueryReqDTO.setMerchantCert(contract.getPublicKey());
        cmpayContractQueryReqDTO.setMobileNo(contractQueryBO.getMobileNo());
        logger.info("cmpayContractQueryReqDTO: " + cmpayContractQueryReqDTO.toString());
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(CmpayPayChannelEnum.CMPAY_ONLINE);
        request.setRoute(CmpayPayChannelEnum.CMPAY_ONLINE);
        request.setBusiType(CmpayPayChannelEnum.CMPAY_ONLINE_CONTRACT_QUERY.getName());
        request.setTarget(cmpayContractQueryReqDTO);

        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .map(result -> {
                    handleResult(contractQueryBO,(CmpayOnlineContractQueryRspDTO) result);
                    return result;
                }).orElseThrow(() ->new BusinessException(MsgCodeEnum.CONTRACT_QUERY_FAILURE));
    }

    private void handleResult(ContractQueryBO contractQueryBO, CmpayOnlineContractQueryRspDTO response) {
//        000000-支付成功。其他信息提示码，提示各类相关失败信息,并且合同时开通状态
        if (StringUtils.equals(CmpayConstants.RETURN_CODE, response.getReturnCode())) {
//            判断状态  1：开通    2：关闭
            if (JudgeUtils.equals(response.getPswdParFlg(), CmpayConstants.CMPAY_CONTRACT_OPEN) &&
                    JudgeUtils.equals(contractQueryBO.getContractQuerySign(),CmpayConstants.CONTRACT_SIGN_OPEN)) {
//                签约查询，只有明确告诉你成功了，才改为成功
                contractQueryBO.setContractStatus(ContractStatusEnum.CONTRACT_SUCCESS.name());
                contractQueryBO.setContractSignedTime(DateTimeUtils.getCurrentDateTimeStr());
            } else if (JudgeUtils.equals(response.getPswdParFlg(), CmpayConstants.CMPAY_CONTRACT_CLOSE) &&
                    JudgeUtils.equals(contractQueryBO.getContractQuerySign(),CmpayConstants.CONTRACT_SIGN_OPEN)) {
            } else if (JudgeUtils.equals(response.getPswdParFlg(), CmpayConstants.CMPAY_CONTRACT_CLOSE) &&
                    JudgeUtils.equals(contractQueryBO.getContractQuerySign(),CmpayConstants.CONTRACT_SIGN_CLOSE)) {
//                解约查询，只有明确解约，才改为解约
                contractQueryBO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
                contractQueryBO.setContractTerminatedTime(DateTimeUtils.getCurrentDateTimeStr());
            }else if (JudgeUtils.equals(response.getPswdParFlg(), CmpayConstants.CMPAY_CONTRACT_OPEN) &&
                    JudgeUtils.equals(contractQueryBO.getContractQuerySign(),CmpayConstants.CONTRACT_SIGN_CLOSE)) {
            } else{
                contractQueryBO.setContractStatus(ContractStatusEnum.CONTRACT_WAIT.name());
            }
        } else {
                BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_QUERY_FAILURE);
        }
    }
}
