package com.cmpay.payment.service.contract.channel.alipay;

import com.cmpay.payment.bo.withhold.ContractApplyWithholdBO;
import com.cmpay.payment.service.contract.ContractChannelService;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface AlipayContractApplyService extends ContractChannelService<ContractApplyWithholdBO> {
    /**
     * 支付宝签约申请扣款
     *
     * @param contractApplyWithholdBO
     */
    void contractApply(ContractApplyWithholdBO contractApplyWithholdBO);
}

