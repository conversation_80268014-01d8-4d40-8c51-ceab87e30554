package com.cmpay.payment.service.contract.listener;

import com.cmpay.payment.bo.withhold.ContractWithholdQueryBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.config.ContractDO;
import com.cmpay.payment.service.contract.ContractChannelService;
import com.cmpay.payment.service.contract.ContractListenerService;
import com.cmpay.payment.service.contract.channel.wechat.WeChatContractWithholdQueryService;
import com.cmpay.payment.service.ext.config.IExtContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatContractWithholdQueryListener extends ContractListenerService<ContractWithholdQueryBO> {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    private IExtContractService contractService;

    /**
     * 微信代扣签约查询订单
     *
     * @param contractWithholdQueryBO
     */
    @EventListener
    public void handleWeChatOrderQuery(ContractWithholdQueryBO contractWithholdQueryBO) {
        execute(contractWithholdQueryBO);
    }

    @Override
    protected boolean checkChannelExecutable(ContractWithholdQueryBO contractWithholdQueryBO) {
        return Optional.ofNullable(contractWithholdQueryBO)
                .map(ContractWithholdQueryBO::getContractWay)
                .filter(contractWay -> StringUtils.equals(PaymentWayEnum.WECHAT.name().toLowerCase(), contractWay))
                .isPresent();
    }

    @Override
    protected ContractChannelService determinateChannelExecuteBean(ContractWithholdQueryBO contractWithholdQueryBO) {
        ContractDO contract = contractService.getContract(contractWithholdQueryBO.getBankMerchantNo());
        contractWithholdQueryBO.setContractSecureValue(contract.getSecretKey());
        return getBean(WeChatContractWithholdQueryService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
