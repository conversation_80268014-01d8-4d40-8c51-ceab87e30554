package com.cmpay.payment.service.contract.channel.cmpay;

import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.service.contract.ContractChannelService;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface CmpayContractService extends ContractChannelService<ContractWithholdBO> {
    /**
     * 和包签约
     * @param contractWithholdBO
     */
    void contractWap(ContractWithholdBO contractWithholdBO);
}

