package com.cmpay.payment.service.contract.channel.cmpay;

import com.cmpay.payment.bo.withhold.ContractApplyWithholdBO;
import com.cmpay.payment.service.contract.ContractChannelService;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface CmpayContractApplyService extends ContractChannelService<ContractApplyWithholdBO> {
    /**
     * 和包签约申请扣款
     *
     * @param contractApplyWithholdBO
     */
    void contractApply(ContractApplyWithholdBO contractApplyWithholdBO);
}

