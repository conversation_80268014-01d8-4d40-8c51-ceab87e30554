package com.cmpay.payment.service.contract.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.dto.wechat.WxPayEntrustWebRequest;
import com.cmpay.payment.dto.wechat.WxPayEntrustWebResponse;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.contract.channel.wechat.WeChatJsapiContractService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.AlipaySDKOrderInfoUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatJsapiContractServiceImpl implements WeChatJsapiContractService {
    private static final Logger logger = LoggerFactory.getLogger(WeChatJsapiContractServiceImpl.class);
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    ExtParamInfoService paramInfoService;
    private static final String CONTRACT_URL = "https://api.mch.weixin.qq.com/papay/entrustweb?";

    @Override
    public void contractJsapi(ContractWithholdBO contractWithholdBO) {
        WxPayEntrustWebRequest wxPayJsapiRequest = new WxPayEntrustWebRequest();
        wxPayJsapiRequest.setAppid(contractWithholdBO.getAppId());
        wxPayJsapiRequest.setPlanId(contractWithholdBO.getPlanId());
        wxPayJsapiRequest.setContractCode(contractWithholdBO.getContractCode());
        wxPayJsapiRequest.setContractDisplayAccount(contractWithholdBO.getContractDisplayAccount());
        wxPayJsapiRequest.setVersion(ContractConstants.WX_CONTRACT_VERSION);

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                wxPayJsapiRequest.setNotifyUrl(UrlUtils.replaceDomainOrIp(weChatProperties.getContractNofityUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                wxPayJsapiRequest.setNotifyUrl(weChatProperties.getContractNofityUrl());
            }
        } else {
            wxPayJsapiRequest.setNotifyUrl(weChatProperties.getContractNofityUrl());
        }

        wxPayJsapiRequest.setMchId(contractWithholdBO.getBankMerchantNo());
        wxPayJsapiRequest.setKey(contractWithholdBO.getHmac());
        if (JudgeUtils.isNotNull(contractWithholdBO.getReturnWeb())) {
            wxPayJsapiRequest.setReturnWeb(ContractConstants.WX_RETURN_WEB);
        }
        wxPayJsapiRequest.setRequestSerial(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.WECHAT_JRN_NO, 5));
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.CONTRACT_JSAPI_SIGN.getSource());
        request.setRoute(WXPayChannel.CONTRACT_JSAPI_SIGN.getRoute());
        request.setBusiType(WXPayChannel.CONTRACT_JSAPI_SIGN.getBusType());
        request.setTarget(wxPayJsapiRequest);
        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        WxPayEntrustWebResponse wxPayEntrustwebResponse = (WxPayEntrustWebResponse) genericRspDTO.getBody().getResult();
        Map<String, String> params = AlipaySDKOrderInfoUtils.buildContractParamMap(wxPayJsapiRequest.getAppid(), wxPayEntrustwebResponse.getMchId(), wxPayJsapiRequest.getPlanId(),
                wxPayJsapiRequest.getContractCode(), wxPayJsapiRequest.getRequestSerial(),
                wxPayJsapiRequest.getContractDisplayAccount(),
                wxPayJsapiRequest.getNotifyUrl(), wxPayJsapiRequest.getVersion(), wxPayEntrustwebResponse.getSign(),
                wxPayEntrustwebResponse.getTimestamp(), wxPayJsapiRequest.getReturnWeb());

        String orderParams = AlipaySDKOrderInfoUtils.buildOrderParam(params);
        final String orderInfo = CONTRACT_URL + orderParams;
        contractWithholdBO.setRedirectUrl(orderInfo);
    }

    @Override
    public void send(ContractWithholdBO contractWithholdBO) {
        contractJsapi(contractWithholdBO);
    }

}
