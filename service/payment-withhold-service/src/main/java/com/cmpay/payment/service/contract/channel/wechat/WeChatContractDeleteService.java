package com.cmpay.payment.service.contract.channel.wechat;

import com.cmpay.payment.bo.withhold.ContractDeleteBO;
import com.cmpay.payment.service.contract.ContractChannelService;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface WeChatContractDeleteService extends ContractChannelService<ContractDeleteBO> {
    /**
     * 微信委托代扣解约
     *
     * @param contractDeleteBO
     */
    void weChatContractDelete(ContractDeleteBO contractDeleteBO);
}
