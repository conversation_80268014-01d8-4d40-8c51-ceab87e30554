package com.cmpay.payment.service.notify.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.payment.bo.notify.WechatContractNotifyBO;
import com.cmpay.payment.bo.withhold.ContractQueryBO;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.constant.contract.ContractNofityTypeEnum;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.IContractSynService;
import com.cmpay.payment.service.notify.IWechatWithHoldNotifyService;
import com.cmpay.payment.util.PaymentUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/14 17:50
 */
@Service
@Slf4j
public class WechatWithHoldNotifyServiceImpl implements IWechatWithHoldNotifyService {
    @Autowired
    private IContractSynService contractSynService;
    @Autowired
    private AsynCommonService asynCommonService;

    @Override
    public String notifyContract(WechatContractNotifyBO weChatNotifyBO) {
        try {
            ContractQueryBO contractQueryBO = new ContractQueryBO();
            contractQueryBO.setContractCode(weChatNotifyBO.getContractCode());
            contractQueryBO.setContractId(weChatNotifyBO.getContractId());
            contractQueryBO.setChangeType(weChatNotifyBO.getChangeType());
            contractQueryBO.setOperateTime(PaymentUtils.getStringDate(weChatNotifyBO.getOperateTime()));
            contractQueryBO.setContractSignedTime(PaymentUtils.getStringDate(weChatNotifyBO.getOperateTime()));
            contractQueryBO.setContractExpiredTime(PaymentUtils.getStringDate(weChatNotifyBO.getContractExpiredTime()));
            contractQueryBO.setOpenid(weChatNotifyBO.getOpenid());
            ContractWithholdBO resultBO = contractSynService.contractNotify(contractQueryBO);
            if (StringUtils.equals(ContractStatusEnum.CONTRACT_SUCCESS.name(), resultBO.getContractStatus())) {
                log.info("contractNotifyWeChat modify status success");
                asynCommonService.asyncContractNotify(resultBO);
            }
            return resultBO.getContractStatus();
        } catch (BusinessException e) {
            log.warn("notifyWeChat modify status fail:.:{}", e);
        } catch (Exception e) {
            log.error("notifyWeChat modify status fail:.:", e);
        }
        return null;
    }

    @Override
    public String notifyContractDelete(WechatContractNotifyBO weChatNotifyBO) {
        try {
            ContractQueryBO contractQueryBO = new ContractQueryBO();
            contractQueryBO.setContractCode(weChatNotifyBO.getContractCode());
            contractQueryBO.setContractId(weChatNotifyBO.getContractId());
            contractQueryBO.setContractTerminatedTime(PaymentUtils.getStringDate(weChatNotifyBO.getOperateTime()));
            contractQueryBO.setContractTerminationMode(weChatNotifyBO.getContractTerminationMode());
            contractQueryBO.setOpenid(weChatNotifyBO.getOpenid());
            contractQueryBO.setChangeType(weChatNotifyBO.getChangeType());
            contractQueryBO.setOperateTime(PaymentUtils.getStringDate(weChatNotifyBO.getOperateTime()));
            ContractWithholdBO resultBO = contractSynService.contractDeleteNotify(contractQueryBO);
            if (StringUtils.equals(ContractStatusEnum.CONTRACT_TERMINATED.name(), resultBO.getContractStatus())) {
                log.info("contractDeleteNotifyWeChat modify status success");
                asynCommonService.asyncContractNotify(resultBO);
                return resultBO.getContractStatus();
            }
        } catch (BusinessException e) {
            log.warn("notifyWeChat modify status fail:{}", e.getMessage());
        } catch (Exception e) {
            log.error("notifyWeChat modify status fail:", e);
        }
        return null;
    }
}
