package com.cmpay.payment.service.contract.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.AlertCapable;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.dto.wechat.WxPayPreEntrustWebRequest;
import com.cmpay.payment.dto.wechat.WxPayPreEntrustWebResponse;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.contract.channel.wechat.WeChatAppContractService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatAppContractServiceImpl implements WeChatAppContractService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatAppContractServiceImpl.class);
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Override
    public void contractAPP(ContractWithholdBO contractWithholdBO) {
        WxPayPreEntrustWebRequest wxPayContractAppRequest = new WxPayPreEntrustWebRequest();
        wxPayContractAppRequest.setRequestSerial(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.WECHAT_JRN_NO, 5));
        wxPayContractAppRequest.setAppid(contractWithholdBO.getAppId());
        wxPayContractAppRequest.setPlanId(contractWithholdBO.getPlanId());
        wxPayContractAppRequest.setContractCode(contractWithholdBO.getContractCode());
        wxPayContractAppRequest.setContractDisplayAccount(contractWithholdBO.getContractDisplayAccount());
        wxPayContractAppRequest.setVersion(ContractConstants.WX_CONTRACT_VERSION);

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(host)) {
            try {
                wxPayContractAppRequest.setNotifyUrl(UrlUtils.replaceDomainOrIp(weChatProperties.getContractNofityUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                wxPayContractAppRequest.setNotifyUrl(weChatProperties.getContractNofityUrl());
            }
        } else {
            wxPayContractAppRequest.setNotifyUrl(weChatProperties.getContractNofityUrl());
        }

        wxPayContractAppRequest.setReturnApp(contractWithholdBO.getReturnApp());
        wxPayContractAppRequest.setMchId(contractWithholdBO.getBankMerchantNo());
        wxPayContractAppRequest.setKey(contractWithholdBO.getHmac());

        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.CONTRACT_APP.getSource());
        request.setRoute(WXPayChannel.CONTRACT_APP.getRoute());
        request.setBusiType(WXPayChannel.CONTRACT_APP.getBusType());
        request.setTarget(wxPayContractAppRequest);
        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(contractWithholdBO, (WxPayPreEntrustWebResponse) result));
    }

    @Override
    public void send(ContractWithholdBO refundBO) {
        contractAPP(refundBO);
    }

    private void handleResult(ContractWithholdBO contractBO, WxPayPreEntrustWebResponse response) {
        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                contractBO.setContractStatus(ContractStatusEnum.CONTRACT_WAIT.name());
                contractBO.setPreEntrustwebId(response.getPreEntrustwebId());

            } else {
                logger.info("contractapp failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getResultCode());
                AlertCapable alertCapable;
                switch (errorCodeEnum) {
                    case SYSTEMERROR:
                    case PARAMERROR:
                    case SIGNERROR:
                    case PAYAUTHERROR:
                    default:
                        alertCapable = MsgCodeEnum.CONTRACT_ADD_FAILURE;
                        break;
                }
                BusinessException.throwBusinessException(alertCapable);
            }
        } else {
            logger.error("contractapp failure, weChat return message is : {}", response.getReturnMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_ADD_FAILURE);
        }
    }
}
