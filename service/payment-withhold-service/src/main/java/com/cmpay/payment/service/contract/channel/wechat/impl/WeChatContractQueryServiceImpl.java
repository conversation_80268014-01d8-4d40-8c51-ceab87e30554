package com.cmpay.payment.service.contract.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.withhold.ContractQueryBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.constant.contract.WeChatContractDeleteEnum;
import com.cmpay.payment.constant.contract.WeChatContractStatusEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.dto.wechat.WxPayQueryContractRequest;
import com.cmpay.payment.dto.wechat.WxPayQueryContractResponse;
import com.cmpay.payment.service.contract.channel.wechat.WeChatContractQueryService;
import com.cmpay.payment.util.PaymentUtils;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatContractQueryServiceImpl implements WeChatContractQueryService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatContractQueryServiceImpl.class);

    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;

    /**
     * 独立签约查询
     *
     * @param contractQueryBO
     */
    @Override
    public void weChatContractQuery(ContractQueryBO contractQueryBO) {
        WxPayQueryContractRequest wxPayQuerycontractRequest = new WxPayQueryContractRequest();
        wxPayQuerycontractRequest.setContractCode(contractQueryBO.getContractCode());
        wxPayQuerycontractRequest.setAppid(contractQueryBO.getAppId());
        wxPayQuerycontractRequest.setPlanId(contractQueryBO.getPlanId());
        wxPayQuerycontractRequest.setContractId(contractQueryBO.getContractId());
        wxPayQuerycontractRequest.setVersion(ContractConstants.WX_CONTRACT_VERSION);
        wxPayQuerycontractRequest.setMchId(contractQueryBO.getBankMerchantNo());
        wxPayQuerycontractRequest.setKey(contractQueryBO.getContractSecureValue());
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.CONTRACT_QUERY.getSource());
        request.setRoute(WXPayChannel.CONTRACT_QUERY.getRoute());
        request.setBusiType(WXPayChannel.CONTRACT_QUERY.getBusType());
        request.setTarget(wxPayQuerycontractRequest);

        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(contractQueryBO, (WxPayQueryContractResponse) result));
    }

    @Override
    public void send(ContractQueryBO contractQueryBO) {
        weChatContractQuery(contractQueryBO);
    }

    private void handleResult(ContractQueryBO contractQueryBO, WxPayQueryContractResponse response) {
        if (!StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            logger.warn("query ContractOrder failure, weChat return message is : {}", response.getReturnMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_QUERY_FAILURE);
        }
        if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
            if (JudgeUtils.equals(response.getContractState(), WeChatContractStatusEnum.SUCCESS.getDesc())) {
                contractQueryBO.setContractStatus(ContractStatusEnum.CONTRACT_SUCCESS.name());
                contractQueryBO.setContractId(response.getContractId());
                contractQueryBO.setContractDisplayAccount(response.getContractDisplayAccount());
                contractQueryBO.setContractSignedTime(PaymentUtils.getStringDate(response.getContractSignedTime()));
                contractQueryBO.setContractExpiredTime(PaymentUtils.getStringDate(response.getContractExpiredTime()));
                contractQueryBO.setOpenid(response.getOpenid());
            } else if (JudgeUtils.equals(response.getContractState(), WeChatContractStatusEnum.DELETE.getDesc())) {
                if (JudgeUtils.equals(response.getContractTerminationMode(), WeChatContractDeleteEnum.NO_DELETE.getDesc())) {
                    contractQueryBO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED_WAIT.name());
                } else {
                    contractQueryBO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
                    contractQueryBO.setContractTerminatedTime(PaymentUtils.getStringDate(response.getContractTerminatedTime()));
                    contractQueryBO.setContractTerminationMode(response.getContractTerminationMode());
                    if (JudgeUtils.isNotEmpty(response.getContractTerminationRemark())) {
                        contractQueryBO.setContractTerminationRemark(response.getContractTerminationRemark());
                    }
                    contractQueryBO.setOpenid(response.getOpenid());
                }
            } else if (JudgeUtils.equals(response.getContractState(), WeChatContractStatusEnum.WAIT.getDesc())) {
                contractQueryBO.setContractStatus(ContractStatusEnum.CONTRACT_WAIT.name());
            }
        } else {
            logger.warn("query ContractOrder failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
            WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getErrCode());
            if (JudgeUtils.isNull(errorCodeEnum)){
                BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_QUERY_FAILURE);
            }
            switch (errorCodeEnum) {
                case SIGN_ERROR:
                    break;
                default:
                    BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_QUERY_FAILURE);
            }
        }

    }
}
