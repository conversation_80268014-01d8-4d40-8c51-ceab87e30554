package com.cmpay.payment.service.contract.channel.alipay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.withhold.ContractQueryBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.TradeTypeEnum;
import com.cmpay.payment.constant.contract.*;
import com.cmpay.payment.dto.alipay.TradeAgreementQueryReq;
import com.cmpay.payment.dto.alipay.TradeAgreementQueryRsp;
import com.cmpay.payment.service.contract.channel.alipay.AlipayContractQueryService;
import com.cmpay.payment.util.PaymentUtils;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class AlipayContractQueryServiceImpl implements AlipayContractQueryService {

    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;

    @Override
    public void alipayContractQuery(ContractQueryBO contractQueryBO) {
        TradeAgreementQueryReq tradeAgreementQueryReq = new TradeAgreementQueryReq();
        tradeAgreementQueryReq.setExternalAgreementNo(contractQueryBO.getContractCode());
//        老的签约：写死固定值
        if (JudgeUtils.equalsAny(contractQueryBO.getScene().toUpperCase(), PaymentSceneEnum.WAP.name(),
                PaymentSceneEnum.WEB.name())) {
            tradeAgreementQueryReq.setSignScene(ContractConstants.INDUSTRY_CARRENTAL);
        } else if (JudgeUtils.equals(contractQueryBO.getScene().toUpperCase(), PaymentSceneEnum.APP.name())) {
            tradeAgreementQueryReq.setSignScene(ContractConstants.INDUSTRY_DIGITAL_MEDIA);
        }
//       支付宝协议状态查询：必须携带签约时传的 业务类型signScene
//      新逻辑，signScene从数据库取，但是为了老的已签约的，进行解约后，也能查询回来，上面的保留
        if(JudgeUtils.isNotEmpty(contractQueryBO.getSignBusType())){
            tradeAgreementQueryReq.setSignScene(contractQueryBO.getSignBusType());
        }

        tradeAgreementQueryReq.setPersonalProductCode(contractQueryBO.getPlanId());

        GenericRspDTO<Response> response = this.nrtCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_AGREEMENT_QUERY.getName(), tradeAgreementQueryReq));


        Optional.ofNullable(response)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .map(result -> handleResult(contractQueryBO,(TradeAgreementQueryRsp) result,response))
                .orElseThrow(() ->new BusinessException(MsgCodeEnum.CONTRACT_QUERY_FAILURE));
    }

    private TradeAgreementQueryRsp handleResult(ContractQueryBO contractQueryBO, TradeAgreementQueryRsp result, GenericRspDTO<Response> response) {
        if (JudgeUtils.isNotSuccess(response.getMsgCd())) {
            if(JudgeUtils.equals(response.getMsgCd(),ContractConstants.SERVICE_ERROR_CODES)
                    && JudgeUtils.equals(contractQueryBO.getChangeType(), ContractChangeTypeEnum.DELETE.name())
                    && JudgeUtils.equalsAny(result.getSubCode(), AlipayContractErrorEnum.USER_AGREEMENT_NOT_EXIST.name(),
                    AlipayContractErrorEnum.AGREEMENT_HAS_UNSIGNED.name())){
                contractQueryBO.setContractTerminationMode(ContractConstants.DELETE_TYPE);
                contractQueryBO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
            }else{
                BusinessException.throwBusinessException(response);
            }
        }
        if (JudgeUtils.equals(ContractConstants.CODE_SUCCESS, result.getCode())) {
            if (JudgeUtils.isNull(result.getAgreementNo())) {
                BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_QUERY_FAILURE);
            }
            contractQueryBO.setContractId(result.getAgreementNo());
            contractQueryBO.setContractSignedTime(PaymentUtils.getStringDate(result.getSignTime()));
            contractQueryBO.setContractExpiredTime(PaymentUtils.getStringDate(result.getInvalidTime()));
            contractQueryBO.setPayingAgencyUserId(result.getAlipayLogonId());
            contractQueryBO.setOpenid(result.getAlipayLogonId());
            contractQueryBO.setOperateTime(PaymentUtils.getStringDate(result.getValidTime()));
            AlipaySignStatusEnum statusEnum = EnumUtils.getEnum(AlipaySignStatusEnum.class, result.getStatus());
            switch (statusEnum) {
                case NORMAL:
                    contractQueryBO.setChangeType(TradeTypeEnum.ADD.name());
                    contractQueryBO.setContractStatus(ContractStatusEnum.CONTRACT_SUCCESS.name());
                    break;
                case STOP:
                    contractQueryBO.setContractStatus(ContractStatusEnum.CONTRACT_STOP.name());
                    break;
                case TEMP:
                    contractQueryBO.setContractStatus(ContractStatusEnum.CONTRACT_FAIL.name());
                    break;
                case UNSIGN:
                    contractQueryBO.setChangeType(TradeTypeEnum.DELETE.name());
                    contractQueryBO.setContractTerminationMode(ContractConstants.DELETE_TYPE);
                    contractQueryBO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
                    break;
                default:
                    break;
            }
        }
        return result;
    }

    @Override
    public void send(ContractQueryBO contractQueryBO) {
        alipayContractQuery(contractQueryBO);
    }
}
