package com.cmpay.payment.service.contract.channel.wechat;

import com.cmpay.payment.bo.withhold.ContractApplyWithholdBO;
import com.cmpay.payment.service.contract.ContractChannelService;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface WeChatContractApplyWithholdService extends ContractChannelService<ContractApplyWithholdBO> {
    /**
     * 微信APP支付统一下单
     *
     * @param contractApplyWithholdBO
     */
    void contractApply(ContractApplyWithholdBO contractApplyWithholdBO);
}
