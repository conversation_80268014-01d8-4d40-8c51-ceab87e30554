package com.cmpay.payment.service.contract.channel.wechat;

import com.cmpay.payment.bo.withhold.ContractWithholdQueryBO;
import com.cmpay.payment.service.contract.ContractChannelService;

/**
 * 查询代扣申请订单
 *
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface WeChatContractWithholdQueryService extends ContractChannelService<ContractWithholdQueryBO> {
    /**
     * 微信H5支付订单查询
     *
     * @param contractWithholdQueryBO
     */
    void weChatContractWithholdQuery(ContractWithholdQueryBO contractWithholdQueryBO);
}
