package com.cmpay.payment.service.contract.channel.alipay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.dto.alipay.TradeAgreementSignReq;
import com.cmpay.payment.dto.alipay.TradeAgreementSignRsp;
import com.cmpay.payment.properties.AlipayProperties;
import com.cmpay.payment.service.contract.channel.alipay.AlipayContractService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.PaymentUtils;
import com.cmpay.payment.util.UrlUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
public class AlipayContractServiceImpl implements AlipayContractService {
    private static final Logger logger = LoggerFactory.getLogger(AlipayContractServiceImpl.class);
    @Autowired
    AlipayProperties alipayProperties;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Override
    public void send(ContractWithholdBO contractWithholdBO) {
        contract(contractWithholdBO);
    }

    @Override
    public void contract(ContractWithholdBO contractWithholdBO) {
        TradeAgreementSignReq tradeAgreementSign = new TradeAgreementSignReq();
        tradeAgreementSign.setSignValidityPeriod(contractWithholdBO.getTimeoutExpress());

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                tradeAgreementSign.setNotifyUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getContractNotifyUrl(),host));
                tradeAgreementSign.setReturnUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getReturnPageContract(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                tradeAgreementSign.setNotifyUrl(alipayProperties.getContractNotifyUrl());
                tradeAgreementSign.setReturnUrl(alipayProperties.getReturnPageContract());
            }
        } else {
            tradeAgreementSign.setNotifyUrl(alipayProperties.getContractNotifyUrl());
            tradeAgreementSign.setReturnUrl(alipayProperties.getReturnPageContract());
        }

        //参数支付宝合同确认
        if (JudgeUtils.equals(contractWithholdBO.getPlanId(), ContractConstants.GENERAL_WITHHOLDING_P)) {
            tradeAgreementSign.setPersonalProductCode(ContractConstants.GENERAL_WITHHOLDING_P);
            tradeAgreementSign.setProductCode(ContractConstants.GENERAL_WITHHOLDING);
        } else if (JudgeUtils.equals(contractWithholdBO.getPlanId(), ContractConstants.CYCLE_PAY_AUTH_P)) {
            tradeAgreementSign.setPersonalProductCode(ContractConstants.CYCLE_PAY_AUTH_P);
            tradeAgreementSign.setProductCode(ContractConstants.CYCLE_PAY_AUTH);
        }
//        signScene修改为商户传，不传则默认'INDUSTRY|CARRENTA'
        tradeAgreementSign.setSignScene(ContractConstants.INDUSTRY_CARRENTAL);
        if(StringUtils.isNotEmpty(contractWithholdBO.getSignBusType())){
            tradeAgreementSign.setSignScene(contractWithholdBO.getSignBusType());
        }
        tradeAgreementSign.setContractScene(contractWithholdBO.getScene());
        tradeAgreementSign.setExternalLogonId(contractWithholdBO.getContractDisplayAccount());
        tradeAgreementSign.setExternalAgreementNo(contractWithholdBO.getContractCode());
        tradeAgreementSign.setChannel(ContractConstants.ALIPAYAPP);
        if (JudgeUtils.isNotEmpty(contractWithholdBO.getSubMerchantName())) {
            tradeAgreementSign.setSubMerchantName(contractWithholdBO.getSubMerchantName());
        }
        if (JudgeUtils.isNotEmpty(contractWithholdBO.getSubMerchantServiceName())) {
            tradeAgreementSign.setSubMerchantServiceName(contractWithholdBO.getSubMerchantServiceName());
        }
        if (JudgeUtils.isNotEmpty(contractWithholdBO.getSubMerchantServiceDescription())) {
            tradeAgreementSign.setSubMerchantServiceDescription(contractWithholdBO.getSubMerchantServiceDescription());
        }
        if (JudgeUtils.isNotEmpty(contractWithholdBO.getPeriodType())) {
            tradeAgreementSign.setPeriodType(contractWithholdBO.getPeriodType());
            tradeAgreementSign.setPeriod(contractWithholdBO.getPeriod());
            tradeAgreementSign.setExecuteTime(PaymentUtils.getDateString(contractWithholdBO.getExecuteTime()));
            tradeAgreementSign.setSingleAmount(contractWithholdBO.getSingleAmount());
        }
        GenericRspDTO<Response> response = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_AGREEMENT_SIGN.getName(), tradeAgreementSign));
        if (JudgeUtils.isNotSuccess(response.getMsgCd())) {
            BusinessException.throwBusinessException(response);
        }
        TradeAgreementSignRsp tradeAgreementSignRsp = (TradeAgreementSignRsp) response.getBody().getResult();
        if (StringUtils.contains(response.getMsgCd(), ContractConstants.SENDGW_SUCCESS)) {
            contractWithholdBO.setContractStatus(ContractStatusEnum.CONTRACT_WAIT.name());
//            小程序签约，获取签约字符串
            if (StringUtils.equalsAnyIgnoreCase(CommonConstant.ALIPAYY_BUSINESS_CHANNEL_APPLET, contractWithholdBO.getScene())) {
                contractWithholdBO.setSignStr((String) tradeAgreementSignRsp.getBody());
            } else {
//                wap（H5）签约，获取签约地址
                contractWithholdBO.setRedirectUrl(UrlUtils.alipayUrlEncode((String) tradeAgreementSignRsp.getBody()));
            }
        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_ALIPAY_WITHHOLD_ADD_FAILURE);
        }
    }

}
