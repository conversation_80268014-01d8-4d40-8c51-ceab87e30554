package com.cmpay.payment.service.contract.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.withhold.ContractDeleteBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.CmpayConstants;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.dto.cmpay.CmpayOnlineContractDeleteReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayOnlineContractDeleteRspDTO;
import com.cmpay.payment.entity.config.ContractDO;
import com.cmpay.payment.service.contract.channel.cmpay.CmpayContractDeleteService;
import com.cmpay.payment.service.ext.config.IExtContractService;
import com.cmpay.payment.service.ext.config.IExtRateService;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
public class CmpayContractDeleteServiceImpl implements CmpayContractDeleteService {

    @Autowired
    IExtRateService rateService;
    @Autowired
    private IExtContractService contractService;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;

    @Override
    public void cmpayContractDelete(ContractDeleteBO contractDeleteBO) {

        ContractDO contract = contractService.getContract(contractDeleteBO.getBankMerchantNo());
        try {
            CmpayOnlineContractDeleteReqDTO cmpayOnlineContractDeleteReqDTO = new CmpayOnlineContractDeleteReqDTO();
            cmpayOnlineContractDeleteReqDTO.setMerchantId(contract.getMerchantNumber());
            cmpayOnlineContractDeleteReqDTO.setSignType(contract.getSignMethod());
            cmpayOnlineContractDeleteReqDTO.setType(CommonConstant.CMPAY_ONLINE_CONTRACT_DELETE_TYPE);
            cmpayOnlineContractDeleteReqDTO.setVersion(CommonConstant.CMPAY_ONLINE_CONTRACT_DELETE_VERSION);
            cmpayOnlineContractDeleteReqDTO.setMerchantCert(contract.getPublicKey());
            cmpayOnlineContractDeleteReqDTO.setHmac(contract.getSecretKey());
            cmpayOnlineContractDeleteReqDTO.setRequestId(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.CMPAY_JRN_NO, 18));
            cmpayOnlineContractDeleteReqDTO.setMobileNo(contractDeleteBO.getMobileNo());
            Request request = new Request();
            request.setRequestId(LemonUtils.getRequestId());
            request.setRoute(CmpayPayChannelEnum.CMPAY_ONLINE);
            request.setBusiType(CmpayPayChannelEnum.CMPAY_ONLINE_CONTRACT_DELETE.getName());
            request.setSource(CmpayPayChannelEnum.CMPAY_ONLINE);
            request.setTarget(cmpayOnlineContractDeleteReqDTO);

            GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            if (JudgeUtils.isNotSuccess(genericRspDTO)) {
                BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_CONTRACT_DELETE_FAIL);
            }
           Optional.of(genericRspDTO)
                    .map(GenericRspDTO::getBody)
                    .map(Response::getResult)
                   .map(result -> {
                       handleOnlineContractDelete(contractDeleteBO, (CmpayOnlineContractDeleteRspDTO) result);
                       return result;
                   }).orElseThrow(() ->new BusinessException(MsgCodeEnum.CMPAY_CONTRACT_DELETE_FAIL));
        } catch (Exception e) {
            throw e;
    }
    }

    @Override
    public void send(ContractDeleteBO contractDeleteBO) {
        cmpayContractDelete(contractDeleteBO);
    }

    private void handleOnlineContractDelete(ContractDeleteBO contractDeleteBO,CmpayOnlineContractDeleteRspDTO result) {
        // 没有明确的报错，来明确返回解约失败，所有状态不变，由于超时等原因解约报错，但是实际上和包解约成功，这边解约中还可以通过查询补偿状态
        if(!StringUtils.equals(CmpayConstants.RETURN_CODE, result.getReturnCode())){
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_CONTRACT_DELETE_FAIL);
        }
    }
}
