package com.cmpay.payment.service.contract.channel.alipay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.withhold.ContractWithholdQueryBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.AlipayTradeStatusEnum;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.dto.alipay.TradeQueryReq;
import com.cmpay.payment.dto.alipay.TradeQueryRsp;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.service.contract.channel.alipay.AlipayContractQueryChannelService;
import com.cmpay.payment.util.DateUtils;
import com.cmpay.payment.util.PaymentUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
@Slf4j
public class AlipayContractWithholdQueryServiceImpl implements AlipayContractQueryChannelService {

    private static final String CODE_SUCCESS = "10000";
    private static final String CODE_FAIL = "40004";
    private static final String ORDER_NOT_EXIST = "ACQ.TRADE_NOT_EXIST";

    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;

    @Override
    public void alipayContractPaymentQuery(ContractWithholdQueryBO contractWithholdQueryBO) {
        if (checkQueryParams(contractWithholdQueryBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.OUT_TRADE_NO_CANNOT_BE_EMPTY);
        }

        TradeQueryReq tradeQueryReq = new TradeQueryReq();
        tradeQueryReq.setOutTradeNo(contractWithholdQueryBO.getPaymentOrder().getTradeOrderNo());

        GenericRspDTO<Response> response = this.nrtCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_QUERY.getName(), tradeQueryReq));

        if (JudgeUtils.isSuccess(response.getMsgCd())) {
            TradeQueryRsp tradeQueryRsp = (TradeQueryRsp) response.getBody().getResult();
            //登记支付宝订单号
            contractWithholdQueryBO.getPaymentOrder().setThirdOrdNo(tradeQueryRsp.getTradeNo());
            if (StringUtils.equals(CODE_SUCCESS, tradeQueryRsp.getCode())) {
                AlipayTradeStatusEnum statusEnum = EnumUtils.getEnum(AlipayTradeStatusEnum.class, tradeQueryRsp.getTradeStatus());
                switch (statusEnum) {
                    case WAIT_BUYER_PAY:
                        contractWithholdQueryBO.setPaymentStatus(OrderStatusEnum.WAIT_PAY.name());
                        break;
                    case TRADE_CLOSED:
                        contractWithholdQueryBO.setPaymentStatus(OrderStatusEnum.TRADE_CLOSED.name());
                        break;
                    case TRADE_SUCCESS:
                    case TRADE_FINISHED:
                        contractWithholdQueryBO.setPaymentStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                        String paymentDate = new SimpleDateFormat("yyyyMMdd").format(tradeQueryRsp.getSendPayDate());
                        contractWithholdQueryBO.getPaymentOrder().setAccountDate(paymentDate);
                        contractWithholdQueryBO.getPaymentOrder().setThirdOrdDt(paymentDate);
                        contractWithholdQueryBO.getPaymentOrder().setMobileNumber(tradeQueryRsp.getBuyerUserId());
                        break;
                    default:
                        break;
                }
            }
        }else if (StringUtils.equals(CODE_FAIL,response.getMsgCd())) {
            String requestDateTime = contractWithholdQueryBO.getPaymentOrder().getOrderDate() + contractWithholdQueryBO.getPaymentOrder().getOrderTime();
            TradeQueryRsp tradeQueryRsp = (TradeQueryRsp) response.getBody().getResult();
            //五分钟之前不存在得订单直接失败
            if (StringUtils.equals(tradeQueryRsp.getSubCode(), ORDER_NOT_EXIST) && !DateUtils.compareTimeLessThanFive(requestDateTime)) {
                contractWithholdQueryBO.setPaymentStatus(OrderStatusEnum.TRADE_FAIL.name());
            } else {
                contractWithholdQueryBO.setPaymentStatus(OrderStatusEnum.WAIT_PAY.name());
            }
        }
    }

    @Override
    public void send(ContractWithholdQueryBO contractWithholdQueryBO) {
        alipayContractPaymentQuery(contractWithholdQueryBO);
    }

    private boolean checkQueryParams(ContractWithholdQueryBO contractWithholdQueryBO) {
        return Optional.ofNullable(contractWithholdQueryBO)
                .map(ContractWithholdQueryBO::getPaymentOrder)
                .map(TradeOrderDO::getOutTradeNo)
                .map(StringUtils::isEmpty)
                .orElse(false);
    }
}
