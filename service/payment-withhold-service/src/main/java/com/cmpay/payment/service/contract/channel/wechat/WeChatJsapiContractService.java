package com.cmpay.payment.service.contract.channel.wechat;

import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.service.contract.ContractChannelService;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface WeChatJsapiContractService extends ContractChannelService<ContractWithholdBO> {
    /**
     * 微信APP签约
     *
     * @param contractWithholdBO
     */
    void contractJsapi(ContractWithholdBO contractWithholdBO);
}
