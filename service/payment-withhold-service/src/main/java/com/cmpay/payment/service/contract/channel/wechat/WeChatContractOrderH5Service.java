package com.cmpay.payment.service.contract.channel.wechat;

import com.cmpay.payment.bo.withhold.ContractPaymentWithholdBO;
import com.cmpay.payment.service.contract.ContractPaymentChannelService;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface WeChatContractOrderH5Service extends ContractPaymentChannelService<ContractPaymentWithholdBO> {
    /**
     * 微信H5支付统一下单
     *
     * @param contractPaymentWithholdBO
     */
    void unifiedOrderH5(ContractPaymentWithholdBO contractPaymentWithholdBO);
}
