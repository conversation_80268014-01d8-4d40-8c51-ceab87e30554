package com.cmpay.payment.service.contract.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.dto.wechat.WxPayH5entrustWebRequest;
import com.cmpay.payment.dto.wechat.WxPayH5entrustWebResponse;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.contract.channel.wechat.WeChatH5ContractService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.AlipaySDKOrderInfoUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatH5ContractServiceImpl implements WeChatH5ContractService {
    private static final Logger logger = LoggerFactory.getLogger(WeChatH5ContractServiceImpl.class);
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    ExtParamInfoService paramInfoService;
    private static final String CONTRACT_URL = "https://api.mch.weixin.qq.com/papay/h5entrustweb?";

    @Override
    public void contractH5(ContractWithholdBO contractWithholdBO) {
        WxPayH5entrustWebRequest wxPayH5entrustwebRequest = new WxPayH5entrustWebRequest();
        wxPayH5entrustwebRequest.setAppid(contractWithholdBO.getAppId());
        wxPayH5entrustwebRequest.setPlanId(contractWithholdBO.getPlanId());
        wxPayH5entrustwebRequest.setContractCode(contractWithholdBO.getContractCode());
        wxPayH5entrustwebRequest.setContractDisplayAccount(contractWithholdBO.getContractDisplayAccount());
        wxPayH5entrustwebRequest.setVersion(ContractConstants.WX_CONTRACT_VERSION);

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                wxPayH5entrustwebRequest.setNotifyUrl(UrlUtils.replaceDomainOrIp(weChatProperties.getContractNofityUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                wxPayH5entrustwebRequest.setNotifyUrl(weChatProperties.getContractNofityUrl());
            }
        } else {
            wxPayH5entrustwebRequest.setNotifyUrl(weChatProperties.getContractNofityUrl());
        }

        wxPayH5entrustwebRequest.setReturnAppid(contractWithholdBO.getReturnAppid());
        wxPayH5entrustwebRequest.setClientip(contractWithholdBO.getContractClientip());
        wxPayH5entrustwebRequest.setRequestSerial(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.WECHAT_JRN_NO, 5));
        wxPayH5entrustwebRequest.setMchId(contractWithholdBO.getBankMerchantNo());
        wxPayH5entrustwebRequest.setKey(contractWithholdBO.getHmac());
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.CONTRACT_H5.getSource());
        request.setRoute(WXPayChannel.CONTRACT_H5.getRoute());
        request.setBusiType(WXPayChannel.CONTRACT_H5.getBusType());
        request.setTarget(wxPayH5entrustwebRequest);
        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        WxPayH5entrustWebResponse wxPayH5entrustwebResponse = (WxPayH5entrustWebResponse) genericRspDTO.getBody().getResult();
        Map<String, String> params = AlipaySDKOrderInfoUtils.buildH5ContractParamMap(wxPayH5entrustwebRequest.getAppid(), wxPayH5entrustwebRequest.getContractCode(),
                wxPayH5entrustwebRequest.getContractDisplayAccount(), wxPayH5entrustwebResponse.getMchId(), wxPayH5entrustwebRequest.getNotifyUrl(),
                wxPayH5entrustwebRequest.getPlanId(), wxPayH5entrustwebRequest.getRequestSerial(), wxPayH5entrustwebRequest.getReturnAppid(),
                wxPayH5entrustwebRequest.getClientip(), wxPayH5entrustwebResponse.getTimestamp(), wxPayH5entrustwebRequest.getVersion(),
                wxPayH5entrustwebResponse.getSign()
        );
        String orderParams = AlipaySDKOrderInfoUtils.buildOrderParam(params);
        final String orderInfo = CONTRACT_URL + orderParams;
        contractWithholdBO.setRedirectUrl(orderInfo);


    }

    @Override
    public void send(ContractWithholdBO contractBO) {
        contractH5(contractBO);
    }

}
