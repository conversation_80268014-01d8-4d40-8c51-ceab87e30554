package com.cmpay.payment.service.contract.channel.cmpay;

import com.cmpay.payment.bo.withhold.ContractDeleteBO;
import com.cmpay.payment.service.contract.ContractChannelService;

/**
 * <AUTHOR>
 * @date 2022/07/27
 */
public interface CmpayContractDeleteService extends ContractChannelService<ContractDeleteBO> {
    /**
     * 和包代扣解约
     *
     * @param contractDeleteBO
     */
    void cmpayContractDelete(ContractDeleteBO contractDeleteBO);
}
