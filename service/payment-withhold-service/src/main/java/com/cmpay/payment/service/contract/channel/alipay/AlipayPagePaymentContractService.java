package com.cmpay.payment.service.contract.channel.alipay;

import com.cmpay.payment.bo.withhold.ContractPaymentWithholdBO;
import com.cmpay.payment.service.contract.ContractPaymentChannelService;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface AlipayPagePaymentContractService extends ContractPaymentChannelService<ContractPaymentWithholdBO> {
    /**
     * 统一收单下单并支付页面
     *
     * @param contractPaymentWithholdBO
     */
    void pagePaymentContract(ContractPaymentWithholdBO contractPaymentWithholdBO);
}
