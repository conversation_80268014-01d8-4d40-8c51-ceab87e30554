package com.cmpay.payment.stream;

import com.cmpay.framework.data.message.CmpayCmdDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.framework.stream.MessageHandler;
import com.cmpay.payment.bo.withhold.ContractApplyWithholdBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * Created on 2019/05/13
 *
 * <AUTHOR>
 */
@Component("asyncContractApplyHandler")
@Slf4j
public class ContractApplyHandler implements MessageHandler<ContractApplyWithholdBO, CmpayCmdDTO<ContractApplyWithholdBO>> {
    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 支付成功 通知应用
     *
     * @param cmpayCmdDTO
     */
    @Override
    public void onMessageReceive(CmpayCmdDTO<ContractApplyWithholdBO> cmpayCmdDTO) {
        ContractApplyWithholdBO contractPaymentBO = cmpayCmdDTO.getBody();
        try {
            applicationContext.publishEvent(contractPaymentBO);
        } catch (BusinessException e) {
            log.warn("签约扣款调用支付机构业务异常,签约协议号：{}，错误码:{},错误信息：{}", contractPaymentBO.getContractId(), e.getMsgCd(), e.getMsgInfo());
        } catch (Exception e) {
            log.warn("签约扣款调用支付机构系统异常,签约协议号：{}，错误信息：{}", contractPaymentBO.getContractId(), e.getMessage());
        }
    }
}
