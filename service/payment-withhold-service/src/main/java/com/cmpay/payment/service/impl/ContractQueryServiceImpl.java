package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.withhold.ContractDeleteBO;
import com.cmpay.payment.bo.withhold.ContractQueryBO;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.constant.CmpayConstants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.contract.ContractChangeTypeEnum;
import com.cmpay.payment.constant.contract.ContractNofityTypeEnum;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.constant.contract.ContractWayEnum;
import com.cmpay.payment.entity.withhold.ContractDeleteDO;
import com.cmpay.payment.entity.withhold.ContractWithholdDO;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.IContractQueryService;
import com.cmpay.payment.service.IContractSynService;
import com.cmpay.payment.service.ext.withhold.IExtContractDeleteService;
import com.cmpay.payment.service.ext.withhold.IExtContractWithholdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class ContractQueryServiceImpl implements IContractQueryService {

    @Autowired
    private IExtContractWithholdService contractWithholdService;
    @Autowired
    IContractSynService contractSynService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IExtContractDeleteService contractDeleteService;
    @Autowired
    private AsynCommonService asynCommonService;
    /**
     * 签约结果查询
     *
     * @param contractQueryBO
     * @return
     */
    @Override
    public ContractQueryBO contractInfoQuery(ContractQueryBO contractQueryBO) {
        ContractWithholdBO contractWithholdBO = new ContractWithholdBO();
        contractWithholdBO.setContractCode(contractQueryBO.getContractCode());
        ContractWithholdDO contractWithholdDO = contractWithholdService.findByContractCode(contractWithholdBO);
        if (JudgeUtils.isNull(contractWithholdDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_NOT_EXISTS);
        }
        if(JudgeUtils.equals(contractWithholdDO.getContractWay(), ContractWayEnum.WECHAT.name())){
            contractQueryBO.setAppId(contractWithholdDO.getAppId());
            contractQueryBO.setOpenid(contractWithholdDO.getOpenid());
        }
        contractQueryBO.setContractId(contractWithholdDO.getContractId());
        contractQueryBO.setPlanId(contractWithholdDO.getPlanId());
        contractQueryBO.setContractDisplayAccount(contractWithholdDO.getDisplayAccount());
        contractQueryBO.setContractStatus(contractWithholdDO.getContractStatus());
        if (JudgeUtils.equalsAny(contractQueryBO.getContractStatus(), ContractStatusEnum.CONTRACT_SUCCESS.name(),
                ContractStatusEnum.CONTRACT_TERMINATED.name(), ContractStatusEnum.CONTRACT_TERMINATED_WAIT.name())) {
            contractQueryBO.setContractSignedTime(contractWithholdDO.getSignedTime());
            contractQueryBO.setContractExpiredTime(contractWithholdDO.getExpiredTime());
            contractQueryBO.setContractRemark(contractWithholdDO.getExtra());
        }
        if (JudgeUtils.equals(contractQueryBO.getContractStatus(), ContractStatusEnum.CONTRACT_TERMINATED.name())) {
            contractQueryBO.setContractTerminatedTime(contractWithholdDO.getTerminatedTime());
            contractQueryBO.setContractTerminationMode(contractWithholdDO.getTerminationMode());
            contractQueryBO.setContractTerminationRemark(contractWithholdDO.getExtra());
        }
        return contractQueryBO;
    }

    @Override
    public ContractQueryBO contractQueryByContractCode(ContractQueryBO contractQueryBO) {
        ContractWithholdBO contractWithholdBO = new ContractWithholdBO();
        contractWithholdBO.setContractCode(contractQueryBO.getContractCode());
        ContractWithholdDO contractWithholdDO = contractWithholdService.findByContractCode(contractWithholdBO);
        if (JudgeUtils.isNull(contractWithholdDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_NOT_EXISTS);
        }
        //如果数据库状态已经签约成功，说明是自动查询查回来了，直接返回
        if (StringUtils.equals(contractWithholdDO.getContractStatus(), ContractStatusEnum.CONTRACT_SUCCESS.name())) {
            BeanUtils.copyProperties(contractQueryBO, contractWithholdDO);
            return contractQueryBO;
        }
        contractQueryBO.setContractWithholdDO(contractWithholdDO);
        try {
            BeanUtils.copyProperties(contractQueryBO, contractWithholdDO);
            contractQueryBO.setChangeType(ContractChangeTypeEnum.ADD.name());
            contractQueryBO.setScene(contractWithholdDO.getContractScene());
            //签约查询
            contractSynService.contractQuery(contractQueryBO);
        } catch (Exception e) {
            throw e;
        }
        return contractQueryBO;
    }

    @Override
    public ContractQueryBO contractDeleteQueryByContractCode(ContractQueryBO contractQueryBO) {
        ContractWithholdBO contractWithholdBO = new ContractWithholdBO();
        contractWithholdBO.setContractCode(contractQueryBO.getContractCode());
        //查询到签约协议
        ContractWithholdDO contractWithholdDO = contractWithholdService.findByContractCode(contractWithholdBO);
        if (JudgeUtils.isNull(contractWithholdDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_NOT_EXISTS);
        }
        //如果数据库状态已经解约成功，说明是自动查询查回来了，直接返回
        if (StringUtils.equals(contractWithholdDO.getContractStatus(), ContractStatusEnum.CONTRACT_TERMINATED.name())) {
            BeanUtils.copyProperties(contractQueryBO, contractWithholdDO);
            return contractQueryBO;
        }
        //1、查询解约表，如果有解约信息，那么查询的信息从解约表拿，没有就从签约表拿
        ContractDeleteBO contractDeleteBO = new ContractDeleteBO();
        contractDeleteBO.setContractCode(contractQueryBO.getContractCode());
        ContractDeleteDO contractDeleteDO = contractDeleteService.findByContractCode(contractDeleteBO);
        //解约表不为空，从解约表拿，和包签约状态查询：需要手机号，和包商户号，通过属性拷贝赋值
        if (JudgeUtils.isNotNull(contractDeleteDO)) {
            BeanUtils.copyProperties(contractQueryBO, contractDeleteDO);
            contractQueryBO.setScene(contractDeleteDO.getContractScene());
        } else {
            //解约表为空，从签约表拿
            BeanUtils.copyProperties(contractQueryBO, contractWithholdDO);
            contractQueryBO.setScene(contractWithholdDO.getContractScene());
        }
        //2、去解约查询，查询状态
        contractQueryBO.setChangeType(ContractChangeTypeEnum.DELETE.name());
        contractQueryBO.setContractQuerySign(CmpayConstants.CONTRACT_SIGN_CLOSE);
        try {
            applicationContext.publishEvent(contractQueryBO);

            //3、查询回来之后，如果是未解约,直接返回。如果不为解约，根本走不到这一步吧，里面就报错了
            if (JudgeUtils.notEquals(contractQueryBO.getContractStatus(), ContractStatusEnum.CONTRACT_TERMINATED.name())) {
                return contractQueryBO;
            }
            //4、如果解约表不为空，则更新解约表，为空则新增解约流水信息
            if (JudgeUtils.isNotNull(contractDeleteDO)) {
                contractDeleteDO.setContractId(contractQueryBO.getContractId());
                contractDeleteDO.setOpenid(contractQueryBO.getOpenid());
                contractDeleteDO.setDisplayAccount(contractQueryBO.getContractDisplayAccount());
                contractDeleteDO.setExpiredTime(contractQueryBO.getContractExpiredTime());
                contractDeleteDO.setTerminationMode(contractQueryBO.getContractTerminationMode());
                contractDeleteDO.setTerminatedTime(contractQueryBO.getContractTerminatedTime());
                contractDeleteDO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
                contractDeleteService.updateContractDeleteInfo(contractDeleteDO);
                updateStatus(contractQueryBO, contractWithholdDO);
                deleteNotify(contractQueryBO, contractDeleteDO);
            } else {//如果没有解约信息，那就新增解约流水
                addContractDeleteInfo(contractWithholdDO);
                updateStatus(contractQueryBO, contractWithholdDO);
            }
        } catch (Exception e) {
            throw e;
        }
        return contractQueryBO;
    }

    /**
     * 根据签约信息，补充、新增一条已解约信息
     */
    public void addContractDeleteInfo(ContractWithholdDO contractWithholdDO) {
        ContractDeleteBO contractDeleteBO = new ContractDeleteBO();
        BeanUtils.copyProperties(contractDeleteBO, contractWithholdDO);
        contractDeleteBO.setContractDisplayAccount(contractWithholdDO.getDisplayAccount());
        contractDeleteBO.setMerchantId(contractWithholdDO.getMerchantNo());
        contractDeleteBO.setChangeType(ContractChangeTypeEnum.DELETE.name());
        //解约的通知地址，由商户传来，和签约地址似乎是同一个
        contractDeleteBO.setScene(contractWithholdDO.getContractScene());
        contractDeleteBO.setContractExpiredTime(contractWithholdDO.getExpiredTime());
        //这字段再看看
//        contractDeleteBO.setContractTerminationRemark(contractDeleteBO.getExtra());
        contractDeleteBO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
        contractDeleteBO.setContractClientip(contractWithholdDO.getClientIp());
        contractDeleteBO.setExtra(contractWithholdDO.getExtra());
        //解约时间设置当前时间，因为也不知道他解约不从我们这里走，我们也不知道他在支付机构啥时候解约的
        contractDeleteBO.setContractTerminatedTime(DateTimeUtils.getCurrentDateTimeStr());
        contractDeleteService.insertContractDeleteInfo(contractDeleteBO);
    }


    public void updateStatus(ContractQueryBO contractQueryBO, ContractWithholdDO contractWithholdDO) {
        //更新签约表
        contractWithholdDO.setContractId(contractQueryBO.getContractId());
        contractWithholdDO.setDisplayAccount(contractQueryBO.getContractDisplayAccount());
        contractWithholdDO.setExpiredTime(contractQueryBO.getContractExpiredTime());
        contractWithholdDO.setTerminationMode(contractQueryBO.getContractTerminationMode());
        contractWithholdDO.setTerminatedTime(contractQueryBO.getContractTerminatedTime());
        contractWithholdDO.setOpenid(contractQueryBO.getOpenid());
        contractWithholdDO.setChangeType(ContractChangeTypeEnum.DELETE.name());
        contractWithholdDO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
        contractWithholdService.updateContractInfo(contractWithholdDO);
    }

    public void deleteNotify(ContractQueryBO contractQueryBO, ContractDeleteDO contractDeleteDO) {
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setMerchantNo(contractDeleteDO.getMerchantNo());
        tradeNotifyBO.setTradeOrderNo(contractQueryBO.getContractCode());
        tradeNotifyBO.setOutOrderNo(contractQueryBO.getContractCode());
        tradeNotifyBO.setOutRequestNo(contractDeleteDO.getContractCode());
        tradeNotifyBO.setTradeDate(contractDeleteDO.getContractDate());
        tradeNotifyBO.setOutRequestNo(contractQueryBO.getContractCode());
        tradeNotifyBO.setNotifyType(ContractNofityTypeEnum.DELETE.name().toLowerCase());
        tradeNotifyBO.setNotifyUrl(contractDeleteDO.getNotifyUrl());
        tradeNotifyBO.setExtra(contractDeleteDO.getExtra());
        tradeNotifyBO.setFinishDate(contractQueryBO.getContractTerminatedTime());
        asynCommonService.asyncNotify(tradeNotifyBO);
    }
}
