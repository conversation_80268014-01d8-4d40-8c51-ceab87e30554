package com.cmpay.payment.service.contract.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.withhold.ContractApplyWithholdBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.constant.wechat.WeChatTradeTypeEnum;
import com.cmpay.payment.dto.wechat.WxPayPapPayApplyRequest;
import com.cmpay.payment.dto.wechat.WxPayPapPayApplyResponse;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.contract.channel.wechat.WeChatContractApplyWithholdService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatContractApplyWithholdServiceImpl implements WeChatContractApplyWithholdService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatContractApplyWithholdServiceImpl.class);

    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Override
    public void contractApply(ContractApplyWithholdBO contractApplyWithholdBO) {
        WxPayPapPayApplyRequest wxPayPappayapplyRequest = new WxPayPapPayApplyRequest();
        wxPayPappayapplyRequest.setAppid(contractApplyWithholdBO.getAppId());
        wxPayPappayapplyRequest.setContractId(contractApplyWithholdBO.getContractId());
        wxPayPappayapplyRequest.setBody(contractApplyWithholdBO.getSubject());
        wxPayPappayapplyRequest.setOutTradeNo(contractApplyWithholdBO.getOutTradeNo());
        wxPayPappayapplyRequest.setTotalFee(contractApplyWithholdBO.getRealAmount().multiply(new BigDecimal(100)).intValue());
        wxPayPappayapplyRequest.setSpbillCreateIp(contractApplyWithholdBO.getPayClientip());

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                wxPayPappayapplyRequest.setNotifyUrl(UrlUtils.replaceDomainOrIp(weChatProperties.getContractPaymentUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                wxPayPappayapplyRequest.setNotifyUrl(weChatProperties.getContractPaymentUrl());
            }
        } else {
            wxPayPappayapplyRequest.setNotifyUrl(weChatProperties.getContractPaymentUrl());
        }

        wxPayPappayapplyRequest.setTradeType(WeChatTradeTypeEnum.PAP.name());
        wxPayPappayapplyRequest.setMchId(contractApplyWithholdBO.getPaymentId());
        wxPayPappayapplyRequest.setKey(contractApplyWithholdBO.getContractSecureValue());
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.CONTRACT_WITHHOLD.getSource());
        request.setRoute(WXPayChannel.CONTRACT_WITHHOLD.getRoute());
        request.setBusiType(WXPayChannel.CONTRACT_WITHHOLD.getBusType());
        request.setTarget(wxPayPappayapplyRequest);

        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(contractApplyWithholdBO, (WxPayPapPayApplyResponse) result));
    }

    @Override
    public void send(ContractApplyWithholdBO contractApplyWithholdBO) {
        contractApply(contractApplyWithholdBO);
    }

    private void handleResult(ContractApplyWithholdBO contractApplyWithholdBO, WxPayPapPayApplyResponse response) {
        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                contractApplyWithholdBO.setPaymentStatus(OrderStatusEnum.WAIT_PAY.name());
            } else {
                logger.error("applyContractOrder failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getErrCode());
                switch (errorCodeEnum) {
                    case SYSTEMERROR:
                    case CONTRACT_NOT_EXIST:
                    case PARAM_ERROR:
                    case ORDERPAID:
                    case ORDERCLOSED:
                    case SIGN_ERROR:
                    case APPID_MCHID_NOT_MATCH:
                    case ORDER_ACCEPTED:
                    case CONTRACTERROR:
                    case INVALID_REQUEST:
                    case FREQUENCY_LIMITED:
                    case RULELIMIT:
                        BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_WITHHOLD_FAILURE);
                        break;
                    default:
                        BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_WITHHOLD_FAILURE);
                }
            }
        } else {
            logger.error("applyContractOrder failure, weChat return message is : {}", response.getReturnMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_WITHHOLD_FAILURE);
        }
    }
}
