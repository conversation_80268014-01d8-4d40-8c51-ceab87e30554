package com.cmpay.payment.service.contract.listener;

import com.cmpay.payment.bo.withhold.ContractWithholdQueryBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.contract.ContractChannelService;
import com.cmpay.payment.service.contract.ContractListenerService;
import com.cmpay.payment.service.contract.channel.alipay.impl.AlipayContractWithholdQueryServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class AlipayContractWithholdQueryListener extends ContractListenerService<ContractWithholdQueryBO> {

    @Autowired
    ApplicationContext applicationContext;

    /**
     * 微信代扣签约查询订单
     *
     * @param contractWithholdQueryBO
     */
    @EventListener
    public void handleAlipayOrderQuery(ContractWithholdQueryBO contractWithholdQueryBO) {
        execute(contractWithholdQueryBO);
    }

    @Override
    protected boolean checkChannelExecutable(ContractWithholdQueryBO contractWithholdQueryBO) {
        return Optional.ofNullable(contractWithholdQueryBO)
                .map(ContractWithholdQueryBO::getContractWay)
                .filter(contractWay -> StringUtils.equals(PaymentWayEnum.ALIPAY.name().toLowerCase(), contractWay))
                .isPresent();
    }

    @Override
    protected ContractChannelService determinateChannelExecuteBean(ContractWithholdQueryBO contractWithholdQueryBO) {
        return getBean(AlipayContractWithholdQueryServiceImpl.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
