package com.cmpay.payment.service.contract.listener;

import com.cmpay.payment.bo.withhold.ContractQueryBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.contract.ContractChannelService;
import com.cmpay.payment.service.contract.ContractListenerService;
import com.cmpay.payment.service.contract.channel.alipay.AlipayContractQueryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class AlipayContractQueryListener extends ContractListenerService<ContractQueryBO> {

    @Autowired
    ApplicationContext applicationContext;

    /**
     * 支付宝代扣查询
     *
     * @param contractQueryBO
     */
    @EventListener
    public void handleWeChatOrderQuery(ContractQueryBO contractQueryBO) {
        execute(contractQueryBO);
    }

    @Override
    protected boolean checkChannelExecutable(ContractQueryBO contractQueryBO) {
        return Optional.ofNullable(contractQueryBO)
                .map(ContractQueryBO::getContractWay)
                .filter(contractWay -> StringUtils.equals(PaymentWayEnum.ALIPAY.name().toLowerCase(), contractWay))
                .isPresent();
    }

    @Override
    protected ContractChannelService determinateChannelExecuteBean(ContractQueryBO contractQueryBO) {
        return getBean(AlipayContractQueryService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
