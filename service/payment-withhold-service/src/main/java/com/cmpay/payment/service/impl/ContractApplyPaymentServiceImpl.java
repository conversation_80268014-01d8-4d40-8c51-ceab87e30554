package com.cmpay.payment.service.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.JkThirdPayParamBO;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.bo.config.PayServiceBeanBO;
import com.cmpay.payment.bo.withhold.ContractApplyWithholdBO;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.bo.withhold.WxPrePayBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.AsyncContractPayClient;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.dto.wechat.WxPrePayNotifyRequest;
import com.cmpay.payment.dto.wechat.WxPrePayNotifyResponse;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.entity.withhold.ContractWithholdDO;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.IContractApplyPaymentService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.ExtThirdPayParamService;
import com.cmpay.payment.service.ext.config.IExtRateService;
import com.cmpay.payment.service.ext.withhold.IExtContractWithholdService;
import com.cmpay.payment.util.DateUtils;
import com.cmpay.payment.util.PaymentUtils;
import feign.RetryableException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
public class ContractApplyPaymentServiceImpl implements IContractApplyPaymentService {

    private static final Logger logger = LoggerFactory.getLogger(ContractApplyPaymentServiceImpl.class);

    @Autowired
    private IExtContractWithholdService contractWithholdService;
    @Autowired
    ExtParamInfoService paramInfoService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    IExtRateService rateService;
    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private IExtContractWithholdService iExtContractWithholdService;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private AsyncContractPayClient contractPayClient;
    @Autowired
    private ExtThirdPayParamService thirdPayParamService;

    /**
     * 支付中签约
     *
     * @param contractApplyWithholdBO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ContractApplyWithholdBO contractApplyPayment(ContractApplyWithholdBO contractApplyWithholdBO) {
        try {
            paymentProcess(contractApplyWithholdBO);
        } catch (Exception e) {
            String msg;
            if (e instanceof BusinessException) {
                msg = ((BusinessException) e).getMsgCd();
                if (JudgeUtils.equals(msg, MsgCodeEnum.SIGN_PAY_TIME_ERROR.getMsgCd())) {
                    BusinessException.throwBusinessException(MsgCodeEnum.SIGN_PAY_TIME_ERROR, contractApplyWithholdBO.getSignPayTimeRange());
                }
            } else if ((e instanceof RetryableException) && e.getMessage().contains(CommonConstant.READ_TIMED_OUT)) {
                msg = MsgCodeEnum.REQUEST_RETURN_TIME_OUT.getMsgCd();
            } else {
                msg = MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd();
                logger.error(msg, e);
            }
            BusinessException.throwBusinessException(msg);
        }
        return contractApplyWithholdBO;
    }

    @Override
    public WxPrePayBO wxPrePayNotifi(WxPrePayBO wxPrePayBO) {

        if (StringUtils.isBlank(wxPrePayBO.getContractCode())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_ORDER_NO_CANNOT_BE_EMPTY);
        }
        //根据签约号查询签约信息
        ContractWithholdBO contractWithholdBO = new ContractWithholdBO();
        contractWithholdBO.setContractCode(wxPrePayBO.getContractCode());
        ContractWithholdDO contractWithholdDO = iExtContractWithholdService.findByContractCode(contractWithholdBO);
        if (JudgeUtils.isNotNull(contractWithholdDO)) {
            if (JudgeUtils.isBlank(contractWithholdDO.getContractId())) {
                BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_NOT_EXISTS);
            }
        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_NOT_EXIST);
        }

        try {
            WxPrePayNotifyRequest wxPrePayNotifyRequest = new WxPrePayNotifyRequest();
            WxPrePayNotifyRequest.EstimatedAmount estimatedAmount = new WxPrePayNotifyRequest.EstimatedAmount();
            wxPrePayNotifyRequest.setContractId(contractWithholdDO.getContractId());
            wxPrePayNotifyRequest.setMchid(contractWithholdDO.getBankMerchantNo());
            //以分为单位
            estimatedAmount.setAmount(wxPrePayBO.getAmount().multiply(new BigDecimal(CommonConstant.AMOUNT_SCALE)).intValue());
            estimatedAmount.setCurrency(CommonConstant.CURRENCY);
            wxPrePayNotifyRequest.setEstimatedAmount(estimatedAmount);
            Request request = new Request();
            request.setRequestId(UUID.randomUUID().toString());
            request.setSource(WXPayChannel.CONTRACT_PREPAY_NOTITY.getSource());
            request.setRoute(WXPayChannel.CONTRACT_PREPAY_NOTITY.getRoute());
            request.setBusiType(WXPayChannel.CONTRACT_PREPAY_NOTITY.getBusType());
            request.setTarget(wxPrePayNotifyRequest);
            GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

            if (JudgeUtils.isNotSuccess(genericRspDTO)) {
                BusinessException.throwBusinessException(MsgCodeEnum.WX_PREPAY_NOTITY_FAIL);
            }
            WxPrePayNotifyResponse wxPrePayNotifyResponse = (WxPrePayNotifyResponse) genericRspDTO.getBody().getResult();
            if (!StringUtils.equals(wxPrePayNotifyResponse.getReturnCode(), WeChatCommonResponseEnum.ReturnCode.SUCCESS.name())) {
                BusinessException.throwBusinessException(MsgCodeEnum.WX_PREPAY_NOTITY_FAIL);
            }
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgCodeEnum.WX_PREPAY_NOTITY_FAIL);
        }
        return wxPrePayBO;
    }

    /**
     * 输入不为空检查
     *
     * @param contractPaymentBO
     */
    private void inputCannotEmptyCheck(ContractApplyWithholdBO contractPaymentBO) {
        if (JudgeUtils.isBlank(contractPaymentBO.getMerchantId())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getOutTradeNo())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ORDER_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getSubject())) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_SUBJECT_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getTradeDate())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_REQUEST_DATE_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isNull(contractPaymentBO.getTotalAmount())) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_AMOUNT_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getNotifyUrl())) {
            BusinessException.throwBusinessException(MsgCodeEnum.NOTIFY_URL_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getContractCode())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_ORDER_NO_CANNOT_BE_EMPTY);
        }
//       当支付方式是alipay，promoParams参数json校验
        if (JudgeUtils.equalsIgnoreCase(contractPaymentBO.getContractWay(), PaymentWayEnum.ALIPAY.name())) {
            if (JudgeUtils.isNotBlank(contractPaymentBO.getPromoParams())) {
//                 如果不是json格式，报错
                if (!PaymentUtils.isJSON2(contractPaymentBO.getPromoParams())) {
                    BusinessException.throwBusinessException(MsgCodeEnum.ALIPAY_PROMO_PARAMS_ONLY_JSON);
                }
            }
        }
    }

    /**
     * 支付处理
     *
     * @param contractPaymentBO
     */
    private void paymentProcess(ContractApplyWithholdBO contractPaymentBO) {
        // 签约扣款参数校验
        payParamsCheck(contractPaymentBO);
        contractPaymentBO.setContractWay(contractPaymentBO.getContractWay().toLowerCase());
        contractPaymentBO.setScene(PaymentSceneEnum.CONTRACTPAY.name().toLowerCase());
        contractPaymentBO.setPayWay(contractPaymentBO.getContractWay().toLowerCase());
        contractPaymentBO.setRealAmount(contractPaymentBO.getTotalAmount());
        contractPaymentBO.setDiscountableAmount(BigDecimal.ZERO);

        ContractWithholdBO contractWithholdBO = new ContractWithholdBO();
        contractWithholdBO.setContractCode(contractPaymentBO.getContractCode());
        ContractWithholdDO contractWithholdDO = new ContractWithholdDO();
        // 和包免密签约，带商户号，避免不同商户也能发起扣款
        if (JudgeUtils.equalsIgnoreCase(contractPaymentBO.getContractWay(), PaymentWayEnum.CMPAY.name())) {
            contractWithholdBO.setMerchantId(contractPaymentBO.getMerchantId());
            contractWithholdDO = contractWithholdService.findByCodeAndMerchantNo(contractWithholdBO);
        } else {
            contractWithholdDO = contractWithholdService.findByContractCode(contractWithholdBO);
        }
        if (JudgeUtils.isNull(contractWithholdDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_NOT_EXISTS);
        }
        if (!JudgeUtils.equals(contractWithholdDO.getContractStatus(), ContractStatusEnum.CONTRACT_SUCCESS.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_STATUS_UNDEFINED);
        }

        TradeOrderDO tradeOrderDO = new TradeOrderDO();
        tradeOrderDO.setMerchantNo(contractPaymentBO.getMerchantId());
        tradeOrderDO.setOutTradeNo(contractPaymentBO.getOutTradeNo());
        tradeOrderDO.setRequestDate(contractPaymentBO.getTradeDate());
        if (JudgeUtils.isNotNull(payOrderService.load(tradeOrderDO))) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_ALREADY_EXISTS);
        }
        PayServiceBeanBO rateBO = new PayServiceBeanBO();
        rateBO.setMerchantNo(contractPaymentBO.getMerchantId());
        rateBO.setPaymentChannl(contractPaymentBO.getContractWay());
        rateBO.setOrderScene(contractPaymentBO.getScene());
        rateBO.setOrderAmount(contractPaymentBO.getRealAmount());
        rateBO.setProvinceCode(JudgeUtils.isBlank(contractPaymentBO.getProvinceCode()) ?
                StringUtils.substring(contractPaymentBO.getOutTradeNo(), 0, 4) : contractWithholdBO.getProvinceCode());
        rateBO.setOrderDate(DateTimeUtils.getCurrentDateStr());
        rateBO = rateService.findPaymentRout(rateBO);
        if (JudgeUtils.isNull(rateBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ROUT_CANNOT_VALIDITY);
        }
        contractPaymentBO.setOrderRate(rateBO.getRate());
        contractPaymentBO.setSignType(rateBO.getSignMethod());
        contractPaymentBO.setPaymentType(rateBO.getBusinessType());
        contractPaymentBO.setPaymentId(rateBO.getBankMerchantNo());
        contractPaymentBO.setPaymentRout(rateBO.getPaymentRout());
        contractPaymentBO.setTradeOrderNo(contractPaymentBO.getOutTradeNo());
        contractPaymentBO.setTradeTime(DateTimeUtils.getCurrentTimeStr());
        contractPaymentBO.setMobileNo(contractWithholdDO.getMobileNo());
        if (JudgeUtils.isBlank(contractPaymentBO.getAppId())) {
            contractPaymentBO.setAppId(weChatProperties.getAppid());
        }
        contractPaymentBO.setContractId(contractWithholdDO.getContractId());
        contractPaymentBO.setPlanId(contractWithholdDO.getPlanId());
        //校验请求时间
        checkRequestTime(contractPaymentBO);
        PayOrderBO payOrderBO = new PayOrderBO();
        BeanUtils.copyProperties(payOrderBO, contractPaymentBO);
        payOrderBO.setServiceCharge(rateBO.getServiceCharge());
        payOrderService.insertByNewTranscation(payOrderBO);
        contractPaymentBO.setOrderDate(payOrderBO.getOrderDate());
        contractPayClient.asyncContractApply(contractPaymentBO);
    }

    /**
     * 签约扣款参数校验
     *
     * @param contractPaymentBO
     */
    private void payParamsCheck(ContractApplyWithholdBO contractPaymentBO) {
        //输入不为空检查
        inputCannotEmptyCheck(contractPaymentBO);
        // 代扣业务类型校验
        if (!checkDebitBusType(contractPaymentBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.DEBIT_BUS_TYPE_ERROR);
        }
    }

    /**
     * 代扣业务类型校验
     *
     * @param contractPaymentBO
     * @return true：通过校验，false：未通过校验
     */
    private boolean checkDebitBusType(ContractApplyWithholdBO contractPaymentBO) {
        String debitBusType = contractPaymentBO.getDebitBusType();
        if (JudgeUtils.isBlank(debitBusType)) {
            // 未传代扣业务类型，不校验
            return true;
        }
        // 查询传入的代扣业务类型是否有配置
        String debitBusTypeMapping = paramInfoService.getDebitBusType(debitBusType);
        if (JudgeUtils.isBlank(debitBusTypeMapping)) {
            //没配置
            return false;
        }
        // 有配置就记录代扣业务类型的映射值
        contractPaymentBO.setDebitBusTypeMapping(debitBusTypeMapping);
        return true;
    }

    /**
     * 校验请求时间是否在允许的时间范围内
     * @param contractPaymentBO
     */
    private void checkRequestTime(ContractApplyWithholdBO contractPaymentBO) {
        //设置查询条件
        JkThirdPayParamBO thirdPayParam = getThirdPayParam(contractPaymentBO);
        // 查询该场景下允许的时间范围是多少
        thirdPayParam = thirdPayParamService.querySignPayTimeRange(thirdPayParam);
        String timeRange = thirdPayParam.getParamValue();
        // 验证是否在允许的时间范围内
        if (!DateUtils.checkTimeExceedRange(timeRange)) {
            // 记录允许扣款的时间范围
            contractPaymentBO.setSignPayTimeRange(new String[]{DateUtils.timeRangeFormatter(timeRange)});
            BusinessException.throwBusinessException(MsgCodeEnum.SIGN_PAY_TIME_ERROR);
        }

    }

    /**
     * 根据签约扣款信息获取查询第三方支付机构参数的条件对象
     *
     * @param contractPaymentBO
     * @return
     */
    private JkThirdPayParamBO getThirdPayParam(ContractApplyWithholdBO contractPaymentBO) {
        JkThirdPayParamBO jkThirdPayParamBO = new JkThirdPayParamBO();
        jkThirdPayParamBO.setAimProductCode(contractPaymentBO.getPaymentRout());
        jkThirdPayParamBO.setPayProductCode(contractPaymentBO.getPayWay());
        jkThirdPayParamBO.setPayWayCode(contractPaymentBO.getScene());
        return jkThirdPayParamBO;
    }

}