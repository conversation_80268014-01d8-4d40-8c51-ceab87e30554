package com.cmpay.payment.service.notify.impl;

import com.cmpay.payment.bo.notify.AlipayNotifyBO;
import com.cmpay.payment.bo.withhold.ContractQueryBO;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.constant.TradeTypeEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.constant.contract.ContractNofityTypeEnum;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.IContractSynService;
import com.cmpay.payment.service.notify.IAlipayWithholdNotifyService;
import com.cmpay.payment.util.PaymentUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/14 13:50
 */
@Service
@Slf4j
public class AlipayWithholdNotifyServiceImpl implements IAlipayWithholdNotifyService {
    @Autowired
    private AsynCommonService asynCommonService;
    @Autowired
    private IContractSynService contractSynService;

    @Override
    public void notifyAlipayContract(AlipayNotifyBO alipayNotifyBO) {
        ContractQueryBO contractQueryBO = new ContractQueryBO();
        contractQueryBO.setContractCode(alipayNotifyBO.getExternal_agreement_no());
        contractQueryBO.setContractId(alipayNotifyBO.getAgreement_no());
        contractQueryBO.setChangeType(TradeTypeEnum.ADD.name());
        contractQueryBO.setOperateTime(PaymentUtils.getStringDate(alipayNotifyBO.getValid_time()));
        contractQueryBO.setContractSignedTime(PaymentUtils.getStringDate(alipayNotifyBO.getSign_time()));
        contractQueryBO.setContractExpiredTime(PaymentUtils.getStringDate(alipayNotifyBO.getInvalid_time()));
        contractQueryBO.setPayingAgencyUserId(alipayNotifyBO.getAlipay_user_id());
        contractQueryBO.setPersonalProductCode(alipayNotifyBO.getPersonal_product_code());
        contractQueryBO.setOpenid(alipayNotifyBO.getAlipay_user_id());
        ContractWithholdBO resultBO = contractSynService.contractNotify(contractQueryBO);
        if (StringUtils.equals(ContractStatusEnum.CONTRACT_SUCCESS.name(), resultBO.getContractStatus())) {
            asynCommonService.asyncContractNotify(resultBO);
        }
    }

    @Override
    public void notifyAlipayContractDelete(AlipayNotifyBO alipayNotifyBO) {
        ContractQueryBO contractQueryBO = new ContractQueryBO();
        contractQueryBO.setContractCode(alipayNotifyBO.getExternal_agreement_no());
        contractQueryBO.setContractId(alipayNotifyBO.getAgreement_no());
        contractQueryBO.setContractTerminatedTime(PaymentUtils.getStringDate(alipayNotifyBO.getUnsign_time()));
        contractQueryBO.setContractTerminationMode(ContractConstants.DELETE_TYPE);
        contractQueryBO.setChangeType(TradeTypeEnum.DELETE.name());
        ContractWithholdBO resultBO  = contractSynService.contractDeleteNotify(contractQueryBO);
        if (StringUtils.equals(ContractStatusEnum.CONTRACT_TERMINATED.name(), resultBO.getContractStatus())) {
            asynCommonService.asyncContractNotify(resultBO);
        }
    }


}
