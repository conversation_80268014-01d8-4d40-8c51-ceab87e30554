package com.cmpay.payment.service.contract.listener;

import com.cmpay.payment.bo.withhold.ContractApplyWithholdBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.contract.ContractChannelService;
import com.cmpay.payment.service.contract.ContractListenerService;
import com.cmpay.payment.service.contract.channel.alipay.AlipayContractApplyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class AlipayContractApplyWithHoldListener extends ContractListenerService<ContractApplyWithholdBO> {

    @Autowired
    ApplicationContext applicationContext;

    /**
     * 申请扣款
     *
     * @param contractApplyWithholdBO
     */
    @EventListener
    public void handleAlipayOrderQuery(ContractApplyWithholdBO contractApplyWithholdBO) {
        execute(contractApplyWithholdBO);
    }

    @Override
    protected boolean checkChannelExecutable(ContractApplyWithholdBO contractApplyWithholdBO) {
        return Optional.ofNullable(contractApplyWithholdBO)
                .map(ContractApplyWithholdBO::getContractWay)
                .filter(contractWay -> StringUtils.equals(PaymentWayEnum.ALIPAY.name().toLowerCase(), contractWay))
                .isPresent();
    }

    @Override
    protected ContractChannelService determinateChannelExecuteBean(ContractApplyWithholdBO contractApplyWithholdBO) {
        return getBean(AlipayContractApplyService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
