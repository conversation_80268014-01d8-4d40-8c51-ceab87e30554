package com.cmpay.payment.service.contract.channel.alipay;

import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.service.contract.ContractChannelService;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface AlipayContractService extends ContractChannelService<ContractWithholdBO> {
    /**
     * 支付宝签约
     * @param contractWithholdBO
     */
    void contract(ContractWithholdBO contractWithholdBO);
}

