package com.cmpay.payment.service.contract.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.withhold.ContractApplyWithholdBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.CmpayConstants;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.dto.cmpay.*;
import com.cmpay.payment.entity.config.ContractDO;
import com.cmpay.payment.service.contract.channel.cmpay.CmpayContractApplyService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.service.ext.config.IExtContractService;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmpayContractApplyServiceImpl implements CmpayContractApplyService {

    private static final Logger logger = LoggerFactory.getLogger(CmpayContractApplyServiceImpl.class);

    @Value("${cmpay.backendNotifyUrl:}")
    private String backendNotifyUrl;
    @Autowired
    private IExtContractService contractService;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;

    @Override
    public void contractApply(ContractApplyWithholdBO contractApplyWithholdBO) {
        try {
            ContractDO contract = contractService.getContract(contractApplyWithholdBO.getPaymentId());
            CmpayOnlineContractPayReqDTO cmpayOnlineContractPayReqDTO=new CmpayOnlineContractPayReqDTO();
            //和包商户号
            cmpayOnlineContractPayReqDTO.setMerchantId(contractApplyWithholdBO.getPaymentId());
            cmpayOnlineContractPayReqDTO.setSignType(contractApplyWithholdBO.getSignType());
            cmpayOnlineContractPayReqDTO.setType(CommonConstant.CMPAY_ONLINE_CONTRACT_PAY_TYPE);
            cmpayOnlineContractPayReqDTO.setVersion(CommonConstant.CMPAY_ONLINE_CONTRACT_PAY_VERSION);
            cmpayOnlineContractPayReqDTO.setMerchantCert(contract.getPublicKey());
            cmpayOnlineContractPayReqDTO.setHmac(contract.getSecretKey());

            String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(host)) {
                try {
                    cmpayOnlineContractPayReqDTO.setNotifyUrl(UrlUtils.replaceDomainOrIp(backendNotifyUrl,host));
                } catch (Exception e) {
                    logger.info("通知地址域名获取异常");
                    cmpayOnlineContractPayReqDTO.setNotifyUrl(backendNotifyUrl);
                }
            } else {
                cmpayOnlineContractPayReqDTO.setNotifyUrl(backendNotifyUrl);
            }

            cmpayOnlineContractPayReqDTO.setRequestId(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.CMPAY_JRN_NO, 18));
            cmpayOnlineContractPayReqDTO.setMobileNo(contractApplyWithholdBO.getMobileNo());
            cmpayOnlineContractPayReqDTO.setAmount(contractApplyWithholdBO.getTotalAmount().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_DOWN));
            cmpayOnlineContractPayReqDTO.setCurrency(CommonConstant.CMPAY_CURRENCY);
            cmpayOnlineContractPayReqDTO.setOrderDate(contractApplyWithholdBO.getOrderDate());
            cmpayOnlineContractPayReqDTO.setOrderId(contractApplyWithholdBO.getTradeOrderNo());
            //商户发起请求的会计日期; 年年年年月月日日
            cmpayOnlineContractPayReqDTO.setMerAcDate(DateTimeUtils.getCurrentDateStr());
            //和包回复：有效期 是支付的时候检查间隔的 下单加支付是一个流程 你默认送个30分钟也不会有问题
            cmpayOnlineContractPayReqDTO.setPeriod(30);
            cmpayOnlineContractPayReqDTO.setPeriodUnit("00");
            cmpayOnlineContractPayReqDTO.setProductName(contractApplyWithholdBO.getProductName());

            Request request = new Request();
            request.setRequestId(LemonUtils.getRequestId());
            request.setRoute(CmpayPayChannelEnum.CMPAY_ONLINE);
            request.setBusiType(CmpayPayChannelEnum.CMPAY_ONLINE_CONTRACT_PAY.getName());
            request.setSource(CmpayPayChannelEnum.CMPAY_ONLINE);
            request.setTarget(cmpayOnlineContractPayReqDTO);
            GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            if (JudgeUtils.isNotSuccess(genericRspDTO)) {
                BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_CONTRACT_PAY_FAIL);
            }
            Optional.of(genericRspDTO)
                    .map(GenericRspDTO::getBody)
                    .map(Response::getResult)
                    .map(result -> {
                        handleOnlineContractPay((CmpayOnlineContractPayRspDTO) result);
                        return result;
                    }).orElseThrow(() ->new BusinessException(MsgCodeEnum.CMPAY_CONTRACT_PAY_FAIL));
        } catch (Exception e) {
            throw e;
        };

    }


    @Override
    public void send(ContractApplyWithholdBO contractApplyWithholdBO) {
        contractApply(contractApplyWithholdBO);
    }

    private void handleOnlineContractPay(CmpayOnlineContractPayRspDTO result) {
        if(!StringUtils.equals(CmpayConstants.RETURN_CODE, result.getReturnCode())){
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_CONTRACT_PAY_FAIL);
        }
    }

}
