package com.cmpay.payment.service.contract.channel.alipay.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.AlipayAgreementSignParamsBO;
import com.cmpay.payment.bo.AlipayBizContentBO;
import com.cmpay.payment.bo.withhold.ContractPaymentWithholdBO;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.properties.AlipayProperties;
import com.cmpay.payment.service.contract.channel.alipay.AlipayPaymentContractService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.AlipaySDKOrderInfoUtils;
import com.cmpay.payment.utils.AlipaySignUtils;
import com.cmpay.payment.util.PaymentUtils;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class AlipayPaymentContractServiceImpl implements AlipayPaymentContractService {
    private static final Logger logger = LoggerFactory.getLogger(AlipayPaymentContractServiceImpl.class);
    @Autowired
    private AlipayProperties alipayProperties;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Override
    public void paymentContract(ContractPaymentWithholdBO contractPaymentWithholdBO) {
        AlipayBizContentBO alipayBizContentBO = new AlipayBizContentBO();

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        String notifyUrl = alipayProperties.getNotifyUrl();
        if (StringUtils.isNotBlank(host)) {
            try {
                notifyUrl = UrlUtils.replaceDomainOrIp(alipayProperties.getNotifyUrl(),host);
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                notifyUrl = alipayProperties.getNotifyUrl();
            }
        }

        alipayBizContentBO.setOut_trade_no(contractPaymentWithholdBO.getTradeOrderNo());
        alipayBizContentBO.setSubject(contractPaymentWithholdBO.getSubject());
        alipayBizContentBO.setTotal_amount(contractPaymentWithholdBO.getTotalAmount());
        alipayBizContentBO.setTimeout_express(contractPaymentWithholdBO.getTimeoutExpress());

        //签约参数类型
        AlipayAgreementSignParamsBO alipayAgreementSignParamsBO = new AlipayAgreementSignParamsBO();
        alipayAgreementSignParamsBO.setPersonal_product_code(contractPaymentWithholdBO.getPlanId());
        alipayAgreementSignParamsBO.setSign_scene(ContractConstants.INDUSTRY_DIGITAL_MEDIA);
        alipayAgreementSignParamsBO.setExternal_agreement_no(contractPaymentWithholdBO.getContractCode());
        alipayAgreementSignParamsBO.setExternal_logon_id(contractPaymentWithholdBO.getContractDisplayAccount());

        //设置签约场景，钱包H5页面签约
        JSONObject jsChannel = new JSONObject();
        jsChannel.put("channel", ContractConstants.ALIPAYAPP);
        alipayAgreementSignParamsBO.setAccess_params(jsChannel);
        //签约子商户类型
        JSONObject jsSubMerchant = new JSONObject();
        if (StringUtils.isNotBlank(contractPaymentWithholdBO.getSubMerchantName())) {
            jsSubMerchant.put("sub_merchant_name", contractPaymentWithholdBO.getSubMerchantName());
        }
        if (StringUtils.isNotBlank(contractPaymentWithholdBO.getSubMerchantServiceName())) {
            jsSubMerchant.put("sub_merchant_service_name", contractPaymentWithholdBO.getSubMerchantServiceName());
        }
        if (StringUtils.isNoneBlank(contractPaymentWithholdBO.getSubMerchantServiceDescription())) {
            jsSubMerchant.put("sub_merchant_service_description", contractPaymentWithholdBO.getSubMerchantServiceDescription());
        }
        if(JudgeUtils.isNotEmpty(jsSubMerchant)){
            alipayAgreementSignParamsBO.setSub_merchant(jsSubMerchant);
        }
        if (JudgeUtils.isNotEmpty(contractPaymentWithholdBO.getPeriodType())) {
            JSONObject jsonPeriod = new JSONObject();
            jsonPeriod.put("period_type", contractPaymentWithholdBO.getPeriodType());
            jsonPeriod.put("period", contractPaymentWithholdBO.getPeriod());
            jsonPeriod.put("execute_time", PaymentUtils.getDateString(contractPaymentWithholdBO.getExecuteTime()));
            jsonPeriod.put("single_amount", contractPaymentWithholdBO.getSingleAmount());
            alipayAgreementSignParamsBO.setPeriod_rule_params(jsonPeriod);
        }
        alipayBizContentBO.setAgreement_sign_params(new Gson().toJson(alipayAgreementSignParamsBO));
        Map<String, String> params = AlipaySDKOrderInfoUtils.buildOrderParamMap(alipayProperties.getAppid(),
                new Gson().toJson(alipayBizContentBO), AlipaySignUtils.AlipaySignTypeEnum.RSA2.name(), alipayProperties.getVersion(), notifyUrl);
        String orderParams = AlipaySDKOrderInfoUtils.buildOrderParam(params);
        String sign = AlipaySDKOrderInfoUtils.getSign(params, alipayProperties.getRsa2Private(), true);
        final String orderInfo = orderParams + "&" + sign;
        contractPaymentWithholdBO.setPayUrl(orderInfo);
    }

    @Override
    public void send(ContractPaymentWithholdBO contractPaymentWithholdBO) {
        paymentContract(contractPaymentWithholdBO);
    }
}
