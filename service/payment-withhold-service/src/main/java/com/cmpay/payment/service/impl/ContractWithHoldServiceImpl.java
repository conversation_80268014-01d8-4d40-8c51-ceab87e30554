package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.config.PayServiceBeanBO;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.constant.contract.TypeContractCheckUtils;
import com.cmpay.payment.entity.withhold.ContractWithholdDO;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.IContractWithHoldService;
import com.cmpay.payment.service.ext.config.IExtRateService;
import com.cmpay.payment.service.ext.withhold.IExtContractWithholdService;
import feign.RetryableException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
public class ContractWithHoldServiceImpl implements IContractWithHoldService {

    private static final Logger logger = LoggerFactory.getLogger(ContractWithHoldServiceImpl.class);
    @Autowired
    private IExtContractWithholdService contractWithholdService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    IExtRateService rateService;

    /**
     * 签约
     *
     * @param contractWithholdBO
     * @return
     */
    @Override
    public ContractWithholdBO contract(ContractWithholdBO contractWithholdBO) {
        contractInfoCheck(contractWithholdBO);
        if (JudgeUtils.isNull(contractWithholdBO.getAppId())
                && JudgeUtils.equals(contractWithholdBO.getContractWay(), PaymentWayEnum.WECHAT.name().toLowerCase())) {
            contractWithholdBO.setAppId(weChatProperties.getAppid());
        }
        //查询路由，获取和包商户号
        PayServiceBeanBO rateBO = new PayServiceBeanBO();
        rateBO.setMerchantNo(contractWithholdBO.getMerchantId());
        rateBO.setPaymentChannl(contractWithholdBO.getContractWay());
        rateBO.setOrderScene(contractWithholdBO.getScene());
        rateBO.setOrderAmount(null);
        rateBO.setProvinceCode(JudgeUtils.isBlank(contractWithholdBO.getProvinceCode()) ?
                StringUtils.substring(contractWithholdBO.getContractCode(), 0, 4) : contractWithholdBO.getProvinceCode());
        rateBO.setOrderDate(DateTimeUtils.getCurrentDateStr());
        rateBO = rateService.findPaymentRout(rateBO);
        if (JudgeUtils.isNull(rateBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ROUT_CANNOT_VALIDITY);
        }
        contractWithholdBO.setBankMerchantNo(rateBO.getBankMerchantNo());
        contractWithholdBO.setSignType(rateBO.getSignMethod());
        contractWithholdBO.setHmac(rateBO.getSecureValue());
        ContractWithholdDO contractWithholdDO = contractWithholdService.findByContractCode(contractWithholdBO);
        if (JudgeUtils.isNotNull(contractWithholdDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_ALREADY_EXISTS);
        }
        //和包通过手机号查，同一手机号只能在同一个和包商户下签约
        if (StringUtils.equalsIgnoreCase(PaymentWayEnum.CMPAY.name(), contractWithholdBO.getContractWay())) {
            //查询这个手机号，在这个和包商户下，签约状态还为成功、签约中（未解约） 的签约信息
            ContractWithholdDO cmpayContractWithholdDO = contractWithholdService.findOpeningContract(contractWithholdBO);
            if (JudgeUtils.isNotNull(cmpayContractWithholdDO)) {
                if(StringUtils.equals(cmpayContractWithholdDO.getContractStatus(), ContractStatusEnum.CONTRACT_SUCCESS.name())){
                    BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_MOBILE_ALREADY_EXISTS);
                //如果还有等待状态的记录，前一笔置为失败
                }else{
                    cmpayContractWithholdDO.setContractStatus(ContractStatusEnum.CONTRACT_FAIL.getDesc());
                    contractWithholdService.update(cmpayContractWithholdDO);
                }
            }
        }
        contractDeal(contractWithholdBO);
        return contractWithholdBO;
    }

    /**
     * 签约申请
     *
     * @param contractWithholdBO
     * @return
     */
    private ContractWithholdBO contractDeal(ContractWithholdBO contractWithholdBO) {
        contractWithholdService.insertContractInfo(contractWithholdBO);
        try {
            applicationContext.publishEvent(contractWithholdBO);
            logger.info("request success");
        } catch (Exception e) {
            String msg;
            if (e instanceof BusinessException) {
                msg = ((BusinessException) e).getMsgCd();
            } else if ((e instanceof RetryableException)&&e.getMessage().contains(CommonConstant.READ_TIMED_OUT)) {
                msg = MsgCodeEnum.REQUEST_RETURN_TIME_OUT.getMsgCd();
            } else {
                msg = MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd();
                logger.error(msg, e);
            }
            BusinessException.throwBusinessException(msg);
        }
        return contractWithholdBO;
    }

    /**
     * 参数检查
     *
     * @param contractWithholdBO
     */
    private void contractInfoCheck(ContractWithholdBO contractWithholdBO) {
        if (JudgeUtils.isBlank(contractWithholdBO.getMerchantId())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractWithholdBO.getContractWay())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_WAY_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.equals(contractWithholdBO.getContractWay(),PaymentWayEnum.CMPAY.name().toLowerCase()) &&
                JudgeUtils.isBlank(contractWithholdBO.getMobileNo())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MOBILE_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractWithholdBO.getScene())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_SCENE_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractWithholdBO.getContractCode())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_ORDER_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractWithholdBO.getContractDate())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_REQUEST_DATE_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractWithholdBO.getNotifyUrl())) {
            BusinessException.throwBusinessException(MsgCodeEnum.NOTIFY_URL_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.notEquals(contractWithholdBO.getContractWay(),PaymentWayEnum.CMPAY.name().toLowerCase())) {
            if (JudgeUtils.isBlank(contractWithholdBO.getPlanId())) {
                BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_PLANID_CANNOT_BE_EMPTY);
            }
            if (JudgeUtils.isBlank(contractWithholdBO.getContractDisplayAccount())) {
                BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_USERINFO_CANNOT_BE_EMPTY);
            }
            if (JudgeUtils.equals(contractWithholdBO.getPlanId(), ContractConstants.CYCLE_PAY_AUTH_P)) {
                if (JudgeUtils.isBlankAny(contractWithholdBO.getPeriodType(), contractWithholdBO .getPeriod(),
                        contractWithholdBO.getExecuteTime(), contractWithholdBO.getSingleAmount().toString())) {
                    BusinessException.throwBusinessException(MsgCodeEnum.CYCLE_CONTRACT_STATUS_ERROR);
                }
            }
        }
        if(JudgeUtils.equals(contractWithholdBO.getContractWay(),PaymentWayEnum.CMPAY.name().toLowerCase())){
            if(JudgeUtils.isBlank(contractWithholdBO.getFrontNotifyUrl())){
                BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_NOTIFY_URL_CANNOT_BE_EMPTY);
            }
            // 生产签约表planId不能为空，故设置默认值
            if (JudgeUtils.isBlank(contractWithholdBO.getPlanId())) {
                contractWithholdBO.setPlanId(CommonConstant.CMPAY_MM);
            }
        }
        if (TypeContractCheckUtils.checkContractWay(PaymentWayEnum.valueOf(contractWithholdBO.getContractWay().toUpperCase()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_WAY_ERROR);
        }
        if (TypeContractCheckUtils.checkContractScene(PaymentSceneEnum.valueOf(contractWithholdBO.getScene().toUpperCase()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_SCENE_ERROR);
        }
    }
}
