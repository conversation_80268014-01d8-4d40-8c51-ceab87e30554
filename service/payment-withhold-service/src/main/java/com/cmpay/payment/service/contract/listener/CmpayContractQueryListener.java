package com.cmpay.payment.service.contract.listener;

import com.cmpay.payment.bo.withhold.ContractQueryBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.contract.ContractChannelService;
import com.cmpay.payment.service.contract.ContractListenerService;
import com.cmpay.payment.service.contract.channel.cmpay.CmpayContractQueryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * @date 2022-08-09 10:48
 * <AUTHOR>
 * @Version 1.0
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmpayContractQueryListener extends ContractListenerService<ContractQueryBO> {

    @Autowired
    ApplicationContext applicationContext;

    /**
     * 和包免免密查询订单
     *
     * @param contractQueryBO
     */
    @EventListener
    public void handleCmpayContractQuery(ContractQueryBO contractQueryBO) {
        execute(contractQueryBO);
    }


    @Override
    protected boolean checkChannelExecutable(ContractQueryBO contractQueryBO) {
        return Optional.ofNullable(contractQueryBO)
                .map(ContractQueryBO::getContractWay)
                .filter(contractWay -> StringUtils.equals(PaymentWayEnum.CMPAY.name().toLowerCase(), contractWay))
                .isPresent();
    }

    @Override
    protected ContractChannelService determinateChannelExecuteBean(ContractQueryBO contractQueryBO) {
        return getBean(CmpayContractQueryService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
