package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.data.OrderFeeBO;
import com.cmpay.payment.bo.withhold.ContractDeleteBO;
import com.cmpay.payment.bo.withhold.ContractQueryBO;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.bo.withhold.ContractWithholdQueryBO;
import com.cmpay.payment.constant.CmpayConstants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.TradeTypeEnum;
import com.cmpay.payment.constant.contract.ContractChangeTypeEnum;
import com.cmpay.payment.constant.contract.ContractNofityTypeEnum;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.entity.withhold.ContractDeleteDO;
import com.cmpay.payment.entity.withhold.ContractWithholdDO;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.IContractSynService;
import com.cmpay.payment.service.data.IDataFeeService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.withhold.IExtContractDeleteService;
import com.cmpay.payment.service.ext.withhold.IExtContractWithholdService;
import com.cmpay.payment.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class ContractSynServiceImpl implements IContractSynService {

    private static final Logger logger = LoggerFactory.getLogger(ContractSynServiceImpl.class);

    @Autowired
    private IExtContractWithholdService contractWithholdService;
    @Autowired
    private IExtContractDeleteService contractDeleteService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IDataFeeService dataFeeService;
    @Autowired
    private ExtPayOrderService paymentOrderService;
    @Autowired
    private AsynCommonService asynCommonService;

    /**
     * 代扣签约结果查询
     *
     * @param contractQueryBO
     * @return
     */
    @Override
    public void contractQuery(ContractQueryBO contractQueryBO) {
        logger.info("contractQueryBO :{} before", contractQueryBO.toString());
        //签约查询标识，和包签约查询需要手机号,和包商户号,通过属性拷贝拷贝过来
        contractQueryBO.setContractQuerySign(CmpayConstants.CONTRACT_SIGN_OPEN);

        applicationContext.publishEvent(contractQueryBO);
        logger.info("contractQueryBO :{} after", contractQueryBO.toString());
        ContractWithholdDO contractWithholdDO = contractQueryBO.getContractWithholdDO();
        if (!JudgeUtils.equals(contractQueryBO.getContractStatus(), ContractStatusEnum.CONTRACT_SUCCESS.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_STATUS_UNDEFINED);
        }
        contractWithholdDO.setContractId(contractQueryBO.getContractId());
        contractWithholdDO.setDisplayAccount(contractQueryBO.getContractDisplayAccount());
        contractWithholdDO.setSignedTime(contractQueryBO.getContractSignedTime());
        contractWithholdDO.setExpiredTime(contractQueryBO.getContractExpiredTime());
        contractWithholdDO.setOpenid(contractQueryBO.getOpenid());
        contractWithholdDO.setContractStatus(contractQueryBO.getContractStatus());
        contractWithholdDO.setChangeType(contractQueryBO.getChangeType());
        contractWithholdDO.setOperateTime(contractQueryBO.getContractSignedTime());
        contractWithholdService.updateContractInfo(contractWithholdDO);

        if (JudgeUtils.equals(contractQueryBO.getContractStatus(), ContractStatusEnum.CONTRACT_SUCCESS.name())) {
            TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
            tradeNotifyBO.setMerchantNo(contractWithholdDO.getMerchantNo());
            tradeNotifyBO.setTradeOrderNo(contractQueryBO.getContractCode());
            tradeNotifyBO.setOutOrderNo(contractQueryBO.getContractCode());
            tradeNotifyBO.setTradeDate(contractQueryBO.getContractWithholdDO().getContractDate());
            tradeNotifyBO.setNotifyType(ContractNofityTypeEnum.ADD.name().toLowerCase());
            tradeNotifyBO.setNotifyUrl(contractQueryBO.getContractWithholdDO().getNotifyUrl());
            tradeNotifyBO.setExtra(contractQueryBO.getContractWithholdDO().getExtra());
            tradeNotifyBO.setFinishDate(contractQueryBO.getContractSignedTime());
            asynCommonService.asyncNotify(tradeNotifyBO);
        }
    }

    /**
     * 代扣签约结果通知
     *
     * @param contractQueryBO
     * @return
     */
    @Override
    public ContractWithholdBO contractNotify(ContractQueryBO contractQueryBO) {
        logger.info("contractNotifyInfo :{}", contractQueryBO.toString());
        ContractWithholdBO queryBO = new ContractWithholdBO();
        queryBO.setContractCode(contractQueryBO.getContractCode());
        ContractWithholdDO contractWithholdDO = contractWithholdService.findByContractCode(queryBO);
        if (JudgeUtils.isNull(contractWithholdDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_NOT_EXISTS);
        }
        ContractWithholdBO resultBO = new ContractWithholdBO();
        BeanUtils.copyProperties(resultBO, contractWithholdDO);
        resultBO.setMerchantId(contractWithholdDO.getMerchantNo());
        resultBO.setContractSignedTime(contractQueryBO.getContractSignedTime());
//        已解约，不更新状态
        if (JudgeUtils.equals(contractWithholdDO.getContractStatus(), ContractStatusEnum.CONTRACT_TERMINATED.name())) {
            resultBO.setContractStatus(ContractStatusEnum.CONTRACT_SUCCESS.name());
            return resultBO;
        }
//        不为签约成功，则更新状态后返回结果集
        if (JudgeUtils.notEquals(contractWithholdDO.getContractStatus(), ContractStatusEnum.CONTRACT_SUCCESS.name())) {
            contractWithholdDO.setContractId(contractQueryBO.getContractId());
            contractWithholdDO.setSignedTime(contractQueryBO.getContractSignedTime());
            contractWithholdDO.setExpiredTime(contractQueryBO.getContractExpiredTime());
            contractWithholdDO.setOpenid(contractQueryBO.getOpenid());
            contractWithholdDO.setContractStatus(ContractStatusEnum.CONTRACT_SUCCESS.name());
            contractWithholdDO.setChangeType(contractQueryBO.getChangeType());
            contractWithholdDO.setOperateTime(contractQueryBO.getOperateTime());
            contractWithholdService.updateContractInfo(contractWithholdDO);
        }
        resultBO.setContractStatus(ContractStatusEnum.CONTRACT_SUCCESS.name());
        resultBO.setNotifyType(ContractNofityTypeEnum.ADD.name().toLowerCase());
        return resultBO;
    }

    /**
     * 解约结果查询
     *
     * @param contractQueryBO
     * @return
     */
    @Override
    public void contractDeleteQuery(ContractQueryBO contractQueryBO) {
        ////签约查询标识，和包签约查询需要手机号,和包商户号,通过属性拷贝拷贝过来
        contractQueryBO.setContractQuerySign(CmpayConstants.CONTRACT_SIGN_CLOSE);

        logger.info("contractDeleteInfo :{}", contractQueryBO.toString());
        applicationContext.publishEvent(contractQueryBO);
        ContractDeleteDO contractDeleteDO = contractQueryBO.getContractDeleteDO();
        if (!JudgeUtils.equals(contractQueryBO.getContractStatus(), ContractStatusEnum.CONTRACT_TERMINATED.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_STATUS_UNDEFINED);
        }
        ContractWithholdBO contractWithholdBO = new ContractWithholdBO();
        contractWithholdBO.setContractCode(contractDeleteDO.getContractCode());
        ContractWithholdDO contractWithholdDO = contractWithholdService.findByContractCode(contractWithholdBO);
        if (JudgeUtils.isNull(contractWithholdDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_NOT_EXISTS);
        }
        //更新解约表
        contractDeleteDO.setContractId(contractQueryBO.getContractId());
        contractDeleteDO.setOpenid(contractQueryBO.getOpenid());
        contractDeleteDO.setDisplayAccount(contractQueryBO.getContractDisplayAccount());
        contractDeleteDO.setExpiredTime(contractQueryBO.getContractExpiredTime());
        contractDeleteDO.setTerminationMode(contractQueryBO.getContractTerminationMode());
        contractDeleteDO.setTerminatedTime(contractQueryBO.getContractTerminatedTime());
        contractDeleteDO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
        contractDeleteService.updateContractDeleteInfo(contractDeleteDO);
        //更新签约表
        contractWithholdDO.setContractId(contractQueryBO.getContractId());
        contractWithholdDO.setDisplayAccount(contractQueryBO.getContractDisplayAccount());
        contractWithholdDO.setExpiredTime(contractQueryBO.getContractExpiredTime());
        contractWithholdDO.setTerminationMode(contractQueryBO.getContractTerminationMode());
        contractWithholdDO.setTerminatedTime(contractQueryBO.getContractTerminatedTime());
        contractWithholdDO.setOpenid(contractQueryBO.getOpenid());
        contractWithholdDO.setChangeType(ContractChangeTypeEnum.DELETE.name());
        contractWithholdDO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
        contractWithholdService.updateContractInfo(contractWithholdDO);

        if (JudgeUtils.equals(contractQueryBO.getContractStatus(), ContractStatusEnum.CONTRACT_TERMINATED.name())) {
            TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
            tradeNotifyBO.setMerchantNo(contractDeleteDO.getMerchantNo());
            tradeNotifyBO.setTradeOrderNo(contractQueryBO.getContractCode());
            tradeNotifyBO.setOutOrderNo(contractQueryBO.getContractCode());
            tradeNotifyBO.setOutRequestNo(contractDeleteDO.getContractCode());
            tradeNotifyBO.setTradeDate(contractDeleteDO.getContractDate());
            tradeNotifyBO.setOutRequestNo(contractQueryBO.getContractCode());
            tradeNotifyBO.setNotifyType(ContractNofityTypeEnum.DELETE.name().toLowerCase());
            tradeNotifyBO.setNotifyUrl(contractDeleteDO.getNotifyUrl());
            tradeNotifyBO.setExtra(contractDeleteDO.getExtra());
            tradeNotifyBO.setFinishDate(contractQueryBO.getContractTerminatedTime());
            asynCommonService.asyncNotify(tradeNotifyBO);
        }
    }

    /**
     * 代扣解约微信、支付宝通知
     *
     * @param contractQueryBO
     * @return
     */
    @Override
    public ContractWithholdBO contractDeleteNotify(ContractQueryBO contractQueryBO) {
        logger.info("contractDeleteInfo :{}", contractQueryBO.toString());
        ContractWithholdBO contractWithholdBO = new ContractWithholdBO();
        contractWithholdBO.setContractCode(contractQueryBO.getContractCode());
        ContractWithholdDO contractWithholdDO = contractWithholdService.findByContractCode(contractWithholdBO);
        if (JudgeUtils.isNull(contractWithholdDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_NOT_EXISTS);
        }
        ContractWithholdBO resultBO = new ContractWithholdBO();
        BeanUtils.copyProperties(resultBO, contractWithholdDO);
        resultBO.setMerchantId(contractWithholdDO.getMerchantNo());
        resultBO.setContractSignedTime(contractQueryBO.getContractSignedTime());
        if (StringUtils.equals(ContractStatusEnum.CONTRACT_TERMINATED.name(), contractWithholdDO.getContractStatus())) {
            resultBO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
            return resultBO;
        }
        ContractDeleteBO contractDeleteInfoBO = new ContractDeleteBO();
        contractDeleteInfoBO.setContractCode(contractQueryBO.getContractCode());
        ContractDeleteDO contractDeleteDO = contractDeleteService.findWaitByContractCode(contractDeleteInfoBO);
        if (JudgeUtils.isNull(contractDeleteDO)) {
            ContractDeleteBO contractDeleteBO = new ContractDeleteBO();
            contractDeleteBO.setMerchantId(contractWithholdDO.getMerchantNo());
            contractDeleteBO.setContractCode(contractWithholdDO.getContractCode());
            contractDeleteBO.setBankMerchantNo(contractWithholdDO.getBankMerchantNo());
            contractDeleteBO.setContractId(contractWithholdDO.getContractId());
            contractDeleteBO.setPlanId(contractWithholdDO.getPlanId());
            contractDeleteBO.setContractDate(DateTimeUtils.getCurrentDateStr());
            contractDeleteBO.setAppId(contractWithholdDO.getAppId());
            contractDeleteBO.setContractWay(contractWithholdDO.getContractWay());
            contractDeleteBO.setScene(contractWithholdDO.getContractScene());
            contractDeleteBO.setContractDisplayAccount(contractWithholdDO.getDisplayAccount());
            contractDeleteBO.setChangeType(ContractChangeTypeEnum.DELETE.name());
            contractDeleteBO.setOperateTime(contractQueryBO.getOperateTime());
            contractDeleteBO.setContractId(contractQueryBO.getContractId());
            contractDeleteBO.setContractExpiredTime(contractQueryBO.getContractExpiredTime());
            contractDeleteBO.setContractTerminationMode(contractQueryBO.getContractTerminationMode());
            contractDeleteBO.setContractTerminatedTime(contractQueryBO.getContractTerminatedTime());
            contractDeleteBO.setOpenid(contractQueryBO.getOpenid());
            contractDeleteBO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
            contractDeleteBO.setNotifyUrl(contractWithholdDO.getDeleteNotifyUrl());
            contractDeleteDO = contractDeleteService.insertContractDeleteInfo(contractDeleteBO);
        } else {
            contractDeleteDO.setContractId(contractQueryBO.getContractId());
            contractDeleteDO.setExpiredTime(contractQueryBO.getContractExpiredTime());
            contractDeleteDO.setTerminationMode(contractQueryBO.getContractTerminationMode());
            contractDeleteDO.setTerminatedTime(contractQueryBO.getContractTerminatedTime());
            contractDeleteDO.setOpenid(contractQueryBO.getOpenid());
            contractDeleteDO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
            contractDeleteService.updateContractDeleteInfo(contractDeleteDO);
        }
        contractWithholdDO.setContractId(contractQueryBO.getContractId());
        contractWithholdDO.setDisplayAccount(contractQueryBO.getContractDisplayAccount());
        contractWithholdDO.setExpiredTime(contractQueryBO.getContractExpiredTime());
        contractWithholdDO.setTerminatedTime(contractQueryBO.getContractTerminatedTime());
        contractWithholdDO.setTerminationMode(contractQueryBO.getContractTerminationMode());
        contractWithholdDO.setOpenid(contractQueryBO.getOpenid());
        contractWithholdDO.setChangeType(contractQueryBO.getChangeType());
        contractWithholdDO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
        contractWithholdService.updateContractInfo(contractWithholdDO);
        resultBO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED.name());
        resultBO.setNotifyType(ContractNofityTypeEnum.DELETE.name().toLowerCase());
        resultBO.setNotifyUrl(contractDeleteDO.getNotifyUrl());
        resultBO.setExtra(contractDeleteDO.getExtra());
        return resultBO;

    }

    @Override
    public TradeNotifyBO contractWithholdQuery(ContractWithholdQueryBO contractWithholdQueryBO) {
        TradeOrderDO tradeOrder = contractWithholdQueryBO.getPaymentOrder();
        contractWithholdQueryBO.setContractWay(tradeOrder.getPayProductCode());
        contractWithholdQueryBO.setOutTradeNo(tradeOrder.getOutTradeNo());
        contractWithholdQueryBO.setBankMerchantNo(tradeOrder.getBankMerchantNo());
        applicationContext.publishEvent(contractWithholdQueryBO);
        if (StringUtils.equalsIgnoreCase(contractWithholdQueryBO.getPaymentStatus(), OrderStatusEnum.WAIT_PAY.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_STATUS_UNDEFINED);
        }
        tradeOrder.setStatus(contractWithholdQueryBO.getPaymentStatus());
        tradeOrder.setErrMsgInfo(contractWithholdQueryBO.getReturnMsg());
        tradeOrder.setOrderCompleteTime(contractWithholdQueryBO.getFinishDateTime());
        if (JudgeUtils.isEmpty(tradeOrder.getOrderCompleteTime())) {
            tradeOrder.setOrderCompleteTime(DateTimeUtils.getCurrentDateTimeStr());
        }
        tradeOrder.setThirdOrdNo(contractWithholdQueryBO.getPaymentOrder().getThirdOrdNo());
        // 仅支付成功设置账期
        if (JudgeUtils.equals(tradeOrder.getStatus(), OrderStatusEnum.TRADE_SUCCESS.name())) {
            tradeOrder.setAccountDate(DateUtils.verifiAndSetAccountDate(contractWithholdQueryBO.getPaymentOrder().getAccountDate()));
        }
        tradeOrder.setCrdAcTyp("2");

        //计算服务费
        OrderFeeBO orderFeeBO = new OrderFeeBO();
        orderFeeBO.setOrderAmount(tradeOrder.getRealAmount());
        orderFeeBO.setOrderRate(tradeOrder.getOrderRate());
        orderFeeBO = dataFeeService.feeCalculate(orderFeeBO);
        tradeOrder.setOrderFeeAmount(orderFeeBO.getOrderFeeAmount());
        if (0 == paymentOrderService.updatePaymentStatus(tradeOrder)) {
            return null;
        }
        asynCommonService.asynConnectThirdNo(tradeOrder);
        TradeNotifyBO tradeNotifyBO = new TradeNotifyBO();
        tradeNotifyBO.setMerchantNo(tradeOrder.getMerchantNo());
        tradeNotifyBO.setTradeOrderNo(tradeOrder.getTradeOrderNo());
        tradeNotifyBO.setOutOrderNo(tradeOrder.getOutTradeNo());
        tradeNotifyBO.setTradeDate(tradeOrder.getRequestDate());
        tradeNotifyBO.setTradeAmount(tradeOrder.getOrderAmount());
        tradeNotifyBO.setDiscountableAmount(tradeOrder.getDiscountableAmount());
        tradeNotifyBO.setNotifyType(TradeTypeEnum.PAYMENT.name().toLowerCase());
        tradeNotifyBO.setNotifyUrl(tradeOrder.getNotifyUrl());
        tradeNotifyBO.setExtra(tradeOrder.getRemark());
        tradeNotifyBO.setSecretIndex(contractWithholdQueryBO.getPaymentOrder().getSecretIndex());
        tradeNotifyBO.setFinishDate(contractWithholdQueryBO.getFinishDateTime());
        return tradeNotifyBO;
    }
}
