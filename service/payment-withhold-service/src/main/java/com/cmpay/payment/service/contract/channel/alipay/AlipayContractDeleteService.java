package com.cmpay.payment.service.contract.channel.alipay;

import com.cmpay.payment.bo.withhold.ContractDeleteBO;
import com.cmpay.payment.service.contract.ContractChannelService;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface AlipayContractDeleteService extends ContractChannelService<ContractDeleteBO> {
    /**
     * 支付宝代扣解约
     *
     * @param contractDeleteBO
     */
    void alipayContractDelete(ContractDeleteBO contractDeleteBO);
}
