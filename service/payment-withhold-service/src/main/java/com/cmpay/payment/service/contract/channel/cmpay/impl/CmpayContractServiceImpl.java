package com.cmpay.payment.service.contract.channel.cmpay.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.channel.CmpayPayChannelEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.CmpayConstants;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.dto.cmpay.CmpayOnlineContractReqDTO;
import com.cmpay.payment.dto.cmpay.CmpayOnlineContractRspDTO;
import com.cmpay.payment.entity.config.ContractDO;
import com.cmpay.payment.service.contract.channel.cmpay.CmpayContractService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.service.ext.config.IExtContractService;
import com.cmpay.payment.util.UrlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
public class CmpayContractServiceImpl implements CmpayContractService {

    private static final Logger logger = LoggerFactory.getLogger(CmpayContractServiceImpl.class);
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Value("${cmpay.cmpayContractNotifyUrl:}")
    private String cmpayContractNotifyUrl;
    @Autowired
    private IExtContractService contractService;
    @Autowired
    ExtParamInfoService paramInfoService;
    @Override
    public void send(ContractWithholdBO contractWithholdBO) {
        contractWap(contractWithholdBO);
    }

    @Override
    public void contractWap(ContractWithholdBO contractWithholdBO) {
        try {
            ContractDO contract = contractService.getContract(contractWithholdBO.getBankMerchantNo());
            CmpayOnlineContractReqDTO cmpayOnlineContractReqDTO=new CmpayOnlineContractReqDTO();
            cmpayOnlineContractReqDTO.setMerchantId(contractWithholdBO.getBankMerchantNo());
            cmpayOnlineContractReqDTO.setSignType(contractWithholdBO.getSignType());
            cmpayOnlineContractReqDTO.setType(CommonConstant.CMPAY_ONLINE_CONTRACT_TYPE);
            cmpayOnlineContractReqDTO.setVersion(CommonConstant.CMPAY_ONLINE_CONTRACT_VERSION);
            cmpayOnlineContractReqDTO.setMerchantCert(contract.getPublicKey());
            cmpayOnlineContractReqDTO.setHmac(contractWithholdBO.getHmac());

            String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(host)) {
                try {
                    cmpayOnlineContractReqDTO.setBackNotifyUrl(UrlUtils.replaceDomainOrIp(cmpayContractNotifyUrl,host));
                } catch (Exception e) {
                    logger.info("通知地址域名获取异常");
                    cmpayOnlineContractReqDTO.setBackNotifyUrl(cmpayContractNotifyUrl);
                }
            } else {
                cmpayOnlineContractReqDTO.setBackNotifyUrl(cmpayContractNotifyUrl);
            }

            cmpayOnlineContractReqDTO.setMerchantRequestNo(contractWithholdBO.getContractCode());
            cmpayOnlineContractReqDTO.setFrontNotifyUrl(contractWithholdBO.getFrontNotifyUrl());
            cmpayOnlineContractReqDTO.setMobileNo(contractWithholdBO.getMobileNo());
            Request request = new Request();
            request.setRequestId(LemonUtils.getRequestId());
            request.setRoute(CmpayPayChannelEnum.CMPAY_ONLINE);
            request.setBusiType(CmpayPayChannelEnum.CMPAY_ONLINE_CONTRACT.getName());
            request.setSource(CmpayPayChannelEnum.CMPAY_ONLINE);
            request.setTarget(cmpayOnlineContractReqDTO);
            GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            if (JudgeUtils.isNotSuccess(genericRspDTO)) {
                BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_CONTRACT_FAIL);
            }
            Optional.of(genericRspDTO)
                    .map(GenericRspDTO::getBody)
                    .map(Response::getResult)
                    .map(result -> {
                        handleOnlineContract(contractWithholdBO, (CmpayOnlineContractRspDTO) result);
                        return result;
                    }).orElseThrow(() ->new BusinessException(MsgCodeEnum.CMPAY_CONTRACT_FAIL));
        } catch (Exception e) {
            throw e;
        };
    }

    private void handleOnlineContract(ContractWithholdBO contractWithholdBO, CmpayOnlineContractRspDTO cmpayOnlineContractRspDTO) {
        if(!StringUtils.equals(CmpayConstants.RETURN_CODE, cmpayOnlineContractRspDTO.getReturnCode())){
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_CONTRACT_FAIL);
        }
        if(StringUtils.isNotBlank(cmpayOnlineContractRspDTO.getMerchantSignAddress())){
            String encodeUrl = null;
            try {
                encodeUrl = URLDecoder.decode(cmpayOnlineContractRspDTO.getMerchantSignAddress(), ContractConstants.CODE);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            contractWithholdBO.setRedirectUrl(encodeUrl);
        }else{
            BusinessException.throwBusinessException(MsgCodeEnum.CMPAY_CONTRACT_FAIL);
        }
    }
}
