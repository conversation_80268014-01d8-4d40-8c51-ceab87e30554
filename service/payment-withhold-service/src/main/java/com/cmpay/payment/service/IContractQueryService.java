package com.cmpay.payment.service;

import com.cmpay.payment.bo.withhold.ContractQueryBO;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface IContractQueryService {

    /**
     * 代扣签约订单查询
     *
     * @param contractQueryBO
     * @return
     */
    ContractQueryBO contractInfoQuery(ContractQueryBO contractQueryBO);

    /**
     * 通过协议号查询签约状态
     * @param contractQueryBO
     * @return
     */
    ContractQueryBO contractQueryByContractCode(ContractQueryBO contractQueryBO);

    /**
     * 通过协议号查询签约状态
     * @param contractQueryBO
     * @return
     */
    ContractQueryBO contractDeleteQueryByContractCode(ContractQueryBO contractQueryBO);

}
