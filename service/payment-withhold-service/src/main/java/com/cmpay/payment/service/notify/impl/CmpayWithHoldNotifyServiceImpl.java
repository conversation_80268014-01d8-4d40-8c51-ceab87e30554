package com.cmpay.payment.service.notify.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.notify.CmpayNotifyBO;
import com.cmpay.payment.bo.withhold.ContractQueryBO;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.TradeTypeEnum;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.entity.withhold.ContractWithholdDO;
import com.cmpay.payment.service.AsynCommonService;
import com.cmpay.payment.service.IContractSynService;
import com.cmpay.payment.service.ext.withhold.IExtContractWithholdService;
import com.cmpay.payment.service.notify.ICmpayWithHoldNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/14 14:44
 */
@Service
@Slf4j
public class CmpayWithHoldNotifyServiceImpl implements ICmpayWithHoldNotifyService {
    @Autowired
    private IContractSynService contractSynService;
    @Autowired
    private IExtContractWithholdService contractWithholdService;
    @Autowired
    private AsynCommonService asynCommonService;

    @Override

    public void notifyCmpayContract(CmpayNotifyBO cmpayNotifyBO) {
        ContractWithholdBO contractWithholdBO = new ContractWithholdBO();
        ContractQueryBO contractQueryBO = new ContractQueryBO();
        contractWithholdBO.setContractCode(cmpayNotifyBO.getMerchantRequestNo());
//                查询到这笔签约，获取手机号
        ContractWithholdDO contractWithholdDO = contractWithholdService.findByContractCode(contractWithholdBO);
        if (JudgeUtils.isNull(contractWithholdDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_INFO_NOT_EXISTS);
        }
        // 直接查询最新的一笔
        contractWithholdBO.setMobileNo(contractWithholdDO.getMobileNo());
        contractWithholdBO.setContractWay(contractWithholdDO.getContractWay());
        contractWithholdBO.setBankMerchantNo(contractWithholdDO.getBankMerchantNo());
        // 查询这个手机号，在等待中的协议记录（最新的协议记录）
        ContractWithholdDO newContractWithholdDO = contractWithholdService.findWaitContract(contractWithholdBO);
        if (JudgeUtils.isNotNull(newContractWithholdDO)) {
            contractQueryBO.setBankMerchantNo(newContractWithholdDO.getBankMerchantNo());
            //和包通知来的商户请求号，就是聚合协议号
            contractQueryBO.setContractCode(newContractWithholdDO.getContractCode());
            contractQueryBO.setContractSignedTime(newContractWithholdDO.getSignedTime());
        } else { //为空，说明没有更新的签约协议，直接就走当前这一笔
            contractQueryBO.setBankMerchantNo(cmpayNotifyBO.getMerchantId());
            contractQueryBO.setContractCode(cmpayNotifyBO.getMerchantRequestNo());
            contractQueryBO.setContractSignedTime(contractWithholdDO.getSignedTime());
        }
        contractQueryBO.setContractStatus(ContractStatusEnum.CONTRACT_SUCCESS.name());
        contractQueryBO.setChangeType(TradeTypeEnum.ADD.name());
        ContractWithholdBO withholdBO = contractSynService.contractNotify(contractQueryBO);
        if (StringUtils.equals(ContractStatusEnum.CONTRACT_SUCCESS.name(), withholdBO.getContractStatus())) {
            asynCommonService.asyncContractNotify(withholdBO);
        }
    }
}
