package com.cmpay.payment.service.contract.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.withhold.ContractWithholdQueryBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentNrtCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.constant.wechat.WeChatTradeStatusEnum;
import com.cmpay.payment.dto.wechat.WxPayPapOrderQueryRequest;
import com.cmpay.payment.dto.wechat.WxPayPapOrderQueryResponse;
import com.cmpay.payment.service.contract.channel.wechat.WeChatContractWithholdQueryService;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatContractWithholdQueryServiceImpl implements WeChatContractWithholdQueryService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatContractWithholdQueryServiceImpl.class);

    @Autowired
    private IntegrationPaymentNrtCgwOutClient nrtCgwOutClient;

    /**
     * 微信代扣签约查询订单
     *
     * @param contractWithholdQueryBO
     */
    @Override
    public void weChatContractWithholdQuery(ContractWithholdQueryBO contractWithholdQueryBO) {
        WxPayPapOrderQueryRequest wxPayPaporderqueryRequest = new WxPayPapOrderQueryRequest();
        wxPayPaporderqueryRequest.setAppid(contractWithholdQueryBO.getAppId());
        wxPayPaporderqueryRequest.setOutTradeNo(contractWithholdQueryBO.getOutTradeNo());
        wxPayPaporderqueryRequest.setMchId(contractWithholdQueryBO.getBankMerchantNo());
        wxPayPaporderqueryRequest.setKey(contractWithholdQueryBO.getContractSecureValue());
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.CONTRACT_ORDER_QUERY.getSource());
        request.setRoute(WXPayChannel.CONTRACT_ORDER_QUERY.getRoute());
        request.setBusiType(WXPayChannel.CONTRACT_ORDER_QUERY.getBusType());
        request.setTarget(wxPayPaporderqueryRequest);

        GenericRspDTO<Response> genericRspDTO = nrtCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(contractWithholdQueryBO, (WxPayPapOrderQueryResponse) result));
    }

    @Override
    public void send(ContractWithholdQueryBO contractWithholdQueryBO) {
        weChatContractWithholdQuery(contractWithholdQueryBO);
    }

    private void handleResult(ContractWithholdQueryBO contractWithholdQueryBO, WxPayPapOrderQueryResponse response) {
        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                if (Objects.isNull(response.getTradeState())) {
                    contractWithholdQueryBO.setPaymentStatus(OrderStatusEnum.WAIT_PAY.name());
                } else {
                    WeChatTradeStatusEnum status = EnumUtils.getEnum(WeChatTradeStatusEnum.class, response.getTradeState());
                    switch (status) {
                        case SUCCESS:
                            contractWithholdQueryBO.setPaymentStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                            contractWithholdQueryBO.setFinishDateTime(response.getTimeEnd());
                            contractWithholdQueryBO.getPaymentOrder().setAccountDate(response.getTimeEnd().substring(0, 8));
                            contractWithholdQueryBO.getPaymentOrder().setThirdOrdNo(response.getTransactionId());
                            break;
                        case REFUND:
                            contractWithholdQueryBO.setPaymentStatus(OrderStatusEnum.REFUND_WAIT.name());
                            contractWithholdQueryBO.setReturnMsg(response.getTradeStateDesc());
                            break;
                        case CLOSED:
                        case REVOKED:
                            contractWithholdQueryBO.setPaymentStatus(OrderStatusEnum.TRADE_CLOSED.name());
                            contractWithholdQueryBO.setReturnMsg(response.getTradeStateDesc());
                            break;
                        case PAYERROR:
                        case PAY_FAIL:
                            contractWithholdQueryBO.setPaymentStatus(OrderStatusEnum.TRADE_FAIL.name());
                            contractWithholdQueryBO.setReturnMsg(response.getTradeStateDesc());
                            break;
                        case USERPAYING:
                        case NOTPAY:
                        case ACCEPT:
                        default:
                            contractWithholdQueryBO.setPaymentStatus(OrderStatusEnum.WAIT_PAY.name());
                            break;
                    }
                }
            } else {
                logger.info("queryWithholdContractOrder failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                contractWithholdQueryBO.setPaymentStatus(OrderStatusEnum.TRADE_FAIL.name());
                contractWithholdQueryBO.setReturnMsg(response.getErrCodeDes());
            }
        } else {
            logger.info("queryWithholdContractOrder failure, weChat return message is : {}", response.getReturnMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_WITHHOLD_QUERY_FAILURE);
        }
    }
}
