package com.cmpay.payment.service.contract.channel.alipay;

import com.cmpay.payment.bo.withhold.ContractPaymentWithholdBO;
import com.cmpay.payment.service.contract.ContractPaymentChannelService;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface AlipayPaymentContractService extends ContractPaymentChannelService<ContractPaymentWithholdBO> {
    /**
     * 支付宝支付中签约
     *
     * @param contractPaymentWithholdBO
     */
    void paymentContract(ContractPaymentWithholdBO contractPaymentWithholdBO);
}
