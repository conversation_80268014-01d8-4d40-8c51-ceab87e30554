package com.cmpay.payment.service.contract.channel.alipay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.withhold.ContractDeleteBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.dto.alipay.TradeAgreementUnsignReq;
import com.cmpay.payment.dto.alipay.TradeAgreementUnsignRsp;
import com.cmpay.payment.properties.AlipayProperties;
import com.cmpay.payment.service.contract.channel.alipay.AlipayContractDeleteService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.PaymentUtils;
import com.cmpay.payment.util.UrlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
public class AlipayContractDeleteServiceImpl implements AlipayContractDeleteService {
    private static final Logger logger = LoggerFactory.getLogger(AlipayContractDeleteServiceImpl.class);
    @Autowired
    private AlipayProperties alipayProperties;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Override
    public void alipayContractDelete(ContractDeleteBO contractDeleteBO) {
        TradeAgreementUnsignReq tradeAgreementUnsignReq = new TradeAgreementUnsignReq();

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                tradeAgreementUnsignReq.setNotifyUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getContractDeleteNotifyUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                tradeAgreementUnsignReq.setNotifyUrl(alipayProperties.getContractDeleteNotifyUrl());
            }
        } else {
            tradeAgreementUnsignReq.setNotifyUrl(alipayProperties.getContractDeleteNotifyUrl());
        }

        tradeAgreementUnsignReq.setAgreementNo(contractDeleteBO.getContractId());
        GenericRspDTO<Response> response = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_AGREEMENT_UNSIGN.getName(), tradeAgreementUnsignReq));
        if (JudgeUtils.isNotSuccess(response.getMsgCd())) {
            BusinessException.throwBusinessException(response);
        }
        TradeAgreementUnsignRsp tradeAgreementUnsignRsp = (TradeAgreementUnsignRsp) response.getBody().getResult();
        if (StringUtils.contains(tradeAgreementUnsignRsp.getCode(), ContractConstants.CODE_SUCCESS)) {

        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_ALIPAY_WITHHOLD_DELETE_FAILURE);
        }
    }

    @Override
    public void send(ContractDeleteBO contractDeleteBO) {
        alipayContractDelete(contractDeleteBO);
    }
}
