package com.cmpay.payment.service.contract.listener;

import com.cmpay.payment.bo.withhold.ContractDeleteBO;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.entity.config.ContractDO;
import com.cmpay.payment.service.contract.ContractChannelService;
import com.cmpay.payment.service.contract.ContractListenerService;
import com.cmpay.payment.service.contract.channel.wechat.WeChatContractDeleteService;
import com.cmpay.payment.service.ext.config.IExtContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
public class WeChatContractDeleteListener extends ContractListenerService<ContractDeleteBO> {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    private IExtContractService contractService;

    /**
     * 微信H5支付申请退款
     *
     * @param contractDeleteBO
     */
    @EventListener
    public void handleWeChatRefund(ContractDeleteBO contractDeleteBO) {
        execute(contractDeleteBO);
    }

    @Override
    protected boolean checkChannelExecutable(ContractDeleteBO contractDeleteBO) {
        return Optional.ofNullable(contractDeleteBO)
                .map(ContractDeleteBO::getContractWay)
                .filter(contractWay -> StringUtils.equals(PaymentWayEnum.WECHAT.name().toLowerCase(),contractWay))
                .isPresent();
    }

    @Override
    protected ContractChannelService determinateChannelExecuteBean(ContractDeleteBO contractDeleteBO) {
        ContractDO contract = contractService.getContract(contractDeleteBO.getBankMerchantNo());
        contractDeleteBO.setContractSecureValue(contract.getSecretKey());
        return getBean(WeChatContractDeleteService.class);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
