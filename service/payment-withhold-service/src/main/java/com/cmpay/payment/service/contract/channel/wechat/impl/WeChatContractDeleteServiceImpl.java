package com.cmpay.payment.service.contract.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.withhold.ContractDeleteBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.dto.wechat.WxPayDeleteContractRequest;
import com.cmpay.payment.dto.wechat.WxPayDeleteContractResponse;
import com.cmpay.payment.service.contract.channel.wechat.WeChatContractDeleteService;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatContractDeleteServiceImpl implements WeChatContractDeleteService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatContractDeleteServiceImpl.class);

    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;

    /**
     * 微信H5支付订单查询
     *
     * @param contractDeleteBO
     */
    @Override
    public void weChatContractDelete(ContractDeleteBO contractDeleteBO) {
        WxPayDeleteContractRequest wxPayDeletecontractRequest = new WxPayDeleteContractRequest();
        wxPayDeletecontractRequest.setAppid(contractDeleteBO.getAppId());
        wxPayDeletecontractRequest.setContractCode(contractDeleteBO.getContractCode());
        wxPayDeletecontractRequest.setPlanId(contractDeleteBO.getPlanId());
        wxPayDeletecontractRequest.setContractId(contractDeleteBO.getContractId());
        wxPayDeletecontractRequest.setContractTerminationRemark(ContractConstants.DELETE);
        wxPayDeletecontractRequest.setVersion(ContractConstants.WX_CONTRACT_VERSION);
        wxPayDeletecontractRequest.setMchId(contractDeleteBO.getBankMerchantNo());
        wxPayDeletecontractRequest.setKey(contractDeleteBO.getContractSecureValue());
        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.CONTRACT_DELETE.getSource());
        request.setRoute(WXPayChannel.CONTRACT_DELETE.getRoute());
        request.setBusiType(WXPayChannel.CONTRACT_DELETE.getBusType());
        request.setTarget(wxPayDeletecontractRequest);

        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(contractDeleteBO, (WxPayDeleteContractResponse) result));
    }

    @Override
    public void send(ContractDeleteBO contractDeleteBO) {
        weChatContractDelete(contractDeleteBO);
    }

    private void handleResult(ContractDeleteBO contractDeleteBO, WxPayDeleteContractResponse response) {
        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                contractDeleteBO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED_WAIT.name());
            } else {
                logger.error("deleteContractOrder failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getErrCode());
                switch (errorCodeEnum) {
                    case SIGN_ERROR:
                        break;
                    default:
                        BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_DELETE_FAILURE);
                }
            }
        } else {
            logger.error("deleteContractOrder failure, weChat return message is : {}", response.getReturnMsg());
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_DELETE_FAILURE);
        }
    }
}
