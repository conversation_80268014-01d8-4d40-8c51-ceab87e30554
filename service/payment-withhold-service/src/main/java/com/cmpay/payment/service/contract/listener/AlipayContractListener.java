package com.cmpay.payment.service.contract.listener;

import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.contract.ContractChannelService;
import com.cmpay.payment.service.contract.ContractListenerService;
import com.cmpay.payment.service.contract.channel.alipay.AlipayContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class AlipayContractListener extends ContractListenerService<ContractWithholdBO> {

    @Autowired
    ApplicationContext applicationContext;

    /**
     * 处理微信事件
     *
     * @param contractWithholdBO
     */
    @EventListener
    public void handleWeChatPaymentOrder(ContractWithholdBO contractWithholdBO) {
        execute(contractWithholdBO);
    }

    @Override
    protected boolean checkChannelExecutable(ContractWithholdBO contractWithholdBO) {
        return contractWithholdBO != null && contractWithholdBO.getContractWay() != null && StringUtils.equalsIgnoreCase(PaymentWayEnum.ALIPAY.name(),
                contractWithholdBO.getContractWay());
    }

    @Override
    protected ContractChannelService determinateChannelExecuteBean(ContractWithholdBO contractWithholdBO) {
        if (contractWithholdBO == null || contractWithholdBO.getScene() == null) {
            return null;
        }
        return Optional.ofNullable(contractWithholdBO)
                .map(ContractWithholdBO::getScene)
                .map(String::toUpperCase)
                .map(PaymentSceneEnum::valueOf)
                .map(scene -> {
                    ContractChannelService contractChannelService = null;
                    switch (scene) {
                        case WAP:
//                            支付宝小程序签约
                        case APPLET:
                            contractChannelService = getBean(AlipayContractService.class);
                            break;
                        default:
                            break;
                    }
                    return contractChannelService;
                })
                .orElse(null);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
