package com.cmpay.payment.service;

import com.cmpay.payment.bo.withhold.ContractApplyWithholdBO;
import com.cmpay.payment.bo.withhold.WxPrePayBO;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface IContractApplyPaymentService {

    /**
     * 签约申请扣款
     *
     * @param contractApplyWithholdBO
     * @return
     */
    ContractApplyWithholdBO contractApplyPayment(ContractApplyWithholdBO contractApplyWithholdBO);

    /**
     * 微信签约预扣款通知
     *
     * @param wxPrePayBO
     * @return
     */
    WxPrePayBO wxPrePayNotifi(WxPrePayBO wxPrePayBO);
}
