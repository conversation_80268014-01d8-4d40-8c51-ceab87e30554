package com.cmpay.payment.service.contract.channel.alipay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.withhold.ContractApplyWithholdBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.dto.alipay.TradePayReq;
import com.cmpay.payment.dto.alipay.TradePayRsp;
import com.cmpay.payment.properties.AlipayProperties;
import com.cmpay.payment.service.contract.channel.alipay.AlipayContractApplyService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.PaymentUtils;
import com.cmpay.payment.util.UrlUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class AlipayContractApplyServiceImpl implements AlipayContractApplyService {

    private static final Logger logger = LoggerFactory.getLogger(AlipayContractApplyServiceImpl.class);

    @Autowired
    AlipayProperties alipayProperties;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    ExtParamInfoService paramInfoService;
    private static final String FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public void contractApply(ContractApplyWithholdBO contractApplyWithholdBO) {
        TradePayReq tradePayReq = new TradePayReq();
        tradePayReq.setOutTradeNo(contractApplyWithholdBO.getOutTradeNo());

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                tradePayReq.setNotifyUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getNotifyUrl(),host));
                tradePayReq.setReturnUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getReturnUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                tradePayReq.setNotifyUrl(alipayProperties.getNotifyUrl());
                tradePayReq.setReturnUrl(alipayProperties.getReturnUrl());
            }
        } else {
            tradePayReq.setNotifyUrl(alipayProperties.getNotifyUrl());
            tradePayReq.setReturnUrl(alipayProperties.getReturnUrl());
        }

        tradePayReq.setTotalAmount(contractApplyWithholdBO.getTotalAmount().toString());
        tradePayReq.setSubject(contractApplyWithholdBO.getSubject());
        tradePayReq.setProductCode(ContractConstants.GENERAL_WITHHOLDING);
        tradePayReq.setAgreementNo(contractApplyWithholdBO.getContractId());
        //绝对超时时间
        LocalDateTime expireTime=DateTimeUtils.getCurrentLocalDateTime().plusMinutes(30);
        tradePayReq.setTimeExpire(DateTimeUtils.formatLocalDateTime(expireTime,FORMAT));
//        判断不为空，设营销参数值
        if(JudgeUtils.isNotNull(contractApplyWithholdBO.getPromoParams())){
            tradePayReq.setPromoParams(contractApplyWithholdBO.getPromoParams());
        }
        tradePayReq.setBizSubType(contractApplyWithholdBO.getDebitBusTypeMapping());
        GenericRspDTO<Response> response = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_PAY.getName(), tradePayReq));
        if (JudgeUtils.isNotSuccess(response.getMsgCd())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_ALIPAY_WITHHOLD_APPLY_FAILURE);
        }
        TradePayRsp tradePayRsp = (TradePayRsp) response.getBody().getResult();
        if (JudgeUtils.equals(ContractConstants.CODE_SUCCESS, tradePayRsp.getCode())) {
            logger.info("request success, rsp param:{}", tradePayRsp.toString());
        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_ALIPAY_WITHHOLD_APPLY_FAILURE);
        }

    }

    @Override
    public void send(ContractApplyWithholdBO contractApplyWithholdBO) {
        contractApply(contractApplyWithholdBO);
    }
}
