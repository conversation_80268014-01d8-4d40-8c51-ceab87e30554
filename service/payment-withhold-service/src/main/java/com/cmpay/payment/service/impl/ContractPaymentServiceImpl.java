package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.bo.config.PayServiceBeanBO;
import com.cmpay.payment.bo.withhold.ContractPaymentWithholdBO;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.constant.*;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.constant.contract.ContractWayEnum;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.entity.withhold.ContractWithholdDO;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.IContractPaymentService;
import com.cmpay.payment.service.ext.ExtPayOrderService;
import com.cmpay.payment.service.ext.config.IExtRateService;
import com.cmpay.payment.service.ext.withhold.IExtContractWithholdService;
import com.cmpay.payment.util.EnumUtilsEx;
import com.cmpay.payment.utils.TypeCheckUtils;
import feign.RetryableException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Locale;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
public class ContractPaymentServiceImpl implements IContractPaymentService {

    private static final Logger logger = LoggerFactory.getLogger(ContractPaymentServiceImpl.class);

    @Autowired
    private IExtContractWithholdService contractWithholdService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    private IExtRateService rateService;
    @Autowired
    private ExtPayOrderService payOrderService;
    private static final Function<String, ExpireTimeTypeEnum> EXPIRE_TIME_TYPE_ENUM_FUNCTION =
            EnumUtilsEx.lookupMap(ExpireTimeTypeEnum.class, ExpireTimeTypeEnum::getDesc);

    /**
     * 支付中签约
     *
     * @param contractPaymentWithholdBO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ContractPaymentWithholdBO contractPayment(ContractPaymentWithholdBO contractPaymentWithholdBO) {
        try {
            paymentProcess(contractPaymentWithholdBO);
        } catch (Exception e) {
            logger.error("Exception:.", e);
            String msg;
            if (e instanceof BusinessException) {
                msg = ((BusinessException) e).getMsgCd();
            } else if ((e instanceof RetryableException)&&e.getMessage().contains(CommonConstant.READ_TIMED_OUT)) {
                msg = MsgCodeEnum.REQUEST_RETURN_TIME_OUT.getMsgCd();
            } else {
                msg = MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd();
                logger.error(msg, e);
            }
            BusinessException.throwBusinessException(msg);
        }
        return contractPaymentWithholdBO;
    }

    /**
     * 支付处理
     *
     * @param contractPaymentBO
     */
    private void paymentProcess(ContractPaymentWithholdBO contractPaymentBO) {
        inputCannotEmptyCheck(contractPaymentBO);
        checkAmount(contractPaymentBO.getTotalAmount(), contractPaymentBO.getRealAmount(), contractPaymentBO.getDiscountableAmount());
        if (TypeCheckUtils.checkPaymentScene(PaymentSceneEnum.valueOf(contractPaymentBO.getScene().toUpperCase()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_SCENE_ERROR);
        }
        if (TypeCheckUtils.checkPaymentWay(PaymentWayEnum.valueOf(contractPaymentBO.getPayWay().toUpperCase()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_WAY_ERROR);
        }
        if (JudgeUtils.equals(contractPaymentBO.getPayWay(), ContractWayEnum.ALIPAY.name().toLowerCase())) {
            if (!checkPayWayScene(contractPaymentBO)) {
                BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_SCENE_ERROR);
            }
        }
        TradeOrderDO tradeOrderDO = new TradeOrderDO();
        tradeOrderDO.setMerchantNo(contractPaymentBO.getMerchantId());
        tradeOrderDO.setOutTradeNo(contractPaymentBO.getOutTradeNo());
        tradeOrderDO.setRequestDate(contractPaymentBO.getTradeDate());
        if (JudgeUtils.isNotNull(payOrderService.load(tradeOrderDO))) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_ALREADY_EXISTS);
        }
        ContractWithholdBO contractWithholdBO = new ContractWithholdBO();
        contractWithholdBO.setContractCode(contractPaymentBO.getContractCode());
        ContractWithholdDO contractWithholdDO = contractWithholdService.findByContractCode(contractWithholdBO);
        if (JudgeUtils.isNotNull(contractWithholdDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_ALREADY_EXISTS);
        }
        PayServiceBeanBO rateBO = new PayServiceBeanBO();
        rateBO.setMerchantNo(contractPaymentBO.getMerchantId());
        rateBO.setOrderScene(contractPaymentBO.getScene());
        rateBO.setPaymentChannl(contractPaymentBO.getPayWay());
        rateBO.setOrderAmount(contractPaymentBO.getRealAmount());
        rateBO.setProvinceCode(JudgeUtils.isBlank(contractPaymentBO.getProvinceCode()) ?
                StringUtils.substring(contractPaymentBO.getOutTradeNo(), 0, 4) : contractPaymentBO.getProvinceCode());
        rateBO.setOrderDate(DateTimeUtils.getCurrentDateStr());
        rateBO = rateService.findPaymentRout(rateBO);
        if (JudgeUtils.isNull(rateBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ROUT_CANNOT_VALIDITY);
        }
        //只支持直连路由
        if (JudgeUtils.notEquals(rateBO.getPaymentRout().toLowerCase(), contractPaymentBO.getPayWay().toLowerCase())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ROUT_CANNOT_VALIDITY);
        }
        contractPaymentBO.setOrderRate(rateBO.getRate());
        contractPaymentBO.setPaymentId(rateBO.getBankMerchantNo());
        contractPaymentBO.setPaymentRout(rateBO.getPaymentRout());
        contractPaymentBO.setPaymentType(rateBO.getBusinessType());
        contractPaymentBO.setBankMerchantNo(rateBO.getBankMerchantNo());
        contractPaymentBO.setTradeOrderNo(contractPaymentBO.getOutTradeNo());
        contractPaymentBO.setTradeTime(DateTimeUtils.getCurrentTimeStr());
        contractPaymentBO.setExpireTime(getExpireTime(contractPaymentBO));
        contractPaymentBO.setContractSecureValue(rateBO.getSecureValue());
        if (JudgeUtils.isBlank(contractPaymentBO.getAppId())
                && JudgeUtils.equals(ContractWayEnum.WECHAT.name().toLowerCase(), contractPaymentBO.getPayWay())) {
            contractPaymentBO.setAppId(weChatProperties.getAppid());
        }
        PayOrderBO payOrderBO = new PayOrderBO();
        BeanUtils.copyProperties(payOrderBO, contractPaymentBO);
        payOrderBO.setServiceCharge(rateBO.getServiceCharge());
        payOrderService.insertByNewTranscation(payOrderBO);
        insertContractOrder(contractPaymentBO);
        try {
            applicationContext.publishEvent(contractPaymentBO);

        } catch (Exception e) {
            throw e;
        }

    }

    /**
     * 输入不为空检查
     *
     * @param contractPaymentBO
     */
    private void inputCannotEmptyCheck(ContractPaymentWithholdBO contractPaymentBO) {
        if (JudgeUtils.isBlank(contractPaymentBO.getMerchantId())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getPayWay())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_WAY_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getTradeDate())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_REQUEST_DATE_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getScene())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_SCENE_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getOutTradeNo())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ORDER_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getSubject())) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_SUBJECT_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isNull(contractPaymentBO.getTotalAmount())) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_AMOUNT_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isNull(contractPaymentBO.getRealAmount())) {
            BusinessException.throwBusinessException(MsgCodeEnum.REAL_AMOUNT_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.equals(contractPaymentBO.getPlanId(), ContractConstants.CYCLE_PAY_AUTH_P)) {
            if (JudgeUtils.isBlankAny(contractPaymentBO.getPeriodType(), contractPaymentBO.getPeriod(),
                    contractPaymentBO.getExecuteTime(), contractPaymentBO.getSingleAmount().toString())) {
                BusinessException.throwBusinessException(MsgCodeEnum.CYCLE_CONTRACT_STATUS_ERROR);
            }
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getNotifyUrl())) {
            BusinessException.throwBusinessException(MsgCodeEnum.NOTIFY_URL_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getContractCode())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_ORDER_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getContractNotifyUrl())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_NOTIFY_URL_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getDeleteNotifyUrl())) {
            BusinessException.throwBusinessException(MsgCodeEnum.DELETE_NOTIFY_URL_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getPlanId())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_PLANID_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractPaymentBO.getContractDisplayAccount())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_USERINFO_CANNOT_BE_EMPTY);
        }
    }

    /**
     * 检查金额
     *
     * @param totalAmount
     * @param realAmount
     * @param discountableAmount
     */
    private void checkAmount(BigDecimal totalAmount, BigDecimal realAmount, BigDecimal discountableAmount) {
        if (JudgeUtils.isNull(discountableAmount)) {
            if (totalAmount.compareTo(realAmount) != 0) {
                BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
            }
        } else {
            if (discountableAmount.add(realAmount).compareTo(totalAmount) != 0) {
                BusinessException.throwBusinessException(MsgCodeEnum.AMOUNT_ERROR);
            }
        }
    }

    /**
     * 新增签约信息
     *
     * @param contractPaymentBO
     */
    public void insertContractOrder(ContractPaymentWithholdBO contractPaymentBO) {
        ContractWithholdBO contractWithholdBO = new ContractWithholdBO();
        contractWithholdBO.setContractCode(contractPaymentBO.getContractCode());
        contractWithholdBO.setContractDate(contractPaymentBO.getTradeDate());
        contractWithholdBO.setPlanId(contractPaymentBO.getPlanId());
        contractWithholdBO.setAppId(contractPaymentBO.getAppId());
        contractWithholdBO.setContractDisplayAccount(contractPaymentBO.getContractDisplayAccount());
        contractWithholdBO.setMerchantId(contractPaymentBO.getMerchantId());
        contractWithholdBO.setBankMerchantNo(contractPaymentBO.getBankMerchantNo());
        contractWithholdBO.setNotifyUrl(contractPaymentBO.getContractNotifyUrl());
        contractWithholdBO.setDeleteNotifyUrl(contractPaymentBO.getDeleteNotifyUrl());
        contractWithholdBO.setContractWay(contractPaymentBO.getPayWay());
        contractWithholdBO.setScene(contractPaymentBO.getScene());
        contractWithholdBO.setContractClientip(contractPaymentBO.getClientIps());
        contractWithholdBO.setOutTradeNo(contractPaymentBO.getOutTradeNo());
        contractWithholdBO.setBankMerchantNo(contractPaymentBO.getBankMerchantNo());
        contractWithholdBO.setSubMerchantName(contractPaymentBO.getSubMerchantName());
        contractWithholdBO.setSubMerchantServiceName(contractPaymentBO.getSubMerchantServiceName());
        contractWithholdBO.setSubMerchantServiceDescription(contractPaymentBO.getSubMerchantServiceDescription());
        contractWithholdBO.setPeriodType(contractPaymentBO.getPeriodType());
        contractWithholdBO.setPeriod(contractPaymentBO.getPeriod());
        contractWithholdBO.setExecuteTime(contractPaymentBO.getExecuteTime());
        contractWithholdBO.setSingleAmount(contractPaymentBO.getSingleAmount());
        contractWithholdBO.setProvinceCode(contractPaymentBO.getProvinceCode());
        contractWithholdService.insertContractInfo(contractWithholdBO);
    }

    /**
     * 得到过期时间
     *
     * @param contractPaymentBO
     * @return String
     */
    private String getExpireTime(ContractPaymentWithholdBO contractPaymentBO) {
        String timeoutExpress = contractPaymentBO.getTimeoutExpress();
        logger.info("过期时间: {}", timeoutExpress);
        LocalDateTime localDateTime = DateTimeUtils.getCurrentLocalDateTime();

        // 微信付款码支付订单有效期30秒加15秒的buffer
        if (StringUtils.equals(contractPaymentBO.getScene(), PaymentSceneEnum.BARCODE.name().toLowerCase())
                && StringUtils.equals(contractPaymentBO.getPaymentRout(), PaymentWayEnum.WECHAT.name().toLowerCase())) {
            return DateTimeUtils.formatLocalDateTime(localDateTime.plusSeconds(45));
        }

        if (JudgeUtils.isEmpty(timeoutExpress)) {
            // 订单默认有效期30分钟
            contractPaymentBO.setValidityNumber(Long.toString(30));
            contractPaymentBO.setValidityUnit(CommonConstant.VALIDITY_UNIT_MINUTE);
            return DateTimeUtils.formatLocalDateTime(localDateTime.plusMinutes(30));
        }

        LocalDateTime expireTime;
        String expireType = StringUtils.substring(timeoutExpress, timeoutExpress.length() - 1);
        long expireValue = Long.parseLong(StringUtils.removeEnd(timeoutExpress, expireType));
        ExpireTimeTypeEnum expireTimeType = EXPIRE_TIME_TYPE_ENUM_FUNCTION.apply(StringUtils.substring(timeoutExpress, timeoutExpress.length() - 1));
        switch (expireTimeType) {
            case DAY:
                expireTime = localDateTime.plusDays(expireValue);
                contractPaymentBO.setValidityUnit(CommonConstant.VALIDITY_UNIT_DAY);
                contractPaymentBO.setValidityNumber(Long.toString(expireValue));
                break;
            case MINUTE:
                expireTime = localDateTime.plusMinutes(expireValue);
                contractPaymentBO.setValidityUnit(CommonConstant.VALIDITY_UNIT_MINUTE);
                contractPaymentBO.setValidityNumber(Long.toString(expireValue));
                break;
            case HOUR:
                expireTime = localDateTime.plusHours(expireValue);
                contractPaymentBO.setValidityUnit(CommonConstant.VALIDITY_UNIT_MINUTE);
                contractPaymentBO.setValidityNumber(Long.toString(expireValue * 60));
                break;
            case TODAY:
            default:
                expireTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateStr() + LocalTime.MAX.toString());
                contractPaymentBO.setValidityUnit(CommonConstant.VALIDITY_UNIT_MINUTE);
                contractPaymentBO.setValidityNumber(CommonConstant.VALIDITY_NUMBER);
                break;
        }
        return DateTimeUtils.formatLocalDateTime(expireTime);
    }

    /**
     * 按照扣款类型判断场景是否支持
     *
     * @param contractPaymentWithholdBO
     * @return
     */
    private static boolean checkPayWayScene(ContractPaymentWithholdBO contractPaymentWithholdBO) {
        boolean flag = false;
        if (JudgeUtils.equals(contractPaymentWithholdBO.getPayWay().toUpperCase(), PaymentWayEnum.ALIPAY.name())) {
            if (JudgeUtils.equals(contractPaymentWithholdBO.getPlanId(), ContractConstants.GENERAL_WITHHOLDING_P)) {
                switch (contractPaymentWithholdBO.getScene().toUpperCase(Locale.ENGLISH)) {
                    case "WAP":
                    case "WEB":
                    case "APP":
                        flag = true;
                        break;
                    default:
                        flag = false;
                }
            }
            if (JudgeUtils.equals(contractPaymentWithholdBO.getPlanId(), ContractConstants.CYCLE_PAY_AUTH_P)) {
                flag = "APP".equalsIgnoreCase(contractPaymentWithholdBO.getScene());
            }
        }
        return flag;
    }
}
