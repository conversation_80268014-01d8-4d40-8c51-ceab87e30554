package com.cmpay.payment.service.contract.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.bo.withhold.ExtraDataBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.dto.wechat.WxPayContractAppletSignRequest;
import com.cmpay.payment.dto.wechat.WxPayContractAppletSignResponse;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.contract.channel.wechat.WeChatAppletContractService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatAppletContractServiceImpl implements WeChatAppletContractService {
    private static final Logger logger = LoggerFactory.getLogger(WeChatAppletContractServiceImpl.class);
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    ExtParamInfoService paramInfoService;

    @Override
    public void contractApplet(ContractWithholdBO contractWithholdBO) {
        WxPayContractAppletSignRequest wxPayAppletRequest = new WxPayContractAppletSignRequest();
        wxPayAppletRequest.setAppid(contractWithholdBO.getAppId());
        wxPayAppletRequest.setPlanId(contractWithholdBO.getPlanId());
        wxPayAppletRequest.setContractCode(contractWithholdBO.getContractCode());
        wxPayAppletRequest.setContractDisplayAccount(contractWithholdBO.getContractDisplayAccount());

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(host)) {
            try {
                wxPayAppletRequest.setNotifyUrl(UrlUtils.replaceDomainOrIp(weChatProperties.getContractNofityUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                wxPayAppletRequest.setNotifyUrl(weChatProperties.getContractNofityUrl());
            }
        } else {
            wxPayAppletRequest.setNotifyUrl(weChatProperties.getContractNofityUrl());
        }

        wxPayAppletRequest.setRequestSerial(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.WECHAT_JRN_NO, 5));
        wxPayAppletRequest.setMchId(contractWithholdBO.getBankMerchantNo());
        wxPayAppletRequest.setKey(contractWithholdBO.getHmac());

        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.CONTRACT_APPLET_SIGN.getSource());
        request.setRoute(WXPayChannel.CONTRACT_APPLET_SIGN.getRoute());
        request.setBusiType(WXPayChannel.CONTRACT_APPLET_SIGN.getBusType());
        request.setTarget(wxPayAppletRequest);
        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        WxPayContractAppletSignResponse wxPayContractAppletSignResponse = (WxPayContractAppletSignResponse) genericRspDTO.getBody().getResult();
        ExtraDataBO extraDataBO = new ExtraDataBO();
        extraDataBO.setAppId(wxPayContractAppletSignResponse.getAppid());
        extraDataBO.setMch_id(wxPayContractAppletSignResponse.getMchId());
        extraDataBO.setPlan_id(wxPayContractAppletSignResponse.getPlanId());
        extraDataBO.setContract_code(wxPayContractAppletSignResponse.getContractCode());
        extraDataBO.setRequest_serial(wxPayContractAppletSignResponse.getRequestSerial());
        extraDataBO.setContract_display_account(wxPayContractAppletSignResponse.getContractDisplayAccount());
        extraDataBO.setNotifyUrl(wxPayContractAppletSignResponse.getNotifyUrl());
        extraDataBO.setSign(wxPayContractAppletSignResponse.getSign());
        extraDataBO.setTimestamp(wxPayContractAppletSignResponse.getTimestamp());
        contractWithholdBO.setExtraData(extraDataBO);
        contractWithholdBO.setContractStatus(ContractStatusEnum.CONTRACT_WAIT.name());
    }

    @Override
    public void send(ContractWithholdBO refundBO) {
        contractApplet(refundBO);
    }

}
