package com.cmpay.payment.service.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.withhold.ContractDeleteBO;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.constant.contract.ContractChangeTypeEnum;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.constant.contract.ContractWayEnum;
import com.cmpay.payment.constant.protocol.ProtocolStatusEnum;
import com.cmpay.payment.entity.withhold.ContractDeleteDO;
import com.cmpay.payment.entity.withhold.ContractWithholdDO;
import com.cmpay.payment.service.IContractDeleteService;
import com.cmpay.payment.service.ext.withhold.IExtContractDeleteService;
import com.cmpay.payment.service.ext.withhold.IExtContractWithholdService;
import feign.RetryableException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
public class ContractDeleteServiceImpl implements IContractDeleteService {

    private static final Logger logger = LoggerFactory.getLogger(ContractDeleteServiceImpl.class);

    @Autowired
    private IExtContractWithholdService contractWithholdService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IExtContractDeleteService contractDeleteService;

    /**
     * 签约结果查询
     *
     * @param contractDeleteBO
     * @return
     */
    @Override
    public ContractDeleteBO contractDelete(ContractDeleteBO contractDeleteBO) {
        checkDeleteInfo(contractDeleteBO);
        //   两笔和包解约，发起第二笔，设置第一笔失败:同一时间，只能有一个解约等待中
        ContractDeleteDO cmpayContractDeleteDO = contractDeleteService.findWaitByContractCode(contractDeleteBO);
        if (JudgeUtils.isNotNull(cmpayContractDeleteDO) && StringUtils.equals(cmpayContractDeleteDO.getContractWay(), ContractWayEnum.CMPAY.name().toLowerCase())) {
            cmpayContractDeleteDO.setContractStatus(ProtocolStatusEnum.CONTRACT_TERMINATED_FAIL.getDesc());
            // 直接抛异常会导致，如果和包解约失败，我们这边有解约中流水，就无法再发起解约了
            contractDeleteService.update(cmpayContractDeleteDO);
        }
        ContractDeleteDO contractDeleteDO = contractDeleteService.findDeleteInfo(contractDeleteBO);
        if (JudgeUtils.isNotNull(contractDeleteDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.DELETE_ALREADY_EXIST);
        }
        ContractWithholdBO contractWithholdBO = new ContractWithholdBO();
        contractWithholdBO.setContractCode(contractDeleteBO.getContractCode());
        //加上商户号、加状态字去查，避免别的商户拿到协议号也可以解约 ，只有签约成功地才可以解约
        contractWithholdBO.setMerchantId(contractDeleteBO.getMerchantId());
        contractWithholdBO.setContractStatus(ContractStatusEnum.CONTRACT_SUCCESS.getDesc());
        ContractWithholdDO contractWithholdDO = contractWithholdService.findByCodeAndMerchantNo(contractWithholdBO);
        if (JudgeUtils.isNull(contractWithholdDO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_CODE_NOT_EXISTS);
        }
        //和包解约不需要判断PlanId和contractId
        if (JudgeUtils.notEquals(contractWithholdDO.getContractWay(), PaymentWayEnum.CMPAY.name().toLowerCase())) {
            if (JudgeUtils.isBlank(contractDeleteBO.getPlanId())) {
                BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_PLANID_CANNOT_BE_EMPTY);
            }
            if (JudgeUtils.isNull(contractDeleteBO.getContractId())) {
                BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_REQUEST_DATE_CANNOT_BE_EMPTY);
            }
        }
        if (!JudgeUtils.equals(ContractStatusEnum.CONTRACT_SUCCESS.name(), contractWithholdDO.getContractStatus())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_STATUS_UNDEFINED);
        }
        contractDeleteBO.setBankMerchantNo(contractWithholdDO.getBankMerchantNo());
        contractDeleteBO.setAppId(contractWithholdDO.getAppId());
        contractDeleteBO.setPlanId(contractWithholdDO.getPlanId());
        contractDeleteBO.setContractId(contractWithholdDO.getContractId());
        contractDeleteBO.setContractWay(contractWithholdDO.getContractWay());
        contractDeleteBO.setScene(contractWithholdDO.getContractScene());
        contractDeleteBO.setContractDisplayAccount(contractWithholdDO.getDisplayAccount());
        contractDeleteBO.setChangeType(ContractChangeTypeEnum.DELETE.name());
        contractDeleteBO.setContractExpiredTime(contractWithholdDO.getExpiredTime());
        contractDeleteBO.setContractTerminationRemark(contractDeleteBO.getExtra());
        contractDeleteBO.setContractStatus(ContractStatusEnum.CONTRACT_TERMINATED_WAIT.name());
        contractDeleteBO.setMobileNo(contractWithholdDO.getMobileNo());
        contractDeleteBO.setSignBusType(contractWithholdDO.getSignBusType());
        contractDeleteService.insertContractDeleteInfo(contractDeleteBO);
        try {
            applicationContext.publishEvent(contractDeleteBO);
            logger.info("contractDelete request success");
        } catch (Exception e) {
            String msg = null;
            if (e instanceof BusinessException) {
                msg = ((BusinessException) e).getMsgCd();
            } else if ((e instanceof RetryableException)&&e.getMessage().contains(CommonConstant.READ_TIMED_OUT)) {
                msg = MsgCodeEnum.REQUEST_RETURN_TIME_OUT.getMsgCd();
            } else {
                msg = MsgCodeEnum.REFUND_SYS_ERROR.getMsgCd();
                logger.error(msg, e);
            }
            BusinessException.throwBusinessException(msg);
        }
        return contractDeleteBO;
    }

    /**
     * 输入参数检查
     *
     * @param contractDeleteBO
     */
    private void checkDeleteInfo(ContractDeleteBO contractDeleteBO) {
        if (JudgeUtils.isNull(contractDeleteBO.getMerchantId())) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractDeleteBO.getContractCode())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CONTRACT_ORDER_NO_CANNOT_BE_EMPTY);
        }
        if (JudgeUtils.isBlank(contractDeleteBO.getNotifyUrl())) {
            BusinessException.throwBusinessException(MsgCodeEnum.NOTIFY_URL_CANNOT_BE_EMPTY);
        }

    }
}
