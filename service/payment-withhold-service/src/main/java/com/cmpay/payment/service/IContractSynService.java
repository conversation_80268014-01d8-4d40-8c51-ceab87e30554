package com.cmpay.payment.service;

import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.withhold.ContractQueryBO;
import com.cmpay.payment.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.bo.withhold.ContractWithholdQueryBO;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
public interface IContractSynService {

    /**
     * 代扣签约订单查询
     *
     * @param contractQueryBO
     * @return
     */
    void contractQuery(ContractQueryBO contractQueryBO);

    /**
     * 代扣签约通知
     *
     * @param contractQueryBO
     * @return
     */
    ContractWithholdBO contractNotify(ContractQueryBO contractQueryBO);

    /**
     * 代扣解约订单查询
     *
     * @param contractQueryBO
     * @return
     */
    void contractDeleteQuery(ContractQueryBO contractQueryBO);

    /**
     * 代扣解约微信通知
     *
     * @param contractQueryBO
     * @return
     */
    ContractWithholdBO contractDeleteNotify(ContractQueryBO contractQueryBO);

    /**
     * 代扣订单查询
     *
     * @param contractWithholdQueryBO
     * @return
     */
    TradeNotifyBO contractWithholdQuery(ContractWithholdQueryBO contractWithholdQueryBO);
}
