package com.cmpay.payment.client;

import com.cmpay.lemon.framework.stream.Source;
import com.cmpay.lemon.framework.stream.StreamClient;
import com.cmpay.payment.bo.withhold.ContractApplyWithholdBO;

/**
 * Created on 2019/05/13
 *
 * <AUTHOR>
 */
@StreamClient("integration-payment")
public interface AsyncContractPayClient {
    /**
     * 异步通知
     *
     * @param contractApplyWithholdBO
     */
    @Source(handlerBeanName = "asyncContractApplyHandler", group = "payment", prefix = "mirror.")
    void asyncContractApply(ContractApplyWithholdBO contractApplyWithholdBO);


}
