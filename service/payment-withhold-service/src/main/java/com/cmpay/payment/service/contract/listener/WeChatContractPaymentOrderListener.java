package com.cmpay.payment.service.contract.listener;

import com.cmpay.payment.bo.withhold.ContractPaymentWithholdBO;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.service.contract.ContractPaymentChannelService;
import com.cmpay.payment.service.contract.ContractPaymentListenerService;
import com.cmpay.payment.service.contract.channel.wechat.WeChatContractOrderAppService;
import com.cmpay.payment.service.contract.channel.wechat.WeChatContractOrderH5Service;
import com.cmpay.payment.service.contract.channel.wechat.WeChatContractOrderJsapiService;
import com.cmpay.payment.service.contract.channel.wechat.WeChatContractOrderScanService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatContractPaymentOrderListener extends ContractPaymentListenerService<ContractPaymentWithholdBO> {

    @Autowired
    ApplicationContext applicationContext;

    /**
     * 处理微信支付订单事件
     *
     * @param contractPaymentWithholdBO
     */
    @EventListener
    public void handleWeChatPaymentOrder(ContractPaymentWithholdBO contractPaymentWithholdBO) {
        execute(contractPaymentWithholdBO);
    }

    @Override
    protected boolean checkChannelExecutable(ContractPaymentWithholdBO contractPaymentWithholdBO) {
        return Optional.ofNullable(contractPaymentWithholdBO)
                .map(ContractPaymentWithholdBO::getPaymentRout)
                .filter(paymentRoute -> StringUtils.equals(paymentRoute, PaymentWayEnum.WECHAT.name().toLowerCase()))
                .isPresent();
    }

    @Override
    protected ContractPaymentChannelService determinateChannelExecuteBean(ContractPaymentWithholdBO contractPaymentWithholdBO) {
        return Optional.ofNullable(contractPaymentWithholdBO)
                .map(ContractPaymentWithholdBO::getScene)
                .map(String::toUpperCase)
                .map(PaymentSceneEnum::valueOf)
                .map(scene -> {
                    ContractPaymentChannelService contractPaymentChannelService = null;
                    switch (scene) {
                        case JSAPI:
                        case APPLET:
                            contractPaymentChannelService = getBean(WeChatContractOrderJsapiService.class);
                            break;
                        case SCAN:
                            contractPaymentChannelService = getBean(WeChatContractOrderScanService.class);
                            break;
                        case APP:
                            contractPaymentChannelService = getBean(WeChatContractOrderAppService.class);
                            break;
                        case WAP:
                            contractPaymentChannelService = getBean(WeChatContractOrderH5Service.class);
                            break;
                        default:
                            break;
                    }
                    return contractPaymentChannelService;
                })
                .orElse(null);
    }

    @Override
    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
