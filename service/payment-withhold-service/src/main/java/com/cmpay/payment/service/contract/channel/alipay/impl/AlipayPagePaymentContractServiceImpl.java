package com.cmpay.payment.service.contract.channel.alipay.impl;

import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.payment.bo.withhold.ContractPaymentWithholdBO;
import com.cmpay.payment.channel.OutGatePayEnum;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.contract.ContractConstants;
import com.cmpay.payment.dto.alipay.TradePagePayReq;
import com.cmpay.payment.dto.alipay.TradePagePayRsp;
import com.cmpay.payment.properties.AlipayProperties;
import com.cmpay.payment.service.contract.channel.alipay.AlipayPagePaymentContractService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.PaymentUtils;
import com.cmpay.payment.util.UrlUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
public class AlipayPagePaymentContractServiceImpl implements AlipayPagePaymentContractService {
    private static final Logger logger = LoggerFactory.getLogger(AlipayPagePaymentContractServiceImpl.class);
    @Autowired
    private AlipayProperties alipayProperties;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    ExtParamInfoService paramInfoService;


    @Override
    public void pagePaymentContract(ContractPaymentWithholdBO contractPaymentWithholdBO) {
        TradePagePayReq tradePagePayReq = new TradePagePayReq();
        tradePagePayReq.setOutTradeNo(contractPaymentWithholdBO.getTradeOrderNo());
        tradePagePayReq.setSubject(contractPaymentWithholdBO.getSubject());
        tradePagePayReq.setTotalAmount(contractPaymentWithholdBO.getRealAmount().toString());

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                tradePagePayReq.setNotifyUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getNotifyUrl(),host));
                tradePagePayReq.setReturnUrl(UrlUtils.replaceDomainOrIp(alipayProperties.getReturnUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                tradePagePayReq.setNotifyUrl(alipayProperties.getNotifyUrl());
                tradePagePayReq.setReturnUrl(alipayProperties.getReturnUrl());
            }
        } else {
            tradePagePayReq.setNotifyUrl(alipayProperties.getNotifyUrl());
            tradePagePayReq.setReturnUrl(alipayProperties.getReturnUrl());
        }

        tradePagePayReq.setPersonalProductCode(contractPaymentWithholdBO.getPlanId());
        tradePagePayReq.setProductCode(ContractConstants.GENERAL_WITHHOLDING);
        tradePagePayReq.setExternalAgreementNo(contractPaymentWithholdBO.getContractCode());
        tradePagePayReq.setSignScene(ContractConstants.INDUSTRY_CARRENTAL);
        tradePagePayReq.setExternalLogonId(contractPaymentWithholdBO.getContractDisplayAccount());
        tradePagePayReq.setIntegrationType(ContractConstants.ALIAPP);
        if (StringUtils.isNotBlank(contractPaymentWithholdBO.getTimeoutExpress())) {
            tradePagePayReq.setTimeoutExpress(contractPaymentWithholdBO.getTimeoutExpress());
        }
        // 配置子商户信息
        if (JudgeUtils.isNotBlank(contractPaymentWithholdBO.getSubMerchantName())) {
            tradePagePayReq.setSubMerchantName(contractPaymentWithholdBO.getSubMerchantName());
        }
        if (JudgeUtils.isNotBlank(contractPaymentWithholdBO.getSubMerchantServiceName())) {
            tradePagePayReq.setSubMerchantServiceName(contractPaymentWithholdBO.getSubMerchantServiceName());
        }
        if (JudgeUtils.isNotBlank(contractPaymentWithholdBO.getSubMerchantServiceDescription())) {
            tradePagePayReq.setSubMerchantServiceDescription(contractPaymentWithholdBO.getSubMerchantServiceDescription());
        }
        GenericRspDTO<Response> response = this.paymentCgwOutClient.request(PaymentUtils.buildRequest(OutGatePayEnum.TRADE_PAGE_PAY.getName(), tradePagePayReq));
        if (JudgeUtils.isNotSuccess(response.getMsgCd())) {
            BusinessException.throwBusinessException(response);
        }
        TradePagePayRsp tradePagePayRsp = (TradePagePayRsp) response.getBody().getResult();
        Response rsp = response.getBody();
        if (StringUtils.contains(response.getMsgCd(), ContractConstants.SENDGW_SUCCESS)) {

            contractPaymentWithholdBO.setPayUrl((String) tradePagePayRsp.getBody());
            Optional.ofNullable(rsp)
                    .map(Response::getResult)
                    .map(result -> {
                        return ((TradePagePayRsp) result).getBody();
                    })
                    .ifPresent(url -> {
                        contractPaymentWithholdBO.setPayUrl((String) url);
                    });
            if (JudgeUtils.equals(PaymentSceneEnum.WAP.name().toLowerCase(), contractPaymentWithholdBO.getScene())) {
                contractPaymentWithholdBO.setPayUrl(UrlUtils.alipayUrlEncode(contractPaymentWithholdBO.getPayUrl()));
            }
            contractPaymentWithholdBO.setForm(((TradePagePayRsp) rsp.getResult()).getForm());
            contractPaymentWithholdBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
        }
    }

    @Override
    public void send(ContractPaymentWithholdBO contractPaymentWithholdBO) {
        pagePaymentContract(contractPaymentWithholdBO);
    }

}
