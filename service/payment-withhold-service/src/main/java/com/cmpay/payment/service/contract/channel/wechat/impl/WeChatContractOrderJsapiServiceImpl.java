package com.cmpay.payment.service.contract.channel.wechat.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.AlertCapable;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.payment.bo.withhold.ContractPaymentWithholdBO;
import com.cmpay.payment.channel.WXPayChannel;
import com.cmpay.payment.client.IntegrationPaymentCgwOutClient;
import com.cmpay.payment.constant.CommonConstant;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.constant.NotifyUrlEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.contract.ContractStatusEnum;
import com.cmpay.payment.constant.wechat.WeChatCommonResponseEnum;
import com.cmpay.payment.constant.wechat.WeChatTradeTypeEnum;
import com.cmpay.payment.dto.wechat.WXPayJsapiSignRequest;
import com.cmpay.payment.dto.wechat.WXPayJsapiSignResponse;
import com.cmpay.payment.dto.wechat.WxPayContractOrderRequest;
import com.cmpay.payment.dto.wechat.WxPayContractOrderResponse;
import com.cmpay.payment.properties.WeChatProperties;
import com.cmpay.payment.service.contract.channel.wechat.WeChatContractOrderJsapiService;
import com.cmpay.payment.service.ext.ExtParamInfoService;
import com.cmpay.payment.util.UrlUtils;
import com.cmpay.payment.utils.JhIdGenUtils;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class WeChatContractOrderJsapiServiceImpl implements WeChatContractOrderJsapiService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatContractOrderJsapiServiceImpl.class);
    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    private IntegrationPaymentCgwOutClient paymentCgwOutClient;
    @Autowired
    ExtParamInfoService paramInfoService;

    /**
     * 微信JSAPI支付或小程序支付统一下单
     *
     * @param contractPaymentBO
     */
    @Override
    public void unifiedOrderJSAPI(ContractPaymentWithholdBO contractPaymentBO) {

        if (StringUtils.isBlank(contractPaymentBO.getWechatOpenId())) {
            BusinessException.throwBusinessException(MsgCodeEnum.REQUEST_PARAM_CANNOT_BE_EMPTY);
        }
        if (StringUtils.isBlank(contractPaymentBO.getAppId())) {
            contractPaymentBO.setAppId(weChatProperties.getAppid());
        }
        WxPayContractOrderRequest wxPayUnifiedorderRequest = new WxPayContractOrderRequest();
        // 微信对body（商品描述）字段格式按使用场景有特殊要求，H5支付的商品字段规则：浏览器打开的移动网页的主页title名-商品概述，样例：腾讯充值中心-QQ会员充值
        wxPayUnifiedorderRequest.setBody(contractPaymentBO.getSubject());
        wxPayUnifiedorderRequest.setOutTradeNo(contractPaymentBO.getOutTradeNo());
        // 金额单位：分
        wxPayUnifiedorderRequest.setTotalFee(contractPaymentBO.getRealAmount().multiply(new BigDecimal(100)).intValue());
        wxPayUnifiedorderRequest.setSpbillCreateIp(contractPaymentBO.getClientIp());
        Optional.ofNullable(contractPaymentBO.getTradeDate() + contractPaymentBO.getTradeTime()).ifPresent(timeStart -> wxPayUnifiedorderRequest.setTimeStart(timeStart));
        Optional.ofNullable(contractPaymentBO.getExpireTime()).ifPresent(expireTime -> wxPayUnifiedorderRequest.setTimeExpire(expireTime));

        String host =  paramInfoService.getNotifyUrl(NotifyUrlEnum.NOTIFY_URL.getDesc());
        if (StringUtils.isNotBlank(host)) {
            try {
                wxPayUnifiedorderRequest.setNotifyUrl(UrlUtils.replaceDomainOrIp(weChatProperties.getNotifyUrl(),host));
                wxPayUnifiedorderRequest.setContractNotifyUrl(UrlUtils.replaceDomainOrIp(weChatProperties.getContractNofityUrl(),host));
            } catch (Exception e) {
                logger.info("通知地址域名获取异常");
                wxPayUnifiedorderRequest.setNotifyUrl(weChatProperties.getNotifyUrl());
                wxPayUnifiedorderRequest.setContractNotifyUrl(weChatProperties.getContractNofityUrl());
            }
        } else {
            wxPayUnifiedorderRequest.setNotifyUrl(weChatProperties.getNotifyUrl());
            wxPayUnifiedorderRequest.setContractNotifyUrl(weChatProperties.getContractNofityUrl());
        }

        wxPayUnifiedorderRequest.setTradeType(WeChatTradeTypeEnum.JSAPI.name());
        Optional.ofNullable(contractPaymentBO.getLimitPay()).ifPresent(limitPay -> wxPayUnifiedorderRequest.setLimitPay(limitPay));
        wxPayUnifiedorderRequest.setOpenid(contractPaymentBO.getWechatOpenId());
        wxPayUnifiedorderRequest.setAppid(contractPaymentBO.getAppId());
        wxPayUnifiedorderRequest.setContractMchid(contractPaymentBO.getBankMerchantNo());
        wxPayUnifiedorderRequest.setContractAppid(contractPaymentBO.getAppId());
        wxPayUnifiedorderRequest.setPlanId(contractPaymentBO.getPlanId());
        wxPayUnifiedorderRequest.setContractCode(contractPaymentBO.getContractCode());
        wxPayUnifiedorderRequest.setRequestSerial(JhIdGenUtils.generateJhIdWithDateTime(CommonConstant.WECHAT_JRN_NO, 5));

        wxPayUnifiedorderRequest.setContractDisplayAccount(contractPaymentBO.getContractDisplayAccount());
        wxPayUnifiedorderRequest.setMchId(contractPaymentBO.getBankMerchantNo());
        wxPayUnifiedorderRequest.setKey(contractPaymentBO.getContractSecureValue());

        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.CONTRACT_PAYMENT.getSource());
        request.setRoute(WXPayChannel.CONTRACT_PAYMENT.getRoute());
        request.setBusiType(WXPayChannel.CONTRACT_PAYMENT.getBusType());
        request.setTarget(wxPayUnifiedorderRequest);

        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> handleResult(contractPaymentBO, (WxPayContractOrderResponse) result));
    }

    @Override
    public void send(ContractPaymentWithholdBO contractPaymentBO) {
        unifiedOrderJSAPI(contractPaymentBO);
    }

    private void handleResult(ContractPaymentWithholdBO contractPaymentBO, WxPayContractOrderResponse response) {
        if (StringUtils.equals(WeChatCommonResponseEnum.ReturnCode.SUCCESS.name(), response.getReturnCode())) {
            if (StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())
                    && StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getContractResultCode())) {
                contractPaymentBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
                contractPaymentBO.setContractStatus(ContractStatusEnum.CONTRACT_WAIT.name());
                contractPaymentBO.setPrepayId(response.getPrepayId());

                // 生成JSAPI页面调用的支付参数并签名
                generateJSAPISign(contractPaymentBO);
            } else if (!StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getResultCode())) {
                logger.info("unified order failure, weChat return error code is : {}, error message is : {}", response.getErrCode(), response.getErrCodeDes());
                WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getErrCode());
                AlertCapable alertCapable;
                switch (errorCodeEnum) {
                    case NOAUTH:
                        alertCapable = MsgCodeEnum.WECHAT_NO_AUTH;
                        break;
                    case NOTENOUGH:
                        alertCapable = MsgCodeEnum.WECHAT_NOT_ENOUGH;
                        break;
                    case ORDERPAID:
                        alertCapable = MsgCodeEnum.WECHAT_ORDER_PAID;
                        break;
                    case ORDERCLOSED:
                        alertCapable = MsgCodeEnum.WECHAT_ORDER_CLOSED;
                        break;
                    case OUT_TRADE_NO_USED:
                        alertCapable = MsgCodeEnum.WECHAT_OUT_TRADE_NO_USED;
                        break;
                    default:
                        alertCapable = MsgCodeEnum.CONTRACT_PAYMENT_FAILURE;
                        break;
                }
                BusinessException.throwBusinessException(alertCapable);
            } else if (!StringUtils.equals(WeChatCommonResponseEnum.ResultCode.SUCCESS.name(), response.getContractResultCode())) {
                logger.info("contracrOrder failure, weChat return error code is : {}, error message is : {}", response.getContractErrCode(), response.getContractErrCodeDes());
                WeChatCommonResponseEnum.ErrorCode errorCodeEnum = EnumUtils.getEnum(WeChatCommonResponseEnum.ErrorCode.class, response.getContractErrCode());
                AlertCapable alertCapable;
                switch (errorCodeEnum) {
                    case PARAM_ERROR:
                    default:
                        alertCapable = MsgCodeEnum.CONTRACT_PAYMENT_FAILURE;
                        break;
                }
                BusinessException.throwBusinessException(alertCapable);
            }
        }
    }

    /**
     * check out {@link //pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=7_7&index=6}.
     *
     * @param contractPaymentBO
     */
    private void generateJSAPISign(ContractPaymentWithholdBO contractPaymentBO) {
        WXPayJsapiSignRequest wxPayJsapiSignRequest = new WXPayJsapiSignRequest();
        wxPayJsapiSignRequest.setFrontPackage(String.join("=", "prepay_id", contractPaymentBO.getPrepayId()));
        wxPayJsapiSignRequest.setAppid(contractPaymentBO.getAppId());
        wxPayJsapiSignRequest.setKey(contractPaymentBO.getContractSecureValue());
        wxPayJsapiSignRequest.setMchId(contractPaymentBO.getBankMerchantNo());


        Request request = new Request();
        request.setRequestId(UUID.randomUUID().toString());
        request.setSource(WXPayChannel.JSAPI_SIGN.getSource());
        request.setRoute(WXPayChannel.JSAPI_SIGN.getRoute());
        request.setBusiType(WXPayChannel.JSAPI_SIGN.getBusType());
        request.setTarget(wxPayJsapiSignRequest);

        GenericRspDTO<Response> genericRspDTO = paymentCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));

        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }

        Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .ifPresent(result -> {
                    WXPayJsapiSignResponse response = (WXPayJsapiSignResponse) result;
                    contractPaymentBO.setWechatAppId(response.getAppid());
                    contractPaymentBO.setWechatTmStamp(response.getTimeStamp());
                    contractPaymentBO.setNonceStr(response.getNonceStr());
                    contractPaymentBO.setWechatPackage(response.getFrontPackage());
                    contractPaymentBO.setWechatSignType(response.getSignType());
                    contractPaymentBO.setWechatSign(response.getSign());
                });
    }
}
