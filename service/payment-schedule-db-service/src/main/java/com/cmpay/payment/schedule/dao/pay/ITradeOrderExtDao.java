package com.cmpay.payment.schedule.dao.pay;

import com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO;
import com.cmpay.payment.entity.TradeOrderDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @author： PengAnHai
 * @date： 2024-05-27
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Mapper
public interface ITradeOrderExtDao extends ITradeOrderDao {

    /**
     * 修改支付状态
     *
     * @param tradeOrderDO
     * @return int
     */
    int updatePaymentStatus(TradeOrderDO tradeOrderDO);

    /**
     * 锁住记录
     *
     * @param tradeOrderNo
     */
    String lock(String tradeOrderNo);


    /**
     * 分支付方式查询订单
     *
     * @param queryBO
     * @return
     */
    List<TradeOrderDO> queryPaymentChannelOrderList(FapOrderQueryBO queryBO);

    /**
     * 分支付机构查条码扫码订单
     *
     * @param queryBO
     * @return
     */
    List<TradeOrderDO> queryOrderScanBarcodeList(FapOrderQueryBO queryBO);


    /**
     * 查询签约自动扣款的订单
     *
     * @param queryBO
     * @return
     */
    List<TradeOrderDO> querySyncContractPayScheduleOrder(FapOrderQueryBO queryBO);

    /**
     * 查询工行不明订单
     *
     * @param queryBO
     * @return
     */
    List<TradeOrderDO> queryIcbcOrderScheduleList(FapOrderQueryBO queryBO);

    /**
     * 分支付方式查询订单测试订单
     *
     * @param queryBO
     * @return
     */
    List<TradeOrderDO> queryPaymentChannelTestOrderList(FapOrderQueryBO queryBO);

    /**
     * 积分超时未返回订单号订单查询
     *
     * @param queryBO
     * @return
     */
    List<TradeOrderDO> queryTimeOutPaymentChannelList(FapOrderQueryBO queryBO);

    /**
     * 条件查询 备用全量订单状态
     *
     * @param queryBO
     * @return
     */
    List<TradeOrderDO> standbyScheduleOrderQuery(FapOrderQueryBO queryBO);


}
