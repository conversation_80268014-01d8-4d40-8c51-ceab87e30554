package com.cmpay.payment.schedule.service.pay.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.framework.datasource.TargetDataSource;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO;
import com.cmpay.payment.schedule.bo.pay.PayOrderBO;
import com.cmpay.payment.schedule.dao.pay.ITradeOrderExtDao;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.schedule.service.pay.ExtPayOrderService;
import com.cmpay.payment.utils.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created on 2018/11/26
 *
 * @author: sun_zhh
 */
@Service
public class ExtPayOrderServiceImpl implements ExtPayOrderService {

    @Autowired
    ITradeOrderExtDao payTradeOrderDao;

    @Override
    @TargetDataSource("oracledb")
    public TradeOrderDO get(String id) {
        return payTradeOrderDao.get(id);
    }

    @Override
    @TargetDataSource("oracledb")
    public List<TradeOrderDO> find(TradeOrderDO obj) {
        TradeOrderDO payOrder = (obj == null) ? new TradeOrderDO() : obj;
        return payTradeOrderDao.find(payOrder);
    }


    @Override
    @TargetDataSource("oracledb")
    public int insert(TradeOrderDO entity) {
        return payTradeOrderDao.insert(entity);
    }

    @Override
    @TargetDataSource("oracledb")
    public int update(TradeOrderDO entity) {
        return payTradeOrderDao.update(entity);
    }

    @Override
    @TargetDataSource("oracledb")
    public int delete(String id) {
        return payTradeOrderDao.delete(id);
    }

    @Override
    public ITradeOrderExtDao getDao() {
        return payTradeOrderDao;
    }

    @Override
    @TargetDataSource("oracledb")
    public TradeOrderDO load(TradeOrderDO obj) {
        if (obj == null) {
            return null;
        }
        List<TradeOrderDO> ls = getDao().find(obj);
        return (ls != null && ls.size() == 1) ? ls.get(0) : null;
    }

    @Override
    @TargetDataSource("oracledb")
    public void lock(TradeOrderDO tradeOrderDO) {
        payTradeOrderDao.lock(tradeOrderDO.getTradeOrderNo());
    }

    @Override
    @TargetDataSource("oracledb")
    public void updateByNewTranscation(TradeOrderDO obj) {
        if (update(obj) != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_STATUS_UPDATE_FALSE);
        }
    }


    @Override
    @TargetDataSource("oracledb")
    public Integer updatePaymentStatus(TradeOrderDO tradeOrderDO) {
        tradeOrderDO.setTmSmp(DateTimeUtils.getCurrentDateTimeStr());
        return payTradeOrderDao.updatePaymentStatus(tradeOrderDO);
    }


    @Override
    @TargetDataSource("oracledb")
    public TradeOrderDO get(PayOrderBO payOrderBO) {
        TradeOrderDO tradeOrderDO = get(payOrderBO.getTradeOrderNo());
        if (tradeOrderDO == null) {
            return tradeOrderDO;
        }
        payOrderBO.setMerchantId(tradeOrderDO.getMerchantNo());
        payOrderBO.setSubject(tradeOrderDO.getSubject());
        payOrderBO.setBuyerId(tradeOrderDO.getBuyerId());
        payOrderBO.setProductCode(tradeOrderDO.getGoodsId());
        payOrderBO.setProductName(tradeOrderDO.getGoodsName());
        payOrderBO.setProductDesc(tradeOrderDO.getGoodsDesc());
        payOrderBO.setOutTradeNo(tradeOrderDO.getOutTradeNo());
        payOrderBO.setScene(tradeOrderDO.getPayWayCode());
        payOrderBO.setPaymentRout(tradeOrderDO.getAimProductCode());
        payOrderBO.setPayWay(tradeOrderDO.getPayProductCode());
        payOrderBO.setTotalAmount(tradeOrderDO.getOrderAmount());
        payOrderBO.setRealAmount(tradeOrderDO.getRealAmount());
        payOrderBO.setDiscountableAmount(tradeOrderDO.getDiscountableAmount());
        payOrderBO.setTradeDate(tradeOrderDO.getRequestDate());
        payOrderBO.setExpireTime(tradeOrderDO.getExpireTime());
        payOrderBO.setProductUrl(tradeOrderDO.getOrderRefererUrl());
        payOrderBO.setWechatAppId(tradeOrderDO.getWechatAppid());

        payOrderBO.setWechatOpenId(tradeOrderDO.getWechatOpenid());
        payOrderBO.setAuthCode(tradeOrderDO.getAuthCode());
        payOrderBO.setHallAreaCode(tradeOrderDO.getHallAreaCode());
        payOrderBO.setTerminalCode(tradeOrderDO.getTerminalCode());
        payOrderBO.setHallCode(tradeOrderDO.getHallCode());
        payOrderBO.setClerkCode(tradeOrderDO.getClerkCode());
        payOrderBO.setHallWindowCode(tradeOrderDO.getHallWindowCode());
        payOrderBO.setOperatorId(tradeOrderDO.getOperatorId());
        payOrderBO.setPageNotifyUrl(tradeOrderDO.getReturnUrl());
        payOrderBO.setNotifyUrl(tradeOrderDO.getNotifyUrl());
        payOrderBO.setPaymentId(tradeOrderDO.getBankMerchantNo());
        payOrderBO.setTradeOrderNo(tradeOrderDO.getBankOrderNo());
        payOrderBO.setExtra(tradeOrderDO.getRemark());
        payOrderBO.setErrMsgCd(tradeOrderDO.getErrMsgCd());
        payOrderBO.setErrMsgInfo(tradeOrderDO.getErrMsgInfo());
        return tradeOrderDO;
    }

    @Override
    @TargetDataSource("oracledb")
    public List<TradeOrderDO> queryWaitPaymentChannelList(FapOrderQueryBO queryBO) {
        return payTradeOrderDao.queryPaymentChannelOrderList(queryBO);
    }

    @Override
    @TargetDataSource("oracledb")
    public List<TradeOrderDO> queryOrderScanBarcodeList(FapOrderQueryBO queryBO) {
        return payTradeOrderDao.queryOrderScanBarcodeList(queryBO);
    }


    @Override
    @TargetDataSource("oracledb")
    public List<TradeOrderDO> queryWaitContractPaymentScheduleList(FapOrderQueryBO queryBO) {
        return payTradeOrderDao.querySyncContractPayScheduleOrder(queryBO);
    }

    @Override
    @TargetDataSource("oracledb")
    public List<TradeOrderDO> queryIcbcWaitPaymentScheduleList(FapOrderQueryBO queryBO) {
        return payTradeOrderDao.queryIcbcOrderScheduleList(queryBO);
    }

    @Override
    @TargetDataSource("oracledb")
    public List<TradeOrderDO> queryWaitPaymentChannelTestList(FapOrderQueryBO queryBO) {
        return payTradeOrderDao.queryPaymentChannelTestOrderList(queryBO);
    }

    @Override
    @TargetDataSource("oracledb")
    public List<TradeOrderDO> queryTimeOutPaymentChannelList(FapOrderQueryBO queryBO) {
        return payTradeOrderDao.queryTimeOutPaymentChannelList(queryBO);
    }


    @Override
    @TargetDataSource("oracledb")
    public List<TradeOrderDO> standbyScheduleOrderQuery(FapOrderQueryBO queryBO) {
        return payTradeOrderDao.standbyScheduleOrderQuery(queryBO);
    }


}
