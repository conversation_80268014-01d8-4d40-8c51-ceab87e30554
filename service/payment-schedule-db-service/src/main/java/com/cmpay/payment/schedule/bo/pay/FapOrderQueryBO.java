package com.cmpay.payment.schedule.bo.pay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created on 18:14 2019/5/21
 *
 * <AUTHOR>
 * <p>
 * description
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class FapOrderQueryBO {
    private String orgOrderNo;

    /**
     *
     交易订单号：
     */
    private String tradeOrderNo;
    /**
     * 商户编号：
     */
    private String merchantNo;
    /**
     * 商户订单号：
     */
    private String outTradeNo;
    /**
     * 订单类型
     */
    private String orderType;
    /**
     * 订单状态：
     */
    private String status;
    /**
     * 支付机构
     */
    private String payProductCode;
    /**
     * 支付渠道：
     */
    private String aimProductCode;
    /**
     * 支付场景：
     */
    private String payWayCode;
    /**
     * 省份：
     */
    private String provinceCode;
    /**
     * 业务类型：
     */
    private String businessType;
    /**
     * 订单请求日期
     */
    private String requestDate;
    /**
     * 账期
     */
    private String accountDate;
    /**
     * 订单日期
     */
    private String orderDate;

    /**
     * 开始时间.
     */
    private String startTime;
    /**
     * 结束时间.
     */
    private String endTime;
    /**
     * 第三方订单号
     */
    private String thirdOrdNo;
    /**
     * 订单日期
     */
    private String orderTime;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页记录数
     */
    private Integer pageSize;
    /**
     * 查询总条数
     */
    private String total;
    /**
     * 总分片数
     */
    private Integer totalSelect;
    /**
     * 当前分片
     */
    private Integer currentIndex;

    private String[] payArray;
    private String[] aimArray;

    private String[] payWayArray;

    /**
     *  机房标识
     */
    private String dataCenter;

}
