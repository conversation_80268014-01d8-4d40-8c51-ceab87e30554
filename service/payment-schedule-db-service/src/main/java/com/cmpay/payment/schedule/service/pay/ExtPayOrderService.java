package com.cmpay.payment.schedule.service.pay;


import com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO;
import com.cmpay.payment.schedule.bo.pay.PayOrderBO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.component.db.IPaymentExtService;
import java.util.List;

/**
 * Created on 2018/11/26
 * 支付订单表DO层扩展类
 *
 * @author: sun_zhh
 */
public interface ExtPayOrderService extends IPaymentExtService<TradeOrderDO, String> {

	/**
	 * 更新带事物
	 * @param tradeOrderDO
	 */
	void updateByNewTranscation(TradeOrderDO tradeOrderDO);

	/**
	 * 更新状态
	 * @param tradeOrderDO
	 */
	Integer updatePaymentStatus(TradeOrderDO tradeOrderDO);

	/**
	 * 获取订单信息
	 * @param payOrderBO
	 * @return
	 */
	TradeOrderDO get(PayOrderBO payOrderBO);




	/**
	 * 分支付机构查单
	 * @param queryBO
	 * @return
	 */
	List<TradeOrderDO> queryWaitPaymentChannelList(FapOrderQueryBO queryBO);

	/**
	 * 分支付机构查询扫码条码订单
	 *
	 * @param queryBO
	 * @return
	 */
	List<TradeOrderDO> queryOrderScanBarcodeList(FapOrderQueryBO queryBO);



	/**
	 * 查询待签约扣款支付订单
	 * @param queryBO
	 * @return
	 */
	List<TradeOrderDO> queryWaitContractPaymentScheduleList(FapOrderQueryBO queryBO);

	/**
	 * 查询工行不明订单
	 * @param queryBO
	 * @return
	 */
	List<TradeOrderDO> queryIcbcWaitPaymentScheduleList(FapOrderQueryBO queryBO);

	/**
	 * 分支付机构查单
	 * @param queryBO
	 * @return
	 */
	List<TradeOrderDO> queryWaitPaymentChannelTestList(FapOrderQueryBO queryBO);


	/**
	 *积分超时未返回订单号订单查询
	 * @param queryBO
	 * @return
	 */
	List<TradeOrderDO> queryTimeOutPaymentChannelList(FapOrderQueryBO queryBO);





	/**
	 * 条件查询 备用全量订单状态
	 * @param queryBO
	 * @return
	 */
    List<TradeOrderDO> standbyScheduleOrderQuery(FapOrderQueryBO queryBO);

}
