<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.payment.dao.IFapCustomerComplaintDao">
    <select id="findNotFinishedList" resultMap="BaseResultMap"
            parameterType="com.cmpay.payment.entity.FapCustomerComplaintDO">
        select
        <include refid="Base_Column_List"/>
        from FAP_CUSTOMER_COMPLAINT_RECORD
        where COMPLAINT_DATE between #{beginDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
        and COMPLAINT_STATE IN ('PENDING','PROCESSING')
    </select>
</mapper>