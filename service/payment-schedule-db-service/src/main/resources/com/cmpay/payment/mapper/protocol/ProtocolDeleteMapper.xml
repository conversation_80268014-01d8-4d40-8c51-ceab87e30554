<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.payment.schedule.dao.protocol.IProtocolDeleteDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.payment.schedule.entity.protocol.ProtocolDeleteDO" >
        <id column="JRN_NO" property="jrnNo" jdbcType="VARCHAR" />
        <id column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="VARCHAR" />
        <result column="AGREEMENT_REQ_NO" property="agreementReqNo" jdbcType="VARCHAR" />
        <result column="AGREEMENT_REQ_DATE" property="agreementReqDate" jdbcType="VARCHAR" />
        <result column="AGREEMENT_ID" property="agreementId" jdbcType="VARCHAR" />
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR" />
        <result column="BANK_MERCHANT_NO" property="bankMerchantNo" jdbcType="VARCHAR" />
        <result column="CHANGE_TYPE" property="changeType" jdbcType="VARCHAR" />
        <result column="SIGN_WAY" property="signWay" jdbcType="VARCHAR" />
        <result column="SIGN_SCENE" property="signScene" jdbcType="VARCHAR" />
        <result column="SIGN_STATUS" property="signStatus" jdbcType="VARCHAR" />
        <result column="RETURN_CODE" property="returnCode" jdbcType="VARCHAR" />
        <result column="RETURN_MSG" property="returnMsg" jdbcType="VARCHAR" />
        <result column="SIGNED_TIME" property="signedTime" jdbcType="VARCHAR" />
        <result column="TERMINATED_TIME" property="terminatedTime" jdbcType="VARCHAR" />
        <result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR" />
        <result column="EXTRA" property="extra" jdbcType="VARCHAR" />
        <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        JRN_NO, CREATE_DATE, CREATE_TIME, AGREEMENT_REQ_NO, AGREEMENT_REQ_DATE, AGREEMENT_ID, 
        MERCHANT_NO, BANK_MERCHANT_NO, CHANGE_TYPE, SIGN_WAY, SIGN_SCENE, SIGN_STATUS, RETURN_CODE, 
        RETURN_MSG, SIGNED_TIME, TERMINATED_TIME, NOTIFY_URL, EXTRA, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.payment.schedule.entity.protocol.ProtocolDeleteDOKey" >
        select 
        <include refid="Base_Column_List" />
        from JK_PROTOCOL_DELETE
        where JRN_NO = #{jrnNo,jdbcType=VARCHAR}
          and CREATE_DATE = #{createDate,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.payment.schedule.entity.protocol.ProtocolDeleteDOKey" >
        delete from JK_PROTOCOL_DELETE
        where JRN_NO = #{jrnNo,jdbcType=VARCHAR}
          and CREATE_DATE = #{createDate,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.payment.schedule.entity.protocol.ProtocolDeleteDO" >
        insert into JK_PROTOCOL_DELETE
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                JRN_NO,
            </if>
            <if test="createDate != null" >
                CREATE_DATE,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="agreementReqNo != null" >
                AGREEMENT_REQ_NO,
            </if>
            <if test="agreementReqDate != null" >
                AGREEMENT_REQ_DATE,
            </if>
            <if test="agreementId != null" >
                AGREEMENT_ID,
            </if>
            <if test="merchantNo != null" >
                MERCHANT_NO,
            </if>
            <if test="bankMerchantNo != null" >
                BANK_MERCHANT_NO,
            </if>
            <if test="changeType != null" >
                CHANGE_TYPE,
            </if>
            <if test="signWay != null" >
                SIGN_WAY,
            </if>
            <if test="signScene != null" >
                SIGN_SCENE,
            </if>
            <if test="signStatus != null" >
                SIGN_STATUS,
            </if>
            <if test="returnCode != null" >
                RETURN_CODE,
            </if>
            <if test="returnMsg != null" >
                RETURN_MSG,
            </if>
            <if test="signedTime != null" >
                SIGNED_TIME,
            </if>
            <if test="terminatedTime != null" >
                TERMINATED_TIME,
            </if>
            <if test="notifyUrl != null" >
                NOTIFY_URL,
            </if>
            <if test="extra != null" >
                EXTRA,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                #{jrnNo,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null" >
                #{createDate,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="agreementReqNo != null" >
                #{agreementReqNo,jdbcType=VARCHAR},
            </if>
            <if test="agreementReqDate != null" >
                #{agreementReqDate,jdbcType=VARCHAR},
            </if>
            <if test="agreementId != null" >
                #{agreementId,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="bankMerchantNo != null" >
                #{bankMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="changeType != null" >
                #{changeType,jdbcType=VARCHAR},
            </if>
            <if test="signWay != null" >
                #{signWay,jdbcType=VARCHAR},
            </if>
            <if test="signScene != null" >
                #{signScene,jdbcType=VARCHAR},
            </if>
            <if test="signStatus != null" >
                #{signStatus,jdbcType=VARCHAR},
            </if>
            <if test="returnCode != null" >
                #{returnCode,jdbcType=VARCHAR},
            </if>
            <if test="returnMsg != null" >
                #{returnMsg,jdbcType=VARCHAR},
            </if>
            <if test="signedTime != null" >
                #{signedTime,jdbcType=VARCHAR},
            </if>
            <if test="terminatedTime != null" >
                #{terminatedTime,jdbcType=VARCHAR},
            </if>
            <if test="notifyUrl != null" >
                #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="extra != null" >
                #{extra,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.payment.schedule.entity.protocol.ProtocolDeleteDO" >
        update JK_PROTOCOL_DELETE
        <set >
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="agreementReqNo != null" >
                AGREEMENT_REQ_NO = #{agreementReqNo,jdbcType=VARCHAR},
            </if>
            <if test="agreementReqDate != null" >
                AGREEMENT_REQ_DATE = #{agreementReqDate,jdbcType=VARCHAR},
            </if>
            <if test="agreementId != null" >
                AGREEMENT_ID = #{agreementId,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="bankMerchantNo != null" >
                BANK_MERCHANT_NO = #{bankMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="changeType != null" >
                CHANGE_TYPE = #{changeType,jdbcType=VARCHAR},
            </if>
            <if test="signWay != null" >
                SIGN_WAY = #{signWay,jdbcType=VARCHAR},
            </if>
            <if test="signScene != null" >
                SIGN_SCENE = #{signScene,jdbcType=VARCHAR},
            </if>
            <if test="signStatus != null" >
                SIGN_STATUS = #{signStatus,jdbcType=VARCHAR},
            </if>
            <if test="returnCode != null" >
                RETURN_CODE = #{returnCode,jdbcType=VARCHAR},
            </if>
            <if test="returnMsg != null" >
                RETURN_MSG = #{returnMsg,jdbcType=VARCHAR},
            </if>
            <if test="signedTime != null" >
                SIGNED_TIME = #{signedTime,jdbcType=VARCHAR},
            </if>
            <if test="terminatedTime != null" >
                TERMINATED_TIME = #{terminatedTime,jdbcType=VARCHAR},
            </if>
            <if test="notifyUrl != null" >
                NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="extra != null" >
                EXTRA = #{extra,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where JRN_NO = #{jrnNo,jdbcType=VARCHAR}
          and CREATE_DATE = #{createDate,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.payment.schedule.entity.protocol.ProtocolDeleteDO" >
        select 
        <include refid="Base_Column_List" />
        from JK_PROTOCOL_DELETE
        <where >
            <if test="jrnNo != null" >
                and JRN_NO = #{jrnNo,jdbcType=VARCHAR}
            </if>
            <if test="createDate != null" >
                and CREATE_DATE = #{createDate,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and CREATE_TIME = #{createTime,jdbcType=VARCHAR}
            </if>
            <if test="agreementReqNo != null" >
                and AGREEMENT_REQ_NO = #{agreementReqNo,jdbcType=VARCHAR}
            </if>
            <if test="agreementReqDate != null" >
                and AGREEMENT_REQ_DATE = #{agreementReqDate,jdbcType=VARCHAR}
            </if>
            <if test="agreementId != null" >
                and AGREEMENT_ID = #{agreementId,jdbcType=VARCHAR}
            </if>
            <if test="merchantNo != null" >
                and MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="bankMerchantNo != null" >
                and BANK_MERCHANT_NO = #{bankMerchantNo,jdbcType=VARCHAR}
            </if>
            <if test="changeType != null" >
                and CHANGE_TYPE = #{changeType,jdbcType=VARCHAR}
            </if>
            <if test="signWay != null" >
                and SIGN_WAY = #{signWay,jdbcType=VARCHAR}
            </if>
            <if test="signScene != null" >
                and SIGN_SCENE = #{signScene,jdbcType=VARCHAR}
            </if>
            <if test="signStatus != null" >
                and SIGN_STATUS = #{signStatus,jdbcType=VARCHAR}
            </if>
            <if test="returnCode != null" >
                and RETURN_CODE = #{returnCode,jdbcType=VARCHAR}
            </if>
            <if test="returnMsg != null" >
                and RETURN_MSG = #{returnMsg,jdbcType=VARCHAR}
            </if>
            <if test="signedTime != null" >
                and SIGNED_TIME = #{signedTime,jdbcType=VARCHAR}
            </if>
            <if test="terminatedTime != null" >
                and TERMINATED_TIME = #{terminatedTime,jdbcType=VARCHAR}
            </if>
            <if test="notifyUrl != null" >
                and NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR}
            </if>
            <if test="extra != null" >
                and EXTRA = #{extra,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and TM_SMP = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>