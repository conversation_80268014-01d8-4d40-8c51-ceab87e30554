<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.payment.schedule.dao.settlement.ITradeSettlementDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.payment.schedule.entity.settlement.TradeSettlementDO" >
        <id column="TRADE_JRN_NO" property="tradeJrnNo" jdbcType="VARCHAR" />
        <id column="SETTLEMENT_DATE" property="settlementDate" jdbcType="VARCHAR" />
        <result column="ORDER_DATE" property="orderDate" jdbcType="VARCHAR" />
        <result column="ORDER_TIME" property="orderTime" jdbcType="VARCHAR" />
        <result column="RECORD_STATUS" property="recordStatus" jdbcType="VARCHAR" />
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR" />
        <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR" />
        <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR" />
        <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
        <result column="REQUEST_DATE" property="requestDate" jdbcType="VARCHAR" />
        <result column="TRADE_REFUND_NO" property="tradeRefundNo" jdbcType="VARCHAR" />
        <result column="BANK_ORDER_NO" property="bankOrderNo" jdbcType="VARCHAR" />
        <result column="BANK_MERCHANT_NO" property="bankMerchantNo" jdbcType="VARCHAR" />
        <result column="PAYMENT_CHANNEL" property="paymentChannel" jdbcType="VARCHAR" />
        <result column="ORDER_SCENE" property="orderScene" jdbcType="VARCHAR" />
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL" />
        <result column="PAYMENT_AMOUNT" property="paymentAmount" jdbcType="DECIMAL" />
        <result column="PAYMENT_FEE_AMOUNT" property="paymentFeeAmount" jdbcType="DECIMAL" />
        <result column="REFUND_AMOUNT" property="refundAmount" jdbcType="DECIMAL" />
        <result column="REFUND_FEE_AMOUNT" property="refundFeeAmount" jdbcType="DECIMAL" />
        <result column="ORDER_RATE" property="orderRate" jdbcType="DECIMAL" />
        <result column="ORDER_COMPLETE_TIME" property="orderCompleteTime" jdbcType="VARCHAR" />
        <result column="SETTLEMENT_STATUS" property="settlementStatus" jdbcType="VARCHAR" />
        <result column="CARD_TYPE" property="cardType" jdbcType="VARCHAR" />
        <result column="REFUND_FEE_WAY" property="refundFeeWay" jdbcType="VARCHAR" />
        <result column="RECONCILIATION_FLAG" property="reconciliationFlag" jdbcType="VARCHAR" />
        <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR" />
        <result column="BANK_FEE_AMOUNT" property="bankFeeAmount" jdbcType="DECIMAL" />
        <result column="REMARK" property="remark" jdbcType="VARCHAR" />
        <result column="CHECK_COMPLETE_DATE" property="checkCompleteDate" jdbcType="VARCHAR" />
        <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR" />
        <result column="AIM_PRODUCT_CODE" property="aimProductCode" jdbcType="VARCHAR" />
        <result column="ACCOUNT_DATE" property="accountDate" jdbcType="VARCHAR" />
        <result column="TRADE_ORDER_NO" property="tradeOrderNo" jdbcType="VARCHAR" />
        <result column="DCEP_FLAG" property="dcepFlag" jdbcType="VARCHAR" />
        <result column="JK_FEE_AMOUNT" property="jkFeeAmount" jdbcType="DECIMAL" />
        <result column="SERVICE_CHARGE" property="serviceCharge" jdbcType="VARCHAR" />
        <result column="SUB_MERCHANT_NO" property="subMerchantNo" jdbcType="VARCHAR" />
        <result column="SETTLEMENT_DEPT" property="settlementDept" jdbcType="VARCHAR" />
        <result column="SETTLEMENT_ITEM" property="settlementItem" jdbcType="VARCHAR" />
        <result column="MERCHANT_CHANNEL_TYPE" property="merchantChannelType" jdbcType="VARCHAR" />
        <result column="SPLIT_FLAG" property="splitFlag" jdbcType="VARCHAR" />
        <result column="MASTER_ORDER_NO" property="masterOrderNo" jdbcType="VARCHAR" />
        <result column="ORDER_POINTS" property="orderPoints" jdbcType="DECIMAL" />
        <result column="INST_DISCOUNT_SETTLEMENT_AMOUNT" property="instDiscountSettlementAmount" jdbcType="DECIMAL" />
        <result column="INST_DISCOUNT_UNSETTLED_AMOUNT" property="instDiscountUnsettledAmount" jdbcType="DECIMAL" />
        <result column="INST_PAID_AMOUNT" property="instPaidAmount" jdbcType="DECIMAL" />
    </resultMap>

    <sql id="Base_Column_List" >
        TRADE_JRN_NO, SETTLEMENT_DATE, ORDER_DATE, ORDER_TIME, RECORD_STATUS, MERCHANT_NO, 
        PROVINCE_CODE, PROVINCE_NAME, OUT_TRADE_NO, REQUEST_DATE, TRADE_REFUND_NO, BANK_ORDER_NO, 
        BANK_MERCHANT_NO, PAYMENT_CHANNEL, ORDER_SCENE, BUSINESS_TYPE, ORDER_AMOUNT, PAYMENT_AMOUNT, 
        PAYMENT_FEE_AMOUNT, REFUND_AMOUNT, REFUND_FEE_AMOUNT, ORDER_RATE, ORDER_COMPLETE_TIME, 
        SETTLEMENT_STATUS, CARD_TYPE, REFUND_FEE_WAY, RECONCILIATION_FLAG, BATCH_NO, BANK_FEE_AMOUNT, 
        REMARK, CHECK_COMPLETE_DATE, TM_SMP, AIM_PRODUCT_CODE, ACCOUNT_DATE, TRADE_ORDER_NO, 
        DCEP_FLAG, JK_FEE_AMOUNT, SERVICE_CHARGE, SUB_MERCHANT_NO, SETTLEMENT_DEPT, SETTLEMENT_ITEM, 
        MERCHANT_CHANNEL_TYPE, SPLIT_FLAG, MASTER_ORDER_NO, ORDER_POINTS, INST_DISCOUNT_SETTLEMENT_AMOUNT,
        INST_DISCOUNT_UNSETTLED_AMOUNT, INST_PAID_AMOUNT
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.payment.schedule.entity.settlement.TradeSettlementDOKey" >
        select
        <include refid="Base_Column_List" />
        from JK_TRADE_SETTLEMENT
        where TRADE_JRN_NO = #{tradeJrnNo,jdbcType=VARCHAR}
          and SETTLEMENT_DATE = #{settlementDate,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.payment.schedule.entity.settlement.TradeSettlementDOKey" >
        delete from JK_TRADE_SETTLEMENT
        where TRADE_JRN_NO = #{tradeJrnNo,jdbcType=VARCHAR}
          and SETTLEMENT_DATE = #{settlementDate,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.payment.schedule.entity.settlement.TradeSettlementDO" >
        insert into JK_TRADE_SETTLEMENT
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="tradeJrnNo != null" >
                TRADE_JRN_NO,
            </if>
            <if test="settlementDate != null" >
                SETTLEMENT_DATE,
            </if>
            <if test="orderDate != null" >
                ORDER_DATE,
            </if>
            <if test="orderTime != null" >
                ORDER_TIME,
            </if>
            <if test="recordStatus != null" >
                RECORD_STATUS,
            </if>
            <if test="merchantNo != null" >
                MERCHANT_NO,
            </if>
            <if test="provinceCode != null" >
                PROVINCE_CODE,
            </if>
            <if test="provinceName != null" >
                PROVINCE_NAME,
            </if>
            <if test="outTradeNo != null" >
                OUT_TRADE_NO,
            </if>
            <if test="requestDate != null" >
                REQUEST_DATE,
            </if>
            <if test="tradeRefundNo != null" >
                TRADE_REFUND_NO,
            </if>
            <if test="bankOrderNo != null" >
                BANK_ORDER_NO,
            </if>
            <if test="bankMerchantNo != null" >
                BANK_MERCHANT_NO,
            </if>
            <if test="paymentChannel != null" >
                PAYMENT_CHANNEL,
            </if>
            <if test="orderScene != null" >
                ORDER_SCENE,
            </if>
            <if test="businessType != null" >
                BUSINESS_TYPE,
            </if>
            <if test="orderAmount != null" >
                ORDER_AMOUNT,
            </if>
            <if test="paymentAmount != null" >
                PAYMENT_AMOUNT,
            </if>
            <if test="paymentFeeAmount != null" >
                PAYMENT_FEE_AMOUNT,
            </if>
            <if test="refundAmount != null" >
                REFUND_AMOUNT,
            </if>
            <if test="refundFeeAmount != null" >
                REFUND_FEE_AMOUNT,
            </if>
            <if test="orderRate != null" >
                ORDER_RATE,
            </if>
            <if test="orderCompleteTime != null" >
                ORDER_COMPLETE_TIME,
            </if>
            <if test="settlementStatus != null" >
                SETTLEMENT_STATUS,
            </if>
            <if test="cardType != null" >
                CARD_TYPE,
            </if>
            <if test="refundFeeWay != null" >
                REFUND_FEE_WAY,
            </if>
            <if test="reconciliationFlag != null" >
                RECONCILIATION_FLAG,
            </if>
            <if test="batchNo != null" >
                BATCH_NO,
            </if>
            <if test="bankFeeAmount != null" >
                BANK_FEE_AMOUNT,
            </if>
            <if test="remark != null" >
                REMARK,
            </if>
            <if test="checkCompleteDate != null" >
                CHECK_COMPLETE_DATE,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
            <if test="aimProductCode != null" >
                AIM_PRODUCT_CODE,
            </if>
            <if test="accountDate != null" >
                ACCOUNT_DATE,
            </if>
            <if test="tradeOrderNo != null" >
                TRADE_ORDER_NO,
            </if>
            <if test="dcepFlag != null" >
                DCEP_FLAG,
            </if>
            <if test="jkFeeAmount != null" >
                JK_FEE_AMOUNT,
            </if>
            <if test="serviceCharge != null" >
                SERVICE_CHARGE,
            </if>
            <if test="subMerchantNo != null" >
                SUB_MERCHANT_NO,
            </if>
            <if test="settlementDept != null" >
                SETTLEMENT_DEPT,
            </if>
            <if test="settlementItem != null" >
                SETTLEMENT_ITEM,
            </if>
            <if test="merchantChannelType != null" >
                MERCHANT_CHANNEL_TYPE,
            </if>
            <if test="splitFlag != null" >
                SPLIT_FLAG,
            </if>
            <if test="masterOrderNo != null" >
                MASTER_ORDER_NO,
            </if>
            <if test="orderPoints != null" >
                ORDER_POINTS,
            </if>
            <if test="instDiscountSettlementAmount != null" >
                INST_DISCOUNT_SETTLEMENT_AMOUNT,
            </if>
            <if test="instDiscountUnsettledAmount != null" >
                INST_DISCOUNT_UNSETTLED_AMOUNT,
            </if>
            <if test="instPaidAmount != null" >
                INST_PAID_AMOUNT,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="tradeJrnNo != null" >
                #{tradeJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="settlementDate != null" >
                #{settlementDate,jdbcType=VARCHAR},
            </if>
            <if test="orderDate != null" >
                #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="orderTime != null" >
                #{orderTime,jdbcType=VARCHAR},
            </if>
            <if test="recordStatus != null" >
                #{recordStatus,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null" >
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null" >
                #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="outTradeNo != null" >
                #{outTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="requestDate != null" >
                #{requestDate,jdbcType=VARCHAR},
            </if>
            <if test="tradeRefundNo != null" >
                #{tradeRefundNo,jdbcType=VARCHAR},
            </if>
            <if test="bankOrderNo != null" >
                #{bankOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="bankMerchantNo != null" >
                #{bankMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="paymentChannel != null" >
                #{paymentChannel,jdbcType=VARCHAR},
            </if>
            <if test="orderScene != null" >
                #{orderScene,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null" >
                #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="orderAmount != null" >
                #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentAmount != null" >
                #{paymentAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentFeeAmount != null" >
                #{paymentFeeAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundAmount != null" >
                #{refundAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundFeeAmount != null" >
                #{refundFeeAmount,jdbcType=DECIMAL},
            </if>
            <if test="orderRate != null" >
                #{orderRate,jdbcType=DECIMAL},
            </if>
            <if test="orderCompleteTime != null" >
                #{orderCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="settlementStatus != null" >
                #{settlementStatus,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null" >
                #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="refundFeeWay != null" >
                #{refundFeeWay,jdbcType=VARCHAR},
            </if>
            <if test="reconciliationFlag != null" >
                #{reconciliationFlag,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null" >
                #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="bankFeeAmount != null" >
                #{bankFeeAmount,jdbcType=DECIMAL},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="checkCompleteDate != null" >
                #{checkCompleteDate,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
            <if test="aimProductCode != null" >
                #{aimProductCode,jdbcType=VARCHAR},
            </if>
            <if test="accountDate != null" >
                #{accountDate,jdbcType=VARCHAR},
            </if>
            <if test="tradeOrderNo != null" >
                #{tradeOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="dcepFlag != null" >
                #{dcepFlag,jdbcType=VARCHAR},
            </if>
            <if test="jkFeeAmount != null" >
                #{jkFeeAmount,jdbcType=DECIMAL},
            </if>
            <if test="serviceCharge != null" >
                #{serviceCharge,jdbcType=VARCHAR},
            </if>
            <if test="subMerchantNo != null" >
                #{subMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="settlementDept != null" >
                #{settlementDept,jdbcType=VARCHAR},
            </if>
            <if test="settlementItem != null" >
                #{settlementItem,jdbcType=VARCHAR},
            </if>
            <if test="merchantChannelType != null" >
                #{merchantChannelType,jdbcType=VARCHAR},
            </if>
            <if test="splitFlag != null" >
                #{splitFlag,jdbcType=VARCHAR},
            </if>
            <if test="masterOrderNo != null" >
                #{masterOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderPoints != null" >
                #{orderPoints,jdbcType=DECIMAL},
            </if>
            <if test="instDiscountSettlementAmount != null" >
                #{instDiscountSettlementAmount,jdbcType=DECIMAL},
            </if>
            <if test="instDiscountUnsettledAmount != null" >
                #{instDiscountUnsettledAmount,jdbcType=DECIMAL},
            </if>
            <if test="instPaidAmount != null" >
                #{instPaidAmount,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.payment.schedule.entity.settlement.TradeSettlementDO" >
        update JK_TRADE_SETTLEMENT
        <set >
            <if test="orderDate != null" >
                ORDER_DATE = #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="orderTime != null" >
                ORDER_TIME = #{orderTime,jdbcType=VARCHAR},
            </if>
            <if test="recordStatus != null" >
                RECORD_STATUS = #{recordStatus,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null" >
                PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null" >
                PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="outTradeNo != null" >
                OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="requestDate != null" >
                REQUEST_DATE = #{requestDate,jdbcType=VARCHAR},
            </if>
            <if test="tradeRefundNo != null" >
                TRADE_REFUND_NO = #{tradeRefundNo,jdbcType=VARCHAR},
            </if>
            <if test="bankOrderNo != null" >
                BANK_ORDER_NO = #{bankOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="bankMerchantNo != null" >
                BANK_MERCHANT_NO = #{bankMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="paymentChannel != null" >
                PAYMENT_CHANNEL = #{paymentChannel,jdbcType=VARCHAR},
            </if>
            <if test="orderScene != null" >
                ORDER_SCENE = #{orderScene,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null" >
                BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="orderAmount != null" >
                ORDER_AMOUNT = #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentAmount != null" >
                PAYMENT_AMOUNT = #{paymentAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentFeeAmount != null" >
                PAYMENT_FEE_AMOUNT = #{paymentFeeAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundAmount != null" >
                REFUND_AMOUNT = #{refundAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundFeeAmount != null" >
                REFUND_FEE_AMOUNT = #{refundFeeAmount,jdbcType=DECIMAL},
            </if>
            <if test="orderRate != null" >
                ORDER_RATE = #{orderRate,jdbcType=DECIMAL},
            </if>
            <if test="orderCompleteTime != null" >
                ORDER_COMPLETE_TIME = #{orderCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="settlementStatus != null" >
                SETTLEMENT_STATUS = #{settlementStatus,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null" >
                CARD_TYPE = #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="refundFeeWay != null" >
                REFUND_FEE_WAY = #{refundFeeWay,jdbcType=VARCHAR},
            </if>
            <if test="reconciliationFlag != null" >
                RECONCILIATION_FLAG = #{reconciliationFlag,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null" >
                BATCH_NO = #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="bankFeeAmount != null" >
                BANK_FEE_AMOUNT = #{bankFeeAmount,jdbcType=DECIMAL},
            </if>
            <if test="remark != null" >
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="checkCompleteDate != null" >
                CHECK_COMPLETE_DATE = #{checkCompleteDate,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=VARCHAR},
            </if>
            <if test="aimProductCode != null" >
                AIM_PRODUCT_CODE = #{aimProductCode,jdbcType=VARCHAR},
            </if>
            <if test="accountDate != null" >
                ACCOUNT_DATE = #{accountDate,jdbcType=VARCHAR},
            </if>
            <if test="tradeOrderNo != null" >
                TRADE_ORDER_NO = #{tradeOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="dcepFlag != null" >
                DCEP_FLAG = #{dcepFlag,jdbcType=VARCHAR},
            </if>
            <if test="jkFeeAmount != null" >
                JK_FEE_AMOUNT = #{jkFeeAmount,jdbcType=DECIMAL},
            </if>
            <if test="serviceCharge != null" >
                SERVICE_CHARGE = #{serviceCharge,jdbcType=VARCHAR},
            </if>
            <if test="subMerchantNo != null" >
                SUB_MERCHANT_NO = #{subMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="settlementDept != null" >
                SETTLEMENT_DEPT = #{settlementDept,jdbcType=VARCHAR},
            </if>
            <if test="settlementItem != null" >
                SETTLEMENT_ITEM = #{settlementItem,jdbcType=VARCHAR},
            </if>
            <if test="merchantChannelType != null" >
                MERCHANT_CHANNEL_TYPE = #{merchantChannelType,jdbcType=VARCHAR},
            </if>
            <if test="splitFlag != null" >
                SPLIT_FLAG = #{splitFlag,jdbcType=VARCHAR},
            </if>
            <if test="masterOrderNo != null" >
                MASTER_ORDER_NO = #{masterOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderPoints != null" >
                ORDER_POINTS = #{orderPoints,jdbcType=DECIMAL},
            </if>
            <if test="instDiscountSettlementAmount != null" >
                INST_DISCOUNT_SETTLEMENT_AMOUNT = #{instDiscountSettlementAmount,jdbcType=DECIMAL},
            </if>
            <if test="instDiscountUnsettledAmount != null" >
                INST_DISCOUNT_UNSETTLED_AMOUNT = #{instDiscountUnsettledAmount,jdbcType=DECIMAL},
            </if>
            <if test="instPaidAmount != null" >
                INST_PAID_AMOUNT = #{instPaidAmount,jdbcType=DECIMAL},
            </if>
        </set>
        where TRADE_JRN_NO = #{tradeJrnNo,jdbcType=VARCHAR}
          and SETTLEMENT_DATE = #{settlementDate,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.payment.schedule.entity.settlement.TradeSettlementDO" >
        select
        <include refid="Base_Column_List" />
        from JK_TRADE_SETTLEMENT
        <where >
            <if test="tradeJrnNo != null" >
                and TRADE_JRN_NO = #{tradeJrnNo,jdbcType=VARCHAR}
            </if>
            <if test="settlementDate != null" >
                and SETTLEMENT_DATE = #{settlementDate,jdbcType=VARCHAR}
            </if>
            <if test="orderDate != null" >
                and ORDER_DATE = #{orderDate,jdbcType=VARCHAR}
            </if>
            <if test="orderTime != null" >
                and ORDER_TIME = #{orderTime,jdbcType=VARCHAR}
            </if>
            <if test="recordStatus != null" >
                and RECORD_STATUS = #{recordStatus,jdbcType=VARCHAR}
            </if>
            <if test="merchantNo != null" >
                and MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="provinceCode != null" >
                and PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR}
            </if>
            <if test="provinceName != null" >
                and PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR}
            </if>
            <if test="outTradeNo != null" >
                and OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
            </if>
            <if test="requestDate != null" >
                and REQUEST_DATE = #{requestDate,jdbcType=VARCHAR}
            </if>
            <if test="tradeRefundNo != null" >
                and TRADE_REFUND_NO = #{tradeRefundNo,jdbcType=VARCHAR}
            </if>
            <if test="bankOrderNo != null" >
                and BANK_ORDER_NO = #{bankOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="bankMerchantNo != null" >
                and BANK_MERCHANT_NO = #{bankMerchantNo,jdbcType=VARCHAR}
            </if>
            <if test="paymentChannel != null" >
                and PAYMENT_CHANNEL = #{paymentChannel,jdbcType=VARCHAR}
            </if>
            <if test="orderScene != null" >
                and ORDER_SCENE = #{orderScene,jdbcType=VARCHAR}
            </if>
            <if test="businessType != null" >
                and BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR}
            </if>
            <if test="orderAmount != null" >
                and ORDER_AMOUNT = #{orderAmount,jdbcType=DECIMAL}
            </if>
            <if test="paymentAmount != null" >
                and PAYMENT_AMOUNT = #{paymentAmount,jdbcType=DECIMAL}
            </if>
            <if test="paymentFeeAmount != null" >
                and PAYMENT_FEE_AMOUNT = #{paymentFeeAmount,jdbcType=DECIMAL}
            </if>
            <if test="refundAmount != null" >
                and REFUND_AMOUNT = #{refundAmount,jdbcType=DECIMAL}
            </if>
            <if test="refundFeeAmount != null" >
                and REFUND_FEE_AMOUNT = #{refundFeeAmount,jdbcType=DECIMAL}
            </if>
            <if test="orderRate != null" >
                and ORDER_RATE = #{orderRate,jdbcType=DECIMAL}
            </if>
            <if test="orderCompleteTime != null" >
                and ORDER_COMPLETE_TIME = #{orderCompleteTime,jdbcType=VARCHAR}
            </if>
            <if test="settlementStatus != null" >
                and SETTLEMENT_STATUS = #{settlementStatus,jdbcType=VARCHAR}
            </if>
            <if test="cardType != null" >
                and CARD_TYPE = #{cardType,jdbcType=VARCHAR}
            </if>
            <if test="refundFeeWay != null" >
                and REFUND_FEE_WAY = #{refundFeeWay,jdbcType=VARCHAR}
            </if>
            <if test="reconciliationFlag != null" >
                and RECONCILIATION_FLAG = #{reconciliationFlag,jdbcType=VARCHAR}
            </if>
            <if test="batchNo != null" >
                and BATCH_NO = #{batchNo,jdbcType=VARCHAR}
            </if>
            <if test="bankFeeAmount != null" >
                and BANK_FEE_AMOUNT = #{bankFeeAmount,jdbcType=DECIMAL}
            </if>
            <if test="remark != null" >
                and REMARK = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="checkCompleteDate != null" >
                and CHECK_COMPLETE_DATE = #{checkCompleteDate,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and TM_SMP = #{tmSmp,jdbcType=VARCHAR}
            </if>
            <if test="aimProductCode != null" >
                and AIM_PRODUCT_CODE = #{aimProductCode,jdbcType=VARCHAR}
            </if>
            <if test="accountDate != null" >
                and ACCOUNT_DATE = #{accountDate,jdbcType=VARCHAR}
            </if>
            <if test="tradeOrderNo != null" >
                and TRADE_ORDER_NO = #{tradeOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="dcepFlag != null" >
                and DCEP_FLAG = #{dcepFlag,jdbcType=VARCHAR}
            </if>
            <if test="jkFeeAmount != null" >
                and JK_FEE_AMOUNT = #{jkFeeAmount,jdbcType=DECIMAL}
            </if>
            <if test="serviceCharge != null" >
                and SERVICE_CHARGE = #{serviceCharge,jdbcType=VARCHAR}
            </if>
            <if test="subMerchantNo != null" >
                and SUB_MERCHANT_NO = #{subMerchantNo,jdbcType=VARCHAR}
            </if>
            <if test="settlementDept != null" >
                and SETTLEMENT_DEPT = #{settlementDept,jdbcType=VARCHAR}
            </if>
            <if test="settlementItem != null" >
                and SETTLEMENT_ITEM = #{settlementItem,jdbcType=VARCHAR}
            </if>
            <if test="merchantChannelType != null" >
                and MERCHANT_CHANNEL_TYPE = #{merchantChannelType,jdbcType=VARCHAR}
            </if>
            <if test="splitFlag != null" >
                and SPLIT_FLAG = #{splitFlag,jdbcType=VARCHAR}
            </if>
            <if test="masterOrderNo != null" >
                and MASTER_ORDER_NO = #{masterOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="orderPoints != null" >
                and ORDER_POINTS = #{orderPoints,jdbcType=DECIMAL}
            </if>
            <if test="instDiscountSettlementAmount != null" >
                and INST_DISCOUNT_SETTLEMENT_AMOUNT = #{instDiscountSettlementAmount,jdbcType=DECIMAL}
            </if>
            <if test="instDiscountUnsettledAmount != null" >
                and INST_DISCOUNT_UNSETTLED_AMOUNT = #{instDiscountUnsettledAmount,jdbcType=DECIMAL}
            </if>
            <if test="instPaidAmount != null" >
                and INST_PAID_AMOUNT = #{instPaidAmount,jdbcType=DECIMAL}
            </if>
        </where>
    </select>
</mapper>