<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.payment.dao.IFapCustomerComplaintDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.payment.entity.FapCustomerComplaintDO" >
        <id column="COMPLAINT_ID" property="complaintId" jdbcType="VARCHAR" />
        <id column="COMPLAINT_DATE" property="complaintDate" jdbcType="VARCHAR" />
        <id column="COMPLAINT_CHANNEL" property="complaintChannel" jdbcType="VARCHAR" />
        <result column="COMPLAINT_CONTENT" property="complaintContent" jdbcType="VARCHAR" />
        <result column="COMPLAINT_TIME" property="complaintTime" jdbcType="VARCHAR" />
        <result column="MERCHANT_ORDER_NO" property="merchantOrderNo" jdbcType="VARCHAR" />
        <result column="ORG_ORDER_NO" property="orgOrderNo" jdbcType="VARCHAR" />
        <result column="ORG_MERCHANT_ID" property="orgMerchantId" jdbcType="VARCHAR" />
        <result column="AMOUNT" property="amount" jdbcType="VARCHAR" />
        <result column="COMPLAINT_STATE" property="complaintState" jdbcType="VARCHAR" />
        <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        COMPLAINT_ID, COMPLAINT_DATE, COMPLAINT_CHANNEL, COMPLAINT_CONTENT, COMPLAINT_TIME, 
        MERCHANT_ORDER_NO, ORG_ORDER_NO, ORG_MERCHANT_ID, AMOUNT, COMPLAINT_STATE, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.payment.entity.FapCustomerComplaintDOKey" >
        select 
        <include refid="Base_Column_List" />
        from FAP_CUSTOMER_COMPLAINT_RECORD
        where COMPLAINT_ID = #{complaintId,jdbcType=VARCHAR}
          and COMPLAINT_DATE = #{complaintDate,jdbcType=VARCHAR}
          and COMPLAINT_CHANNEL = #{complaintChannel,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.payment.entity.FapCustomerComplaintDOKey" >
        delete from FAP_CUSTOMER_COMPLAINT_RECORD
        where COMPLAINT_ID = #{complaintId,jdbcType=VARCHAR}
          and COMPLAINT_DATE = #{complaintDate,jdbcType=VARCHAR}
          and COMPLAINT_CHANNEL = #{complaintChannel,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.payment.entity.FapCustomerComplaintDO" >
        insert into FAP_CUSTOMER_COMPLAINT_RECORD
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="complaintId != null" >
                COMPLAINT_ID,
            </if>
            <if test="complaintDate != null" >
                COMPLAINT_DATE,
            </if>
            <if test="complaintChannel != null" >
                COMPLAINT_CHANNEL,
            </if>
            <if test="complaintContent != null" >
                COMPLAINT_CONTENT,
            </if>
            <if test="complaintTime != null" >
                COMPLAINT_TIME,
            </if>
            <if test="merchantOrderNo != null" >
                MERCHANT_ORDER_NO,
            </if>
            <if test="orgOrderNo != null" >
                ORG_ORDER_NO,
            </if>
            <if test="orgMerchantId != null" >
                ORG_MERCHANT_ID,
            </if>
            <if test="amount != null" >
                AMOUNT,
            </if>
            <if test="complaintState != null" >
                COMPLAINT_STATE,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="complaintId != null" >
                #{complaintId,jdbcType=VARCHAR},
            </if>
            <if test="complaintDate != null" >
                #{complaintDate,jdbcType=VARCHAR},
            </if>
            <if test="complaintChannel != null" >
                #{complaintChannel,jdbcType=VARCHAR},
            </if>
            <if test="complaintContent != null" >
                #{complaintContent,jdbcType=VARCHAR},
            </if>
            <if test="complaintTime != null" >
                #{complaintTime,jdbcType=VARCHAR},
            </if>
            <if test="merchantOrderNo != null" >
                #{merchantOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="orgOrderNo != null" >
                #{orgOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="orgMerchantId != null" >
                #{orgMerchantId,jdbcType=VARCHAR},
            </if>
            <if test="amount != null" >
                #{amount,jdbcType=VARCHAR},
            </if>
            <if test="complaintState != null" >
                #{complaintState,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.payment.entity.FapCustomerComplaintDO" >
        update FAP_CUSTOMER_COMPLAINT_RECORD
        <set >
            <if test="complaintContent != null" >
                COMPLAINT_CONTENT = #{complaintContent,jdbcType=VARCHAR},
            </if>
            <if test="complaintTime != null" >
                COMPLAINT_TIME = #{complaintTime,jdbcType=VARCHAR},
            </if>
            <if test="merchantOrderNo != null" >
                MERCHANT_ORDER_NO = #{merchantOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="orgOrderNo != null" >
                ORG_ORDER_NO = #{orgOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="orgMerchantId != null" >
                ORG_MERCHANT_ID = #{orgMerchantId,jdbcType=VARCHAR},
            </if>
            <if test="amount != null" >
                AMOUNT = #{amount,jdbcType=VARCHAR},
            </if>
            <if test="complaintState != null" >
                COMPLAINT_STATE = #{complaintState,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where COMPLAINT_ID = #{complaintId,jdbcType=VARCHAR}
          and COMPLAINT_DATE = #{complaintDate,jdbcType=VARCHAR}
          and COMPLAINT_CHANNEL = #{complaintChannel,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.payment.entity.FapCustomerComplaintDO" >
        select 
        <include refid="Base_Column_List" />
        from FAP_CUSTOMER_COMPLAINT_RECORD
        <where >
            <if test="complaintId != null" >
                and COMPLAINT_ID = #{complaintId,jdbcType=VARCHAR}
            </if>
            <if test="complaintDate != null" >
                and COMPLAINT_DATE = #{complaintDate,jdbcType=VARCHAR}
            </if>
            <if test="complaintChannel != null" >
                and COMPLAINT_CHANNEL = #{complaintChannel,jdbcType=VARCHAR}
            </if>
            <if test="complaintContent != null" >
                and COMPLAINT_CONTENT = #{complaintContent,jdbcType=VARCHAR}
            </if>
            <if test="complaintTime != null" >
                and COMPLAINT_TIME = #{complaintTime,jdbcType=VARCHAR}
            </if>
            <if test="merchantOrderNo != null" >
                and MERCHANT_ORDER_NO = #{merchantOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="orgOrderNo != null" >
                and ORG_ORDER_NO = #{orgOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="orgMerchantId != null" >
                and ORG_MERCHANT_ID = #{orgMerchantId,jdbcType=VARCHAR}
            </if>
            <if test="amount != null" >
                and AMOUNT = #{amount,jdbcType=VARCHAR}
            </if>
            <if test="complaintState != null" >
                and COMPLAINT_STATE = #{complaintState,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and TM_SMP = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>