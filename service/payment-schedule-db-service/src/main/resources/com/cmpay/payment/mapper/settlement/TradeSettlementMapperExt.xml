<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.payment.schedule.dao.settlement.ITradeSettlementExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.payment.schedule.entity.settlement.TradeSettlementDO">
        <id column="TRADE_JRN_NO" property="tradeJrnNo" jdbcType="VARCHAR"/>
        <id column="SETTLEMENT_DATE" property="settlementDate" jdbcType="VARCHAR"/>
        <result column="ORDER_DATE" property="orderDate" jdbcType="VARCHAR"/>
        <result column="ORDER_TIME" property="orderTime" jdbcType="VARCHAR"/>
        <result column="RECORD_STATUS" property="recordStatus" jdbcType="VARCHAR"/>
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR"/>
        <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR"/>
        <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR"/>
        <result column="REQUEST_DATE" property="requestDate" jdbcType="VARCHAR"/>
        <result column="TRADE_REFUND_NO" property="tradeRefundNo" jdbcType="VARCHAR"/>
        <result column="BANK_ORDER_NO" property="bankOrderNo" jdbcType="VARCHAR"/>
        <result column="BANK_MERCHANT_NO" property="bankMerchantNo" jdbcType="VARCHAR"/>
        <result column="PAYMENT_CHANNEL" property="paymentChannel" jdbcType="VARCHAR"/>
        <result column="ORDER_SCENE" property="orderScene" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="PAYMENT_AMOUNT" property="paymentAmount" jdbcType="DECIMAL"/>
        <result column="PAYMENT_FEE_AMOUNT" property="paymentFeeAmount" jdbcType="DECIMAL"/>
        <result column="REFUND_AMOUNT" property="refundAmount" jdbcType="DECIMAL"/>
        <result column="REFUND_FEE_AMOUNT" property="refundFeeAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_RATE" property="orderRate" jdbcType="DECIMAL"/>
        <result column="ORDER_COMPLETE_TIME" property="orderCompleteTime" jdbcType="VARCHAR"/>
        <result column="SETTLEMENT_STATUS" property="settlementStatus" jdbcType="VARCHAR"/>
        <result column="CARD_TYPE" property="cardType" jdbcType="VARCHAR"/>
        <result column="REFUND_FEE_WAY" property="refundFeeWay" jdbcType="VARCHAR"/>
        <result column="RECONCILIATION_FLAG" property="reconciliationFlag" jdbcType="VARCHAR"/>
        <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR"/>
        <result column="BANK_FEE_AMOUNT" property="bankFeeAmount" jdbcType="DECIMAL"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="CHECK_COMPLETE_DATE" property="checkCompleteDate" jdbcType="VARCHAR"/>
        <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR"/>
        <result column="AIM_PRODUCT_CODE" property="aimProductCode" jdbcType="VARCHAR"/>
        <result column="ACCOUNT_DATE" property="accountDate" jdbcType="VARCHAR"/>
        <result column="TRADE_ORDER_NO" property="tradeOrderNo" jdbcType="VARCHAR"/>
        <result column="DCEP_FLAG" property="dcepFlag" jdbcType="VARCHAR"/>
        <result column="total_count" property="totalCount" jdbcType="VARCHAR"/>
        <result column="max_amount" property="maxAmount" jdbcType="VARCHAR"/>
        <result column="JK_FEE_AMOUNT" property="jkFeeAmount" jdbcType="DECIMAL"/>
        <result column="SERVICE_CHARGE" property="serviceCharge" jdbcType="VARCHAR"/>
        <result column="SUB_MERCHANT_NO" property="subMerchantNo" jdbcType="VARCHAR"/>
        <result column="SETTLEMENT_DEPT" property="settlementDept" jdbcType="VARCHAR"/>
        <result column="SETTLEMENT_ITEM" property="settlementItem" jdbcType="VARCHAR"/>
        <result column="MERCHANT_CHANNEL_TYPE" property="merchantChannelType" jdbcType="VARCHAR"/>
        <result column="SPLIT_FLAG" property="splitFlag" jdbcType="VARCHAR"/>
        <result column="MASTER_ORDER_NO" property="masterOrderNo" jdbcType="VARCHAR"/>
        <result column="ORDER_POINTS" property="orderPoints" jdbcType="DECIMAL"/>
        <result column="INST_DISCOUNT_SETTLEMENT_AMOUNT" property="instDiscountSettlementAmount" jdbcType="DECIMAL" />
        <result column="INST_DISCOUNT_UNSETTLED_AMOUNT" property="instDiscountUnsettledAmount" jdbcType="DECIMAL" />
        <result column="INST_PAID_AMOUNT" property="instPaidAmount" jdbcType="DECIMAL" />
    </resultMap>

    <resultMap id="SubAmountResultMap" type="com.cmpay.payment.schedule.entity.settlement.JkSubTradeAmountDO">
        <result column="COUNT_DATE" property="countDate" jdbcType="VARCHAR"/>
        <result column="COUNT_TIME" property="countTime" jdbcType="VARCHAR"/>
        <result column="SUB_MERCHANT_NO" property="subMerchantNo" jdbcType="VARCHAR"/>
        <result column="COUNT_AMOUNT" property="countAmount" jdbcType="DECIMAL"/>
        <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="Base_Column_List">
        TRADE_JRN_NO
        , SETTLEMENT_DATE, ORDER_DATE, ORDER_TIME, RECORD_STATUS, MERCHANT_NO,
        PROVINCE_CODE, PROVINCE_NAME, OUT_TRADE_NO, REQUEST_DATE, TRADE_REFUND_NO, BANK_ORDER_NO,
        BANK_MERCHANT_NO, PAYMENT_CHANNEL, ORDER_SCENE, BUSINESS_TYPE, ORDER_AMOUNT, PAYMENT_AMOUNT,
        PAYMENT_FEE_AMOUNT, REFUND_AMOUNT, REFUND_FEE_AMOUNT, ORDER_RATE, ORDER_COMPLETE_TIME,
        SETTLEMENT_STATUS, CARD_TYPE, REFUND_FEE_WAY, RECONCILIATION_FLAG, BATCH_NO, BANK_FEE_AMOUNT,
        REMARK, CHECK_COMPLETE_DATE, TM_SMP, AIM_PRODUCT_CODE, ACCOUNT_DATE, TRADE_ORDER_NO, DCEP_FLAG,
        JK_FEE_AMOUNT, SERVICE_CHARGE, SUB_MERCHANT_NO, SETTLEMENT_DEPT,
        SETTLEMENT_ITEM, MERCHANT_CHANNEL_TYPE, SPLIT_FLAG, MASTER_ORDER_NO, ORDER_POINTS, INST_DISCOUNT_SETTLEMENT_AMOUNT,
        INST_DISCOUNT_UNSETTLED_AMOUNT, INST_PAID_AMOUNT
    </sql>
    <select id="getFirstSettlementByDay" resultMap="BaseResultMap"
            parameterType="com.cmpay.payment.schedule.entity.settlement.TradeSettlementDO">
        select
        <include refid="Base_Column_List"/>
        from JK_TRADE_SETTLEMENT
        where PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR}
        and SETTLEMENT_DATE = #{settlementDate,jdbcType=VARCHAR}
        AND ROWNUM = 1
        order by ORDER_COMPLETE_TIME
    </select>
    <select id="getSettlementByProvinceCode" resultMap="BaseResultMap"
            parameterType="com.cmpay.payment.schedule.entity.settlement.TradeSettlementQueryDO">
        select
        <include refid="Base_Column_List"/>
        from JK_TRADE_SETTLEMENT
        where TRADE_JRN_NO > #{tradeJrnNo,jdbcType=VARCHAR}
        and PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR}
        and ORDER_COMPLETE_TIME &lt;= #{orderCompleteTime,jdbcType=VARCHAR}
        and SETTLEMENT_DATE >= #{settlementDate,jdbcType=VARCHAR}
        AND ROWNUM &lt;= #{totalQueryNumber,jdbcType=INTEGER}
        order by ORDER_COMPLETE_TIME desc
    </select>

    <select id="queryMixPayOrder" resultMap="BaseResultMap"
            parameterType="com.cmpay.payment.schedule.entity.settlement.TradeSettlementQueryDO">
        select
        <include refid="Base_Column_List"/>
        from JK_TRADE_SETTLEMENT
        WHERE SETTLEMENT_DATE >= #{requestDate,jdbcType=VARCHAR}
        and ORA_HASH(TRADE_ORDER_NO, #{totalSelect,jdbcType=INTEGER}) = #{currentIndex,jdbcType=INTEGER}
        AND SETTLEMENT_DATE &lt;= #{orderDate,jdbcType=VARCHAR}
        AND SUB_MERCHANT_NO = #{subMerchantNo,jdbcType=VARCHAR}
        AND (MASTER_ORDER_NO = '' or MASTER_ORDER_NO is null)
        <if test="tradeOrderNo != null and tradeOrderNo != ''">
            and TRADE_ORDER_NO = #{tradeOrderNo,jdbcType=VARCHAR}
        </if>
        and RECONCILIATION_FLAG ='complete'
        AND ROWNUM &lt;= #{total,jdbcType=VARCHAR}
    </select>

    <update id="updateMasterOrderNo" parameterType="com.cmpay.payment.schedule.entity.settlement.TradeSettlementDO">
        update JK_TRADE_SETTLEMENT
        set MASTER_ORDER_NO = #{masterOrderNo,jdbcType=VARCHAR}
        where TRADE_JRN_NO = #{tradeJrnNo,jdbcType=VARCHAR}
          and SETTLEMENT_DATE = #{settlementDate,jdbcType=VARCHAR}
    </update>


    <select id="getTradeSettleBySettlementDateByTwoDay" resultMap="BaseResultMap" parameterType="com.cmpay.payment.schedule.entity.settlement.TradeSettlementExtDO">
        select <include refid="Base_Column_List"/>
        from JK_TRADE_SETTLEMENT
        where SETTLEMENT_DATE >= TO_CHAR(SYSDATE-1, 'yyyyMMdd')
        and SETTLEMENT_DATE &lt;= TO_CHAR(SYSDATE, 'yyyyMMdd')
        and OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
        and TRADE_REFUND_NO = #{tradeRefundNo,jdbcType=VARCHAR}
        and MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}
        and SETTLEMENT_STATUS = #{settlementStatus,jdbcType=VARCHAR}
    </select>

    <!-- 查询JK_TRADE_SETTLEMENT数据用于统计子商户交易金额，配合PageHelper使用 -->
    <select id="queryTradeSettlementForSubMerchantCount" resultMap="SubAmountResultMap" parameterType="com.cmpay.payment.schedule.entity.settlement.JkCountSubMerTradeAmountQueryDO">
        select #{countDate,jdbcType=VARCHAR} COUNT_DATE,
               #{countTime,jdbcType=VARCHAR} COUNT_TIME,
               a.SUB_MERCHANT_NO,
               nvl(sum(a.PAYMENT_AMOUNT), 0) COUNT_AMOUNT,
               to_char(sysdate, 'YYYYMMDDHH24MISS') TM_SMP
        from fapadm.JK_TRADE_SETTLEMENT a, fapadm.JK_SUB_MERCHANT b
        where a.SETTLEMENT_DATE = #{countDate,jdbcType=VARCHAR}
          and  a.ORDER_COMPLETE_TIME between #{countTimeStart,jdbcType=VARCHAR} and #{countTimeEnd,jdbcType=VARCHAR}
          and a.SUB_MERCHANT_NO = b.SUB_MERCHANT_NO
          and a.SETTLEMENT_STATUS = 'S'
        group by a.SUB_MERCHANT_NO
    </select>

</mapper>
