<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.payment.schedule.dao.withhold.IContractWithholdDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.payment.schedule.entity.withhold.ContractWithholdDO" >
        <id column="CONTRACT_CODE" property="contractCode" jdbcType="VARCHAR" />
        <id column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="PLAN_ID" property="planId" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="VARCHAR" />
        <result column="CONTRACT_DATE" property="contractDate" jdbcType="VARCHAR" />
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR" />
        <result column="BANK_MERCHANT_NO" property="bankMerchantNo" jdbcType="VARCHAR" />
        <result column="APP_ID" property="appId" jdbcType="VARCHAR" />
        <result column="CONTRACT_ID" property="contractId" jdbcType="VARCHAR" />
        <result column="DISPLAY_ACCOUNT" property="displayAccount" jdbcType="VARCHAR" />
        <result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR" />
        <result column="VERSION" property="version" jdbcType="VARCHAR" />
        <result column="OPENID" property="openid" jdbcType="VARCHAR" />
        <result column="CHANGE_TYPE" property="changeType" jdbcType="VARCHAR" />
        <result column="OPERATE_TIME" property="operateTime" jdbcType="VARCHAR" />
        <result column="EXPIRED_TIME" property="expiredTime" jdbcType="VARCHAR" />
        <result column="TERMINATION_MODE" property="terminationMode" jdbcType="VARCHAR" />
        <result column="CONTRACT_WAY" property="contractWay" jdbcType="VARCHAR" />
        <result column="CONTRACT_SCENE" property="contractScene" jdbcType="VARCHAR" />
        <result column="CONTRACT_STATUS" property="contractStatus" jdbcType="VARCHAR" />
        <result column="RETURN_CODE" property="returnCode" jdbcType="VARCHAR" />
        <result column="RETURN_MSG" property="returnMsg" jdbcType="VARCHAR" />
        <result column="RETURN_WEB" property="returnWeb" jdbcType="VARCHAR" />
        <result column="RETURN_APP" property="returnApp" jdbcType="VARCHAR" />
        <result column="RETURN_APP_ID" property="returnAppId" jdbcType="VARCHAR" />
        <result column="CLIENT_IP" property="clientIp" jdbcType="VARCHAR" />
        <result column="EXTRA" property="extra" jdbcType="VARCHAR" />
        <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR" />
        <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR" />
        <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR" />
        <result column="SIGNED_TIME" property="signedTime" jdbcType="VARCHAR" />
        <result column="DELETE_NOTIFY_URL" property="deleteNotifyUrl" jdbcType="VARCHAR" />
        <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
        <result column="CONTRACT_TYPE" property="contractType" jdbcType="VARCHAR" />
        <result column="TERMINATED_TIME" property="terminatedTime" jdbcType="VARCHAR" />
        <result column="SUB_MERCHANT_NAME" property="subMerchantName" jdbcType="VARCHAR" />
        <result column="SUB_MERCHANT_SERVICE_NAME" property="subMerchantServiceName" jdbcType="VARCHAR" />
        <result column="SUB_MERCHANT_DESCRIPTION" property="subMerchantDescription" jdbcType="VARCHAR" />
        <result column="PERIOD_TYPE" property="periodType" jdbcType="VARCHAR" />
        <result column="PERIOD" property="period" jdbcType="VARCHAR" />
        <result column="EXECUTE_TIME" property="executeTime" jdbcType="VARCHAR" />
        <result column="SINGLE_AMOUNT" property="singleAmount" jdbcType="DECIMAL" />
        <result column="MOBILE_NO" property="mobileNo" jdbcType="VARCHAR" />
        <result column="FRONT_NOTIFY_URL" property="frontNotifyUrl" jdbcType="VARCHAR" />
        <result column="SIGN_BUS_TYPE" property="signBusType" jdbcType="VARCHAR" />
        <result column="DATA_CENTER" property="dataCenter" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        CONTRACT_CODE, CREATE_DATE, PLAN_ID, CREATE_TIME, CONTRACT_DATE, MERCHANT_NO, BANK_MERCHANT_NO, 
        APP_ID, CONTRACT_ID, DISPLAY_ACCOUNT, NOTIFY_URL, VERSION, OPENID, CHANGE_TYPE, OPERATE_TIME, 
        EXPIRED_TIME, TERMINATION_MODE, CONTRACT_WAY, CONTRACT_SCENE, CONTRACT_STATUS, RETURN_CODE, 
        RETURN_MSG, RETURN_WEB, RETURN_APP, RETURN_APP_ID, CLIENT_IP, EXTRA, PROVINCE_CODE, 
        PROVINCE_NAME, TM_SMP, SIGNED_TIME, DELETE_NOTIFY_URL, OUT_TRADE_NO, CONTRACT_TYPE, 
        TERMINATED_TIME, SUB_MERCHANT_NAME, SUB_MERCHANT_SERVICE_NAME, SUB_MERCHANT_DESCRIPTION,
        PERIOD_TYPE, PERIOD, EXECUTE_TIME, SINGLE_AMOUNT, MOBILE_NO, FRONT_NOTIFY_URL, SIGN_BUS_TYPE,
        DATA_CENTER
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.payment.schedule.entity.withhold.ContractWithholdDOKey" >
        select 
        <include refid="Base_Column_List" />
        from JK_CONTRACT_WITHHOLD
        where CONTRACT_CODE = #{contractCode,jdbcType=VARCHAR}
          and CREATE_DATE = #{createDate,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.payment.schedule.entity.withhold.ContractWithholdDOKey" >
        delete from JK_CONTRACT_WITHHOLD
        where CONTRACT_CODE = #{contractCode,jdbcType=VARCHAR}
          and CREATE_DATE = #{createDate,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.payment.schedule.entity.withhold.ContractWithholdDO" >
        insert into JK_CONTRACT_WITHHOLD
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="contractCode != null" >
                CONTRACT_CODE,
            </if>
            <if test="createDate != null" >
                CREATE_DATE,
            </if>
            <if test="planId != null" >
                PLAN_ID,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="contractDate != null" >
                CONTRACT_DATE,
            </if>
            <if test="merchantNo != null" >
                MERCHANT_NO,
            </if>
            <if test="bankMerchantNo != null" >
                BANK_MERCHANT_NO,
            </if>
            <if test="appId != null" >
                APP_ID,
            </if>
            <if test="contractId != null" >
                CONTRACT_ID,
            </if>
            <if test="displayAccount != null" >
                DISPLAY_ACCOUNT,
            </if>
            <if test="notifyUrl != null" >
                NOTIFY_URL,
            </if>
            <if test="version != null" >
                VERSION,
            </if>
            <if test="openid != null" >
                OPENID,
            </if>
            <if test="changeType != null" >
                CHANGE_TYPE,
            </if>
            <if test="operateTime != null" >
                OPERATE_TIME,
            </if>
            <if test="expiredTime != null" >
                EXPIRED_TIME,
            </if>
            <if test="terminationMode != null" >
                TERMINATION_MODE,
            </if>
            <if test="contractWay != null" >
                CONTRACT_WAY,
            </if>
            <if test="contractScene != null" >
                CONTRACT_SCENE,
            </if>
            <if test="contractStatus != null" >
                CONTRACT_STATUS,
            </if>
            <if test="returnCode != null" >
                RETURN_CODE,
            </if>
            <if test="returnMsg != null" >
                RETURN_MSG,
            </if>
            <if test="returnWeb != null" >
                RETURN_WEB,
            </if>
            <if test="returnApp != null" >
                RETURN_APP,
            </if>
            <if test="returnAppId != null" >
                RETURN_APP_ID,
            </if>
            <if test="clientIp != null" >
                CLIENT_IP,
            </if>
            <if test="extra != null" >
                EXTRA,
            </if>
            <if test="provinceCode != null" >
                PROVINCE_CODE,
            </if>
            <if test="provinceName != null" >
                PROVINCE_NAME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
            <if test="signedTime != null" >
                SIGNED_TIME,
            </if>
            <if test="deleteNotifyUrl != null" >
                DELETE_NOTIFY_URL,
            </if>
            <if test="outTradeNo != null" >
                OUT_TRADE_NO,
            </if>
            <if test="contractType != null" >
                CONTRACT_TYPE,
            </if>
            <if test="terminatedTime != null" >
                TERMINATED_TIME,
            </if>
            <if test="subMerchantName != null" >
                SUB_MERCHANT_NAME,
            </if>
            <if test="subMerchantServiceName != null" >
                SUB_MERCHANT_SERVICE_NAME,
            </if>
            <if test="subMerchantDescription != null" >
                SUB_MERCHANT_DESCRIPTION,
            </if>
            <if test="periodType != null" >
                PERIOD_TYPE,
            </if>
            <if test="period != null" >
                PERIOD,
            </if>
            <if test="executeTime != null" >
                EXECUTE_TIME,
            </if>
            <if test="singleAmount != null" >
                SINGLE_AMOUNT,
            </if>
            <if test="mobileNo != null" >
                MOBILE_NO,
            </if>
            <if test="frontNotifyUrl != null" >
                FRONT_NOTIFY_URL,
            </if>
            <if test="signBusType != null" >
                SIGN_BUS_TYPE,
            </if>
            <if test="dataCenter != null" >
                DATA_CENTER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="contractCode != null" >
                #{contractCode,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null" >
                #{createDate,jdbcType=VARCHAR},
            </if>
            <if test="planId != null" >
                #{planId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="contractDate != null" >
                #{contractDate,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="bankMerchantNo != null" >
                #{bankMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="appId != null" >
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="contractId != null" >
                #{contractId,jdbcType=VARCHAR},
            </if>
            <if test="displayAccount != null" >
                #{displayAccount,jdbcType=VARCHAR},
            </if>
            <if test="notifyUrl != null" >
                #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="version != null" >
                #{version,jdbcType=VARCHAR},
            </if>
            <if test="openid != null" >
                #{openid,jdbcType=VARCHAR},
            </if>
            <if test="changeType != null" >
                #{changeType,jdbcType=VARCHAR},
            </if>
            <if test="operateTime != null" >
                #{operateTime,jdbcType=VARCHAR},
            </if>
            <if test="expiredTime != null" >
                #{expiredTime,jdbcType=VARCHAR},
            </if>
            <if test="terminationMode != null" >
                #{terminationMode,jdbcType=VARCHAR},
            </if>
            <if test="contractWay != null" >
                #{contractWay,jdbcType=VARCHAR},
            </if>
            <if test="contractScene != null" >
                #{contractScene,jdbcType=VARCHAR},
            </if>
            <if test="contractStatus != null" >
                #{contractStatus,jdbcType=VARCHAR},
            </if>
            <if test="returnCode != null" >
                #{returnCode,jdbcType=VARCHAR},
            </if>
            <if test="returnMsg != null" >
                #{returnMsg,jdbcType=VARCHAR},
            </if>
            <if test="returnWeb != null" >
                #{returnWeb,jdbcType=VARCHAR},
            </if>
            <if test="returnApp != null" >
                #{returnApp,jdbcType=VARCHAR},
            </if>
            <if test="returnAppId != null" >
                #{returnAppId,jdbcType=VARCHAR},
            </if>
            <if test="clientIp != null" >
                #{clientIp,jdbcType=VARCHAR},
            </if>
            <if test="extra != null" >
                #{extra,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null" >
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null" >
                #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
            <if test="signedTime != null" >
                #{signedTime,jdbcType=VARCHAR},
            </if>
            <if test="deleteNotifyUrl != null" >
                #{deleteNotifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="outTradeNo != null" >
                #{outTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="contractType != null" >
                #{contractType,jdbcType=VARCHAR},
            </if>
            <if test="terminatedTime != null" >
                #{terminatedTime,jdbcType=VARCHAR},
            </if>
            <if test="subMerchantName != null" >
                #{subMerchantName,jdbcType=VARCHAR},
            </if>
            <if test="subMerchantServiceName != null" >
                #{subMerchantServiceName,jdbcType=VARCHAR},
            </if>
            <if test="subMerchantDescription != null" >
                #{subMerchantDescription,jdbcType=VARCHAR},
            </if>
            <if test="periodType != null" >
                #{periodType,jdbcType=VARCHAR},
            </if>
            <if test="period != null" >
                #{period,jdbcType=VARCHAR},
            </if>
            <if test="executeTime != null" >
                #{executeTime,jdbcType=VARCHAR},
            </if>
            <if test="singleAmount != null" >
                #{singleAmount,jdbcType=DECIMAL},
            </if>
            <if test="mobileNo != null" >
                #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="frontNotifyUrl != null" >
                #{frontNotifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="signBusType != null" >
                #{signBusType,jdbcType=VARCHAR},
            </if>
            <if test="dataCenter != null" >
                #{dataCenter,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.payment.schedule.entity.withhold.ContractWithholdDO" >
        update JK_CONTRACT_WITHHOLD
        <set >
            <if test="planId != null" >
                PLAN_ID = #{planId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="contractDate != null" >
                CONTRACT_DATE = #{contractDate,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="bankMerchantNo != null" >
                BANK_MERCHANT_NO = #{bankMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="appId != null" >
                APP_ID = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="contractId != null" >
                CONTRACT_ID = #{contractId,jdbcType=VARCHAR},
            </if>
            <if test="displayAccount != null" >
                DISPLAY_ACCOUNT = #{displayAccount,jdbcType=VARCHAR},
            </if>
            <if test="notifyUrl != null" >
                NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="version != null" >
                VERSION = #{version,jdbcType=VARCHAR},
            </if>
            <if test="openid != null" >
                OPENID = #{openid,jdbcType=VARCHAR},
            </if>
            <if test="changeType != null" >
                CHANGE_TYPE = #{changeType,jdbcType=VARCHAR},
            </if>
            <if test="operateTime != null" >
                OPERATE_TIME = #{operateTime,jdbcType=VARCHAR},
            </if>
            <if test="expiredTime != null" >
                EXPIRED_TIME = #{expiredTime,jdbcType=VARCHAR},
            </if>
            <if test="terminationMode != null" >
                TERMINATION_MODE = #{terminationMode,jdbcType=VARCHAR},
            </if>
            <if test="contractWay != null" >
                CONTRACT_WAY = #{contractWay,jdbcType=VARCHAR},
            </if>
            <if test="contractScene != null" >
                CONTRACT_SCENE = #{contractScene,jdbcType=VARCHAR},
            </if>
            <if test="contractStatus != null" >
                CONTRACT_STATUS = #{contractStatus,jdbcType=VARCHAR},
            </if>
            <if test="returnCode != null" >
                RETURN_CODE = #{returnCode,jdbcType=VARCHAR},
            </if>
            <if test="returnMsg != null" >
                RETURN_MSG = #{returnMsg,jdbcType=VARCHAR},
            </if>
            <if test="returnWeb != null" >
                RETURN_WEB = #{returnWeb,jdbcType=VARCHAR},
            </if>
            <if test="returnApp != null" >
                RETURN_APP = #{returnApp,jdbcType=VARCHAR},
            </if>
            <if test="returnAppId != null" >
                RETURN_APP_ID = #{returnAppId,jdbcType=VARCHAR},
            </if>
            <if test="clientIp != null" >
                CLIENT_IP = #{clientIp,jdbcType=VARCHAR},
            </if>
            <if test="extra != null" >
                EXTRA = #{extra,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null" >
                PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null" >
                PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=VARCHAR},
            </if>
            <if test="signedTime != null" >
                SIGNED_TIME = #{signedTime,jdbcType=VARCHAR},
            </if>
            <if test="deleteNotifyUrl != null" >
                DELETE_NOTIFY_URL = #{deleteNotifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="outTradeNo != null" >
                OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="contractType != null" >
                CONTRACT_TYPE = #{contractType,jdbcType=VARCHAR},
            </if>
            <if test="terminatedTime != null" >
                TERMINATED_TIME = #{terminatedTime,jdbcType=VARCHAR},
            </if>
            <if test="subMerchantName != null" >
                SUB_MERCHANT_NAME = #{subMerchantName,jdbcType=VARCHAR},
            </if>
            <if test="subMerchantServiceName != null" >
                SUB_MERCHANT_SERVICE_NAME = #{subMerchantServiceName,jdbcType=VARCHAR},
            </if>
            <if test="subMerchantDescription != null" >
                SUB_MERCHANT_DESCRIPTION = #{subMerchantDescription,jdbcType=VARCHAR},
            </if>
            <if test="periodType != null" >
                PERIOD_TYPE = #{periodType,jdbcType=VARCHAR},
            </if>
            <if test="period != null" >
                PERIOD = #{period,jdbcType=VARCHAR},
            </if>
            <if test="executeTime != null" >
                EXECUTE_TIME = #{executeTime,jdbcType=VARCHAR},
            </if>
            <if test="singleAmount != null" >
                SINGLE_AMOUNT = #{singleAmount,jdbcType=DECIMAL},
            </if>
            <if test="mobileNo != null" >
                MOBILE_NO = #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="frontNotifyUrl != null" >
                FRONT_NOTIFY_URL = #{frontNotifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="signBusType != null" >
                SIGN_BUS_TYPE = #{signBusType,jdbcType=VARCHAR},
            </if>
            <if test="dataCenter != null" >
                DATA_CENTER = #{dataCenter,jdbcType=VARCHAR},
            </if>
        </set>
        where CONTRACT_CODE = #{contractCode,jdbcType=VARCHAR}
          and CREATE_DATE = #{createDate,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.payment.schedule.entity.withhold.ContractWithholdDO" >
        select 
        <include refid="Base_Column_List" />
        from JK_CONTRACT_WITHHOLD
        <where >
            <if test="contractCode != null" >
                and CONTRACT_CODE = #{contractCode,jdbcType=VARCHAR}
            </if>
            <if test="createDate != null" >
                and CREATE_DATE = #{createDate,jdbcType=VARCHAR}
            </if>
            <if test="planId != null" >
                and PLAN_ID = #{planId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and CREATE_TIME = #{createTime,jdbcType=VARCHAR}
            </if>
            <if test="contractDate != null" >
                and CONTRACT_DATE = #{contractDate,jdbcType=VARCHAR}
            </if>
            <if test="merchantNo != null" >
                and MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="bankMerchantNo != null" >
                and BANK_MERCHANT_NO = #{bankMerchantNo,jdbcType=VARCHAR}
            </if>
            <if test="appId != null" >
                and APP_ID = #{appId,jdbcType=VARCHAR}
            </if>
            <if test="contractId != null" >
                and CONTRACT_ID = #{contractId,jdbcType=VARCHAR}
            </if>
            <if test="displayAccount != null" >
                and DISPLAY_ACCOUNT = #{displayAccount,jdbcType=VARCHAR}
            </if>
            <if test="notifyUrl != null" >
                and NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR}
            </if>
            <if test="version != null" >
                and VERSION = #{version,jdbcType=VARCHAR}
            </if>
            <if test="openid != null" >
                and OPENID = #{openid,jdbcType=VARCHAR}
            </if>
            <if test="changeType != null" >
                and CHANGE_TYPE = #{changeType,jdbcType=VARCHAR}
            </if>
            <if test="operateTime != null" >
                and OPERATE_TIME = #{operateTime,jdbcType=VARCHAR}
            </if>
            <if test="expiredTime != null" >
                and EXPIRED_TIME = #{expiredTime,jdbcType=VARCHAR}
            </if>
            <if test="terminationMode != null" >
                and TERMINATION_MODE = #{terminationMode,jdbcType=VARCHAR}
            </if>
            <if test="contractWay != null" >
                and CONTRACT_WAY = #{contractWay,jdbcType=VARCHAR}
            </if>
            <if test="contractScene != null" >
                and CONTRACT_SCENE = #{contractScene,jdbcType=VARCHAR}
            </if>
            <if test="contractStatus != null" >
                and CONTRACT_STATUS = #{contractStatus,jdbcType=VARCHAR}
            </if>
            <if test="returnCode != null" >
                and RETURN_CODE = #{returnCode,jdbcType=VARCHAR}
            </if>
            <if test="returnMsg != null" >
                and RETURN_MSG = #{returnMsg,jdbcType=VARCHAR}
            </if>
            <if test="returnWeb != null" >
                and RETURN_WEB = #{returnWeb,jdbcType=VARCHAR}
            </if>
            <if test="returnApp != null" >
                and RETURN_APP = #{returnApp,jdbcType=VARCHAR}
            </if>
            <if test="returnAppId != null" >
                and RETURN_APP_ID = #{returnAppId,jdbcType=VARCHAR}
            </if>
            <if test="clientIp != null" >
                and CLIENT_IP = #{clientIp,jdbcType=VARCHAR}
            </if>
            <if test="extra != null" >
                and EXTRA = #{extra,jdbcType=VARCHAR}
            </if>
            <if test="provinceCode != null" >
                and PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR}
            </if>
            <if test="provinceName != null" >
                and PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and TM_SMP = #{tmSmp,jdbcType=VARCHAR}
            </if>
            <if test="signedTime != null" >
                and SIGNED_TIME = #{signedTime,jdbcType=VARCHAR}
            </if>
            <if test="deleteNotifyUrl != null" >
                and DELETE_NOTIFY_URL = #{deleteNotifyUrl,jdbcType=VARCHAR}
            </if>
            <if test="outTradeNo != null" >
                and OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
            </if>
            <if test="contractType != null" >
                and CONTRACT_TYPE = #{contractType,jdbcType=VARCHAR}
            </if>
            <if test="terminatedTime != null" >
                and TERMINATED_TIME = #{terminatedTime,jdbcType=VARCHAR}
            </if>
            <if test="subMerchantName != null" >
                and SUB_MERCHANT_NAME = #{subMerchantName,jdbcType=VARCHAR}
            </if>
            <if test="subMerchantServiceName != null" >
                and SUB_MERCHANT_SERVICE_NAME = #{subMerchantServiceName,jdbcType=VARCHAR}
            </if>
            <if test="subMerchantDescription != null" >
                and SUB_MERCHANT_DESCRIPTION = #{subMerchantDescription,jdbcType=VARCHAR}
            </if>
            <if test="periodType != null" >
                and PERIOD_TYPE = #{periodType,jdbcType=VARCHAR}
            </if>
            <if test="period != null" >
                and PERIOD = #{period,jdbcType=VARCHAR}
            </if>
            <if test="executeTime != null" >
                and EXECUTE_TIME = #{executeTime,jdbcType=VARCHAR}
            </if>
            <if test="singleAmount != null" >
                and SINGLE_AMOUNT = #{singleAmount,jdbcType=DECIMAL}
            </if>
            <if test="mobileNo != null" >
                and MOBILE_NO = #{mobileNo,jdbcType=VARCHAR}
            </if>
            <if test="frontNotifyUrl != null" >
                and FRONT_NOTIFY_URL = #{frontNotifyUrl,jdbcType=VARCHAR}
            </if>
            <if test="signBusType != null" >
                and SIGN_BUS_TYPE = #{signBusType,jdbcType=VARCHAR}
            </if>
            <if test="dataCenter != null" >
                and DATA_CENTER = #{dataCenter,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>