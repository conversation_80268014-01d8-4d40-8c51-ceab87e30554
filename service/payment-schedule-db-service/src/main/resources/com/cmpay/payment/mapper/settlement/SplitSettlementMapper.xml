<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.payment.schedule.dao.settlement.ISplitSettlementDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.payment.schedule.entity.settlement.SplitSettlementDO" >
        <id column="JRN_NO" property="jrnNo" jdbcType="VARCHAR" />
        <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
        <result column="TRADE_REFUND_NO" property="tradeRefundNo" jdbcType="VARCHAR" />
        <result column="SUB_ORDER_NO" property="subOrderNo" jdbcType="VARCHAR" />
        <result column="SETTLEMENT_STATUS" property="settlementStatus" jdbcType="VARCHAR" />
        <result column="SUB_ORDER_AMOUNT" property="subOrderAmount" jdbcType="DECIMAL" />
        <result column="SUB_ORDER_FEE" property="subOrderFee" jdbcType="DECIMAL" />
        <result column="SETTLEMENT_DATE" property="settlementDate" jdbcType="VARCHAR" />
        <result column="ORDER_DATE" property="orderDate" jdbcType="VARCHAR" />
        <result column="ORDER_TIME" property="orderTime" jdbcType="VARCHAR" />
        <result column="SETTLEMENT_DEPT" property="settlementDept" jdbcType="VARCHAR" />
        <result column="SETTLEMENT_ITEM" property="settlementItem" jdbcType="VARCHAR" />
        <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        JRN_NO, OUT_TRADE_NO, TRADE_REFUND_NO, SUB_ORDER_NO, SETTLEMENT_STATUS, SUB_ORDER_AMOUNT, 
        SUB_ORDER_FEE, SETTLEMENT_DATE, ORDER_DATE, ORDER_TIME, SETTLEMENT_DEPT, SETTLEMENT_ITEM, 
        TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from JK_SPLIT_SETTLEMENT
        where JRN_NO = #{jrnNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from JK_SPLIT_SETTLEMENT
        where JRN_NO = #{jrnNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.payment.schedule.entity.settlement.SplitSettlementDO" >
        insert into JK_SPLIT_SETTLEMENT
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                JRN_NO,
            </if>
            <if test="outTradeNo != null" >
                OUT_TRADE_NO,
            </if>
            <if test="tradeRefundNo != null" >
                TRADE_REFUND_NO,
            </if>
            <if test="subOrderNo != null" >
                SUB_ORDER_NO,
            </if>
            <if test="settlementStatus != null" >
                SETTLEMENT_STATUS,
            </if>
            <if test="subOrderAmount != null" >
                SUB_ORDER_AMOUNT,
            </if>
            <if test="subOrderFee != null" >
                SUB_ORDER_FEE,
            </if>
            <if test="settlementDate != null" >
                SETTLEMENT_DATE,
            </if>
            <if test="orderDate != null" >
                ORDER_DATE,
            </if>
            <if test="orderTime != null" >
                ORDER_TIME,
            </if>
            <if test="settlementDept != null" >
                SETTLEMENT_DEPT,
            </if>
            <if test="settlementItem != null" >
                SETTLEMENT_ITEM,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                #{jrnNo,jdbcType=VARCHAR},
            </if>
            <if test="outTradeNo != null" >
                #{outTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="tradeRefundNo != null" >
                #{tradeRefundNo,jdbcType=VARCHAR},
            </if>
            <if test="subOrderNo != null" >
                #{subOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="settlementStatus != null" >
                #{settlementStatus,jdbcType=VARCHAR},
            </if>
            <if test="subOrderAmount != null" >
                #{subOrderAmount,jdbcType=DECIMAL},
            </if>
            <if test="subOrderFee != null" >
                #{subOrderFee,jdbcType=DECIMAL},
            </if>
            <if test="settlementDate != null" >
                #{settlementDate,jdbcType=VARCHAR},
            </if>
            <if test="orderDate != null" >
                #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="orderTime != null" >
                #{orderTime,jdbcType=VARCHAR},
            </if>
            <if test="settlementDept != null" >
                #{settlementDept,jdbcType=VARCHAR},
            </if>
            <if test="settlementItem != null" >
                #{settlementItem,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.payment.schedule.entity.settlement.SplitSettlementDO" >
        update JK_SPLIT_SETTLEMENT
        <set >
            <if test="outTradeNo != null" >
                OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="tradeRefundNo != null" >
                TRADE_REFUND_NO = #{tradeRefundNo,jdbcType=VARCHAR},
            </if>
            <if test="subOrderNo != null" >
                SUB_ORDER_NO = #{subOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="settlementStatus != null" >
                SETTLEMENT_STATUS = #{settlementStatus,jdbcType=VARCHAR},
            </if>
            <if test="subOrderAmount != null" >
                SUB_ORDER_AMOUNT = #{subOrderAmount,jdbcType=DECIMAL},
            </if>
            <if test="subOrderFee != null" >
                SUB_ORDER_FEE = #{subOrderFee,jdbcType=DECIMAL},
            </if>
            <if test="settlementDate != null" >
                SETTLEMENT_DATE = #{settlementDate,jdbcType=VARCHAR},
            </if>
            <if test="orderDate != null" >
                ORDER_DATE = #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="orderTime != null" >
                ORDER_TIME = #{orderTime,jdbcType=VARCHAR},
            </if>
            <if test="settlementDept != null" >
                SETTLEMENT_DEPT = #{settlementDept,jdbcType=VARCHAR},
            </if>
            <if test="settlementItem != null" >
                SETTLEMENT_ITEM = #{settlementItem,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where JRN_NO = #{jrnNo,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.payment.schedule.entity.settlement.SplitSettlementDO" >
        select 
        <include refid="Base_Column_List" />
        from JK_SPLIT_SETTLEMENT
        <where >
            <if test="jrnNo != null" >
                and JRN_NO = #{jrnNo,jdbcType=VARCHAR}
            </if>
            <if test="outTradeNo != null" >
                and OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
            </if>
            <if test="tradeRefundNo != null" >
                and TRADE_REFUND_NO = #{tradeRefundNo,jdbcType=VARCHAR}
            </if>
            <if test="subOrderNo != null" >
                and SUB_ORDER_NO = #{subOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="settlementStatus != null" >
                and SETTLEMENT_STATUS = #{settlementStatus,jdbcType=VARCHAR}
            </if>
            <if test="subOrderAmount != null" >
                and SUB_ORDER_AMOUNT = #{subOrderAmount,jdbcType=DECIMAL}
            </if>
            <if test="subOrderFee != null" >
                and SUB_ORDER_FEE = #{subOrderFee,jdbcType=DECIMAL}
            </if>
            <if test="settlementDate != null" >
                and SETTLEMENT_DATE = #{settlementDate,jdbcType=VARCHAR}
            </if>
            <if test="orderDate != null" >
                and ORDER_DATE = #{orderDate,jdbcType=VARCHAR}
            </if>
            <if test="orderTime != null" >
                and ORDER_TIME = #{orderTime,jdbcType=VARCHAR}
            </if>
            <if test="settlementDept != null" >
                and SETTLEMENT_DEPT = #{settlementDept,jdbcType=VARCHAR}
            </if>
            <if test="settlementItem != null" >
                and SETTLEMENT_ITEM = #{settlementItem,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and TM_SMP = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>