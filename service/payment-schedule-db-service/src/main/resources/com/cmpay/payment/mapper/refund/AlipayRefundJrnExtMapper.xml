<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.payment.schedule.dao.refund.IAlipayRefundJrnExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.payment.schedule.entity.refund.AlipayRefundJrnExtDO" >
        <id column="TRADE_ORDER_NO" property="tradeOrderNo" jdbcType="VARCHAR" />
        <result column="TRADE_DATE" property="tradeDate" jdbcType="VARCHAR" />
        <result column="TRADE_TIME" property="tradeTime" jdbcType="VARCHAR" />
        <result column="TRADE_STATUS" property="tradeStatus" jdbcType="VARCHAR" />
        <result column="ORG_ID" property="orgId" jdbcType="VARCHAR" />
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR" />
        <result column="SIGN_TYPE" property="signType" jdbcType="VARCHAR" />
        <result column="INTERFACE_TYPE" property="interfaceType" jdbcType="VARCHAR" />
        <result column="INTERFACE_VERSION" property="interfaceVersion" jdbcType="VARCHAR" />
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
        <result column="MERCHANT_TRADE_TYPE" property="merchantTradeType" jdbcType="VARCHAR" />
        <result column="MERCHANT_BUSINESS_CHANNEL" property="merchantBusinessChannel" jdbcType="VARCHAR" />
        <result column="MERCHANT_BUSINESS_TYPE" property="merchantBusinessType" jdbcType="VARCHAR" />
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL" />
        <result column="ORG_ORDER_NO" property="orgOrderNo" jdbcType="VARCHAR" />
        <result column="MERCHANT_ORDER_NO" property="merchantOrderNo" jdbcType="VARCHAR" />
        <result column="MERCHANT_ORDER_DATE" property="merchantOrderDate" jdbcType="VARCHAR" />
        <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR" />
        <result column="RESERVED1" property="reserved1" jdbcType="VARCHAR" />
        <result column="RESERVED2" property="reserved2" jdbcType="VARCHAR" />
        <result column="REFUND_REASON" property="refundReason" jdbcType="VARCHAR" />
        <result column="RETURN_MSG" property="returnMsg" jdbcType="VARCHAR" />
        <result column="RETURN_MSG_INFO" property="returnMsgInfo" jdbcType="VARCHAR" />
        <result column="RETURN_DATE" property="returnDate" jdbcType="VARCHAR" />
        <result column="RETURN_TIME" property="returnTime" jdbcType="VARCHAR" />
        <result column="CHECK_EXTEND_KEY" property="checkExtendKey" jdbcType="VARCHAR" />
        <result column="CHECK_STATUS" property="checkStatus" jdbcType="VARCHAR" />
        <result column="REMARK" property="remark" jdbcType="VARCHAR" />
        <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

</mapper>