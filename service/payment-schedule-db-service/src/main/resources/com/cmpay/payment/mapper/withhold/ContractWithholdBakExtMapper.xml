<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.payment.schedule.dao.withhold.IContractWithholdBakDao" >

     <sql id="Insert_Column_List" >
        CONTRACT_CODE, CREATE_DATE, PLAN_ID, CREATE_TIME, CONTRACT_DATE, MERCHANT_NO, BANK_MERCHANT_NO,
        APP_ID, CONTRACT_ID, DISPLAY_ACCOUNT, NOTIFY_URL, VERSION, OPENID, CHANGE_TYPE, OPERATE_TIME,
        EXPIRED_TIME, TERMINATION_MODE, CONTRACT_WAY, CONTRACT_SCENE, CONTRACT_STATUS, R<PERSON>UR<PERSON>_CODE,
        R<PERSON>UR<PERSON>_MSG, RETURN_WEB, R<PERSON><PERSON>N_APP, RETURN_APP_ID, CLIENT_IP, EXTRA, PROVINCE_CODE,
        PROVINCE_NAME, TM_SMP, SIGNED_TIME, DELETE_NOTIFY_URL, OUT_TRADE_NO, CONTRACT_TYPE,
        TERMINATED_TIME, SUB_MERCHANT_NAME, SUB_MERCHANT_SERVICE_NAME, SUB_MERCHANT_DESCRIPTION,
        PERIOD_TYPE, PERIOD, EXECUTE_TIME, SINGLE_AMOUNT, MOBILE_NO, FRONT_NOTIFY_URL, SIGN_BUS_TYPE
    </sql>

    <insert id="insertBatch" parameterType="list">
        insert into JK_CONTRACT_WITHHOLD_BAK(<include refid="Insert_Column_List"/>)
        <foreach collection="list" item="item" separator="union all" >
            select
                #{item.contractCode,jdbcType=VARCHAR},#{item.createDate,jdbcType=VARCHAR},#{item.planId,jdbcType=VARCHAR},#{item.createTime,jdbcType=VARCHAR},#{item.contractDate,jdbcType=VARCHAR},#{item.merchantNo,jdbcType=VARCHAR},#{item.bankMerchantNo,jdbcType=VARCHAR},
                #{item.appId,jdbcType=VARCHAR},#{item.contractId,jdbcType=VARCHAR},#{item.displayAccount,jdbcType=VARCHAR},#{item.notifyUrl,jdbcType=VARCHAR},#{item.version,jdbcType=VARCHAR},#{item.openid,jdbcType=VARCHAR},#{item.changeType,jdbcType=VARCHAR},#{item.operateTime,jdbcType=VARCHAR},
                #{item.expiredTime,jdbcType=VARCHAR},#{item.terminationMode,jdbcType=VARCHAR},#{item.contractWay,jdbcType=VARCHAR},#{item.contractScene,jdbcType=VARCHAR},#{item.contractStatus,jdbcType=VARCHAR},#{item.returnCode,jdbcType=VARCHAR},
                #{item.returnMsg,jdbcType=VARCHAR},#{item.returnWeb,jdbcType=VARCHAR},#{item.returnApp,jdbcType=VARCHAR},#{item.returnAppId,jdbcType=VARCHAR},#{item.clientIp,jdbcType=VARCHAR},#{item.extra,jdbcType=VARCHAR},#{item.provinceCode,jdbcType=VARCHAR},
                #{item.provinceName,jdbcType=VARCHAR},#{item.tmSmp,jdbcType=VARCHAR},#{item.signedTime,jdbcType=VARCHAR},#{item.deleteNotifyUrl,jdbcType=VARCHAR},#{item.outTradeNo,jdbcType=VARCHAR},#{item.contractType,jdbcType=VARCHAR},
                #{item.terminatedTime,jdbcType=VARCHAR},#{item.subMerchantName,jdbcType=VARCHAR},#{item.subMerchantServiceName,jdbcType=VARCHAR},#{item.subMerchantDescription,jdbcType=VARCHAR},
                #{item.periodType,jdbcType=VARCHAR},#{item.period,jdbcType=VARCHAR},#{item.executeTime,jdbcType=VARCHAR},#{item.singleAmount,jdbcType=DECIMAL},#{item.mobileNo,jdbcType=VARCHAR},#{item.frontNotifyUrl,jdbcType=VARCHAR},#{item.signBusType,jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>
</mapper>