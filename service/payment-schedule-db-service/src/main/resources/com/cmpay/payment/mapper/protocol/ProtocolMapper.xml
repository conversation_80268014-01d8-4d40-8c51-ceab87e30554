<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.payment.schedule.dao.protocol.IProtocolDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.payment.schedule.entity.protocol.ProtocolDO" >
        <id column="AGREEMENT_REQ_DATE" property="agreementReqDate" jdbcType="VARCHAR" />
        <id column="AGREEMENT_REQ_NO" property="agreementReqNo" jdbcType="VARCHAR" />
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="VARCHAR" />
        <result column="AGREEMENT_ID" property="agreementId" jdbcType="VARCHAR" />
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR" />
        <result column="BANK_MERCHANT_NO" property="bankMerchantNo" jdbcType="VARCHAR" />
        <result column="ID_TYPE" property="idType" jdbcType="VARCHAR" />
        <result column="ID_NO" property="idNo" jdbcType="VARCHAR" />
        <result column="MOBILE_NO" property="mobileNo" jdbcType="VARCHAR" />
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
        <result column="CHANGE_TYPE" property="changeType" jdbcType="VARCHAR" />
        <result column="SIGN_WAY" property="signWay" jdbcType="VARCHAR" />
        <result column="SIGN_SCENE" property="signScene" jdbcType="VARCHAR" />
        <result column="SIGN_STATUS" property="signStatus" jdbcType="VARCHAR" />
        <result column="SERIAL_NO" property="serialNo" jdbcType="VARCHAR" />
        <result column="WALLET_ID" property="walletId" jdbcType="VARCHAR" />
        <result column="ORDER_ID" property="orderId" jdbcType="VARCHAR" />
        <result column="APPLY_RETURN_CODE" property="applyReturnCode" jdbcType="VARCHAR" />
        <result column="APPLY_RETURN_MSG" property="applyReturnMsg" jdbcType="VARCHAR" />
        <result column="SIGN_RETURN_CODE" property="signReturnCode" jdbcType="VARCHAR" />
        <result column="SIGN_RETURN_MSG" property="signReturnMsg" jdbcType="VARCHAR" />
        <result column="SIGNED_TIME" property="signedTime" jdbcType="VARCHAR" />
        <result column="TERMINATED_TIME" property="terminatedTime" jdbcType="VARCHAR" />
        <result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR" />
        <result column="EXTRA" property="extra" jdbcType="VARCHAR" />
        <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        AGREEMENT_REQ_DATE, AGREEMENT_REQ_NO, CREATE_DATE, CREATE_TIME, AGREEMENT_ID, MERCHANT_NO,
        BANK_MERCHANT_NO, ID_TYPE, ID_NO, MOBILE_NO, USER_NAME, CHANGE_TYPE, SIGN_WAY, SIGN_SCENE,
        SIGN_STATUS, SERIAL_NO, WALLET_ID, ORDER_ID, APPLY_RETURN_CODE, APPLY_RETURN_MSG,
        SIGN_RETURN_CODE, SIGN_RETURN_MSG, SIGNED_TIME, TERMINATED_TIME, NOTIFY_URL, EXTRA,
        TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.payment.schedule.entity.protocol.ProtocolDOKey" >
        select
        <include refid="Base_Column_List" />
        from JK_PROTOCOL
        where AGREEMENT_REQ_DATE = #{agreementReqDate,jdbcType=VARCHAR}
          and AGREEMENT_REQ_NO = #{agreementReqNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.payment.schedule.entity.protocol.ProtocolDOKey" >
        delete from JK_PROTOCOL
        where AGREEMENT_REQ_DATE = #{agreementReqDate,jdbcType=VARCHAR}
          and AGREEMENT_REQ_NO = #{agreementReqNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.payment.schedule.entity.protocol.ProtocolDO" >
        insert into JK_PROTOCOL
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="agreementReqDate != null" >
                AGREEMENT_REQ_DATE,
            </if>
            <if test="agreementReqNo != null" >
                AGREEMENT_REQ_NO,
            </if>
            <if test="createDate != null" >
                CREATE_DATE,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="agreementId != null" >
                AGREEMENT_ID,
            </if>
            <if test="merchantNo != null" >
                MERCHANT_NO,
            </if>
            <if test="bankMerchantNo != null" >
                BANK_MERCHANT_NO,
            </if>
            <if test="idType != null" >
                ID_TYPE,
            </if>
            <if test="idNo != null" >
                ID_NO,
            </if>
            <if test="mobileNo != null" >
                MOBILE_NO,
            </if>
            <if test="userName != null" >
                USER_NAME,
            </if>
            <if test="changeType != null" >
                CHANGE_TYPE,
            </if>
            <if test="signWay != null" >
                SIGN_WAY,
            </if>
            <if test="signScene != null" >
                SIGN_SCENE,
            </if>
            <if test="signStatus != null" >
                SIGN_STATUS,
            </if>
            <if test="serialNo != null" >
                SERIAL_NO,
            </if>
            <if test="walletId != null" >
                WALLET_ID,
            </if>
            <if test="orderId != null" >
                ORDER_ID,
            </if>
            <if test="applyReturnCode != null" >
                APPLY_RETURN_CODE,
            </if>
            <if test="applyReturnMsg != null" >
                APPLY_RETURN_MSG,
            </if>
            <if test="signReturnCode != null" >
                SIGN_RETURN_CODE,
            </if>
            <if test="signReturnMsg != null" >
                SIGN_RETURN_MSG,
            </if>
            <if test="signedTime != null" >
                SIGNED_TIME,
            </if>
            <if test="terminatedTime != null" >
                TERMINATED_TIME,
            </if>
            <if test="notifyUrl != null" >
                NOTIFY_URL,
            </if>
            <if test="extra != null" >
                EXTRA,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="agreementReqDate != null" >
                #{agreementReqDate,jdbcType=VARCHAR},
            </if>
            <if test="agreementReqNo != null" >
                #{agreementReqNo,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null" >
                #{createDate,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="agreementId != null" >
                #{agreementId,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="bankMerchantNo != null" >
                #{bankMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="idType != null" >
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null" >
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="mobileNo != null" >
                #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="userName != null" >
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="changeType != null" >
                #{changeType,jdbcType=VARCHAR},
            </if>
            <if test="signWay != null" >
                #{signWay,jdbcType=VARCHAR},
            </if>
            <if test="signScene != null" >
                #{signScene,jdbcType=VARCHAR},
            </if>
            <if test="signStatus != null" >
                #{signStatus,jdbcType=VARCHAR},
            </if>
            <if test="serialNo != null" >
                #{serialNo,jdbcType=VARCHAR},
            </if>
            <if test="walletId != null" >
                #{walletId,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null" >
                #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="applyReturnCode != null" >
                #{applyReturnCode,jdbcType=VARCHAR},
            </if>
            <if test="applyReturnMsg != null" >
                #{applyReturnMsg,jdbcType=VARCHAR},
            </if>
            <if test="signReturnCode != null" >
                #{signReturnCode,jdbcType=VARCHAR},
            </if>
            <if test="signReturnMsg != null" >
                #{signReturnMsg,jdbcType=VARCHAR},
            </if>
            <if test="signedTime != null" >
                #{signedTime,jdbcType=VARCHAR},
            </if>
            <if test="terminatedTime != null" >
                #{terminatedTime,jdbcType=VARCHAR},
            </if>
            <if test="notifyUrl != null" >
                #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="extra != null" >
                #{extra,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.payment.schedule.entity.protocol.ProtocolDO" >
        update JK_PROTOCOL
        <set >
            <if test="createDate != null" >
                CREATE_DATE = #{createDate,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="agreementId != null" >
                AGREEMENT_ID = #{agreementId,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="bankMerchantNo != null" >
                BANK_MERCHANT_NO = #{bankMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="idType != null" >
                ID_TYPE = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null" >
                ID_NO = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="mobileNo != null" >
                MOBILE_NO = #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="userName != null" >
                USER_NAME = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="changeType != null" >
                CHANGE_TYPE = #{changeType,jdbcType=VARCHAR},
            </if>
            <if test="signWay != null" >
                SIGN_WAY = #{signWay,jdbcType=VARCHAR},
            </if>
            <if test="signScene != null" >
                SIGN_SCENE = #{signScene,jdbcType=VARCHAR},
            </if>
            <if test="signStatus != null" >
                SIGN_STATUS = #{signStatus,jdbcType=VARCHAR},
            </if>
            <if test="serialNo != null" >
                SERIAL_NO = #{serialNo,jdbcType=VARCHAR},
            </if>
            <if test="walletId != null" >
                WALLET_ID = #{walletId,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null" >
                ORDER_ID = #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="applyReturnCode != null" >
                APPLY_RETURN_CODE = #{applyReturnCode,jdbcType=VARCHAR},
            </if>
            <if test="applyReturnMsg != null" >
                APPLY_RETURN_MSG = #{applyReturnMsg,jdbcType=VARCHAR},
            </if>
            <if test="signReturnCode != null" >
                SIGN_RETURN_CODE = #{signReturnCode,jdbcType=VARCHAR},
            </if>
            <if test="signReturnMsg != null" >
                SIGN_RETURN_MSG = #{signReturnMsg,jdbcType=VARCHAR},
            </if>
            <if test="signedTime != null" >
                SIGNED_TIME = #{signedTime,jdbcType=VARCHAR},
            </if>
            <if test="terminatedTime != null" >
                TERMINATED_TIME = #{terminatedTime,jdbcType=VARCHAR},
            </if>
            <if test="notifyUrl != null" >
                NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="extra != null" >
                EXTRA = #{extra,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where AGREEMENT_REQ_DATE = #{agreementReqDate,jdbcType=VARCHAR}
          and AGREEMENT_REQ_NO = #{agreementReqNo,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.payment.schedule.entity.protocol.ProtocolDO" >
        select
        <include refid="Base_Column_List" />
        from JK_PROTOCOL
        <where >
            <if test="agreementReqDate != null" >
                and AGREEMENT_REQ_DATE = #{agreementReqDate,jdbcType=VARCHAR}
            </if>
            <if test="agreementReqNo != null" >
                and AGREEMENT_REQ_NO = #{agreementReqNo,jdbcType=VARCHAR}
            </if>
            <if test="createDate != null" >
                and CREATE_DATE = #{createDate,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and CREATE_TIME = #{createTime,jdbcType=VARCHAR}
            </if>
            <if test="agreementId != null" >
                and AGREEMENT_ID = #{agreementId,jdbcType=VARCHAR}
            </if>
            <if test="merchantNo != null" >
                and MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="bankMerchantNo != null" >
                and BANK_MERCHANT_NO = #{bankMerchantNo,jdbcType=VARCHAR}
            </if>
            <if test="idType != null" >
                and ID_TYPE = #{idType,jdbcType=VARCHAR}
            </if>
            <if test="idNo != null" >
                and ID_NO = #{idNo,jdbcType=VARCHAR}
            </if>
            <if test="mobileNo != null" >
                and MOBILE_NO = #{mobileNo,jdbcType=VARCHAR}
            </if>
            <if test="userName != null" >
                and USER_NAME = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="changeType != null" >
                and CHANGE_TYPE = #{changeType,jdbcType=VARCHAR}
            </if>
            <if test="signWay != null" >
                and SIGN_WAY = #{signWay,jdbcType=VARCHAR}
            </if>
            <if test="signScene != null" >
                and SIGN_SCENE = #{signScene,jdbcType=VARCHAR}
            </if>
            <if test="signStatus != null" >
                and SIGN_STATUS = #{signStatus,jdbcType=VARCHAR}
            </if>
            <if test="serialNo != null" >
                and SERIAL_NO = #{serialNo,jdbcType=VARCHAR}
            </if>
            <if test="walletId != null" >
                and WALLET_ID = #{walletId,jdbcType=VARCHAR}
            </if>
            <if test="orderId != null" >
                and ORDER_ID = #{orderId,jdbcType=VARCHAR}
            </if>
            <if test="applyReturnCode != null" >
                and APPLY_RETURN_CODE = #{applyReturnCode,jdbcType=VARCHAR}
            </if>
            <if test="applyReturnMsg != null" >
                and APPLY_RETURN_MSG = #{applyReturnMsg,jdbcType=VARCHAR}
            </if>
            <if test="signReturnCode != null" >
                and SIGN_RETURN_CODE = #{signReturnCode,jdbcType=VARCHAR}
            </if>
            <if test="signReturnMsg != null" >
                and SIGN_RETURN_MSG = #{signReturnMsg,jdbcType=VARCHAR}
            </if>
            <if test="signedTime != null" >
                and SIGNED_TIME = #{signedTime,jdbcType=VARCHAR}
            </if>
            <if test="terminatedTime != null" >
                and TERMINATED_TIME = #{terminatedTime,jdbcType=VARCHAR}
            </if>
            <if test="notifyUrl != null" >
                and NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR}
            </if>
            <if test="extra != null" >
                and EXTRA = #{extra,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and TM_SMP = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>
