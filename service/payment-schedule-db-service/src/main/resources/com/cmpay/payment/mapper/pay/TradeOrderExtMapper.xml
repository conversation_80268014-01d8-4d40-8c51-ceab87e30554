<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.payment.schedule.dao.pay.ITradeOrderExtDao" >

    <resultMap id="FirstResultMap" type="com.cmpay.payment.entity.TradeOrderExtDO">
        <id column="TRADE_ORDER_NO" property="tradeOrderNo" jdbcType="VARCHAR"/>
        <result column="TRADE_NO_HASH" property="tradeNoHash" jdbcType="INTEGER"/>
        <result column="SUBJECT" property="subject" jdbcType="VARCHAR"/>
        <result column="BUYER_ID" property="buyerId" jdbcType="VARCHAR"/>
        <result column="GOODS_ID" property="goodsId" jdbcType="VARCHAR"/>
        <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"/>
        <result column="GOODS_CATEGORY" property="goodsCategory" jdbcType="VARCHAR"/>
        <result column="GOODS_DESC" property="goodsDesc" jdbcType="VARCHAR"/>
        <result column="GOODS_URL" property="goodsUrl" jdbcType="VARCHAR"/>
        <result column="QUANTITY" property="quantity" jdbcType="DECIMAL"/>
        <result column="PRICE" property="price" jdbcType="DECIMAL"/>
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR"/>
        <result column="REQUEST_DATE" property="requestDate" jdbcType="VARCHAR"/>
        <result column="BANK_ORDER_NO" property="bankOrderNo" jdbcType="VARCHAR"/>
        <result column="BANK_MERCHANT_NO" property="bankMerchantNo" jdbcType="VARCHAR"/>
        <result column="PAY_PRODUCT_CODE" property="payProductCode" jdbcType="VARCHAR"/>
        <result column="PAY_WAY_CODE" property="payWayCode" jdbcType="VARCHAR"/>
        <result column="AIM_PRODUCT_CODE" property="aimProductCode" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE_DESC" property="businessTypeDesc" jdbcType="VARCHAR"/>
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL"/>
        <result column="DISCOUNTABLE_AMOUNT" property="discountableAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_FEE_AMOUNT" property="orderFeeAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_DATE" property="orderDate" jdbcType="VARCHAR"/>
        <result column="ORDER_TIME" property="orderTime" jdbcType="VARCHAR"/>
        <result column="EXPIRE_TIME" property="expireTime" jdbcType="VARCHAR"/>
        <result column="ORDER_COMPLETE_TIME" property="orderCompleteTime" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="ORDER_IP" property="orderIp" jdbcType="VARCHAR"/>
        <result column="ORDER_REFERER_URL" property="orderRefererUrl" jdbcType="VARCHAR"/>
        <result column="WECHAT_APPID" property="wechatAppid" jdbcType="VARCHAR"/>
        <result column="WECHAT_OPENID" property="wechatOpenid" jdbcType="VARCHAR"/>
        <result column="AUTH_CODE" property="authCode" jdbcType="VARCHAR"/>
        <result column="HALL_AREA_CODE" property="hallAreaCode" jdbcType="VARCHAR"/>
        <result column="HALL_CODE" property="hallCode" jdbcType="VARCHAR"/>
        <result column="HALL_WINDOW_CODE" property="hallWindowCode" jdbcType="VARCHAR"/>
        <result column="TERMINAL_CODE" property="terminalCode" jdbcType="VARCHAR"/>
        <result column="CLERK_CODE" property="clerkCode" jdbcType="VARCHAR"/>
        <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR"/>
        <result column="RETURN_URL" property="returnUrl" jdbcType="VARCHAR"/>
        <result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR"/>
        <result column="REFUND_TIMES" property="refundTimes" jdbcType="DECIMAL"/>
        <result column="SUCCESS_REFUND_AMOUNT" property="successRefundAmount" jdbcType="DECIMAL"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR"/>
        <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR"/>
        <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR"/>
        <result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR"/>
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="VARCHAR"/>
        <result column="APP_ID" property="appId" jdbcType="VARCHAR"/>
        <result column="REFUND_FEE_WAY" property="refundFeeWay" jdbcType="VARCHAR"/>
        <result column="ORDER_RATE" property="orderRate" jdbcType="DECIMAL"/>
        <result column="SECRET_INDEX" property="secretIndex" jdbcType="VARCHAR"/>
        <result column="ACCOUNT_DATE" property="accountDate" jdbcType="VARCHAR"/>
        <result column="BNK_TRACE_NO" property="bnkTraceNo" jdbcType="VARCHAR"/>
        <result column="MOBILE_NUMBER" property="mobileNumber" jdbcType="VARCHAR"/>
        <result column="THIRD_ORD_NO" property="thirdOrdNo" jdbcType="VARCHAR"/>
        <result column="THIRD_ORD_DT" property="thirdOrdDt" jdbcType="VARCHAR"/>
        <result column="CONTRACT_CODE" property="contractCode" jdbcType="VARCHAR" />
        <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL"/>
        <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"/>
        <result column="ACTIVITY_NAME" property="activityName" jdbcType="VARCHAR"/>
        <result column="PROVINCE_ORDER_NO" property="provinceOrderNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_NOTIFY_TIME" property="receiveNotifyTime" jdbcType="VARCHAR" />
        <result column="ERR_MSG_INFO" property="errMsgInfo" jdbcType="VARCHAR" />
        <result column="ERR_MSG_CD" property="errMsgCd" jdbcType="VARCHAR" />
        <result column="TIMEOUT_EXPRESS" property="timeoutExpress" jdbcType="VARCHAR" />
        <result column="SPLIT_RULE" property="splitRule" jdbcType="VARCHAR" />
        <result column="SHORT_SUM" property="shortSum" jdbcType="VARCHAR" />
        <result column="SH_STS" property="shSts" jdbcType="VARCHAR" />
        <result column="DCEP_FLAG" property="dcepFlag" jdbcType="VARCHAR" />
        <result column="SERVICE_CHARGE" property="serviceCharge" jdbcType="VARCHAR" />
        <result column="SUB_MERCHANT_NO" property="subMerchantNo" jdbcType="VARCHAR" />
        <result column="SETTLEMENT_DEPT" property="settlementDept" jdbcType="VARCHAR" />
        <result column="SETTLEMENT_ITEM" property="settlementItem" jdbcType="VARCHAR" />
        <result column="MERCHANT_CHANNEL_TYPE" property="merchantChannelType" jdbcType="VARCHAR" />
        <result column="SPLIT_FLAG" property="splitFlag" jdbcType="VARCHAR" />
        <result column="SUB_ORDER_INFOS" property="subOrderInfos" jdbcType="VARCHAR" />
        <result column="DATA_CENTER" property="dataCenter" jdbcType="VARCHAR" />
        <result column="DEBIT_BUS_TYPE" property="debitBusType" jdbcType="VARCHAR" />
        <result column="ORDER_POINTS" property="orderPoints" jdbcType="DECIMAL" />
        <result column="PAY_AMOUNT_LIST" property="payAmountList" jdbcType="VARCHAR" />
        <result column="INST_DISCOUNT_SETTLEMENT_AMOUNT" property="instDiscountSettlementAmount" jdbcType="DECIMAL" />
        <result column="INST_DISCOUNT_UNSETTLED_AMOUNT" property="instDiscountUnsettledAmount" jdbcType="DECIMAL" />
        <result column="INST_PAID_AMOUNT" property="instPaidAmount" jdbcType="DECIMAL" />
    </resultMap>

    <sql id="Base_Column_List" >
        TRADE_ORDER_NO, SUBJECT, BUYER_ID, GOODS_ID, GOODS_NAME, GOODS_CATEGORY, GOODS_DESC,
        GOODS_URL, QUANTITY, PRICE, MERCHANT_NO, OUT_TRADE_NO, REQUEST_DATE, BANK_ORDER_NO,
        BANK_MERCHANT_NO, PAY_PRODUCT_CODE, PAY_WAY_CODE, AIM_PRODUCT_CODE, BUSINESS_TYPE,
        BUSINESS_TYPE_DESC, ORDER_AMOUNT, REAL_AMOUNT, DISCOUNTABLE_AMOUNT, ORDER_FEE_AMOUNT,
        ORDER_DATE, ORDER_TIME, EXPIRE_TIME, ORDER_COMPLETE_TIME, STATUS, ORDER_IP, ORDER_REFERER_URL,
        WECHAT_APPID, WECHAT_OPENID, AUTH_CODE, HALL_AREA_CODE, HALL_CODE, HALL_WINDOW_CODE,
        TERMINAL_CODE, CLERK_CODE, OPERATOR_ID, RETURN_URL, NOTIFY_URL, REFUND_TIMES, SUCCESS_REFUND_AMOUNT,
        REMARK, TM_SMP, PROVINCE_CODE, PROVINCE_NAME, BUSINESS_CODE, CRD_AC_TYP, APP_ID,
        REFUND_FEE_WAY, ORDER_RATE, SECRET_INDEX, ACCOUNT_DATE, BNK_TRACE_NO, MOBILE_NUMBER,
        THIRD_ORD_NO, THIRD_ORD_DT, CONTRACT_CODE, DISCOUNT_AMOUNT, ACTIVITY_ID, ACTIVITY_NAME,
        PROVINCE_ORDER_NO, RECEIVE_NOTIFY_TIME, ERR_MSG_INFO, ERR_MSG_CD, TIMEOUT_EXPRESS,
        SPLIT_RULE, SHORT_SUM, SH_STS, DCEP_FLAG, SERVICE_CHARGE, SUB_MERCHANT_NO, SETTLEMENT_DEPT,
        SETTLEMENT_ITEM, MERCHANT_CHANNEL_TYPE, SPLIT_FLAG, SUB_ORDER_INFOS, DATA_CENTER,
        DEBIT_BUS_TYPE, ORDER_POINTS, PAY_AMOUNT_LIST, INST_DISCOUNT_SETTLEMENT_AMOUNT, INST_DISCOUNT_UNSETTLED_AMOUNT,
        INST_PAID_AMOUNT
    </sql>


    <select id="lock" resultType="java.lang.String" parameterType="java.lang.String" >
        SELECT TRADE_ORDER_NO FROM FAP_TRADE_ORDER WHERE TRADE_ORDER_NO = #{tradeOrderNo,jdbcType=VARCHAR}  FOR UPDATE NOWAIT
    </select>

    <update id="updatePaymentStatus" parameterType="com.cmpay.payment.entity.TradeOrderDO" >
        UPDATE FAP_TRADE_ORDER
        <set>
            <if test="status !=null">
                STATUS = #{status,jdbcType=VARCHAR},
            </if>
            <if test="errMsgCd !=null">
                ERR_MSG_CD = #{errMsgCd,jdbcType=VARCHAR},
            </if>
            <if test="errMsgInfo !=null">
                ERR_MSG_INFO = #{errMsgInfo,jdbcType=VARCHAR},
            </if>
            <if test="orderCompleteTime !=null">
                ORDER_COMPLETE_TIME = #{orderCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="accountDate != null" >
                ACCOUNT_DATE = #{accountDate,jdbcType=VARCHAR},
            </if>
            <if test="bnkTraceNo != null" >
                BNK_TRACE_NO = #{bnkTraceNo,jdbcType=VARCHAR},
            </if>
            <if test="mobileNumber != null" >
                MOBILE_NUMBER = #{mobileNumber,jdbcType=VARCHAR},
            </if>
            <if test="thirdOrdNo != null" >
                THIRD_ORD_NO = #{thirdOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="thirdOrdDt != null" >
                THIRD_ORD_DT = #{thirdOrdDt,jdbcType=VARCHAR},
            </if>
            <if test="orderFeeAmount != null" >
                ORDER_FEE_AMOUNT = #{orderFeeAmount,jdbcType=DECIMAL},
            </if>
            <if test="orderRate != null" >
                ORDER_RATE = #{orderRate,jdbcType=DECIMAL},
            </if>
            <if test="discountAmount != null" >
                DISCOUNT_AMOUNT = #{discountAmount,jdbcType=DECIMAL},
            </if>
            <if test="activityId != null" >
                ACTIVITY_ID = #{activityId,jdbcType=VARCHAR},
            </if>
            <if test="activityName != null" >
                ACTIVITY_NAME = #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="payProductCode != null" >
                PAY_PRODUCT_CODE = #{payProductCode,jdbcType=VARCHAR},
            </if>
            <if test="aimProductCode != null" >
                AIM_PRODUCT_CODE = #{aimProductCode,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=VARCHAR},
            </if>
            <if test="receiveNotifyTime != null" >
                RECEIVE_NOTIFY_TIME = #{receiveNotifyTime,jdbcType=VARCHAR},
            </if>
            <if test="shortSum != null" >
                SHORT_SUM = #{shortSum,jdbcType=VARCHAR},
            </if>
            <if test="shSts != null" >
                SH_STS = #{shSts,jdbcType=VARCHAR},
            </if>
            <if test="payAmountList != null and payAmountList != ''" >
                PAY_AMOUNT_LIST = #{payAmountList,jdbcType=VARCHAR},
            </if>
            <if test="instDiscountSettlementAmount != null" >
                INST_DISCOUNT_SETTLEMENT_AMOUNT = #{instDiscountSettlementAmount,jdbcType=DECIMAL},
            </if>
            <if test="instDiscountUnsettledAmount != null" >
                INST_DISCOUNT_UNSETTLED_AMOUNT = #{instDiscountUnsettledAmount,jdbcType=DECIMAL},
            </if>
            <if test="instPaidAmount != null" >
                INST_PAID_AMOUNT = #{instPaidAmount,jdbcType=DECIMAL},
            </if>
            <choose>
                <when test="crdAcTyp !=null">
                    CRD_AC_TYP = #{crdAcTyp,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    CRD_AC_TYP = '2',
                </otherwise>
            </choose>
        </set>
        WHERE MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}
        AND ORDER_DATE = #{orderDate,jdbcType=VARCHAR}
        AND OUT_TRADE_NO = #{outTradeNo,jdbcType=VARCHAR}
        AND TRADE_ORDER_NO = #{tradeOrderNo,jdbcType=VARCHAR}
        AND STATUS in ('WAIT_PAY','TRADE_CLOSED')
    </update>


    <select id="queryPaymentChannelOrderList" resultMap="FirstResultMap"
            parameterType="com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO">
        SELECT TRADE_ORDER_NO, SUBJECT, BUYER_ID, GOODS_ID, GOODS_NAME, GOODS_CATEGORY, GOODS_DESC,
        GOODS_URL, QUANTITY, PRICE, MERCHANT_NO, OUT_TRADE_NO, REQUEST_DATE, BANK_ORDER_NO,
        BANK_MERCHANT_NO, PAY_PRODUCT_CODE, PAY_WAY_CODE, AIM_PRODUCT_CODE, BUSINESS_TYPE,
        BUSINESS_TYPE_DESC, ORDER_AMOUNT, REAL_AMOUNT, DISCOUNTABLE_AMOUNT, ORDER_FEE_AMOUNT,
        ORDER_DATE, ORDER_TIME, EXPIRE_TIME, ORDER_COMPLETE_TIME, STATUS, ORDER_IP, ORDER_REFERER_URL,
        WECHAT_APPID, WECHAT_OPENID, AUTH_CODE, HALL_AREA_CODE, HALL_CODE, HALL_WINDOW_CODE,
        TERMINAL_CODE, CLERK_CODE, OPERATOR_ID, RETURN_URL, NOTIFY_URL, REFUND_TIMES, SUCCESS_REFUND_AMOUNT,
        REMARK, TM_SMP, PROVINCE_CODE, PROVINCE_NAME, BUSINESS_CODE, CRD_AC_TYP, APP_ID,
        REFUND_FEE_WAY, ORDER_RATE, SECRET_INDEX, ACCOUNT_DATE, THIRD_ORD_NO, THIRD_ORD_DT, MOBILE_NUMBER, SERVICE_CHARGE,
        SUB_MERCHANT_NO, SETTLEMENT_DEPT, SETTLEMENT_ITEM, MERCHANT_CHANNEL_TYPE, SPLIT_FLAG,SUB_ORDER_INFOS, DATA_CENTER, INST_DISCOUNT_SETTLEMENT_AMOUNT, INST_DISCOUNT_UNSETTLED_AMOUNT,
        INST_PAID_AMOUNT
        FROM FAP_TRADE_ORDER
        WHERE  ORDER_DATE >= #{requestDate,jdbcType=VARCHAR}
        and ORA_HASH(TRADE_ORDER_NO, #{totalSelect,jdbcType=INTEGER}) = #{currentIndex,jdbcType=INTEGER}
        AND(ORDER_DATE||ORDER_TIME) &lt;= #{orderDate,jdbcType=VARCHAR}
        AND STATUS = #{status,jdbcType=VARCHAR}
        AND AIM_PRODUCT_CODE = #{payProductCode,jdbcType=VARCHAR}
        AND (ORDER_DATE||ORDER_TIME)  >= #{orderTime,jdbcType=VARCHAR}
        AND PAY_WAY_CODE NOT IN ('barcode','scan','mmpay','micropay')
        <if test="dataCenter == 'XPQ'" >
            AND (DATA_CENTER = #{dataCenter,jdbcType=VARCHAR} OR DATA_CENTER is null)
        </if>
        <if test="dataCenter == 'YL'" >
            AND DATA_CENTER = #{dataCenter,jdbcType=VARCHAR}
        </if>
        AND ROWNUM &lt;= #{total,jdbcType=VARCHAR}
    </select>

    <select id="queryOrderScanBarcodeList" resultMap="FirstResultMap"
            parameterType="com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO">
        SELECT TRADE_ORDER_NO, SUBJECT, BUYER_ID, GOODS_ID, GOODS_NAME, GOODS_CATEGORY, GOODS_DESC,
        GOODS_URL, QUANTITY, PRICE, MERCHANT_NO, OUT_TRADE_NO, REQUEST_DATE, BANK_ORDER_NO,
        BANK_MERCHANT_NO, PAY_PRODUCT_CODE, PAY_WAY_CODE, AIM_PRODUCT_CODE, BUSINESS_TYPE,
        BUSINESS_TYPE_DESC, ORDER_AMOUNT, REAL_AMOUNT, DISCOUNTABLE_AMOUNT, ORDER_FEE_AMOUNT,
        ORDER_DATE, ORDER_TIME, EXPIRE_TIME, ORDER_COMPLETE_TIME, STATUS, ORDER_IP, ORDER_REFERER_URL,
        WECHAT_APPID, WECHAT_OPENID, AUTH_CODE, HALL_AREA_CODE, HALL_CODE, HALL_WINDOW_CODE,
        TERMINAL_CODE, CLERK_CODE, OPERATOR_ID, RETURN_URL, NOTIFY_URL, REFUND_TIMES, SUCCESS_REFUND_AMOUNT,
        REMARK, TM_SMP, PROVINCE_CODE, PROVINCE_NAME, BUSINESS_CODE, CRD_AC_TYP, APP_ID,
        REFUND_FEE_WAY, ORDER_RATE, SECRET_INDEX, ACCOUNT_DATE, THIRD_ORD_NO, THIRD_ORD_DT, SERVICE_CHARGE,
        SUB_MERCHANT_NO, SETTLEMENT_DEPT, SETTLEMENT_ITEM, MERCHANT_CHANNEL_TYPE, SPLIT_FLAG,
        SUB_ORDER_INFOS, DATA_CENTER, INST_DISCOUNT_SETTLEMENT_AMOUNT, INST_DISCOUNT_UNSETTLED_AMOUNT,
        INST_PAID_AMOUNT
        FROM FAP_TRADE_ORDER
        WHERE  ORDER_DATE >= #{requestDate,jdbcType=VARCHAR}
        and ORA_HASH(TRADE_ORDER_NO, #{totalSelect,jdbcType=INTEGER}) = #{currentIndex,jdbcType=INTEGER}
        AND(ORDER_DATE||ORDER_TIME) &lt;= #{orderDate,jdbcType=VARCHAR}
        AND STATUS = #{status,jdbcType=VARCHAR}
        AND (ORDER_DATE||ORDER_TIME)  >= #{orderTime,jdbcType=VARCHAR}
        AND PAY_WAY_CODE IN ('barcode','scan')
        AND AIM_PRODUCT_CODE = #{payProductCode,jdbcType=VARCHAR}
        <if test="dataCenter == 'XPQ'" >
            AND (DATA_CENTER = #{dataCenter,jdbcType=VARCHAR} OR DATA_CENTER is null)
        </if>
        <if test="dataCenter == 'YL'" >
            AND DATA_CENTER = #{dataCenter,jdbcType=VARCHAR}
        </if>
        AND ROWNUM &lt;= #{total,jdbcType=VARCHAR}
        order by ORDER_TIME desc
    </select>


    <select id="querySyncContractPayScheduleOrder" resultMap="FirstResultMap" parameterType="com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO">
        SELECT TRADE_ORDER_NO, SUBJECT, BUYER_ID, GOODS_ID, GOODS_NAME, GOODS_CATEGORY, GOODS_DESC,
        GOODS_URL, QUANTITY, PRICE, MERCHANT_NO, OUT_TRADE_NO, REQUEST_DATE, BANK_ORDER_NO,
        BANK_MERCHANT_NO, PAY_PRODUCT_CODE, PAY_WAY_CODE, AIM_PRODUCT_CODE, BUSINESS_TYPE,
        BUSINESS_TYPE_DESC, ORDER_AMOUNT, REAL_AMOUNT, DISCOUNTABLE_AMOUNT, ORDER_FEE_AMOUNT,
        ORDER_DATE, ORDER_TIME, EXPIRE_TIME, ORDER_COMPLETE_TIME, STATUS, ORDER_IP, ORDER_REFERER_URL,
        WECHAT_APPID, WECHAT_OPENID, AUTH_CODE, HALL_AREA_CODE, HALL_CODE, HALL_WINDOW_CODE,
        TERMINAL_CODE, CLERK_CODE, OPERATOR_ID, RETURN_URL, NOTIFY_URL, REFUND_TIMES, SUCCESS_REFUND_AMOUNT,
        REMARK, TM_SMP, PROVINCE_CODE, PROVINCE_NAME, BUSINESS_CODE, CRD_AC_TYP, APP_ID,
        REFUND_FEE_WAY, ORDER_RATE, SECRET_INDEX, ACCOUNT_DATE, CONTRACT_CODE, THIRD_ORD_NO, THIRD_ORD_DT, SERVICE_CHARGE,
        SUB_MERCHANT_NO, SETTLEMENT_DEPT, SETTLEMENT_ITEM, MERCHANT_CHANNEL_TYPE, SPLIT_FLAG, SUB_ORDER_INFOS,
        INST_DISCOUNT_SETTLEMENT_AMOUNT, INST_DISCOUNT_UNSETTLED_AMOUNT,INST_PAID_AMOUNT
        FROM FAP_TRADE_ORDER
        WHERE ORDER_DATE = #{requestDate,jdbcType=VARCHAR}
        AND ORDER_DATE||ORDER_TIME >= #{startTime,jdbcType=VARCHAR}
        AND ORDER_DATE||ORDER_TIME &lt;= #{endTime,jdbcType=VARCHAR}
        AND ORA_HASH(TRADE_ORDER_NO, #{totalSelect,jdbcType=INTEGER}) = #{currentIndex,jdbcType=INTEGER}
        AND STATUS = #{status,jdbcType=VARCHAR}
        AND PAY_WAY_CODE = 'contractpay'
        AND AIM_PRODUCT_CODE = #{aimProductCode,jdbcType=VARCHAR}
        <if test="dataCenter == 'XPQ'" >
            AND (DATA_CENTER = #{dataCenter,jdbcType=VARCHAR} OR DATA_CENTER is null)
        </if>
        <if test="dataCenter == 'YL'" >
            AND DATA_CENTER = #{dataCenter,jdbcType=VARCHAR}
        </if>
        AND ROWNUM &lt;= #{total,jdbcType=VARCHAR}
    </select>

    <select id="queryIcbcOrderScheduleList" resultMap="FirstResultMap" parameterType="com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO">
        SELECT TRADE_ORDER_NO, SUBJECT, BUYER_ID, GOODS_ID, GOODS_NAME, GOODS_CATEGORY, GOODS_DESC,
        GOODS_URL, QUANTITY, PRICE, MERCHANT_NO, OUT_TRADE_NO, REQUEST_DATE, BANK_ORDER_NO,
        BANK_MERCHANT_NO, PAY_PRODUCT_CODE, PAY_WAY_CODE, AIM_PRODUCT_CODE, BUSINESS_TYPE,
        BUSINESS_TYPE_DESC, ORDER_AMOUNT, REAL_AMOUNT, DISCOUNTABLE_AMOUNT, ORDER_FEE_AMOUNT,
        ORDER_DATE, ORDER_TIME, EXPIRE_TIME, ORDER_COMPLETE_TIME, STATUS, ORDER_IP, ORDER_REFERER_URL,
        WECHAT_APPID, WECHAT_OPENID, AUTH_CODE, HALL_AREA_CODE, HALL_CODE, HALL_WINDOW_CODE,
        TERMINAL_CODE, CLERK_CODE, OPERATOR_ID, RETURN_URL, NOTIFY_URL, REFUND_TIMES, SUCCESS_REFUND_AMOUNT,
        REMARK, TM_SMP, PROVINCE_CODE, PROVINCE_NAME, BUSINESS_CODE, CRD_AC_TYP, APP_ID,
        REFUND_FEE_WAY, ORDER_RATE, SECRET_INDEX, ACCOUNT_DATE, THIRD_ORD_NO, THIRD_ORD_DT, DCEP_FLAG, SERVICE_CHARGE,
        SUB_MERCHANT_NO, SETTLEMENT_DEPT, SETTLEMENT_ITEM, MERCHANT_CHANNEL_TYPE, SPLIT_FLAG, SUB_ORDER_INFOS,
        INST_DISCOUNT_SETTLEMENT_AMOUNT, INST_DISCOUNT_UNSETTLED_AMOUNT, INST_PAID_AMOUNT
        FROM FAP_TRADE_ORDER
        WHERE  ORDER_DATE >= #{requestDate,jdbcType=VARCHAR}
        AND ORA_HASH(TRADE_ORDER_NO, #{totalSelect,jdbcType=INTEGER}) = #{currentIndex,jdbcType=INTEGER}
        AND (ORDER_DATE||ORDER_TIME) &lt;= #{orderDate,jdbcType=VARCHAR}
        AND (ORDER_DATE||ORDER_TIME)  >= #{orderTime,jdbcType=VARCHAR}
        AND AIM_PRODUCT_CODE in  ('icbcpay' ,'dceppay')
        AND STATUS = #{status,jdbcType=VARCHAR}
        <if test="dataCenter == 'XPQ'" >
            AND (DATA_CENTER = #{dataCenter,jdbcType=VARCHAR} OR DATA_CENTER is null)
        </if>
        <if test="dataCenter == 'YL'" >
            AND DATA_CENTER = #{dataCenter,jdbcType=VARCHAR}
        </if>
        AND ROWNUM &lt;= #{total,jdbcType=VARCHAR}
    </select>

    <select id="queryPaymentChannelTestOrderList" resultMap="FirstResultMap"
            parameterType="com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO">
        SELECT TRADE_ORDER_NO, SUBJECT, BUYER_ID, GOODS_ID, GOODS_NAME, GOODS_CATEGORY, GOODS_DESC,
        GOODS_URL, QUANTITY, PRICE, MERCHANT_NO, OUT_TRADE_NO, REQUEST_DATE, BANK_ORDER_NO,
        BANK_MERCHANT_NO, PAY_PRODUCT_CODE, PAY_WAY_CODE, AIM_PRODUCT_CODE, BUSINESS_TYPE,
        BUSINESS_TYPE_DESC, ORDER_AMOUNT, REAL_AMOUNT, DISCOUNTABLE_AMOUNT, ORDER_FEE_AMOUNT,
        ORDER_DATE, ORDER_TIME, EXPIRE_TIME, ORDER_COMPLETE_TIME, STATUS, ORDER_IP, ORDER_REFERER_URL,
        WECHAT_APPID, WECHAT_OPENID, AUTH_CODE, HALL_AREA_CODE, HALL_CODE, HALL_WINDOW_CODE,
        TERMINAL_CODE, CLERK_CODE, OPERATOR_ID, RETURN_URL, NOTIFY_URL, REFUND_TIMES, SUCCESS_REFUND_AMOUNT,
        REMARK, TM_SMP, PROVINCE_CODE, PROVINCE_NAME, BUSINESS_CODE, CRD_AC_TYP, APP_ID,
        REFUND_FEE_WAY, ORDER_RATE, SECRET_INDEX, ACCOUNT_DATE, THIRD_ORD_NO, THIRD_ORD_DT, SERVICE_CHARGE,
        SUB_MERCHANT_NO, SETTLEMENT_DEPT, SETTLEMENT_ITEM, MERCHANT_CHANNEL_TYPE, SPLIT_FLAG, SUB_ORDER_INFOS,
        DATA_CENTER, INST_DISCOUNT_SETTLEMENT_AMOUNT, INST_DISCOUNT_UNSETTLED_AMOUNT, INST_PAID_AMOUNT
        FROM FAP_TRADE_ORDER
        WHERE  ORDER_DATE >= #{requestDate,jdbcType=VARCHAR}
        AND ORA_HASH(TRADE_ORDER_NO, #{totalSelect,jdbcType=INTEGER}) = #{currentIndex,jdbcType=INTEGER}
        AND(ORDER_DATE||ORDER_TIME) &lt;= #{orderDate,jdbcType=VARCHAR}
        AND MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}
        AND STATUS = #{status,jdbcType=VARCHAR}
        AND (ORDER_DATE||ORDER_TIME)  >= #{orderTime,jdbcType=VARCHAR}
        <if test="dataCenter == 'XPQ'" >
            AND (DATA_CENTER = #{dataCenter,jdbcType=VARCHAR} OR DATA_CENTER is null)
        </if>
        <if test="dataCenter == 'YL'" >
            AND DATA_CENTER = #{dataCenter,jdbcType=VARCHAR}
        </if>
        AND ROWNUM &lt;= #{total,jdbcType=VARCHAR}
    </select>

    <select id="queryTimeOutPaymentChannelList" resultMap="FirstResultMap"
            parameterType="com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO">
        SELECT TRADE_ORDER_NO, SUBJECT, BUYER_ID, GOODS_ID, GOODS_NAME, GOODS_CATEGORY, GOODS_DESC,
        GOODS_URL, QUANTITY, PRICE, MERCHANT_NO, OUT_TRADE_NO, REQUEST_DATE, BANK_ORDER_NO,
        BANK_MERCHANT_NO, PAY_PRODUCT_CODE, PAY_WAY_CODE, AIM_PRODUCT_CODE, BUSINESS_TYPE,
        BUSINESS_TYPE_DESC, ORDER_AMOUNT, REAL_AMOUNT, DISCOUNTABLE_AMOUNT, ORDER_FEE_AMOUNT,
        ORDER_DATE, ORDER_TIME, EXPIRE_TIME, ORDER_COMPLETE_TIME, STATUS, ORDER_IP, ORDER_REFERER_URL,
        WECHAT_APPID, WECHAT_OPENID, AUTH_CODE, HALL_AREA_CODE, HALL_CODE, HALL_WINDOW_CODE,
        TERMINAL_CODE, CLERK_CODE, OPERATOR_ID, RETURN_URL, NOTIFY_URL, REFUND_TIMES, SUCCESS_REFUND_AMOUNT,
        REMARK, TM_SMP, PROVINCE_CODE, PROVINCE_NAME, BUSINESS_CODE, CRD_AC_TYP, APP_ID, ERR_MSG_CD,
        REFUND_FEE_WAY, ORDER_RATE, SECRET_INDEX, ACCOUNT_DATE, THIRD_ORD_NO, THIRD_ORD_DT, MOBILE_NUMBER, SERVICE_CHARGE,
        SUB_MERCHANT_NO, SETTLEMENT_DEPT, SETTLEMENT_ITEM, MERCHANT_CHANNEL_TYPE, SPLIT_FLAG,SUB_ORDER_INFOS,
        INST_DISCOUNT_SETTLEMENT_AMOUNT, INST_DISCOUNT_UNSETTLED_AMOUNT, INST_PAID_AMOUNT
        FROM FAP_TRADE_ORDER
        WHERE  ORDER_DATE >= #{requestDate,jdbcType=VARCHAR}
        and ORA_HASH(TRADE_ORDER_NO, #{totalSelect,jdbcType=INTEGER}) = #{currentIndex,jdbcType=INTEGER}
        AND(ORDER_DATE||ORDER_TIME) &lt;= #{orderDate,jdbcType=VARCHAR}
        AND STATUS = 'WAIT_PAY'
        AND PAY_PRODUCT_CODE = 'integralpay'
        AND (ORDER_DATE||ORDER_TIME)  >= #{orderTime,jdbcType=VARCHAR}
        <if test="dataCenter == 'XPQ'" >
            AND (DATA_CENTER = #{dataCenter,jdbcType=VARCHAR} OR DATA_CENTER is null)
        </if>
        <if test="dataCenter == 'YL'" >
            AND DATA_CENTER = #{dataCenter,jdbcType=VARCHAR}
        </if>
        AND ROWNUM &lt;= #{total,jdbcType=VARCHAR}
        AND BANK_ORDER_NO is not null
    </select>


    <select id="standbyScheduleOrderQuery" resultMap="FirstResultMap"
        parameterType="com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO">
        select <include refid="Base_Column_List" />
        from FAP_TRADE_ORDER
        <where>
            ORDER_DATE = #{orderDate,jdbcType=VARCHAR}
            <if test="merchantNo != null" >
                and MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="payWayCode != null" >
                and PAY_WAY_CODE in
                <foreach collection="payWayArray" item="item" index="index" open="(" close=")" separator=",">
                  #{item}
                </foreach>
            </if>
            <if test="aimProductCode != null" >
                and AIM_PRODUCT_CODE in
                <foreach collection="aimArray" item="item" index="index" open="(" close=")" separator=",">
                  #{item}
                </foreach>
            </if>
            <if test="payProductCode != null" >
                and PAY_PRODUCT_CODE in
                <foreach collection="payArray" item="item" index="index" open="(" close=")" separator=",">
                  #{item}
                </foreach>
            </if>
             <if test="startTime != null and endTime != null">
                and ORDER_TIME between #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR}
             </if>
            <if test="status != null" >
                and STATUS = #{status,jdbcType=VARCHAR}
            </if>
            <if test="dataCenter == 'XPQ'" >
                AND (DATA_CENTER = #{dataCenter,jdbcType=VARCHAR} OR DATA_CENTER is null)
            </if>
            <if test="dataCenter == 'YL'" >
                AND DATA_CENTER = #{dataCenter,jdbcType=VARCHAR}
            </if>
            and ROWNUM &lt;= #{total,jdbcType=VARCHAR}
        </where>
    </select>
</mapper>
