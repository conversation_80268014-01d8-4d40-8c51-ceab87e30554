<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.payment.schedule.dao.withhold.IContractDeleteExtDao" >

    <resultMap id="FirstResultMap" type="com.cmpay.payment.schedule.entity.withhold.ContractDeleteExtDO" >
        <id column="JRN_NO" property="jrnNo" jdbcType="VARCHAR" />
        <id column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="TRADE_NO_HASH" property="tradeNoHash" jdbcType="INTEGER" />
        <result column="CONTRACT_CODE" property="contractCode" jdbcType="VARCHAR" />
        <result column="PLAN_ID" property="planId" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="VARCHAR" />
        <result column="CONTRACT_DATE" property="contractDate" jdbcType="VARCHAR" />
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR" />
        <result column="BANK_MERCHANT_NO" property="bankMerchantNo" jdbcType="VARCHAR" />
        <result column="APP_ID" property="appId" jdbcType="VARCHAR" />
        <result column="CONTRACT_ID" property="contractId" jdbcType="VARCHAR" />
        <result column="DISPLAY_ACCOUNT" property="displayAccount" jdbcType="VARCHAR" />
        <result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR" />
        <result column="OPENID" property="openid" jdbcType="VARCHAR" />
        <result column="CHANGE_TYPE" property="changeType" jdbcType="VARCHAR" />
        <result column="OPERATE_TIME" property="operateTime" jdbcType="VARCHAR" />
        <result column="EXPIRED_TIME" property="expiredTime" jdbcType="VARCHAR" />
        <result column="TERMINATION_MODE" property="terminationMode" jdbcType="VARCHAR" />
        <result column="CONTRACT_WAY" property="contractWay" jdbcType="VARCHAR" />
        <result column="CONTRACT_SCENE" property="contractScene" jdbcType="VARCHAR" />
        <result column="CONTRACT_STATUS" property="contractStatus" jdbcType="VARCHAR" />
        <result column="RETURN_CODE" property="returnCode" jdbcType="VARCHAR" />
        <result column="RETURN_MSG" property="returnMsg" jdbcType="VARCHAR" />
        <result column="CLIENT_IP" property="clientIp" jdbcType="VARCHAR" />
        <result column="EXTRA" property="extra" jdbcType="VARCHAR" />
        <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR" />
        <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR" />
        <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR" />
        <result column="TERMINATED_TIME" property="terminatedTime" jdbcType="VARCHAR" />
        <result column="MOBILE_NO" property="mobileNo" jdbcType="VARCHAR" />
        <result column="SIGN_BUS_TYPE" property="signBusType" jdbcType="VARCHAR" />
        <result column="DATA_CENTER" property="dataCenter" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        JRN_NO, CREATE_DATE, CONTRACT_CODE, PLAN_ID, CREATE_TIME, CONTRACT_DATE, MERCHANT_NO,
        BANK_MERCHANT_NO, APP_ID, CONTRACT_ID, DISPLAY_ACCOUNT, NOTIFY_URL, OPENID, CHANGE_TYPE,
        OPERATE_TIME, EXPIRED_TIME, TERMINATION_MODE, CONTRACT_WAY, CONTRACT_SCENE, CONTRACT_STATUS,
        RETURN_CODE, RETURN_MSG, CLIENT_IP, EXTRA, PROVINCE_CODE, PROVINCE_NAME, TM_SMP,
        TERMINATED_TIME, MOBILE_NO, SIGN_BUS_TYPE, DATA_CENTER
    </sql>

    <select id="findDeleteInfo" resultMap="FirstResultMap" parameterType="com.cmpay.payment.schedule.entity.withhold.ContractDeleteDOKey" >
        select
        <include refid="Base_Column_List" />
        from JK_CONTRACT_DELETE
        where CONTRACT_CODE = #{contractCode,jdbcType=VARCHAR}
        and  CONTRACT_STATUS = 'CONTRACT_TERMINATED_WAIT'
        order by TM_SMP desc
    </select>


    <select id="findDeleteWaitList" resultMap="FirstResultMap"
            parameterType="com.cmpay.payment.schedule.bo.withhold.ContractQueryBO">
        select <include refid="Base_Column_List"/>
        from JK_CONTRACT_DELETE
        where CREATE_DATE >= #{createDate,jdbcType=VARCHAR}
        and ORA_HASH(CONTRACT_CODE, #{totalSelect,jdbcType=INTEGER}) = #{currentIndex,jdbcType=INTEGER}
        and CONTRACT_STATUS = 'CONTRACT_TERMINATED_WAIT'
        and CONTRACT_WAY = #{contractWay,jdbcType=VARCHAR}
        <if test="dataCenter == 'XPQ'" >
            and (DATA_CENTER = #{dataCenter,jdbcType=VARCHAR} OR DATA_CENTER is null)
        </if>
        <if test="dataCenter == 'YL'" >
            and DATA_CENTER = #{dataCenter,jdbcType=VARCHAR}
        </if>
        AND ROWNUM &lt;=#{param,jdbcType=VARCHAR}
    </select>
</mapper>
