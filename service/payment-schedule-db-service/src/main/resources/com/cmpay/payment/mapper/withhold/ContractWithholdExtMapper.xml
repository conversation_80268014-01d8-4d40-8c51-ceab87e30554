<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.payment.schedule.dao.withhold.IContractWithholdExtDao">

    <resultMap id="FirstResultMap" type="com.cmpay.payment.schedule.entity.withhold.ContractWithholdExtDO" >
        <id column="CONTRACT_CODE" property="contractCode" jdbcType="VARCHAR" />
        <id column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="TRADE_NO_HASH" property="tradeNoHash" jdbcType="INTEGER" />
        <result column="PLAN_ID" property="planId" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="VARCHAR" />
        <result column="CONTRACT_DATE" property="contractDate" jdbcType="VARCHAR" />
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR" />
        <result column="BANK_MERCHANT_NO" property="bankMerchantNo" jdbcType="VARCHAR" />
        <result column="APP_ID" property="appId" jdbcType="VARCHAR" />
        <result column="CONTRACT_ID" property="contractId" jdbcType="VARCHAR" />
        <result column="DISPLAY_ACCOUNT" property="displayAccount" jdbcType="VARCHAR" />
        <result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR" />
        <result column="VERSION" property="version" jdbcType="VARCHAR" />
        <result column="OPENID" property="openid" jdbcType="VARCHAR" />
        <result column="CHANGE_TYPE" property="changeType" jdbcType="VARCHAR" />
        <result column="OPERATE_TIME" property="operateTime" jdbcType="VARCHAR" />
        <result column="EXPIRED_TIME" property="expiredTime" jdbcType="VARCHAR" />
        <result column="TERMINATION_MODE" property="terminationMode" jdbcType="VARCHAR" />
        <result column="CONTRACT_WAY" property="contractWay" jdbcType="VARCHAR" />
        <result column="CONTRACT_SCENE" property="contractScene" jdbcType="VARCHAR" />
        <result column="CONTRACT_STATUS" property="contractStatus" jdbcType="VARCHAR" />
        <result column="RETURN_CODE" property="returnCode" jdbcType="VARCHAR" />
        <result column="RETURN_MSG" property="returnMsg" jdbcType="VARCHAR" />
        <result column="RETURN_WEB" property="returnWeb" jdbcType="VARCHAR" />
        <result column="RETURN_APP" property="returnApp" jdbcType="VARCHAR" />
        <result column="RETURN_APP_ID" property="returnAppId" jdbcType="VARCHAR" />
        <result column="CLIENT_IP" property="clientIp" jdbcType="VARCHAR" />
        <result column="EXTRA" property="extra" jdbcType="VARCHAR" />
        <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR" />
        <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR" />
        <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR" />
        <result column="SIGNED_TIME" property="signedTime" jdbcType="VARCHAR" />
        <result column="DELETE_NOTIFY_URL" property="deleteNotifyUrl" jdbcType="VARCHAR" />
        <result column="OUT_TRADE_NO" property="outTradeNo" jdbcType="VARCHAR" />
        <result column="CONTRACT_TYPE" property="contractType" jdbcType="VARCHAR" />
        <result column="TERMINATED_TIME" property="terminatedTime" jdbcType="VARCHAR" />
        <result column="SUB_MERCHANT_NAME" property="subMerchantName" jdbcType="VARCHAR" />
        <result column="SUB_MERCHANT_SERVICE_NAME" property="subMerchantServiceName" jdbcType="VARCHAR" />
        <result column="SUB_MERCHANT_DESCRIPTION" property="subMerchantDescription" jdbcType="VARCHAR" />
        <result column="PERIOD_TYPE" property="periodType" jdbcType="VARCHAR" />
        <result column="PERIOD" property="period" jdbcType="VARCHAR" />
        <result column="EXECUTE_TIME" property="executeTime" jdbcType="VARCHAR" />
        <result column="SINGLE_AMOUNT" property="singleAmount" jdbcType="DECIMAL" />
        <result column="MOBILE_NO" property="mobileNo" jdbcType="VARCHAR" />
        <result column="FRONT_NOTIFY_URL" property="frontNotifyUrl" jdbcType="VARCHAR" />
        <result column="SIGN_BUS_TYPE" property="signBusType" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Info_Column_List">
        CONTRACT_CODE, CREATE_DATE, PLAN_ID, CREATE_TIME, CONTRACT_DATE, MERCHANT_NO, BANK_MERCHANT_NO,
        APP_ID, CONTRACT_ID, DISPLAY_ACCOUNT, NOTIFY_URL, VERSION, OPENID, CHANGE_TYPE, OPERATE_TIME,
        EXPIRED_TIME, TERMINATION_MODE, CONTRACT_WAY, CONTRACT_SCENE, CONTRACT_STATUS, RETURN_CODE,
        RETURN_MSG, RETURN_WEB, RETURN_APP, RETURN_APP_ID, CLIENT_IP, EXTRA, PROVINCE_CODE,
        PROVINCE_NAME, TM_SMP, SIGNED_TIME, DELETE_NOTIFY_URL, OUT_TRADE_NO, CONTRACT_TYPE,MOBILE_NO,
        FRONT_NOTIFY_URL ,SIGN_BUS_TYPE
    </sql>

    <sql id="Base_Column_List" >
        CONTRACT_CODE, CREATE_DATE, PLAN_ID, CREATE_TIME, CONTRACT_DATE, MERCHANT_NO, BANK_MERCHANT_NO,
        APP_ID, CONTRACT_ID, DISPLAY_ACCOUNT, NOTIFY_URL, VERSION, OPENID, CHANGE_TYPE, OPERATE_TIME,
        EXPIRED_TIME, TERMINATION_MODE, CONTRACT_WAY, CONTRACT_SCENE, CONTRACT_STATUS, RETURN_CODE,
        RETURN_MSG, RETURN_WEB, RETURN_APP, RETURN_APP_ID, CLIENT_IP, EXTRA, PROVINCE_CODE,
        PROVINCE_NAME, TM_SMP, SIGNED_TIME, DELETE_NOTIFY_URL, OUT_TRADE_NO, CONTRACT_TYPE,
        TERMINATED_TIME, SUB_MERCHANT_NAME, SUB_MERCHANT_SERVICE_NAME, SUB_MERCHANT_DESCRIPTION,
        PERIOD_TYPE, PERIOD, EXECUTE_TIME, SINGLE_AMOUNT, MOBILE_NO, FRONT_NOTIFY_URL, SIGN_BUS_TYPE,
        DATA_CENTER
    </sql>

    <select id="findHourContractWaitList" resultMap="FirstResultMap"
            parameterType="com.cmpay.payment.schedule.bo.withhold.ContractQueryBO">
        select ROWNUM, <include refid="Info_Column_List"/>
        from JK_CONTRACT_WITHHOLD
        where CREATE_DATE >= #{requestDate,jdbcType=VARCHAR}
        and (CREATE_DATE || CREATE_TIME) >= #{createDate,jdbcType=VARCHAR}
        and ORA_HASH(CONTRACT_CODE, #{totalSelect,jdbcType=INTEGER}) = #{currentIndex,jdbcType=INTEGER}
        and CONTRACT_STATUS = 'CONTRACT_WAIT'
        and CONTRACT_WAY = #{contractWay,jdbcType=VARCHAR}
        <if test="dataCenter == 'XPQ'" >
            AND (DATA_CENTER = #{dataCenter,jdbcType=VARCHAR} OR DATA_CENTER is null)
        </if>
        <if test="dataCenter == 'YL'" >
            AND DATA_CENTER = #{dataCenter,jdbcType=VARCHAR}
        </if>
        and ROWNUM &lt;=#{param,jdbcType=VARCHAR}
    </select>

    <select id="findContractWaitList" resultMap="FirstResultMap"
            parameterType="com.cmpay.payment.schedule.bo.withhold.ContractQueryBO">
        select ROWNUM, <include refid="Info_Column_List"/>
        from JK_CONTRACT_WITHHOLD
        where CREATE_DATE >= #{requestDate,jdbcType=VARCHAR}
        and (CREATE_DATE || CREATE_TIME) >= #{startTime,jdbcType=VARCHAR}
        and (CREATE_DATE || CREATE_TIME) &lt;= #{endTime,jdbcType=VARCHAR}
        and ORA_HASH(CONTRACT_CODE, #{totalSelect,jdbcType=INTEGER}) = #{currentIndex,jdbcType=INTEGER}
        and CONTRACT_STATUS = 'CONTRACT_WAIT'
        and CONTRACT_WAY = #{contractWay,jdbcType=VARCHAR}
        <if test="dataCenter == 'XPQ'" >
            AND (DATA_CENTER = #{dataCenter,jdbcType=VARCHAR} OR DATA_CENTER is null)
        </if>
        <if test="dataCenter == 'YL'" >
            AND DATA_CENTER = #{dataCenter,jdbcType=VARCHAR}
        </if>
        and ROWNUM &lt;=#{param,jdbcType=VARCHAR}
    </select>


    <delete id="deleteBatch" parameterType="list">
        delete from JK_CONTRACT_WITHHOLD where CONTRACT_CODE in
        <foreach collection="list" item="contractCode" separator="," open="(" close=")">
            #{contractCode,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="findContractCleanList" resultMap="FirstResultMap"
        parameterType="com.cmpay.payment.schedule.bo.withhold.ContractQueryBO">
        select ROWNUM, <include refid="Base_Column_List"/>
        from JK_CONTRACT_WITHHOLD
        where TERMINATED_TIME >= #{startTime,jdbcType=VARCHAR}
        and TERMINATED_TIME &lt;= #{endTime,jdbcType=VARCHAR}
        and ORA_HASH(CONTRACT_CODE, #{totalSelect,jdbcType=INTEGER}) = #{currentIndex,jdbcType=INTEGER}
        and CONTRACT_STATUS in ('CONTRACT_WAIT', 'CONTRACT_FAIL')
        <if test="dataCenter == 'XPQ'" >
            AND (DATA_CENTER = #{dataCenter,jdbcType=VARCHAR} OR DATA_CENTER is null)
        </if>
        <if test="dataCenter == 'YL'" >
            AND DATA_CENTER = #{dataCenter,jdbcType=VARCHAR}
        </if>
        and ROWNUM &lt;=#{param,jdbcType=VARCHAR}
    </select>

    <select id="findTerminationContractCleanList" resultMap="FirstResultMap">
        select ROWNUM,<include refid="Base_Column_List"/>
        from JK_CONTRACT_WITHHOLD
        where TERMINATED_TIME >= #{startTime,jdbcType=VARCHAR}
        and TERMINATED_TIME &lt;= #{endTime,jdbcType=VARCHAR}
        and ORA_HASH(CONTRACT_CODE, #{totalSelect,jdbcType=INTEGER}) = #{currentIndex,jdbcType=INTEGER}
        and CONTRACT_STATUS = 'CONTRACT_TERMINATED'
        <if test="dataCenter == 'XPQ'" >
            AND (DATA_CENTER = #{dataCenter,jdbcType=VARCHAR} OR DATA_CENTER is null)
        </if>
        <if test="dataCenter == 'YL'" >
            AND DATA_CENTER = #{dataCenter,jdbcType=VARCHAR}
        </if>
        and ROWNUM &lt;=#{param,jdbcType=VARCHAR}
    </select>
</mapper>
